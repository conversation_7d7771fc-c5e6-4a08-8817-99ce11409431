@echo off
echo 正在编译hangevacuation插件...
echo.

REM 检查Maven是否安装
mvn --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Maven，请确保Maven已安装并添加到PATH环境变量中
    pause
    exit /b 1
)

REM 清理并编译项目
echo 清理项目...
mvn clean

echo.
echo 编译项目...
mvn package

if %errorlevel% equ 0 (
    echo.
    echo ================================
    echo 编译成功！
    echo 插件文件位置: target\HangEvacuation-Universal-1.9.2.jar
    echo ================================
    echo.
    echo 将jar文件复制到服务器的plugins文件夹中即可使用
) else (
    echo.
    echo ================================
    echo 编译失败！请检查错误信息
    echo ================================
)

echo.
pause
