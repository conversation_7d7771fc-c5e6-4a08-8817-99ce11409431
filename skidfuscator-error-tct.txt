handler=Block #EK, types=[Ljava/io/IOException;], range=[Block #W, Block #V]
handler=Block #EO, types=[Ljava/lang/RuntimeException;], range=[Block #Z, Block #Y]
handler=Block #ES, types=[Ljava/lang/IllegalAccessException;], range=[Block #AC, Block #AB]
handler=Block #EW, types=[Ljava/io/IOException;], range=[Block #AF, Block #AE]
handler=Block #FA, types=[Ljava/lang/RuntimeException;], range=[Block #AI, Block #AH]
handler=Block #FE, types=[Ljava/lang/IllegalAccessException;], range=[Block #AL, Block #AK]
handler=Block #FI, types=[Ljava/lang/RuntimeException;], range=[Block #AO, Block #AN]
handler=Block #FM, types=[Ljava/lang/IllegalAccessException;], range=[Block #AR, Block #AQ]
handler=Block #FQ, types=[Ljava/io/IOException;], range=[Block #AU, Block #AT]
handler=Block #FU, types=[Ljava/lang/RuntimeException;], range=[Block #AX, Block #AW]
handler=Block #FY, types=[Ljava/io/IOException;], range=[Block #BA, Block #AZ]
handler=Block #GC, types=[Ljava/lang/IllegalAccessException;], range=[Block #BD, Block #BC]
handler=Block #GG, types=[Ljava/io/IOException;], range=[Block #BG, Block #BF]
handler=Block #GK, types=[Ljava/lang/IllegalAccessException;], range=[Block #BJ, Block #BI]
handler=Block #GO, types=[Ljava/lang/RuntimeException;], range=[Block #BM, Block #BL]
handler=Block #GS, types=[Ljava/lang/IllegalAccessException;], range=[Block #BP, Block #BO]
handler=Block #GW, types=[Ljava/io/IOException;], range=[Block #BS, Block #BR]
===#Block A(size=4, flags=1)===
   0. lvar35 = {1321617258 ^ {1433751662 ^ 388355704}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. lvar35 = {194581126 ^ lvar35};
      -> Immediate #A -> #B
===#Block B(size=3, flags=0)===
   0. lvar3 = lvar1;
   1. svar37 = {lvar3 ^ lvar35};
   2. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(svar37)) {
      case 111071648:
      	 goto	#DM
      case 111071649:
      	 goto	#DN
      case 111071650:
      	 goto	#DP
      case 111071651:
      	 goto	#DQ
      case 111071652:
      	 goto	#DS
      case 111071653:
      	 goto	#DU
      case 111071654:
      	 goto	#DV
      case 111071659:
      	 goto	#DW
      case 111071660:
      	 goto	#DY
      case 111071661:
      	 goto	#EA
      case 111071672:
      	 goto	#EB
      case 111071673:
      	 goto	#EC
      case 111071674:
      	 goto	#ED
      case 111071675:
      	 goto	#EE
      case 111071676:
      	 goto	#EF
      case 111071678:
      	 goto	#EG
      case 111071679:
      	 goto	#EI
      default:
      	 goto	#EJ
   }
      -> Switch[111071653] #B -> #DU
      -> Switch[111071659] #B -> #DW
      -> Switch[111071675] #B -> #EE
      -> Switch[111071648] #B -> #DM
      -> Switch[111071649] #B -> #DN
      -> DefaultSwitch #B -> #EJ
      -> Switch[111071661] #B -> #EA
      -> Switch[111071654] #B -> #DV
      -> Switch[111071651] #B -> #DQ
      -> Switch[111071650] #B -> #DP
      -> Switch[111071652] #B -> #DS
      -> Switch[111071660] #B -> #DY
      -> Switch[111071672] #B -> #EB
      -> Switch[111071674] #B -> #ED
      -> Switch[111071673] #B -> #EC
      -> Switch[111071679] #B -> #EI
      -> Switch[111071676] #B -> #EF
      -> Switch[111071678] #B -> #EG
      <- Immediate #A -> #B
===#Block EG(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 111071669:
      	 goto	#EH
      case 116721162:
      	 goto	#G
      case 418892311:
      	 goto	#BZ
      case 1141693683:
      	 goto	#EG
      default:
      	 goto	#BZ
   }
      -> Switch[116721162] #EG -> #G
      -> Switch[1141693683] #EG -> #EG
      -> Switch[418892311] #EG -> #BZ
      -> DefaultSwitch #EG -> #BZ
      -> Switch[111071669] #EG -> #EH
      -> Immediate #EG -> #EH
      <- Switch[1141693683] #EG -> #EG
      <- Switch[111071678] #B -> #EG
===#Block EH(size=2, flags=100)===
   0. lvar35 = {1700221860 ^ lvar35};
   1. goto G
      -> UnconditionalJump[GOTO] #EH -> #G
      <- Switch[111071669] #EG -> #EH
      <- Immediate #EG -> #EH
===#Block G(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar9 = lvar0;
   2. _consume(lvar9.resetChance(1386570215));
   3. goto CU
      -> UnconditionalJump[GOTO] #G -> #CU
      <- Switch[116721162] #EG -> #G
      <- UnconditionalJump[GOTO] #EH -> #G
===#Block CU(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 243997422:
      	 goto	#CV
      case 422252319:
      	 goto	#BZ
      case 1316732029:
      	 goto	#CU
      case 1797593881:
      	 goto	#AC
      default:
      	 goto	#BZ
   }
      -> DefaultSwitch #CU -> #BZ
      -> Switch[243997422] #CU -> #CV
      -> Switch[422252319] #CU -> #BZ
      -> Switch[1316732029] #CU -> #CU
      -> Switch[1797593881] #CU -> #AC
      -> Immediate #CU -> #CV
      <- Switch[1316732029] #CU -> #CU
      <- UnconditionalJump[GOTO] #G -> #CU
===#Block CV(size=2, flags=100)===
   0. lvar35 = {302955347 ^ lvar35};
   1. goto AC
      -> UnconditionalJump[GOTO] #CV -> #AC
      <- Switch[243997422] #CU -> #CV
      <- Immediate #CU -> #CV
===#Block AC(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 161964607)
      goto AB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AC -> #AB
      -> TryCatch range: [AC...AB] -> ES ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CV -> #AC
      <- Switch[1797593881] #CU -> #AC
===#Block AB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AC...AB] -> ES ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AC -> #AB
===#Block ES(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -2102273941:
      	 goto	#ET
      case 1621180840:
      	 goto	#EU
      default:
      	 goto	#EV
   }
      -> Switch[1621180840] #ES -> #EU
      -> DefaultSwitch #ES -> #EV
      -> Switch[-2102273941] #ES -> #ET
      <- TryCatch range: [AC...AB] -> ES ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AC...AB] -> ES ([Ljava/lang/IllegalAccessException;])
===#Block ET(size=2, flags=10100)===
   0. lvar35 = {219801176 ^ lvar35};
   1. goto AD
      -> UnconditionalJump[GOTO] #ET -> #AD
      <- Switch[-2102273941] #ES -> #ET
===#Block EV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #ES -> #EV
===#Block EU(size=2, flags=10100)===
   0. lvar35 = {1901852000 ^ lvar35};
   1. goto AD
      -> UnconditionalJump[GOTO] #EU -> #AD
      <- Switch[1621180840] #ES -> #EU
===#Block AD(size=2, flags=0)===
   0. _consume(catch());
   1. goto CG
      -> UnconditionalJump[GOTO] #AD -> #CG
      <- UnconditionalJump[GOTO] #ET -> #AD
      <- UnconditionalJump[GOTO] #EU -> #AD
===#Block CG(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 507046645);
   1. goto U
      -> UnconditionalJump[GOTO] #CG -> #U
      <- UnconditionalJump[GOTO] #AD -> #CG
===#Block EF(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1857585895);
   1. goto S
      -> UnconditionalJump[GOTO] #EF -> #S
      <- Switch[111071676] #B -> #EF
===#Block S(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = lvar0;
   2. lvar32 = {-1773621525 ^ lvar35};
   3. _consume(lvar21.adjustChance(lvar32, 1079902377));
   4. goto CM
      -> UnconditionalJump[GOTO] #S -> #CM
      <- UnconditionalJump[GOTO] #EF -> #S
===#Block CM(size=2, flags=10100)===
   0. lvar35 = {2081262597 ^ lvar35};
   1. goto AO
      -> UnconditionalJump[GOTO] #CM -> #AO
      <- UnconditionalJump[GOTO] #S -> #CM
===#Block AO(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 169482724)
      goto AN
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AO -> #AN
      -> TryCatch range: [AO...AN] -> FI ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #CM -> #AO
===#Block AN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AO...AN] -> FI ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AO -> #AN
===#Block FI(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -1378424640:
      	 goto	#FJ
      case 1090084064:
      	 goto	#FK
      default:
      	 goto	#FL
   }
      -> Switch[1090084064] #FI -> #FK
      -> DefaultSwitch #FI -> #FL
      -> Switch[-1378424640] #FI -> #FJ
      <- TryCatch range: [AO...AN] -> FI ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AO...AN] -> FI ([Ljava/lang/RuntimeException;])
===#Block FJ(size=2, flags=10100)===
   0. lvar35 = {1134072868 ^ lvar35};
   1. goto AP
      -> UnconditionalJump[GOTO] #FJ -> #AP
      <- Switch[-1378424640] #FI -> #FJ
===#Block FL(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #FI -> #FL
===#Block FK(size=2, flags=10100)===
   0. lvar35 = {1581081248 ^ lvar35};
   1. goto AP
      -> UnconditionalJump[GOTO] #FK -> #AP
      <- Switch[1090084064] #FI -> #FK
===#Block AP(size=2, flags=0)===
   0. _consume(catch());
   1. goto CH
      -> UnconditionalJump[GOTO] #AP -> #CH
      <- UnconditionalJump[GOTO] #FJ -> #AP
      <- UnconditionalJump[GOTO] #FK -> #AP
===#Block CH(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 894795420);
   1. goto U
      -> UnconditionalJump[GOTO] #CH -> #U
      <- UnconditionalJump[GOTO] #AP -> #CH
===#Block EI(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1258561725);
   1. goto N
      -> UnconditionalJump[GOTO] #EI -> #N
      <- Switch[111071679] #B -> #EI
===#Block N(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar0;
   2. _consume(lvar16.resetAmount(657008041));
   3. goto DA
      -> UnconditionalJump[GOTO] #N -> #DA
      <- UnconditionalJump[GOTO] #EI -> #N
===#Block DA(size=2, flags=10100)===
   0. lvar35 = {1885359314 ^ lvar35};
   1. goto AU
      -> UnconditionalJump[GOTO] #DA -> #AU
      <- UnconditionalJump[GOTO] #N -> #DA
===#Block AU(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 85000027)
      goto AT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AU -> #AT
      -> TryCatch range: [AU...AT] -> FQ ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #DA -> #AU
===#Block AT(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AU...AT] -> FQ ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AU -> #AT
===#Block FQ(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -480379735:
      	 goto	#FR
      case 1106955707:
      	 goto	#FS
      default:
      	 goto	#FT
   }
      -> DefaultSwitch #FQ -> #FT
      -> Switch[-480379735] #FQ -> #FR
      -> Switch[1106955707] #FQ -> #FS
      <- TryCatch range: [AU...AT] -> FQ ([Ljava/io/IOException;])
      <- TryCatch range: [AU...AT] -> FQ ([Ljava/io/IOException;])
===#Block FS(size=2, flags=10100)===
   0. lvar35 = {164731368 ^ lvar35};
   1. goto AV
      -> UnconditionalJump[GOTO] #FS -> #AV
      <- Switch[1106955707] #FQ -> #FS
===#Block FR(size=2, flags=10100)===
   0. lvar35 = {1569030090 ^ lvar35};
   1. goto AV
      -> UnconditionalJump[GOTO] #FR -> #AV
      <- Switch[-480379735] #FQ -> #FR
===#Block AV(size=2, flags=0)===
   0. _consume(catch());
   1. goto CD
      -> UnconditionalJump[GOTO] #AV -> #CD
      <- UnconditionalJump[GOTO] #FS -> #AV
      <- UnconditionalJump[GOTO] #FR -> #AV
===#Block CD(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 43644927);
   1. goto U
      -> UnconditionalJump[GOTO] #CD -> #U
      <- UnconditionalJump[GOTO] #AV -> #CD
===#Block FT(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #FQ -> #FT
===#Block EC(size=2, flags=10100)===
   0. lvar35 = {418023635 ^ lvar35};
   1. goto L
      -> UnconditionalJump[GOTO] #EC -> #L
      <- Switch[111071673] #B -> #EC
===#Block L(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar14 = lvar0;
   2. lvar28 = {535124776 ^ lvar35};
   3. _consume(lvar14.adjustChance(lvar28, 1079902377));
   4. goto DL
      -> UnconditionalJump[GOTO] #L -> #DL
      <- UnconditionalJump[GOTO] #EC -> #L
===#Block DL(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1952408832);
   1. goto BS
      -> UnconditionalJump[GOTO] #DL -> #BS
      <- UnconditionalJump[GOTO] #L -> #DL
===#Block BS(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 12104661)
      goto BR
   1. throw nullconst;
      -> TryCatch range: [BS...BR] -> GW ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #BS -> #BR
      <- UnconditionalJump[GOTO] #DL -> #BS
===#Block BR(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BS...BR] -> GW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BS -> #BR
===#Block GW(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -1603115679:
      	 goto	#GY
      case 1574039883:
      	 goto	#GX
      default:
      	 goto	#GZ
   }
      -> Switch[-1603115679] #GW -> #GY
      -> Switch[1574039883] #GW -> #GX
      -> DefaultSwitch #GW -> #GZ
      <- TryCatch range: [BS...BR] -> GW ([Ljava/io/IOException;])
      <- TryCatch range: [BS...BR] -> GW ([Ljava/io/IOException;])
===#Block GZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #GW -> #GZ
===#Block GX(size=2, flags=10100)===
   0. lvar35 = {2130672307 ^ lvar35};
   1. goto BT
      -> UnconditionalJump[GOTO] #GX -> #BT
      <- Switch[1574039883] #GW -> #GX
===#Block GY(size=2, flags=10100)===
   0. lvar35 = {558565430 ^ lvar35};
   1. goto BT
      -> UnconditionalJump[GOTO] #GY -> #BT
      <- Switch[-1603115679] #GW -> #GY
===#Block BT(size=2, flags=0)===
   0. _consume(catch());
   1. goto CP
      -> UnconditionalJump[GOTO] #BT -> #CP
      <- UnconditionalJump[GOTO] #GX -> #BT
      <- UnconditionalJump[GOTO] #GY -> #BT
===#Block CP(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 154542511:
      	 goto	#CQ
      case 1908046437:
      	 goto	#BZ
      case 1928597624:
      	 goto	#U
      case 2124186593:
      	 goto	#CP
      default:
      	 goto	#BZ
   }
      -> Switch[1928597624] #CP -> #U
      -> Switch[2124186593] #CP -> #CP
      -> Switch[1908046437] #CP -> #BZ
      -> DefaultSwitch #CP -> #BZ
      -> Switch[154542511] #CP -> #CQ
      -> Immediate #CP -> #CQ
      <- Switch[2124186593] #CP -> #CP
      <- UnconditionalJump[GOTO] #BT -> #CP
===#Block CQ(size=2, flags=100)===
   0. lvar35 = {1983017786 ^ lvar35};
   1. goto U
      -> UnconditionalJump[GOTO] #CQ -> #U
      <- Switch[154542511] #CP -> #CQ
      <- Immediate #CP -> #CQ
===#Block ED(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 921093555);
   1. goto F
      -> UnconditionalJump[GOTO] #ED -> #F
      <- Switch[111071674] #B -> #ED
===#Block F(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar8 = lvar0;
   2. lvar25 = {-837359178 ^ lvar35};
   3. _consume(lvar8.adjustSearchSpeed(lvar25, 83052625));
   4. goto CX
      -> UnconditionalJump[GOTO] #F -> #CX
      <- UnconditionalJump[GOTO] #ED -> #F
===#Block CX(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 11774660:
      	 goto	#CY
      case 1091694545:
      	 goto	#CX
      case 1814736857:
      	 goto	#BZ
      case 1937264289:
      	 goto	#AL
      default:
      	 goto	#BZ
   }
      -> DefaultSwitch #CX -> #BZ
      -> Immediate #CX -> #CY
      -> Switch[1091694545] #CX -> #CX
      -> Switch[1814736857] #CX -> #BZ
      -> Switch[1937264289] #CX -> #AL
      -> Switch[11774660] #CX -> #CY
      <- UnconditionalJump[GOTO] #F -> #CX
      <- Switch[1091694545] #CX -> #CX
===#Block CY(size=2, flags=100)===
   0. lvar35 = {1902363283 ^ lvar35};
   1. goto AL
      -> UnconditionalJump[GOTO] #CY -> #AL
      <- Immediate #CX -> #CY
      <- Switch[11774660] #CX -> #CY
===#Block AL(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 218938604)
      goto AK
   1. throw nullconst;
      -> TryCatch range: [AL...AK] -> FE ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AL -> #AK
      <- UnconditionalJump[GOTO] #CY -> #AL
      <- Switch[1937264289] #CX -> #AL
===#Block AK(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AL...AK] -> FE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AL -> #AK
===#Block FE(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -1369981679:
      	 goto	#FG
      case 72787666:
      	 goto	#FF
      default:
      	 goto	#FH
   }
      -> Switch[72787666] #FE -> #FF
      -> DefaultSwitch #FE -> #FH
      -> Switch[-1369981679] #FE -> #FG
      <- TryCatch range: [AL...AK] -> FE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AL...AK] -> FE ([Ljava/lang/IllegalAccessException;])
===#Block FG(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 2122173207);
   1. goto AM
      -> UnconditionalJump[GOTO] #FG -> #AM
      <- Switch[-1369981679] #FE -> #FG
===#Block FH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #FE -> #FH
===#Block FF(size=2, flags=10100)===
   0. lvar35 = {188606575 ^ lvar35};
   1. goto AM
      -> UnconditionalJump[GOTO] #FF -> #AM
      <- Switch[72787666] #FE -> #FF
===#Block AM(size=2, flags=0)===
   0. _consume(catch());
   1. goto DJ
      -> UnconditionalJump[GOTO] #AM -> #DJ
      <- UnconditionalJump[GOTO] #FG -> #AM
      <- UnconditionalJump[GOTO] #FF -> #AM
===#Block DJ(size=2, flags=10100)===
   0. lvar35 = {683674389 ^ lvar35};
   1. goto U
      -> UnconditionalJump[GOTO] #DJ -> #U
      <- UnconditionalJump[GOTO] #AM -> #DJ
===#Block EB(size=2, flags=10100)===
   0. lvar35 = {1316312819 ^ lvar35};
   1. goto M
      -> UnconditionalJump[GOTO] #EB -> #M
      <- Switch[111071672] #B -> #EB
===#Block M(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar15 = lvar0;
   2. lvar29 = {1232766211 ^ lvar35};
   3. _consume(lvar15.adjustChance(lvar29, 1079902377));
   4. goto CF
      -> UnconditionalJump[GOTO] #M -> #CF
      <- UnconditionalJump[GOTO] #EB -> #M
===#Block CF(size=2, flags=10100)===
   0. lvar35 = {1506352304 ^ lvar35};
   1. goto BJ
      -> UnconditionalJump[GOTO] #CF -> #BJ
      <- UnconditionalJump[GOTO] #M -> #CF
===#Block BJ(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 6023525)
      goto BI
   1. throw nullconst;
      -> TryCatch range: [BJ...BI] -> GK ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #BJ -> #BI
      <- UnconditionalJump[GOTO] #CF -> #BJ
===#Block BI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BJ...BI] -> GK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BJ -> #BI
===#Block GK(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -2053321272:
      	 goto	#GL
      case -585930093:
      	 goto	#GM
      default:
      	 goto	#GN
   }
      -> Switch[-2053321272] #GK -> #GL
      -> DefaultSwitch #GK -> #GN
      -> Switch[-585930093] #GK -> #GM
      <- TryCatch range: [BJ...BI] -> GK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BJ...BI] -> GK ([Ljava/lang/IllegalAccessException;])
===#Block GM(size=2, flags=10100)===
   0. lvar35 = {1668542512 ^ lvar35};
   1. goto BK
      -> UnconditionalJump[GOTO] #GM -> #BK
      <- Switch[-585930093] #GK -> #GM
===#Block GN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #GK -> #GN
===#Block GL(size=2, flags=10100)===
   0. lvar35 = {140641499 ^ lvar35};
   1. goto BK
      -> UnconditionalJump[GOTO] #GL -> #BK
      <- Switch[-2053321272] #GK -> #GL
===#Block BK(size=2, flags=0)===
   0. _consume(catch());
   1. goto CI
      -> UnconditionalJump[GOTO] #BK -> #CI
      <- UnconditionalJump[GOTO] #GL -> #BK
      <- UnconditionalJump[GOTO] #GM -> #BK
===#Block CI(size=2, flags=10100)===
   0. lvar35 = {2074520258 ^ lvar35};
   1. goto U
      -> UnconditionalJump[GOTO] #CI -> #U
      <- UnconditionalJump[GOTO] #BK -> #CI
===#Block DY(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 111071669:
      	 goto	#DZ
      case 320373074:
      	 goto	#I
      case 1043820640:
      	 goto	#DY
      case 1669450027:
      	 goto	#BZ
      default:
      	 goto	#BZ
   }
      -> Switch[320373074] #DY -> #I
      -> DefaultSwitch #DY -> #BZ
      -> Switch[1043820640] #DY -> #DY
      -> Switch[1669450027] #DY -> #BZ
      -> Switch[111071669] #DY -> #DZ
      -> Immediate #DY -> #DZ
      <- Switch[1043820640] #DY -> #DY
      <- Switch[111071660] #B -> #DY
===#Block DZ(size=2, flags=100)===
   0. lvar35 = {1696385088 ^ lvar35};
   1. goto I
      -> UnconditionalJump[GOTO] #DZ -> #I
      <- Switch[111071669] #DY -> #DZ
      <- Immediate #DY -> #DZ
===#Block I(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar11 = lvar0;
   2. _consume(lvar11.editCommands(1997560677));
   3. goto DB
      -> UnconditionalJump[GOTO] #I -> #DB
      <- Switch[320373074] #DY -> #I
      <- UnconditionalJump[GOTO] #DZ -> #I
===#Block DB(size=2, flags=10100)===
   0. lvar35 = {463073136 ^ lvar35};
   1. goto BD
      -> UnconditionalJump[GOTO] #DB -> #BD
      <- UnconditionalJump[GOTO] #I -> #DB
===#Block BD(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 192758733)
      goto BC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BD -> #BC
      -> TryCatch range: [BD...BC] -> GC ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #DB -> #BD
===#Block BC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BD...BC] -> GC ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BD -> #BC
===#Block GC(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -866671021:
      	 goto	#GD
      case 369020472:
      	 goto	#GE
      default:
      	 goto	#GF
   }
      -> DefaultSwitch #GC -> #GF
      -> Switch[-866671021] #GC -> #GD
      -> Switch[369020472] #GC -> #GE
      <- TryCatch range: [BD...BC] -> GC ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BD...BC] -> GC ([Ljava/lang/IllegalAccessException;])
===#Block GE(size=2, flags=10100)===
   0. lvar35 = {2129048079 ^ lvar35};
   1. goto BE
      -> UnconditionalJump[GOTO] #GE -> #BE
      <- Switch[369020472] #GC -> #GE
===#Block GD(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 97754882);
   1. goto BE
      -> UnconditionalJump[GOTO] #GD -> #BE
      <- Switch[-866671021] #GC -> #GD
===#Block BE(size=2, flags=0)===
   0. _consume(catch());
   1. goto CZ
      -> UnconditionalJump[GOTO] #BE -> #CZ
      <- UnconditionalJump[GOTO] #GE -> #BE
      <- UnconditionalJump[GOTO] #GD -> #BE
===#Block CZ(size=2, flags=10100)===
   0. lvar35 = {523132008 ^ lvar35};
   1. goto U
      -> UnconditionalJump[GOTO] #CZ -> #U
      <- UnconditionalJump[GOTO] #BE -> #CZ
===#Block GF(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #GC -> #GF
===#Block DS(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 111071669:
      	 goto	#DT
      case 350475498:
      	 goto	#P
      case 569027752:
      	 goto	#BZ
      case 1630193930:
      	 goto	#DS
      default:
      	 goto	#BZ
   }
      -> DefaultSwitch #DS -> #BZ
      -> Switch[111071669] #DS -> #DT
      -> Switch[1630193930] #DS -> #DS
      -> Switch[569027752] #DS -> #BZ
      -> Switch[350475498] #DS -> #P
      -> Immediate #DS -> #DT
      <- Switch[1630193930] #DS -> #DS
      <- Switch[111071652] #B -> #DS
===#Block DT(size=2, flags=100)===
   0. lvar35 = {961932073 ^ lvar35};
   1. goto P
      -> UnconditionalJump[GOTO] #DT -> #P
      <- Switch[111071669] #DS -> #DT
      <- Immediate #DS -> #DT
===#Block P(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar18 = lvar0;
   2. _consume(lvar18.resetSearchSpeed(236281246));
   3. goto BX
      -> UnconditionalJump[GOTO] #P -> #BX
      <- Switch[350475498] #DS -> #P
      <- UnconditionalJump[GOTO] #DT -> #P
===#Block BX(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 147779650:
      	 goto	#BY
      case 558987165:
      	 goto	#AR
      case 769566538:
      	 goto	#BX
      case 1464133341:
      	 goto	#BZ
      default:
      	 goto	#BZ
   }
      -> DefaultSwitch #BX -> #BZ
      -> Switch[147779650] #BX -> #BY
      -> Switch[769566538] #BX -> #BX
      -> Switch[1464133341] #BX -> #BZ
      -> Immediate #BX -> #BY
      -> Switch[558987165] #BX -> #AR
      <- Switch[769566538] #BX -> #BX
      <- UnconditionalJump[GOTO] #P -> #BX
===#Block BY(size=2, flags=100)===
   0. lvar35 = {382871100 ^ lvar35};
   1. goto AR
      -> UnconditionalJump[GOTO] #BY -> #AR
      <- Switch[147779650] #BX -> #BY
      <- Immediate #BX -> #BY
===#Block AR(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 243766119)
      goto AQ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AR -> #AQ
      -> TryCatch range: [AR...AQ] -> FM ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BY -> #AR
      <- Switch[558987165] #BX -> #AR
===#Block AQ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AR...AQ] -> FM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AR -> #AQ
===#Block FM(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -2047169095:
      	 goto	#FO
      case 1145108345:
      	 goto	#FN
      default:
      	 goto	#FP
   }
      -> DefaultSwitch #FM -> #FP
      -> Switch[1145108345] #FM -> #FN
      -> Switch[-2047169095] #FM -> #FO
      <- TryCatch range: [AR...AQ] -> FM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AR...AQ] -> FM ([Ljava/lang/IllegalAccessException;])
===#Block FO(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1110113181);
   1. goto AS
      -> UnconditionalJump[GOTO] #FO -> #AS
      <- Switch[-2047169095] #FM -> #FO
===#Block FN(size=2, flags=10100)===
   0. lvar35 = {1511895237 ^ lvar35};
   1. goto AS
      -> UnconditionalJump[GOTO] #FN -> #AS
      <- Switch[1145108345] #FM -> #FN
===#Block AS(size=2, flags=0)===
   0. _consume(catch());
   1. goto DF
      -> UnconditionalJump[GOTO] #AS -> #DF
      <- UnconditionalJump[GOTO] #FO -> #AS
      <- UnconditionalJump[GOTO] #FN -> #AS
===#Block DF(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 235041764:
      	 goto	#DG
      case 390815783:
      	 goto	#BZ
      case 1305457667:
      	 goto	#U
      case 1787313665:
      	 goto	#DF
      default:
      	 goto	#BZ
   }
      -> Switch[390815783] #DF -> #BZ
      -> Switch[235041764] #DF -> #DG
      -> DefaultSwitch #DF -> #BZ
      -> Immediate #DF -> #DG
      -> Switch[1787313665] #DF -> #DF
      -> Switch[1305457667] #DF -> #U
      <- UnconditionalJump[GOTO] #AS -> #DF
      <- Switch[1787313665] #DF -> #DF
===#Block DG(size=2, flags=100)===
   0. lvar35 = {300063114 ^ lvar35};
   1. goto U
      -> UnconditionalJump[GOTO] #DG -> #U
      <- Switch[235041764] #DF -> #DG
      <- Immediate #DF -> #DG
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #FM -> #FP
===#Block DP(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 905394105);
   1. goto R
      -> UnconditionalJump[GOTO] #DP -> #R
      <- Switch[111071650] #B -> #DP
===#Block R(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar20 = lvar0;
   2. lvar31 = {855172162 ^ lvar35};
   3. _consume(lvar20.adjustAmount(lvar31, 1029574434));
   4. goto BU
      -> UnconditionalJump[GOTO] #R -> #BU
      <- UnconditionalJump[GOTO] #DP -> #R
===#Block BU(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1555172265);
   1. goto W
      -> UnconditionalJump[GOTO] #BU -> #W
      <- UnconditionalJump[GOTO] #R -> #BU
===#Block W(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 95510175)
      goto V
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #W -> #V
      -> TryCatch range: [W...V] -> EK ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BU -> #W
===#Block V(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [W...V] -> EK ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #W -> #V
===#Block EK(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case 270518754:
      	 goto	#EM
      case 1918336851:
      	 goto	#EL
      default:
      	 goto	#EN
   }
      -> Switch[1918336851] #EK -> #EL
      -> DefaultSwitch #EK -> #EN
      -> Switch[270518754] #EK -> #EM
      <- TryCatch range: [W...V] -> EK ([Ljava/io/IOException;])
      <- TryCatch range: [W...V] -> EK ([Ljava/io/IOException;])
===#Block EM(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1680040091);
   1. goto X
      -> UnconditionalJump[GOTO] #EM -> #X
      <- Switch[270518754] #EK -> #EM
===#Block EN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #EK -> #EN
===#Block EL(size=2, flags=10100)===
   0. lvar35 = {1214936653 ^ lvar35};
   1. goto X
      -> UnconditionalJump[GOTO] #EL -> #X
      <- Switch[1918336851] #EK -> #EL
===#Block X(size=2, flags=0)===
   0. _consume(catch());
   1. goto CE
      -> UnconditionalJump[GOTO] #X -> #CE
      <- UnconditionalJump[GOTO] #EM -> #X
      <- UnconditionalJump[GOTO] #EL -> #X
===#Block CE(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1163374087);
   1. goto U
      -> UnconditionalJump[GOTO] #CE -> #U
      <- UnconditionalJump[GOTO] #X -> #CE
===#Block DQ(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 111071669:
      	 goto	#DR
      case 169222254:
      	 goto	#BZ
      case 831054703:
      	 goto	#DQ
      case 1800083055:
      	 goto	#C
      default:
      	 goto	#BZ
   }
      -> Switch[169222254] #DQ -> #BZ
      -> DefaultSwitch #DQ -> #BZ
      -> Switch[111071669] #DQ -> #DR
      -> Switch[831054703] #DQ -> #DQ
      -> Immediate #DQ -> #DR
      -> Switch[1800083055] #DQ -> #C
      <- Switch[111071651] #B -> #DQ
      <- Switch[831054703] #DQ -> #DQ
===#Block DR(size=2, flags=100)===
   0. lvar35 = {1687994208 ^ lvar35};
   1. goto C
      -> UnconditionalJump[GOTO] #DR -> #C
      <- Switch[111071669] #DQ -> #DR
      <- Immediate #DQ -> #DR
===#Block C(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar5 = lvar0;
   2. lvar4 = {1670605979 ^ lvar35};
   3. _consume(lvar5.adjustSearchSpeed(lvar4, 83052625));
   4. goto CK
      -> UnconditionalJump[GOTO] #C -> #CK
      <- UnconditionalJump[GOTO] #DR -> #C
      <- Switch[1800083055] #DQ -> #C
===#Block CK(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1934783172);
   1. goto AI
      -> UnconditionalJump[GOTO] #CK -> #AI
      <- UnconditionalJump[GOTO] #C -> #CK
===#Block AI(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 7748119)
      goto AH
   1. throw nullconst;
      -> TryCatch range: [AI...AH] -> FA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AI -> #AH
      <- UnconditionalJump[GOTO] #CK -> #AI
===#Block AH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AI...AH] -> FA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AI -> #AH
===#Block FA(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -2046233872:
      	 goto	#FB
      case -475090983:
      	 goto	#FC
      default:
      	 goto	#FD
   }
      -> DefaultSwitch #FA -> #FD
      -> Switch[-2046233872] #FA -> #FB
      -> Switch[-475090983] #FA -> #FC
      <- TryCatch range: [AI...AH] -> FA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AI...AH] -> FA ([Ljava/lang/RuntimeException;])
===#Block FC(size=2, flags=10100)===
   0. lvar35 = {1361812811 ^ lvar35};
   1. goto AJ
      -> UnconditionalJump[GOTO] #FC -> #AJ
      <- Switch[-475090983] #FA -> #FC
===#Block FB(size=2, flags=10100)===
   0. lvar35 = {2107594478 ^ lvar35};
   1. goto AJ
      -> UnconditionalJump[GOTO] #FB -> #AJ
      <- Switch[-2046233872] #FA -> #FB
===#Block AJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CJ
      -> UnconditionalJump[GOTO] #AJ -> #CJ
      <- UnconditionalJump[GOTO] #FC -> #AJ
      <- UnconditionalJump[GOTO] #FB -> #AJ
===#Block CJ(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 237600528);
   1. goto U
      -> UnconditionalJump[GOTO] #CJ -> #U
      <- UnconditionalJump[GOTO] #AJ -> #CJ
===#Block FD(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #FA -> #FD
===#Block DV(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 894933708);
   1. goto T
      -> UnconditionalJump[GOTO] #DV -> #T
      <- Switch[111071654] #B -> #DV
===#Block T(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar0;
   2. lvar33 = {844646707 ^ lvar35};
   3. _consume(lvar22.adjustSearchSpeed(lvar33, 83052625));
   4. goto CS
      -> UnconditionalJump[GOTO] #T -> #CS
      <- UnconditionalJump[GOTO] #DV -> #T
===#Block CS(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1913189112);
   1. goto BM
      -> UnconditionalJump[GOTO] #CS -> #BM
      <- UnconditionalJump[GOTO] #T -> #CS
===#Block BM(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 211561359)
      goto BL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BM -> #BL
      -> TryCatch range: [BM...BL] -> GO ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #CS -> #BM
===#Block BL(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BM...BL] -> GO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BM -> #BL
===#Block GO(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -1290281320:
      	 goto	#GQ
      case 42327666:
      	 goto	#GP
      default:
      	 goto	#GR
   }
      -> DefaultSwitch #GO -> #GR
      -> Switch[-1290281320] #GO -> #GQ
      -> Switch[42327666] #GO -> #GP
      <- TryCatch range: [BM...BL] -> GO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BM...BL] -> GO ([Ljava/lang/RuntimeException;])
===#Block GP(size=2, flags=10100)===
   0. lvar35 = {443322298 ^ lvar35};
   1. goto BN
      -> UnconditionalJump[GOTO] #GP -> #BN
      <- Switch[42327666] #GO -> #GP
===#Block GQ(size=2, flags=10100)===
   0. lvar35 = {1281281959 ^ lvar35};
   1. goto BN
      -> UnconditionalJump[GOTO] #GQ -> #BN
      <- Switch[-1290281320] #GO -> #GQ
===#Block BN(size=2, flags=0)===
   0. _consume(catch());
   1. goto CR
      -> UnconditionalJump[GOTO] #BN -> #CR
      <- UnconditionalJump[GOTO] #GP -> #BN
      <- UnconditionalJump[GOTO] #GQ -> #BN
===#Block CR(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 961217492);
   1. goto U
      -> UnconditionalJump[GOTO] #CR -> #U
      <- UnconditionalJump[GOTO] #BN -> #CR
===#Block GR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #GO -> #GR
===#Block EA(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1631161850);
   1. goto O
      -> UnconditionalJump[GOTO] #EA -> #O
      <- Switch[111071661] #B -> #EA
===#Block O(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar0;
   2. _consume(lvar17.cancelAndReturn(675615461));
   3. lvar35 = {88172960 ^ lvar35};
      -> Immediate #O -> #U
      <- UnconditionalJump[GOTO] #EA -> #O
===#Block EJ(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1685644378);
   1. goto U
      -> UnconditionalJump[GOTO] #EJ -> #U
      <- DefaultSwitch #B -> #EJ
===#Block DN(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 111071669:
      	 goto	#DO
      case 124149198:
      	 goto	#DN
      case 595505497:
      	 goto	#K
      case 1753899481:
      	 goto	#BZ
      default:
      	 goto	#BZ
   }
      -> Switch[595505497] #DN -> #K
      -> DefaultSwitch #DN -> #BZ
      -> Switch[111071669] #DN -> #DO
      -> Switch[1753899481] #DN -> #BZ
      -> Switch[124149198] #DN -> #DN
      -> Immediate #DN -> #DO
      <- Switch[111071649] #B -> #DN
      <- Switch[124149198] #DN -> #DN
===#Block DO(size=2, flags=100)===
   0. lvar35 = {1748624805 ^ lvar35};
   1. goto K
      -> UnconditionalJump[GOTO] #DO -> #K
      <- Switch[111071669] #DN -> #DO
      <- Immediate #DN -> #DO
===#Block K(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar13 = lvar0;
   2. lvar27 = {1865823829 ^ lvar35};
   3. _consume(lvar13.adjustAmount(lvar27, 1029574434));
   4. goto CN
      -> UnconditionalJump[GOTO] #K -> #CN
      <- Switch[595505497] #DN -> #K
      <- UnconditionalJump[GOTO] #DO -> #K
===#Block CN(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 125396590:
      	 goto	#CO
      case 317489359:
      	 goto	#CN
      case 490471672:
      	 goto	#AX
      case 1004895687:
      	 goto	#BZ
      default:
      	 goto	#BZ
   }
      -> Switch[317489359] #CN -> #CN
      -> DefaultSwitch #CN -> #BZ
      -> Switch[1004895687] #CN -> #BZ
      -> Switch[125396590] #CN -> #CO
      -> Switch[490471672] #CN -> #AX
      -> Immediate #CN -> #CO
      <- Switch[317489359] #CN -> #CN
      <- UnconditionalJump[GOTO] #K -> #CN
===#Block CO(size=2, flags=100)===
   0. lvar35 = {952413223 ^ lvar35};
   1. goto AX
      -> UnconditionalJump[GOTO] #CO -> #AX
      <- Switch[125396590] #CN -> #CO
      <- Immediate #CN -> #CO
===#Block AX(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 174480570)
      goto AW
   1. throw nullconst;
      -> TryCatch range: [AX...AW] -> FU ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AX -> #AW
      <- UnconditionalJump[GOTO] #CO -> #AX
      <- Switch[490471672] #CN -> #AX
===#Block AW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AX...AW] -> FU ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AX -> #AW
===#Block FU(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -1080765502:
      	 goto	#FV
      case -962155309:
      	 goto	#FW
      default:
      	 goto	#FX
   }
      -> Switch[-1080765502] #FU -> #FV
      -> Switch[-962155309] #FU -> #FW
      -> DefaultSwitch #FU -> #FX
      <- TryCatch range: [AX...AW] -> FU ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AX...AW] -> FU ([Ljava/lang/RuntimeException;])
===#Block FX(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #FU -> #FX
===#Block FW(size=2, flags=10100)===
   0. lvar35 = {463804044 ^ lvar35};
   1. goto AY
      -> UnconditionalJump[GOTO] #FW -> #AY
      <- Switch[-962155309] #FU -> #FW
===#Block FV(size=2, flags=10100)===
   0. lvar35 = {881023342 ^ lvar35};
   1. goto AY
      -> UnconditionalJump[GOTO] #FV -> #AY
      <- Switch[-1080765502] #FU -> #FV
===#Block AY(size=2, flags=0)===
   0. _consume(catch());
   1. goto DD
      -> UnconditionalJump[GOTO] #AY -> #DD
      <- UnconditionalJump[GOTO] #FW -> #AY
      <- UnconditionalJump[GOTO] #FV -> #AY
===#Block DD(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 11274891:
      	 goto	#DE
      case 13239125:
      	 goto	#DD
      case 1388080108:
      	 goto	#BZ
      case 1612836067:
      	 goto	#U
      default:
      	 goto	#BZ
   }
      -> Switch[13239125] #DD -> #DD
      -> Immediate #DD -> #DE
      -> DefaultSwitch #DD -> #BZ
      -> Switch[11274891] #DD -> #DE
      -> Switch[1388080108] #DD -> #BZ
      -> Switch[1612836067] #DD -> #U
      <- Switch[13239125] #DD -> #DD
      <- UnconditionalJump[GOTO] #AY -> #DD
===#Block DE(size=2, flags=100)===
   0. lvar35 = {455862 ^ lvar35};
   1. goto U
      -> UnconditionalJump[GOTO] #DE -> #U
      <- Immediate #DD -> #DE
      <- Switch[11274891] #DD -> #DE
===#Block DM(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1993644962);
   1. goto E
      -> UnconditionalJump[GOTO] #DM -> #E
      <- Switch[111071648] #B -> #DM
===#Block E(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar7 = lvar0;
   2. lvar24 = {-1910196313 ^ lvar35};
   3. _consume(lvar7.adjustAmount(lvar24, 1029574434));
   4. goto CW
      -> UnconditionalJump[GOTO] #E -> #CW
      <- UnconditionalJump[GOTO] #DM -> #E
===#Block CW(size=2, flags=10100)===
   0. lvar35 = {1707184047 ^ lvar35};
   1. goto AF
      -> UnconditionalJump[GOTO] #CW -> #AF
      <- UnconditionalJump[GOTO] #E -> #CW
===#Block AF(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 116651892)
      goto AE
   1. throw nullconst;
      -> TryCatch range: [AF...AE] -> EW ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #AF -> #AE
      <- UnconditionalJump[GOTO] #CW -> #AF
===#Block AE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AF...AE] -> EW ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AF -> #AE
===#Block EW(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -1596571720:
      	 goto	#EX
      case 2114991280:
      	 goto	#EY
      default:
      	 goto	#EZ
   }
      -> DefaultSwitch #EW -> #EZ
      -> Switch[2114991280] #EW -> #EY
      -> Switch[-1596571720] #EW -> #EX
      <- TryCatch range: [AF...AE] -> EW ([Ljava/io/IOException;])
      <- TryCatch range: [AF...AE] -> EW ([Ljava/io/IOException;])
===#Block EX(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 706390270);
   1. goto AG
      -> UnconditionalJump[GOTO] #EX -> #AG
      <- Switch[-1596571720] #EW -> #EX
===#Block EY(size=2, flags=10100)===
   0. lvar35 = {834824607 ^ lvar35};
   1. goto AG
      -> UnconditionalJump[GOTO] #EY -> #AG
      <- Switch[2114991280] #EW -> #EY
===#Block AG(size=2, flags=0)===
   0. _consume(catch());
   1. goto CC
      -> UnconditionalJump[GOTO] #AG -> #CC
      <- UnconditionalJump[GOTO] #EX -> #AG
      <- UnconditionalJump[GOTO] #EY -> #AG
===#Block CC(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1568102569);
   1. goto U
      -> UnconditionalJump[GOTO] #CC -> #U
      <- UnconditionalJump[GOTO] #AG -> #CC
===#Block EZ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #EW -> #EZ
===#Block EE(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 343418317);
   1. goto D
      -> UnconditionalJump[GOTO] #EE -> #D
      <- Switch[111071675] #B -> #EE
===#Block D(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar6 = lvar0;
   2. lvar23 = {-326628916 ^ lvar35};
   3. _consume(lvar6.adjustSearchSpeed(lvar23, 83052625));
   4. goto CA
      -> UnconditionalJump[GOTO] #D -> #CA
      <- UnconditionalJump[GOTO] #EE -> #D
===#Block CA(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 95977085:
      	 goto	#CB
      case 164013607:
      	 goto	#BA
      case 570464513:
      	 goto	#BZ
      case 1516732211:
      	 goto	#CA
      default:
      	 goto	#BZ
   }
      -> Switch[95977085] #CA -> #CB
      -> Switch[1516732211] #CA -> #CA
      -> DefaultSwitch #CA -> #BZ
      -> Switch[164013607] #CA -> #BA
      -> Immediate #CA -> #CB
      -> Switch[570464513] #CA -> #BZ
      <- Switch[1516732211] #CA -> #CA
      <- UnconditionalJump[GOTO] #D -> #CA
===#Block CB(size=2, flags=100)===
   0. lvar35 = {730449525 ^ lvar35};
   1. goto BA
      -> UnconditionalJump[GOTO] #CB -> #BA
      <- Switch[95977085] #CA -> #CB
      <- Immediate #CA -> #CB
===#Block BA(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 241976705)
      goto AZ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BA -> #AZ
      -> TryCatch range: [BA...AZ] -> FY ([Ljava/io/IOException;])
      <- Switch[164013607] #CA -> #BA
      <- UnconditionalJump[GOTO] #CB -> #BA
===#Block AZ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BA...AZ] -> FY ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BA -> #AZ
===#Block FY(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -940473839:
      	 goto	#FZ
      case 1561454826:
      	 goto	#GA
      default:
      	 goto	#GB
   }
      -> DefaultSwitch #FY -> #GB
      -> Switch[1561454826] #FY -> #GA
      -> Switch[-940473839] #FY -> #FZ
      <- TryCatch range: [BA...AZ] -> FY ([Ljava/io/IOException;])
      <- TryCatch range: [BA...AZ] -> FY ([Ljava/io/IOException;])
===#Block FZ(size=2, flags=10100)===
   0. lvar35 = {1472763236 ^ lvar35};
   1. goto BB
      -> UnconditionalJump[GOTO] #FZ -> #BB
      <- Switch[-940473839] #FY -> #FZ
===#Block GA(size=2, flags=10100)===
   0. lvar35 = {613718075 ^ lvar35};
   1. goto BB
      -> UnconditionalJump[GOTO] #GA -> #BB
      <- Switch[1561454826] #FY -> #GA
===#Block BB(size=2, flags=0)===
   0. _consume(catch());
   1. goto DC
      -> UnconditionalJump[GOTO] #BB -> #DC
      <- UnconditionalJump[GOTO] #GA -> #BB
      <- UnconditionalJump[GOTO] #FZ -> #BB
===#Block DC(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 205623942);
   1. goto U
      -> UnconditionalJump[GOTO] #DC -> #U
      <- UnconditionalJump[GOTO] #BB -> #DC
===#Block GB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #FY -> #GB
===#Block DW(size=1, flags=10100)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35)) {
      case 111071669:
      	 goto	#DX
      case 421643459:
      	 goto	#J
      case 944340653:
      	 goto	#BZ
      case 1334860048:
      	 goto	#DW
      default:
      	 goto	#BZ
   }
      -> DefaultSwitch #DW -> #BZ
      -> Switch[944340653] #DW -> #BZ
      -> Immediate #DW -> #DX
      -> Switch[1334860048] #DW -> #DW
      -> Switch[111071669] #DW -> #DX
      -> Switch[421643459] #DW -> #J
      <- Switch[111071659] #B -> #DW
      <- Switch[1334860048] #DW -> #DW
===#Block DX(size=2, flags=100)===
   0. lvar35 = {1422199732 ^ lvar35};
   1. goto J
      -> UnconditionalJump[GOTO] #DX -> #J
      <- Immediate #DW -> #DX
      <- Switch[111071669] #DW -> #DX
===#Block J(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar0;
   2. _consume(lvar12.saveAndReturn(819145927));
   3. goto BV
      -> UnconditionalJump[GOTO] #J -> #BV
      <- UnconditionalJump[GOTO] #DX -> #J
      <- Switch[421643459] #DW -> #J
===#Block BV(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 122728848);
   1. goto Z
      -> UnconditionalJump[GOTO] #BV -> #Z
      <- UnconditionalJump[GOTO] #J -> #BV
===#Block Z(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 65763716)
      goto Y
   1. throw nullconst;
      -> TryCatch range: [Z...Y] -> EO ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #Z -> #Y
      <- UnconditionalJump[GOTO] #BV -> #Z
===#Block Y(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [Z...Y] -> EO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #Z -> #Y
===#Block EO(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case -1529655566:
      	 goto	#EP
      case 1605336723:
      	 goto	#EQ
      default:
      	 goto	#ER
   }
      -> DefaultSwitch #EO -> #ER
      -> Switch[-1529655566] #EO -> #EP
      -> Switch[1605336723] #EO -> #EQ
      <- TryCatch range: [Z...Y] -> EO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [Z...Y] -> EO ([Ljava/lang/RuntimeException;])
===#Block EQ(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 2108421379);
   1. goto AA
      -> UnconditionalJump[GOTO] #EQ -> #AA
      <- Switch[1605336723] #EO -> #EQ
===#Block EP(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1120170767);
   1. goto AA
      -> UnconditionalJump[GOTO] #EP -> #AA
      <- Switch[-1529655566] #EO -> #EP
===#Block AA(size=2, flags=0)===
   0. _consume(catch());
   1. goto CT
      -> UnconditionalJump[GOTO] #AA -> #CT
      <- UnconditionalJump[GOTO] #EP -> #AA
      <- UnconditionalJump[GOTO] #EQ -> #AA
===#Block CT(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 1965632881);
   1. goto U
      -> UnconditionalJump[GOTO] #CT -> #U
      <- UnconditionalJump[GOTO] #AA -> #CT
===#Block ER(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #EO -> #ER
===#Block BZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      <- DefaultSwitch #DW -> #BZ
      <- Switch[944340653] #DW -> #BZ
      <- DefaultSwitch #CA -> #BZ
      <- Switch[418892311] #EG -> #BZ
      <- DefaultSwitch #EG -> #BZ
      <- DefaultSwitch #DD -> #BZ
      <- DefaultSwitch #DN -> #BZ
      <- Switch[1753899481] #DN -> #BZ
      <- Switch[1388080108] #DD -> #BZ
      <- DefaultSwitch #CX -> #BZ
      <- DefaultSwitch #CU -> #BZ
      <- Switch[422252319] #CU -> #BZ
      <- DefaultSwitch #DY -> #BZ
      <- Switch[1669450027] #DY -> #BZ
      <- Switch[1814736857] #CX -> #BZ
      <- DefaultSwitch #CN -> #BZ
      <- DefaultSwitch #BX -> #BZ
      <- DefaultSwitch #DS -> #BZ
      <- Switch[1004895687] #CN -> #BZ
      <- Switch[1464133341] #BX -> #BZ
      <- Switch[569027752] #DS -> #BZ
      <- Switch[1908046437] #CP -> #BZ
      <- DefaultSwitch #CP -> #BZ
      <- Switch[390815783] #DF -> #BZ
      <- DefaultSwitch #DH -> #BZ
      <- Switch[1113244197] #DH -> #BZ
      <- Switch[169222254] #DQ -> #BZ
      <- DefaultSwitch #DQ -> #BZ
      <- DefaultSwitch #DF -> #BZ
      <- Switch[570464513] #CA -> #BZ
===#Block DU(size=2, flags=10100)===
   0. lvar35 = {910133315 ^ lvar35};
   1. goto H
      -> UnconditionalJump[GOTO] #DU -> #H
      <- Switch[111071653] #B -> #DU
===#Block H(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar10 = lvar0;
   2. lvar26 = {-825252785 ^ lvar35};
   3. _consume(lvar10.adjustAmount(lvar26, 1029574434));
   4. goto DK
      -> UnconditionalJump[GOTO] #H -> #DK
      <- UnconditionalJump[GOTO] #DU -> #H
===#Block DK(size=2, flags=10100)===
   0. lvar35 = {1512650411 ^ lvar35};
   1. goto BP
      -> UnconditionalJump[GOTO] #DK -> #BP
      <- UnconditionalJump[GOTO] #H -> #DK
===#Block BP(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 260122699)
      goto BO
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BP -> #BO
      -> TryCatch range: [BP...BO] -> GS ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #DK -> #BP
===#Block BO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BP...BO] -> GS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BP -> #BO
===#Block GS(size=1, flags=0)===
   0. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.mfdrdkjnmcawtkgw(lvar35)) {
      case 693483105:
      	 goto	#GU
      case 1489725587:
      	 goto	#GT
      default:
      	 goto	#GV
   }
      -> Switch[1489725587] #GS -> #GT
      -> Switch[693483105] #GS -> #GU
      -> DefaultSwitch #GS -> #GV
      <- TryCatch range: [BP...BO] -> GS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BP...BO] -> GS ([Ljava/lang/IllegalAccessException;])
===#Block GV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #GS -> #GV
===#Block GU(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.TreasureEditGUI.wvbtcivbwcvqbrag(lvar35, 2029628826);
   1. goto BQ
      -> UnconditionalJump[GOTO] #GU -> #BQ
      <- Switch[693483105] #GS -> #GU
===#Block GT(size=2, flags=10100)===
   0. lvar35 = {919238212 ^ lvar35};
   1. goto BQ
      -> UnconditionalJump[GOTO] #GT -> #BQ
      <- Switch[1489725587] #GS -> #GT
===#Block BQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto BW
      -> UnconditionalJump[GOTO] #BQ -> #BW
      <- UnconditionalJump[GOTO] #GU -> #BQ
      <- UnconditionalJump[GOTO] #GT -> #BQ
===#Block BW(size=2, flags=10100)===
   0. lvar35 = {1050946806 ^ lvar35};
   1. goto U
      -> UnconditionalJump[GOTO] #BW -> #U
      <- UnconditionalJump[GOTO] #BQ -> #BW
===#Block U(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. return;
      <- UnconditionalJump[GOTO] #CI -> #U
      <- UnconditionalJump[GOTO] #CZ -> #U
      <- UnconditionalJump[GOTO] #DE -> #U
      <- UnconditionalJump[GOTO] #CT -> #U
      <- UnconditionalJump[GOTO] #CC -> #U
      <- UnconditionalJump[GOTO] #CG -> #U
      <- Switch[1612836067] #DD -> #U
      <- UnconditionalJump[GOTO] #CQ -> #U
      <- UnconditionalJump[GOTO] #CR -> #U
      <- Immediate #O -> #U
      <- UnconditionalJump[GOTO] #CL -> #U
      <- Switch[1305457667] #DF -> #U
      <- UnconditionalJump[GOTO] #CJ -> #U
      <- UnconditionalJump[GOTO] #CH -> #U
      <- UnconditionalJump[GOTO] #BW -> #U
      <- UnconditionalJump[GOTO] #EJ -> #U
      <- Switch[1928597624] #CP -> #U
      <- UnconditionalJump[GOTO] #DC -> #U
      <- UnconditionalJump[GOTO] #CE -> #U
      <- UnconditionalJump[GOTO] #DJ -> #U
      <- UnconditionalJump[GOTO] #CD -> #U
      <- UnconditionalJump[GOTO] #DG -> #U
