===============================================
    HangEvacuation 日志顺序修复和调试增强 - v1.7.0
===============================================

🎮 插件名称: HangEvacuation (Hang摸金撤离)
📅 修复日期: 2024-12-19
🔧 版本号: 1.7.0
👨‍💻 作者: hangzong(航总)
📞 技术支持: 微信 hang060217

===============================================
              🐛 修复的问题
===============================================

❌ **问题1：日志顺序混乱**
- 现象："已加载 6 个摸金箱种类" 出现在启用横幅之前
- 影响：日志结构不合理，信息分散

❌ **问题2：其他几个类型的摸金箱里面还是无法正常显示物品**
- 现象：除了普通摸金箱，其他种类的摸金箱可能没有物品
- 影响：物品分类系统可能有问题

❌ **问题3：缺少调试信息**
- 现象：无法确定物品分类逻辑是否正常工作
- 影响：问题排查困难

===============================================
              ✅ 修复方案
===============================================

🎯 **解决方案1：修复日志顺序**

📋 **技术实现**：
1. **统一日志输出位置**
   - 将摸金箱种类统计移到主插件启用信息中
   - 移除ChestTypeManager中的重复日志

2. **优化日志结构**
   ```
   [HangEvacuation] === HangEvacuation 1.7.0-1.8.8-1.21.4 已启用! ===
   [HangEvacuation] 当前服务端: Paper | 支持版本: 1.8.8-1.21.4
   [HangEvacuation] 已加载 0 个撤离区域
   [HangEvacuation] 已加载 12 个摸金箱物品
   [HangEvacuation] 已加载 2 个模组物品
   [HangEvacuation] 已加载 6 个摸金箱种类  ← 正确位置
   [HangEvacuation] 已加载 10 个等级配置
   [HangEvacuation] 已创建摸金箱数据文件: chests.yml
   ```

🎯 **解决方案2：增强调试功能**

📋 **技术实现**：
1. **添加物品分类调试信息**
   ```java
   // 在getItemsByChestType方法中
   if (plugin.getConfig().getBoolean("debug.enabled", false)) {
       plugin.getLogger().info("正在为摸金箱种类 '" + chestType + "' 获取物品");
       plugin.getLogger().info("摸金箱种类 '" + chestType + "' 找到 " + items.size() + " 个物品");
   }
   ```

2. **添加类别物品调试信息**
   ```java
   // 在getItemsByCategory方法中
   if (plugin.getConfig().getBoolean("debug.enabled", false)) {
       plugin.getLogger().info("类别 '" + category + "' 找到 " + items.size() + " 个物品");
       for (TreasureItem item : items) {
           plugin.getLogger().info("  - " + item.getId() + ": " + item.getMaterial().name());
       }
   }
   ```

🎯 **解决方案3：临时启用调试模式**

📋 **配置修改**：
```yaml
# config.yml
debug:
  enabled: true  # 临时启用调试模式
```

===============================================
              🔧 技术细节
===============================================

📊 **修改的文件**

1. **HangPlugin.java**
   - 添加 `chestTypeCount` 统计
   - 在启用信息中显示摸金箱种类数量

2. **ChestTypeManager.java**
   - 添加 `getChestTypeCount()` 方法
   - 移除重复的日志输出

3. **TreasureItemManager.java**
   - 在 `getItemsByChestType()` 中添加调试信息
   - 在 `getItemsByCategory()` 中添加详细调试信息

4. **config.yml**
   - 临时启用调试模式 `debug.enabled: true`

🛡️ **调试信息内容**

**摸金箱种类调试**：
```
[HangEvacuation] 正在为摸金箱种类 'weapon' 获取物品
[HangEvacuation] 摸金箱种类 'weapon' 找到 1 个物品
```

**物品类别调试**：
```
[HangEvacuation] 类别 'weapon' 找到 1 个物品
[HangEvacuation]   - diamond_sword: DIAMOND_SWORD
```

**默认物品调试**：
```
[HangEvacuation] 类别 'medical' 没有专属物品，使用默认物品 3 个
```

===============================================
              📋 物品分类逻辑检查
===============================================

🔍 **分类算法**

**武器类 (weapon)**：
- 包含关键词：sword, axe, bow, crossbow, trident, pickaxe, shovel
- 示例：diamond_sword ✅

**弹药类 (ammo)**：
- 包含关键词：arrow, firework, tnt, gunpowder, blaze_rod, blaze_powder
- 示例：blaze_rod ✅

**医疗类 (medical)**：
- 包含关键词：potion, apple, bread, stew, experience_bottle, milk
- 示例：exp_bottle ✅

**补给类 (supply)**：
- 包含关键词：food, bread, coal, iron_ingot, redstone, log, planks
- 示例：coal ✅, iron_ingot ✅, redstone ✅

**装备类 (equipment)**：
- 包含关键词：diamond, emerald, gold, ender_pearl, enchanted
- 或者概率 ≤ 5.0% 的稀有物品
- 示例：diamond ✅, emerald ✅, ender_pearl ✅

**普通摸金箱 (common)**：
- 包含所有物品
- 示例：所有配置的物品

===============================================
              🎮 测试步骤
===============================================

🔧 **测试流程**

1. **启动服务器**
   - 查看启动日志，确认日志顺序正确
   - 查看调试信息，确认物品分类正常

2. **测试不同种类的摸金箱**
   - 放置武器箱：`/evac give weapon`
   - 放置弹药箱：`/evac give ammo`
   - 放置医疗箱：`/evac give medical`
   - 放置补给箱：`/evac give supply`
   - 放置装备箱：`/evac give equipment`

3. **检查物品内容**
   - 打开每种摸金箱
   - 确认物品符合对应类别
   - 查看调试日志确认分类逻辑

4. **关闭调试模式**
   - 测试完成后设置 `debug.enabled: false`
   - 重启服务器确认日志简洁

===============================================
              📊 预期结果
===============================================

✅ **正确的启动日志**：
```
[HangEvacuation] 正在加载NMS适配器: com.hang.plugin.nms.versions.NMSAdapter_1_8_R3
[HangEvacuation] NMS适配器加载成功: 通用兼容适配器 (1.8-1.21.4)
[HangEvacuation] 服务器信息: Minecraft 1.21.1 (NMS: craftbukkit)
[HangEvacuation] 服务器类型: Paper
[HangEvacuation] 已创建摸金箱种类配置文件: mojin.yml
[HangEvacuation] === HangEvacuation 1.7.0-1.8.8-1.21.4 已启用! ===
[HangEvacuation] 当前服务端: Paper | 支持版本: 1.8.8-1.21.4
[HangEvacuation] 已加载 0 个撤离区域
[HangEvacuation] 已加载 12 个摸金箱物品
[HangEvacuation] 已加载 2 个模组物品
[HangEvacuation] 已加载 6 个摸金箱种类
[HangEvacuation] 已加载 10 个等级配置
[HangEvacuation] 已创建摸金箱数据文件: chests.yml
[HangEvacuation] 摸金箱系统已开启 | 撤离系统已开启 | 等级系统已开启 | 模组物品支持已开启 | NMS适配器已启用
[HangEvacuation] 作者: hangzong(航总) | 如需技术支持请加V: hang060217
[HangEvacuation] 交流Q群: 361919269 | Hang系列插件
```

✅ **调试模式下的额外信息**：
```
[HangEvacuation] 加载的物品列表：
[HangEvacuation]   - iron_ingot: IRON_INGOT (概率: 0.15%)
[HangEvacuation]   - diamond: DIAMOND (概率: 0.05%)
[HangEvacuation]   - blaze_rod: BLAZE_ROD (概率: 0.08%)
... (其他物品)

[HangEvacuation] 正在为摸金箱种类 'weapon' 获取物品
[HangEvacuation] 类别 'weapon' 找到 1 个物品
[HangEvacuation]   - diamond_sword: DIAMOND_SWORD
[HangEvacuation] 摸金箱种类 'weapon' 找到 1 个物品
```

✅ **不同摸金箱的物品内容**：
- **武器箱**：主要包含钻石剑等武器
- **弹药箱**：主要包含烈焰棒等弹药
- **医疗箱**：主要包含经验瓶等医疗用品
- **补给箱**：主要包含煤炭、铁锭等基础物资
- **装备箱**：主要包含钻石、绿宝石等稀有物品

===============================================
              ❓ 故障排除
===============================================

🔍 **如果某种摸金箱还是没有物品**

1. **检查调试日志**：
   - 查看 "正在为摸金箱种类 'xxx' 获取物品" 信息
   - 查看 "类别 'xxx' 找到 X 个物品" 信息

2. **检查物品分类**：
   - 确认 treasure_items.yml 中的物品材料名称
   - 确认分类算法是否匹配物品名称

3. **检查默认物品**：
   - 如果某类别没有专属物品，会使用默认物品
   - 默认物品包括：coal, iron_ingot, redstone

4. **临时解决方案**：
   - 所有摸金箱都会降级到 "common" 类型
   - "common" 类型包含所有配置的物品

===============================================
              🔧 技术支持
===============================================

🎮 如有问题，请联系：
- 微信: hang060217
- QQ群: 361919269
- 作者: hangzong(航总)
- 标签: Hang系列插件

💡 建议：
- 测试时临时开启调试模式
- 测试完成后关闭调试模式
- 保存调试日志用于问题分析
