package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 箱子替换管理器
 * 处理世界箱子替换为摸金箱的逻辑
 */
public class ChestReplacementManager {
    
    private final HangPlugin plugin;
    private FileConfiguration config;
    private File configFile;
    
    // 统计数据
    private final Map<String, Integer> worldReplacementStats = new ConcurrentHashMap<>();
    
    public ChestReplacementManager(HangPlugin plugin) {
        this.plugin = plugin;
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    public void loadConfig() {
        configFile = new File(plugin.getDataFolder(), "world_chest_replacement.yml");
        
        // 如果配置文件不存在，从资源中复制
        if (!configFile.exists()) {
            try {
                plugin.getDataFolder().mkdirs();
                InputStream inputStream = plugin.getResource("world_chest_replacement.yml");
                if (inputStream != null) {
                    Files.copy(inputStream, configFile.toPath());
                    plugin.getLogger().info("已创建世界箱子替换配置文件: " + configFile.getName());
                }
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建配置文件: " + e.getMessage());
                return;
            }
        }
        
        config = YamlConfiguration.loadConfiguration(configFile);
        // 日志信息将在showStartupInfo()中统一显示
    }
    
    /**
     * 保存配置文件
     */
    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存箱子替换配置: " + e.getMessage());
        }
    }
    
    /**
     * 重新加载配置
     */
    public void reloadConfig() {
        config = YamlConfiguration.loadConfiguration(configFile);
        plugin.getLogger().info("已重新加载世界箱子替换配置");
    }
    
    /**
     * 检查是否应该替换箱子
     * 只检测配置文件中指定的世界和容器类型
     */
    public boolean shouldReplaceChest(Player player, Block block) {
        // 检查全局是否启用
        if (!config.getBoolean("global.enabled", true)) {
            return false;
        }



        // 重要：检查这个位置是否已经是摸金箱
        if (isAlreadyTreasureChest(block.getLocation())) {
            return false;
        }

        World world = block.getWorld();
        String worldName = world.getName();

        // 只检测配置文件中指定的世界
        ConfigurationSection worldConfig = config.getConfigurationSection("worlds." + worldName);
        if (worldConfig == null || !worldConfig.getBoolean("enabled", false)) {
            return false; // 世界未配置或未启用，直接返回false
        }

        // 只检测配置文件中指定的容器类型
        return hasMatchingRule(block, worldConfig);
    }

    /**
     * 检查是否有匹配的替换规则
     * 只检测配置文件中明确指定的容器类型
     */
    private boolean hasMatchingRule(Block block, ConfigurationSection worldConfig) {
        List<Map<?, ?>> rules = worldConfig.getMapList("rules");
        if (rules.isEmpty()) {
            return false;
        }

        String blockType = block.getType().name();

        // 调试信息：输出方块类型（从主配置文件读取调试设置）
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("箱子替换检查 - 方块类型: " + blockType + " 位置: " +
                block.getLocation().getWorld().getName() + " " +
                block.getLocation().getBlockX() + "," +
                block.getLocation().getBlockY() + "," +
                block.getLocation().getBlockZ());
        }

        for (Map<?, ?> rule : rules) {
            // 检查规则是否启用
            if (!getBooleanFromMap(rule, "enabled", true)) {
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("箱子替换 - 规则已禁用，跳过");
                }
                continue;
            }

            // 检查方块类型是否精确匹配配置中的类型
            @SuppressWarnings("unchecked")
            List<String> replaceableBlocks = (List<String>) rule.get("replaceable_blocks");
            if (replaceableBlocks != null && replaceableBlocks.contains(blockType)) {
                // 调试信息：找到匹配的规则
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("箱子替换 - 找到匹配的规则，方块类型: " + blockType);
                }

                // 检查排除区域
                @SuppressWarnings("unchecked")
                List<Map<?, ?>> excludedAreas = (List<Map<?, ?>>) rule.get("excluded_areas");
                if (excludedAreas != null && !excludedAreas.isEmpty()) {
                    if (isLocationInExcludedAreas(block.getLocation(), excludedAreas)) {
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().info("箱子替换 - 方块在排除区域内，跳过替换");
                        }
                        continue;
                    }
                }

                return true; // 找到匹配的规则
            } else if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                // 调试信息：规则不匹配
                if (replaceableBlocks != null) {
                    plugin.getLogger().info("箱子替换 - 规则不匹配，方块类型: " + blockType +
                        " 规则支持的类型: " + replaceableBlocks.toString());
                }
            }
        }

        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("箱子替换 - 没有找到匹配的规则，方块类型: " + blockType);
        }

        return false; // 没有匹配的规则
    }

    /**
     * 从Map中获取布尔值
     */
    private boolean getBooleanFromMap(Map<?, ?> map, String key, boolean defaultValue) {
        Object value = map.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    /**
     * 检查位置是否在排除区域列表中
     */
    private boolean isLocationInExcludedAreas(Location location, List<Map<?, ?>> excludedAreas) {
        for (Map<?, ?> area : excludedAreas) {
            if (isLocationInArea(location, area)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查指定位置是否已经是摸金箱
     * 增强版：同时检查内存和文件中的数据
     */
    private boolean isAlreadyTreasureChest(Location location) {
        // 首先检查PlayerListener中是否已有此位置的摸金箱数据（内存中）
        if (plugin.getPlayerListener() != null) {
            com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
                plugin.getPlayerListener().getTreasureChestData(location);
            if (data != null) {
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("箱子替换 - 在内存中找到摸金箱数据: " + locationToString(location));
                }
                return true;
            }
        }

        // 如果内存中没有，检查文件中是否有数据（可能还未加载到内存）
        if (plugin.getChestManager() != null) {
            com.hang.plugin.listeners.PlayerListener.TreasureChestData fileData =
                plugin.getChestManager().loadChestData(location);
            if (fileData != null) {
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("箱子替换 - 在文件中找到摸金箱数据: " + locationToString(location));
                }
                // 将文件中的数据加载到内存中
                plugin.getPlayerListener().saveTreasureChestData(location, fileData);
                return true;
            }
        }

        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("箱子替换 - 未找到摸金箱数据: " + locationToString(location));
        }
        return false;
    }
    
    /**
     * 执行箱子替换
     */
    public boolean replaceChest(Player player, Block block) {
        try {
            World world = block.getWorld();
            String worldName = world.getName();
            Location location = block.getLocation();
            
            // 获取世界配置
            ConfigurationSection worldConfig = config.getConfigurationSection("worlds." + worldName);
            if (worldConfig == null) {
                return false;
            }
            
            // 选择摸金箱类型
            String chestType = selectChestType(block, worldConfig);
            if (chestType == null) {
                return false;
            }
            
            // 不改变方块类型，只是将其转换为摸金箱（添加摸金箱数据）

            // 创建摸金箱数据
            createTreasureChestData(location, chestType);



            // 播放音效
            if (config.getBoolean("global.play_sound", true)) {
                playReplacementSound(player);
            }

            // 发送消息
            if (config.getBoolean("global.show_message", true)) {
                String message = config.getString("global.replacement_message", "§6这个箱子被神秘力量转化为了{type}摸金箱！");

                // 获取摸金箱种类的显示名称
                String chestTypeName = getChestTypeDisplayName(chestType);

                // 替换变量
                message = message.replace("{type}", chestTypeName);

                player.sendMessage(message);
            }

            // 更新统计
            updateStats(worldName);
            
            plugin.getLogger().info("玩家 " + player.getName() + " 在世界 " + worldName + 
                " 的箱子被替换为 " + chestType + " 摸金箱");
            
            return true;

        } catch (Exception e) {
            plugin.getLogger().severe("替换箱子时出错: " + e.getMessage());
            return false;
        }
    }

    /**
     * 根据配置选择摸金箱类型
     */
    private String selectChestType(Block block, ConfigurationSection worldConfig) {
        // 查找匹配的规则
        List<Map<?, ?>> rules = worldConfig.getMapList("rules");
        String blockType = block.getType().name();

        for (Map<?, ?> rule : rules) {
            // 检查规则是否启用
            if (!getBooleanFromMap(rule, "enabled", true)) {
                continue;
            }

            // 检查方块类型是否匹配
            @SuppressWarnings("unchecked")
            List<String> replaceableBlocks = (List<String>) rule.get("replaceable_blocks");
            if (replaceableBlocks != null && replaceableBlocks.contains(blockType)) {
                // 找到匹配的规则，使用该规则的chest_types配置
                @SuppressWarnings("unchecked")
                Map<String, Object> chestTypes = (Map<String, Object>) rule.get("chest_types");
                if (chestTypes != null) {
                    return selectChestTypeFromWeights(chestTypes);
                }
            }
        }

        return getDefaultChestType(); // 没有找到匹配规则时使用默认类型
    }

    /**
     * 根据权重配置选择摸金箱类型
     */
    private String selectChestTypeFromWeights(Map<String, Object> chestTypes) {
        // 获取所有可用的摸金箱种类ID
        Set<String> availableTypes = plugin.getChestTypeManager().getAllChestTypeIds();

        // 收集有效的类型和权重
        List<String> types = new ArrayList<>();
        List<Integer> typeWeights = new ArrayList<>();
        int totalWeight = 0;

        for (String type : availableTypes) {
            if (chestTypes.containsKey(type)) {
                Object weightObj = chestTypes.get(type);
                int weight = 0;
                if (weightObj instanceof Integer) {
                    weight = (Integer) weightObj;
                } else if (weightObj instanceof String) {
                    try {
                        weight = Integer.parseInt((String) weightObj);
                    } catch (NumberFormatException e) {
                        // 忽略无效的权重值
                    }
                }

                if (weight > 0) {
                    types.add(type);
                    typeWeights.add(weight);
                    totalWeight += weight;
                }
            }
        }

        if (types.isEmpty()) {
            return getDefaultChestType();
        }

        // 根据权重随机选择
        int randomValue = new Random().nextInt(totalWeight);
        int currentWeight = 0;

        for (int i = 0; i < types.size(); i++) {
            currentWeight += typeWeights.get(i);
            if (randomValue < currentWeight) {
                return types.get(i);
            }
        }

        return types.get(0); // 备用返回
    }

    /**
     * 获取默认摸金箱类型
     */
    private String getDefaultChestType() {
        Set<String> availableTypes = plugin.getChestTypeManager().getAllChestTypeIds();
        if (availableTypes.isEmpty()) {
            return "common"; // 硬编码备用
        }
        return availableTypes.iterator().next(); // 返回第一个可用类型
    }

    /**
     * 获取摸金箱种类的显示名称
     */
    private String getChestTypeDisplayName(String chestTypeId) {
        try {
            // 从ChestTypeManager获取种类的显示名称
            com.hang.plugin.manager.ChestTypeManager.ChestType chestType =
                plugin.getChestTypeManager().getChestType(chestTypeId);

            if (chestType != null) {
                String displayName = chestType.getDisplayName();
                if (displayName != null && !displayName.isEmpty()) {
                    return displayName;
                }
            }

            // 如果没有找到显示名称，返回格式化的ID
            return formatChestTypeId(chestTypeId);

        } catch (Exception e) {
            // 如果出错，返回格式化的ID
            return formatChestTypeId(chestTypeId);
        }
    }

    /**
     * 格式化摸金箱种类ID为显示名称
     */
    private String formatChestTypeId(String chestTypeId) {
        if (chestTypeId == null || chestTypeId.isEmpty()) {
            return "普通";
        }

        // 简单的格式化：首字母大写
        switch (chestTypeId.toLowerCase()) {
            case "common":
                return "普通";
            case "rare":
                return "稀有";
            case "epic":
                return "史诗";
            case "legendary":
                return "传说";
            case "mythic":
                return "神话";
            default:
                // 对于其他类型，首字母大写
                return chestTypeId.substring(0, 1).toUpperCase() + chestTypeId.substring(1).toLowerCase();
        }
    }

    /**
     * 创建摸金箱数据
     */
    private void createTreasureChestData(Location location, String chestType) {
        // 创建新的摸金箱数据并保存
        try {
            // 使用反射或直接调用PlayerListener的方法来创建TreasureChestData
            // 这里我们直接创建一个空的摸金箱数据，让玩家打开时生成物品
            plugin.getPlayerListener().createEmptyTreasureChest(location, chestType);
        } catch (Exception e) {
            plugin.getLogger().warning("创建摸金箱数据失败: " + e.getMessage());
        }
    }

    /**
     * 播放替换音效
     */
    private void playReplacementSound(Player player) {
        try {
            String soundName = config.getString("global.replacement_sound", "ENTITY_EXPERIENCE_ORB_PICKUP");
            Sound sound = Sound.valueOf(soundName);
            player.playSound(player.getLocation(), sound, 1.0f, 1.0f);
        } catch (Exception e) {
            // 如果音效名称无效，播放默认音效
            try {
                player.playSound(player.getLocation(), Sound.valueOf("ENTITY_EXPERIENCE_ORB_PICKUP"), 1.0f, 1.0f);
            } catch (Exception ex) {
                // 如果默认音效也无效，忽略
            }
        }
    }

    /**
     * 更新统计数据
     */
    private void updateStats(String worldName) {
        worldReplacementStats.put(worldName, worldReplacementStats.getOrDefault(worldName, 0) + 1);
    }

    /**
     * 位置转字符串
     */
    private String locationToString(Location location) {
        return location.getWorld().getName() + "_" +
               location.getBlockX() + "_" +
               location.getBlockY() + "_" +
               location.getBlockZ();
    }



    /**
     * 检查是否在排除区域
     */
    private boolean isInExcludedArea(Location location, ConfigurationSection worldConfig) {
        // 检查排除的坐标范围
        List<Map<?, ?>> excludedAreas = worldConfig.getMapList("excluded_areas");
        for (Map<?, ?> area : excludedAreas) {
            if (isLocationInArea(location, area)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查位置是否在指定区域内
     */
    private boolean isLocationInArea(Location location, Map<?, ?> area) {
        try {
            String worldName = (String) area.get("world");
            if (worldName != null && !worldName.equals(location.getWorld().getName())) {
                return false;
            }

            Integer minX = (Integer) area.get("min_x");
            Integer maxX = (Integer) area.get("max_x");
            Integer minZ = (Integer) area.get("min_z");
            Integer maxZ = (Integer) area.get("max_z");

            if (minX != null && location.getBlockX() < minX) return false;
            if (maxX != null && location.getBlockX() > maxX) return false;
            if (minZ != null && location.getBlockZ() < minZ) return false;
            if (maxZ != null && location.getBlockZ() > maxZ) return false;

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取世界替换统计
     */
    public Map<String, Integer> getWorldStats() {
        return new HashMap<>(worldReplacementStats);
    }



    /**
     * 获取配置文件
     */
    public FileConfiguration getConfig() {
        return config;
    }
}
