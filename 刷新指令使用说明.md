# 🔄 摸金箱刷新指令使用说明

## 📋 **指令概览**

新增的刷新指令可以帮助管理员快速刷新浮空字和摸金箱，解决各种显示和数据问题。

### **基础语法**
```
/evacuation refresh [类型] [世界名] [参数]
```

### **权限要求**
- `evacuation.admin` - 管理员权限

---

## 🎯 **指令详解**

### **1. 查看帮助**
```bash
/evacuation refresh
```
**功能**: 显示所有可用的刷新选项和说明

**输出示例**:
```
§6=== 刷新命令帮助 ===
§e/evacuation refresh all [世界名] - 刷新所有浮空字和摸金箱
§e/evacuation refresh holograms [世界名] - 仅刷新所有浮空字
§e/evacuation refresh chests [世界名] - 仅刷新所有摸金箱物品
§e/evacuation refresh items [世界名] [confirm] - 清空并重新生成所有摸金箱物品
§7参数说明:
§7  [世界名] - 可选，指定要刷新的世界，不填则刷新所有世界
§7  confirm - items 命令必需的确认参数
§c警告: refresh items 会删除所有现有物品数据！
§6=== 使用示例 ===
§e/evacuation refresh all world - 仅刷新 world 世界
§e/evacuation refresh holograms world_nether - 仅刷新地狱世界的浮空字
```

---

### **2. 刷新所有内容**
```bash
# 刷新所有世界
/evacuation refresh all

# 刷新指定世界
/evacuation refresh all world
/evacuation refresh all world_nether
/evacuation refresh all world_the_end
```

**功能**:
- ✅ 清理并重建所有浮空字
- ✅ 刷新所有摸金箱物品（保留现有数据）
- ✅ 强制保存所有数据

**适用场景**:
- 服务器重启后浮空字丢失
- 摸金箱显示异常
- 定期维护清理

**执行过程**:
```
# 刷新所有世界
§6=== 开始刷新所有世界的内容 ===
§e[1/3] 正在刷新浮空字...
§a✓ 浮空字刷新完成
§e[2/3] 正在刷新摸金箱...
§a✓ 摸金箱刷新完成 (15 个)
§e[3/3] 正在保存数据...
§a✓ 数据保存完成
§6=== 刷新完成 ===
§e总耗时: §f1250ms
§e刷新的摸金箱: §f15 个

# 刷新指定世界
§6=== 开始刷新世界 'world' 的所有内容 ===
§e[1/3] 正在刷新浮空字...
§a✓ 浮空字刷新完成
§e[2/3] 正在刷新摸金箱...
§a✓ 摸金箱刷新完成 (8 个)
§e[3/3] 正在保存数据...
§a✓ 数据保存完成
§6=== 刷新完成 ===
§e总耗时: §f850ms
§e刷新的摸金箱: §f8 个
§e目标世界: §fworld
```

---

### **3. 仅刷新浮空字**
```bash
/evacuation refresh holograms
# 或
/evacuation refresh hologram
```

**功能**:
- ✅ 清理所有现有浮空字实体
- ✅ 从备份数据重建所有浮空字
- ✅ 显示详细的统计信息

**适用场景**:
- 浮空字显示错乱
- 浮空字位置偏移
- 浮空字文本不更新

**输出示例**:
```
§6正在刷新所有浮空字...
§a浮空字刷新完成！
§e耗时: §f850ms
§e浮空字统计: 活跃=12, 死亡=0, 丢失=0, 备份=12
```

---

### **4. 仅刷新摸金箱**
```bash
/evacuation refresh chests
# 或
/evacuation refresh chest
```

**功能**:
- ✅ 刷新所有摸金箱的物品显示
- ✅ 保留玩家的搜索进度
- ✅ 保留现有的物品数据
- ✅ 更新浮空字显示

**适用场景**:
- 摸金箱物品显示异常
- 冷却时间显示错误
- 物品数量统计错误

**输出示例**:
```
§6正在刷新所有摸金箱...
§a摸金箱刷新完成！
§e耗时: §f650ms
§e刷新的摸金箱: §f8 个
```

---

### **5. 重新生成所有物品** ⚠️
```bash
/evacuation refresh items confirm
```

**⚠️ 警告**: 这是一个危险操作！

**功能**:
- ❌ **删除所有现有物品数据**
- ❌ **重置所有玩家搜索进度**
- ✅ 重新生成所有摸金箱物品
- ✅ 重新计算物品分布

**适用场景**:
- 更新了物品配置文件
- 需要重新分配物品
- 摸金箱数据损坏

**安全确认**:
```bash
# 第一次执行会显示警告
/evacuation refresh items

§c警告：此操作将删除所有摸金箱中的现有物品数据！
§c所有玩家的搜索进度将被重置！
§e如果确认执行，请使用: §f/evacuation refresh items confirm

# 确认执行
/evacuation refresh items confirm
```

**输出示例**:
```
§6正在重新生成所有摸金箱物品...
§a摸金箱物品重新生成完成！
§e耗时: §f1150ms
§e重新生成的摸金箱: §f15 个
§c注意：所有玩家的搜索进度已重置！
```

---

## 🛠️ **技术细节**

### **刷新机制**
1. **浮空字刷新**: 清理现有实体 → 从备份重建 → 更新显示
2. **摸金箱刷新**: 检查过期时间 → 重新生成物品 → 更新浮空字
3. **数据保存**: 内存数据 → 配置文件 → 磁盘持久化

### **性能优化**
- ✅ 批量操作减少I/O
- ✅ 异步保存避免卡顿
- ✅ 智能检查避免重复操作
- ✅ 详细计时监控性能

### **错误处理**
- ✅ 单个摸金箱失败不影响其他
- ✅ 详细错误日志便于排查
- ✅ 自动跳过损坏的数据
- ✅ 异常恢复机制

---

## 📝 **使用建议**

### **日常维护**
```bash
# 每日维护 - 刷新所有内容
/evacuation refresh all
```

### **问题排查**
```bash
# 浮空字问题
/evacuation refresh holograms

# 摸金箱问题
/evacuation refresh chests
```

### **配置更新后**
```bash
# 更新物品配置后（谨慎使用）
/evacuation refresh items confirm
```

### **性能监控**
- 观察刷新耗时，正常应在1-3秒内
- 检查错误日志，及时处理异常
- 定期备份数据，防止意外丢失

---

## ⚡ **常见问题**

### **Q: 刷新后浮空字还是不显示？**
A: 检查配置文件中 `hologram_enabled` 是否为 `true`

### **Q: 刷新耗时很长怎么办？**
A: 检查摸金箱数量，考虑分批处理或优化配置

### **Q: refresh items 后玩家投诉？**
A: 这是正常的，该操作会重置所有进度，使用前需告知玩家

### **Q: 刷新后部分摸金箱没有物品？**
A: 检查物品配置文件，确保对应种类有配置物品

---

## 🎯 **最佳实践**

1. **定期维护**: 每周执行一次 `refresh all`
2. **配置更新**: 修改配置后使用对应的刷新指令
3. **问题排查**: 先尝试单项刷新，再考虑全量刷新
4. **数据备份**: 执行 `refresh items` 前务必备份数据
5. **玩家通知**: 重要操作前提前通知在线玩家

通过合理使用这些刷新指令，可以有效维护摸金箱系统的稳定运行！
