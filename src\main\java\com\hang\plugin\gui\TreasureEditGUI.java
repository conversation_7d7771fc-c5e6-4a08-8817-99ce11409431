package com.hang.plugin.gui;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.manager.TreasureItemManager;
import com.hang.plugin.utils.VersionUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;

/**
 * 战利品编辑GUI
 */
public class TreasureEditGUI {
    
    private final HangPlugin plugin;
    private final Player player;
    private final Inventory inventory;
    private final TreasureItemManager.TreasureItem treasureItem;
    private final TreasureManagementGUI parentGUI;
    
    public TreasureEditGUI(HangPlugin plugin, Player player, TreasureItemManager.TreasureItem treasureItem, TreasureManagementGUI parentGUI) {
        this.plugin = plugin;
        this.player = player;
        this.treasureItem = treasureItem;
        this.parentGUI = parentGUI;
        this.inventory = Bukkit.createInventory(null, 27, "§6编辑: " + treasureItem.getId());
        
        setupGUI();
    }
    
    /**
     * 设置GUI
     */
    private void setupGUI() {
        // 显示当前物品
        ItemStack currentItem = plugin.getTreasureItemManager().createItemStack(treasureItem);
        inventory.setItem(4, currentItem);
        
        // 概率调整按钮
        setupChanceButtons();
        
        // 搜索速度调整按钮
        setupSearchSpeedButtons();
        
        // 数量调整按钮
        setupAmountButtons();
        
        // 控制按钮
        setupControlButtons();
    }
    
    /**
     * 设置概率调整按钮
     */
    private void setupChanceButtons() {
        // 概率 -10
        ItemStack chance10Down = new ItemStack(VersionUtils.getCompatibleColoredMaterial("WOOL", 14)); // 红色羊毛
        ItemMeta meta = chance10Down.getItemMeta();
        meta.setDisplayName("§c概率 -10%");
        meta.setLore(Arrays.asList("§7当前概率: §f" + treasureItem.getChance() + "%"));
        chance10Down.setItemMeta(meta);
        inventory.setItem(9, chance10Down);

        // 概率 -1
        ItemStack chance1Down = new ItemStack(VersionUtils.getCompatibleColoredMaterial("WOOL", 1)); // 橙色羊毛
        ItemMeta meta1 = chance1Down.getItemMeta();
        meta1.setDisplayName("§6概率 -1%");
        meta1.setLore(Arrays.asList("§7当前概率: §f" + treasureItem.getChance() + "%"));
        chance1Down.setItemMeta(meta1);
        inventory.setItem(10, chance1Down);
        
        // 概率显示
        ItemStack chanceDisplay = new ItemStack(Material.PAPER);
        ItemMeta chanceDisplayMeta = chanceDisplay.getItemMeta();
        chanceDisplayMeta.setDisplayName("§e概率: " + treasureItem.getChance() + "%");
        chanceDisplayMeta.setLore(Arrays.asList("§7点击重置为默认值"));
        chanceDisplay.setItemMeta(chanceDisplayMeta);
        inventory.setItem(11, chanceDisplay);
        
        // 概率 +1
        ItemStack chance1Up = new ItemStack(VersionUtils.getCompatibleColoredMaterial("WOOL", 5)); // 浅绿色羊毛
        ItemMeta meta2 = chance1Up.getItemMeta();
        meta2.setDisplayName("§a概率 +1%");
        meta2.setLore(Arrays.asList("§7当前概率: §f" + treasureItem.getChance() + "%"));
        chance1Up.setItemMeta(meta2);
        inventory.setItem(12, chance1Up);

        // 概率 +10
        ItemStack chance10Up = new ItemStack(VersionUtils.getCompatibleColoredMaterial("WOOL", 13)); // 绿色羊毛
        ItemMeta meta3 = chance10Up.getItemMeta();
        meta3.setDisplayName("§a概率 +10%");
        meta3.setLore(Arrays.asList("§7当前概率: §f" + treasureItem.getChance() + "%"));
        chance10Up.setItemMeta(meta3);
        inventory.setItem(13, chance10Up);
    }
    
    /**
     * 设置搜索速度调整按钮
     */
    private void setupSearchSpeedButtons() {
        // 搜索速度 -5秒
        ItemStack speed5Down = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_CLAY", 14)); // 红色粘土
        ItemMeta meta = speed5Down.getItemMeta();
        meta.setDisplayName("§c搜索速度 -5秒");
        meta.setLore(Arrays.asList("§7当前搜索速度: §f" + treasureItem.getSearchSpeed() + "秒"));
        speed5Down.setItemMeta(meta);
        inventory.setItem(14, speed5Down);

        // 搜索速度 -1秒
        ItemStack speed1Down = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_CLAY", 1)); // 橙色粘土
        ItemMeta meta1 = speed1Down.getItemMeta();
        meta1.setDisplayName("§6搜索速度 -1秒");
        meta1.setLore(Arrays.asList("§7当前搜索速度: §f" + treasureItem.getSearchSpeed() + "秒"));
        speed1Down.setItemMeta(meta1);
        inventory.setItem(15, speed1Down);

        // 搜索速度显示
        ItemStack speedDisplay = new ItemStack(Material.BOOK);
        ItemMeta speedDisplayMeta = speedDisplay.getItemMeta();
        speedDisplayMeta.setDisplayName("§e搜索速度: " + treasureItem.getSearchSpeed() + "秒");
        speedDisplayMeta.setLore(Arrays.asList("§7点击重置为默认值", "§7物品越贵重搜索越慢"));
        speedDisplay.setItemMeta(speedDisplayMeta);
        inventory.setItem(16, speedDisplay);

        // 搜索速度 +1秒
        ItemStack speed1Up = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_CLAY", 5)); // 浅绿色粘土
        ItemMeta meta2 = speed1Up.getItemMeta();
        meta2.setDisplayName("§a搜索速度 +1秒");
        meta2.setLore(Arrays.asList("§7当前搜索速度: §f" + treasureItem.getSearchSpeed() + "秒"));
        speed1Up.setItemMeta(meta2);
        inventory.setItem(17, speed1Up);

        // 搜索速度 +5秒
        ItemStack speed5Up = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_CLAY", 13)); // 绿色粘土
        ItemMeta meta3 = speed5Up.getItemMeta();
        meta3.setDisplayName("§a搜索速度 +5秒");
        meta3.setLore(Arrays.asList("§7当前搜索速度: §f" + treasureItem.getSearchSpeed() + "秒"));
        speed5Up.setItemMeta(meta3);
        inventory.setItem(18, speed5Up);
    }
    
    /**
     * 设置数量调整按钮
     */
    private void setupAmountButtons() {
        // 数量 -10
        ItemStack amount10Down = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS", 14)); // 红色玻璃
        ItemMeta meta = amount10Down.getItemMeta();
        meta.setDisplayName("§c数量 -10");
        meta.setLore(Arrays.asList("§7当前数量: §f" + treasureItem.getAmount()));
        amount10Down.setItemMeta(meta);
        inventory.setItem(19, amount10Down);

        // 数量 -1
        ItemStack amount1Down = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS", 1)); // 橙色玻璃
        ItemMeta meta1 = amount1Down.getItemMeta();
        meta1.setDisplayName("§6数量 -1");
        meta1.setLore(Arrays.asList("§7当前数量: §f" + treasureItem.getAmount()));
        amount1Down.setItemMeta(meta1);
        inventory.setItem(20, amount1Down);
        
        // 数量显示
        ItemStack amountDisplay = new ItemStack(Material.EMERALD);
        ItemMeta amountDisplayMeta = amountDisplay.getItemMeta();
        amountDisplayMeta.setDisplayName("§e数量: " + treasureItem.getAmount());
        amountDisplayMeta.setLore(Arrays.asList("§7点击重置为默认值"));
        amountDisplay.setItemMeta(amountDisplayMeta);
        inventory.setItem(21, amountDisplay);
        
        // 数量 +1
        ItemStack amount1Up = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS", 5)); // 浅绿色玻璃
        ItemMeta meta2 = amount1Up.getItemMeta();
        meta2.setDisplayName("§a数量 +1");
        meta2.setLore(Arrays.asList("§7当前数量: §f" + treasureItem.getAmount()));
        amount1Up.setItemMeta(meta2);
        inventory.setItem(22, amount1Up);

        // 数量 +10
        ItemStack amount10Up = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS", 13)); // 绿色玻璃
        ItemMeta meta3 = amount10Up.getItemMeta();
        meta3.setDisplayName("§a数量 +10");
        meta3.setLore(Arrays.asList("§7当前数量: §f" + treasureItem.getAmount()));
        amount10Up.setItemMeta(meta3);
        inventory.setItem(23, amount10Up);
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 保存按钮
        ItemStack saveButton = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta saveMeta = saveButton.getItemMeta();
        saveMeta.setDisplayName("§a保存修改");
        saveMeta.setLore(Arrays.asList("§7保存当前修改并返回"));
        saveButton.setItemMeta(saveMeta);
        inventory.setItem(25, saveButton);
        
        // 取消按钮
        ItemStack cancelButton = new ItemStack(Material.REDSTONE_BLOCK);
        ItemMeta cancelMeta = cancelButton.getItemMeta();
        cancelMeta.setDisplayName("§c取消修改");
        cancelMeta.setLore(Arrays.asList("§7放弃修改并返回"));
        cancelButton.setItemMeta(cancelMeta);
        inventory.setItem(26, cancelButton);
        
        // 命令编辑按钮
        ItemStack commandButton = new ItemStack(VersionUtils.getCompatibleMaterial("COMMAND"));
        ItemMeta commandMeta = commandButton.getItemMeta();
        commandMeta.setDisplayName("§e编辑命令");
        commandMeta.setLore(Arrays.asList(
            "§7当前命令数: §f" + (treasureItem.getCommands() != null ? treasureItem.getCommands().size() : 0),
            "§7点击编辑执行命令"
        ));
        commandButton.setItemMeta(commandMeta);
        inventory.setItem(24, commandButton);
    }
    
    /**
     * 处理点击事件
     */
    public void handleClick(int slot) {
        switch (slot) {
            // 概率调整
            case 9: adjustChance(-10); break;
            case 10: adjustChance(-1); break;
            case 11: resetChance(); break;
            case 12: adjustChance(1); break;
            case 13: adjustChance(10); break;
            
            // 搜索速度调整
            case 14: adjustSearchSpeed(-5); break;
            case 15: adjustSearchSpeed(-1); break;
            case 16: resetSearchSpeed(); break;
            case 17: adjustSearchSpeed(1); break;
            case 18: adjustSearchSpeed(5); break;
            
            // 数量调整
            case 19: adjustAmount(-10); break;
            case 20: adjustAmount(-1); break;
            case 21: resetAmount(); break;
            case 22: adjustAmount(1); break;
            case 23: adjustAmount(10); break;
            
            // 控制按钮
            case 24: editCommands(); break;
            case 25: saveAndReturn(); break;
            case 26: cancelAndReturn(); break;
        }
    }
    
    /**
     * 调整概率
     */
    private void adjustChance(int delta) {
        double newChance = Math.max(0, Math.min(100, treasureItem.getChance() + delta));
        treasureItem.setChance(newChance);
        setupGUI();
    }
    
    /**
     * 重置概率
     */
    private void resetChance() {
        treasureItem.setChance(10.0); // 默认概率
        setupGUI();
    }
    
    /**
     * 调整搜索速度
     */
    private void adjustSearchSpeed(int delta) {
        int newSpeed = Math.max(1, Math.min(30, treasureItem.getSearchSpeed() + delta));
        treasureItem.setSearchSpeed(newSpeed);
        setupGUI();
    }

    /**
     * 重置搜索速度
     */
    private void resetSearchSpeed() {
        treasureItem.setSearchSpeed(3); // 默认搜索速度3秒
        setupGUI();
    }
    
    /**
     * 调整数量
     */
    private void adjustAmount(int delta) {
        int newAmount = Math.max(1, Math.min(64, treasureItem.getAmount() + delta));
        treasureItem.setAmount(newAmount);
        setupGUI();
    }
    
    /**
     * 重置数量
     */
    private void resetAmount() {
        treasureItem.setAmount(1); // 默认数量
        setupGUI();
    }
    
    /**
     * 编辑命令
     */
    private void editCommands() {
        CommandEditGUI commandEditGUI = new CommandEditGUI(plugin, player, treasureItem, this);
        commandEditGUI.open();
    }
    
    /**
     * 保存并返回
     */
    private void saveAndReturn() {
        plugin.getTreasureItemManager().updateItem(treasureItem);
        player.sendMessage("§a物品已保存！");
        parentGUI.refresh();
        parentGUI.open();
    }
    
    /**
     * 取消并返回
     */
    private void cancelAndReturn() {
        player.sendMessage("§c已取消修改");
        parentGUI.open();
    }
    
    /**
     * 打开GUI
     */
    public void open() {
        // 注册GUI到监听器
        plugin.getPlayerListener().registerEditGUI(player, this);
        player.openInventory(inventory);
    }
    
    /**
     * 获取库存对象
     */
    public Inventory getInventory() {
        return inventory;
    }
}
