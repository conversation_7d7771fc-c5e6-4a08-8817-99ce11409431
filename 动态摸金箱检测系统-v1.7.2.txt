# 🔧 动态摸金箱检测系统 - v1.7.2

## 📋 修复内容

### 🎯 **核心问题解决**
**问题**: 摸金箱检测逻辑使用硬编码名称，无法适应用户自定义的摸金箱名称
**解决**: 创建了完全动态的检测系统，支持用户在 mojin.yml 中自定义任何摸金箱名称

### ✅ **主要改进**

#### 1. 动态检测逻辑
- **新增**: `TreasureChestItem.getChestTypeFromItem()` 动态检测方法
- **优先级**: 配置文件检测 > 硬编码检测 > 通用检测
- **支持**: 完全自定义的摸金箱名称和颜色

#### 2. 检测流程优化
```java
// 检测流程：
1. 检查物品是否有自定义名称
2. 遍历 mojin.yml 中所有配置的摸金箱种类
3. 匹配显示名称与配置中的 name 字段
4. 如果匹配失败，降级到硬编码检测（向下兼容）
5. 最后使用通用检测（包含"箱"字）
```

#### 3. 配置支持增强
- **完全自定义**: 用户可以在 mojin.yml 中设置任意名称和颜色
- **向下兼容**: 保留对默认名称的支持
- **通用检测**: 支持任何包含"箱"字的物品

## 🔍 技术实现

### 动态检测代码：
```java
// 遍历所有配置的摸金箱种类
for (ChestType chestType : plugin.getChestTypeManager().getAllChestTypes()) {
    if (chestType != null && chestType.getName().equals(displayName)) {
        return chestType.getTypeId();
    }
}
```

### 配置示例：
```yaml
# mojin.yml
chest_types:
  custom_chest:
    name: "§5我的自定义摸金箱"  # 完全自定义的名称
    display_name: "§5超级摸金箱"
    material: "ENDER_CHEST"
    slots: 10
    refresh_time: 30
```

## 🧪 测试场景

### 1. 自定义名称测试
```yaml
# 在 mojin.yml 中修改摸金箱名称
weapon:
  name: "§4【传说武器箱】"  # 自定义名称
```
- 使用 `/evac give weapon` 获取摸金箱
- 放置后应该能正常检测和工作

### 2. 多语言支持测试
```yaml
medical:
  name: "§a医療ボックス"  # 日文名称
supply:
  name: "§b보급상자"      # 韩文名称
```

### 3. 特殊字符测试
```yaml
equipment:
  name: "§d★装备箱★"     # 特殊符号
```

## 📝 配置指南

### 自定义摸金箱名称：
1. 打开 `mojin.yml` 配置文件
2. 找到要修改的摸金箱种类
3. 修改 `name` 字段为你想要的名称
4. 重启服务器或使用 `/evac reload`

### 注意事项：
- `name` 字段是物品的显示名称，用于检测
- `display_name` 字段是GUI中显示的名称
- 支持颜色代码和特殊字符
- 建议保持名称的唯一性

## 🚀 兼容性

### 向下兼容：
- 保留对默认名称的支持
- 现有配置无需修改即可正常工作
- 硬编码检测作为降级方案

### 扩展性：
- 支持无限数量的自定义摸金箱种类
- 支持任意语言和字符
- 支持复杂的命名规则

## 🔧 故障排除

### 如果摸金箱无法检测：
1. 检查 mojin.yml 中的 name 字段是否正确
2. 确认物品名称与配置完全匹配（包括颜色代码）
3. 查看控制台是否有错误信息
4. 尝试使用 `/evac reload` 重载配置

### 调试方法：
- 启用调试模式查看详细检测过程
- 检查物品的实际显示名称
- 验证配置文件格式是否正确

## 📊 性能优化

- 使用缓存避免重复配置读取
- 优化检测算法减少遍历次数
- 降级检测确保快速响应

## 🎯 版本信息
- 版本: v1.7.2
- 修复日期: 2024年
- 主要特性: 动态摸金箱检测系统
- 兼容性: 完全向下兼容
