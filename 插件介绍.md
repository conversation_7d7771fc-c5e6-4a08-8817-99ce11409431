# 🏺 HangEvacuation - 航式摸金插件

## 📖 插件简介

**HangEvacuation** 是一款专为 Minecraft 1.12.2 设计的沉浸式摸金探宝插件。插件融合了传统摸金校尉的神秘色彩与现代游戏机制，为玩家提供刺激的宝藏搜寻体验和紧张的撤离逃生玩法。

### 🎯 核心特色

- **🔍 智能搜索系统**: 自动化的宝藏搜索机制，无需手动点击
- **⏱️ 动态进度显示**: 实时进度反馈，支持百分比显示
- **🎵 沉浸式音效**: 搜索开始和成功的专属音效反馈
- **🔒 独占搜索机制**: 防止多人冲突，确保公平竞争
- **⚙️ 高度可配置**: 丰富的配置选项，适应不同服务器需求

---

## 🎮 主要功能

### 📦 摸金箱系统

#### 🔍 自动搜索机制
- **智能搜索**: 打开摸金箱后自动开始搜索物品
- **进度可视化**: 通过彩色玻璃板显示搜索进度
- **速度可配置**: 每个物品的搜索时间可单独设置

#### 🎨 进度显示系统
- **颜色变化**: 红色→橙色→黄色→绿色，直观显示进度
- **百分比显示**: 物品名称直接显示"正在搜索中 50%"
- **双向模式**: 支持倒计时(100%→0%)和正计时(0%→100%)

#### 🔒 独占搜索保护
- **防冲突机制**: 一个摸金箱同时只能被一个玩家搜索
- **实时提示**: 显示正在搜索的玩家名称
- **自动清理**: 离线或超时玩家的搜索状态自动清除

### 🎵 音效系统

#### 🔊 双重音效反馈
- **搜索开始音效**: 经验球拾取音，轻柔不干扰
- **搜索成功音效**: 玩家升级音，明显的成功反馈

#### ⚙️ 完全可配置
```yaml
sounds:
  search-start:
    enabled: true    # 是否启用
    volume: 0.5      # 音量控制
    pitch: 1.0       # 音调控制
  search-success:
    enabled: true
    volume: 0.8
    pitch: 1.2
```

### 🏃 撤离系统

#### 📍 撤离点管理
- **金斧工具**: 使用金斧左右键选择撤离点坐标
- **统一目标**: 所有撤离点指向同一个安全区域
- **倒计时机制**: 可配置的撤离倒计时时间

### 🛠️ 管理系统

#### 💎 战利品管理
- **GUI界面**: 使用 `/evac gui` 打开可视化管理界面
- **物品配置**: 支持自定义物品、概率、搜索时间
- **命令执行**: 获得物品时可执行自定义命令

#### 📊 数据持久化
- **状态保存**: 摸金箱搜索状态自动保存
- **冷却机制**: 搜索完毕后的冷却时间管理
- **数据恢复**: 服务器重启后数据自动恢复

---

## 🎯 适用场景

### 🏰 RPG服务器
- **探险元素**: 为地下城和遗迹添加摸金探宝玩法
- **经济系统**: 通过战利品概率控制服务器经济
- **任务系统**: 结合任务插件创建寻宝任务

### 🎪 娱乐服务器
- **休闲玩法**: 轻松有趣的摸金体验
- **社交互动**: 玩家间的宝藏竞争
- **活动道具**: 特殊活动的奖励机制

### 🎓 教育服务器
- **历史教学**: 结合摸金校尉的历史文化
- **概率学习**: 通过战利品概率学习数学概念
- **团队协作**: 撤离机制培养团队合作

---

## ⚙️ 配置说明

### 📦 摸金箱配置
```yaml
treasure-chest:
  # 自动搜索配置
  auto-search:
    enabled: true              # 启用自动搜索
    check-interval: 20         # 检查间隔(tick)
    delay-between-searches: 60 # 搜索间隔(tick)
  
  # 进度动画配置
  animation:
    update-interval: 3         # 更新间隔(tick)
    progress-direction:
      mode: "countup"          # 进度方向
  
  # 冷却配置
  refresh-time: 300           # 刷新时间(秒)
```

### 🎵 音效配置
```yaml
sounds:
  search-start:
    enabled: true
    volume: 0.5
    pitch: 1.0
  search-success:
    enabled: true
    volume: 0.8
    pitch: 1.2
```

### 💎 战利品配置
```yaml
items:
  coal:
    material: COAL
    amount: 5
    probability: 0.8
    search_speed: 2
  diamond:
    material: DIAMOND
    amount: 1
    probability: 0.1
    search_speed: 8
    commands:
      - "tell %player% 恭喜获得钻石！"
```

---

## 🚀 安装使用

### 📋 系统要求
- **Minecraft版本**: 1.12.2
- **服务端**: Spigot/Paper/PaperSpigot
- **Java版本**: Java 8+

### 📥 安装步骤
1. 下载插件文件 `HangEvacuation-1.3.8.jar`
2. 将文件放入服务器的 `plugins` 文件夹
3. 重启服务器或使用 `/reload` 命令
4. 插件将自动生成配置文件

### 🎮 快速开始
1. **放置摸金箱**: 在世界中放置箱子
2. **打开摸金箱**: 右键点击箱子开始摸金
3. **观察搜索**: 自动搜索开始，观察进度变化
4. **获取战利品**: 点击搜索完成的物品获得奖励

---

## 📝 命令列表

### 👑 管理员命令
- `/evac gui` - 打开战利品管理界面
- `/evac reload` - 重载插件配置
- `/evac login` - 管理员登录
- `/evac setevac` - 设置撤离目标点

### 🔧 工具命令
- 金斧工具 - 左键/右键选择撤离点坐标

---

## 🎨 界面预览

### 📦 摸金箱界面
```
┌─────────────────────────────────┐
│           摸金箱                │
├─────────────────────────────────┤
│ [灰] [绿] [黄] [灰] [灰] [灰] [灰] [灰] [灰] │
│ [灰] [灰] [灰] [绿] [灰] [灰] [灰] [灰] [灰] │
│ [灰] [灰] [灰] [灰] [灰] [灰] [灰] [灰] [灰] │
└─────────────────────────────────┘
```
- **灰色**: 未搜索物品
- **红色**: 0-25% 进度
- **橙色**: 26-50% 进度
- **黄色**: 51-75% 进度
- **绿色**: 76-100% 进度

### 💎 管理界面
```
┌─────────────────────────────────┐
│         战利品管理              │
├─────────────────────────────────┤
│ [煤炭] [铁锭] [金锭] [钻石] [+添加] │
│ 概率:80% 概率:60% 概率:30% 概率:10% │
│ 时间:2秒 时间:3秒 时间:5秒 时间:8秒 │
└─────────────────────────────────┘
```

---

## 🔧 技术特性

### 🛡️ 安全机制
- **数据验证**: 严格的配置文件验证
- **错误处理**: 完善的异常处理机制
- **内存管理**: 自动清理无用数据

### ⚡ 性能优化
- **异步处理**: 搜索任务异步执行
- **缓存机制**: 配置数据智能缓存
- **资源管理**: 自动清理过期任务

### 🔄 兼容性
- **向后兼容**: 支持配置文件升级
- **插件兼容**: 与主流插件良好兼容
- **版本稳定**: 专为1.12.2优化

---

## 🎖️ 插件优势

### 🏆 独特创新
- **原创玩法**: 首创自动搜索摸金机制
- **文化融合**: 结合中国摸金校尉文化
- **沉浸体验**: 完整的音效和视觉反馈

### 🛠️ 技术领先
- **高性能**: 优化的异步处理机制
- **高稳定**: 完善的错误处理和恢复
- **高兼容**: 与主流插件无冲突

### 🎯 用户友好
- **简单易用**: 开箱即用，无需复杂配置
- **高度自定义**: 丰富的配置选项
- **持续更新**: 定期更新和功能改进

---

## 🔮 未来规划

### 🎯 短期目标 (v1.4.x)
- [ ] 添加更多音效和粒子效果
- [ ] 优化性能和内存使用
- [ ] 增加更多配置选项
- [ ] 完善保存配置功能

### 🚀 长期目标 (v2.0.x)
- [ ] 支持更多Minecraft版本
- [ ] 添加多语言支持
- [ ] 集成经济系统
- [ ] 添加更多摸金箱类型

---

## 📊 更新历史

### 🔥 最新版本 v1.3.8
- **音效系统重新设计**: 简化音效配置，提升稳定性
- **进度显示优化**: 改为简洁的百分比显示
- **独占搜索机制**: 防止多人冲突
- **配置系统完善**: 支持搜索速度配置

### 📈 版本统计
- **总版本数**: 18个版本
- **主要更新**: 8次重大功能更新
- **Bug修复**: 7次重要问题修复
- **功能增强**: 11次功能改进

---

## 💡 使用技巧

### 🎮 玩家技巧
1. **耐心等待**: 稀有物品搜索时间较长，耐心等待获得更好奖励
2. **及时关闭**: 搜索完毕后及时关闭界面，释放摸金箱给其他玩家
3. **背包空间**: 确保背包有足够空间存放战利品

### 👑 管理员技巧
1. **概率平衡**: 合理设置物品概率，维护服务器经济平衡
2. **搜索时间**: 根据物品价值设置合理的搜索时间
3. **定期维护**: 定期检查和清理摸金箱数据

---

## 🔧 故障排除

### ❓ 常见问题

**Q: 音效无法播放？**
A: 检查配置文件中音效是否启用，确认客户端音效设置正常

**Q: 摸金箱无法打开？**
A: 可能被其他玩家占用或处于冷却状态，等待一段时间后重试

**Q: 搜索进度卡住？**
A: 使用 `/evac reload` 重载插件，或重启服务器

**Q: 配置修改不生效？**
A: 修改配置后使用 `/evac reload` 重载配置

### 🛠️ 调试方法
1. **查看日志**: 检查服务器控制台的错误信息
2. **重载插件**: 使用 `/evac reload` 重载插件
3. **重置配置**: 删除配置文件让插件重新生成
4. **联系支持**: 如问题持续存在，联系技术支持

---

## 📞 支持与反馈

### 🆘 技术支持
- **QQ**: hang060217
- **微信**: hang060217
- **问题反馈**: 欢迎提交Bug报告和功能建议
- **定制开发**: 支持个性化功能定制

### 📚 文档资源
- **配置教程**: 详细的配置文件说明
- **视频教程**: 插件使用演示视频
- **常见问题**: FAQ解答常见问题
- **更新日志**: 完整的版本更新记录

### 🎯 服务承诺
- **快速响应**: 24小时内回复技术问题
- **持续更新**: 定期发布功能更新和Bug修复
- **免费支持**: 提供免费的技术支持和使用指导

---

## 📄 版权信息

**插件名称**: HangEvacuation - 航式摸金插件
**版本**: v1.3.8
**作者**: hangzong
**适用版本**: Minecraft 1.12.2
**开发语言**: Java
**许可协议**: 自定义许可
**发布日期**: 2025年6月

### 📋 使用条款
- 仅限个人和非商业用途
- 禁止逆向工程和二次分发
- 保留所有技术支持权利
- 插件功能持续改进和优化

---

*🏺 体验神秘的摸金之旅，感受刺激的撤离逃生！*
*💎 让每一次探宝都充满惊喜，让每一次撤离都惊心动魄！*
