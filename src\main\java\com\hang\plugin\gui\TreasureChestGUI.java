package com.hang.plugin.gui;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.utils.VersionUtils;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.*;

/**
 * 摸金箱GUI类
 *
 * <AUTHOR>
 */
public class TreasureChestGUI {

    private final HangPlugin plugin;
    private final Player player;
    private final Inventory inventory;
    private final org.bukkit.Location chestLocation;
    private final Set<Integer> searchedSlots;
    private final Map<Integer, ItemStack> treasureItems;
    private final Map<Integer, Object> treasureItemData;
    private final Map<Player, Long> searchCooldowns;
    private final Map<Integer, BukkitTask> searchTasks;
    private BukkitTask autoSearchTask;
    private int sequentialIndex = 0;
    private final Random random = new Random();

    public TreasureChestGUI(HangPlugin plugin, Player player, org.bukkit.Location chestLocation) {
        this.plugin = plugin;
        this.player = player;
        this.chestLocation = chestLocation;
        this.searchedSlots = new HashSet<>();
        this.treasureItems = new HashMap<>();
        this.treasureItemData = new HashMap<>();
        this.searchCooldowns = new HashMap<>();
        this.searchTasks = new HashMap<>();

        // 获取摸金箱种类和对应的标题
        String chestType = getChestTypeFromLocation(chestLocation);
        String title = plugin.getTreasureItemManager().getChestName(chestType);
        this.inventory = Bukkit.createInventory(null, 27, title);

        initializeGUI();

        // 设置当前搜索者
        setCurrentSearcher();
    }

    /**
     * 初始化GUI
     */
    private void initializeGUI() {
        // 根据摸金箱种类获取槽位数量
        String chestType = getChestTypeFromLocation(chestLocation);
        int itemCount = plugin.getTreasureItemManager().getChestSlots(chestType);

        // 尝试加载已存在的摸金箱数据
        com.hang.plugin.listeners.PlayerListener.TreasureChestData existingData =
            plugin.getPlayerListener().getTreasureChestData(chestLocation);

        // 修复：检查摸金箱是否有倒计时（无论是否完全搜索）
        if (existingData != null) {
            long nextRefresh = existingData.getNextRefreshTime();
            if (nextRefresh > 0 && System.currentTimeMillis() < nextRefresh) {
                // 摸金箱还在倒计时中
                if (existingData.isFullySearched()) {
                    // 已搜索完毕且还在冷却中，显示之前搜索出来但没拿走的物品
                    loadExistingDataInCooldown(existingData, nextRefresh);
                    return;
                } else {
                    // 自动刷新倒计时中，但还没搜索完毕，正常显示现有物品
                    // 继续执行正常流程
                }
            } else if (nextRefresh > 0 && System.currentTimeMillis() >= nextRefresh) {
                // 修复：刷新时间已到，强制刷新摸金箱
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("GUI中检测到摸金箱刷新: " + chestLocation +
                        " (倒计时已结束，重置摸金箱状态)");
                }

                existingData.reset();
                plugin.getPlayerListener().saveTreasureChestData(chestLocation, existingData);
                // 移除浮空字，让新的GUI重新创建
                plugin.getHologramManager().removeHologram(chestLocation);
                // 修复：摸金箱刷新时，清除玩家的搜索冷却时间
                searchCooldowns.remove(player);
                // 继续执行正常的初始化流程（生成新物品）
            }
        }

        // 检查自动刷新功能
        checkAutoRefresh(existingData);

        if (existingData != null && !existingData.getItems().isEmpty()) {
            // 加载已存在的数据（只有当数据不为空时）
            loadExistingData(existingData, itemCount);
        } else if (existingData != null && existingData.getItems().isEmpty()) {
            // 修复：摸金箱为空时（无论是刷新后还是全新的），都重新生成物品
            // 这样确保玩家打开摸金箱时总是有物品可以搜索
            generateTreasureItems(itemCount);
            saveCurrentData();
        } else {
            // 全新的摸金箱，生成随机战利品
            generateTreasureItems(itemCount);
            // 保存新生成的数据
            saveCurrentData();
        }

        // 设置GUI显示状态
        updateGUIDisplay(itemCount);

        // 创建或更新浮空字
        updateHologram();
    }



    /**
     * 新增：加载冷却期间的摸金箱数据（显示之前搜索出来但没拿走的物品）
     */
    private void loadExistingDataInCooldown(com.hang.plugin.listeners.PlayerListener.TreasureChestData data, long nextRefresh) {
        // 加载之前搜索出来的物品数据
        treasureItems.putAll(data.getItems());
        treasureItemData.putAll(data.getItemData());
        searchedSlots.addAll(data.getSearchedSlots());

        // 计算剩余时间
        long remaining = (nextRefresh - System.currentTimeMillis()) / 1000;
        long minutes = remaining / 60;
        long seconds = remaining % 60;

        // 修复：使用配置文件中的冷却消息（如果为空则不发送）
        plugin.sendMessageIfNotEmpty(player, "treasure-chest-cooldown-title", "§6摸金箱已搜索完毕！");
        plugin.sendMessageIfNotEmpty(player, "treasure-chest-cooldown-refresh",
            "§e物品将在 §c{minutes}:{seconds} §e后刷新",
            "minutes", String.valueOf(minutes),
            "seconds", String.format("%02d", seconds));
        plugin.sendMessageIfNotEmpty(player, "treasure-chest-cooldown-hint",
            "§7您可以拿走剩余的物品，但需要等待刷新时间到达才会有新物品");

        // 设置GUI显示状态（显示之前搜索出来的物品）
        updateGUIDisplayForCooldown();

        // 创建或更新浮空字显示倒计时
        updateHologram();
    }

    /**
     * 新增：更新冷却期间的GUI显示状态
     */
    private void updateGUIDisplayForCooldown() {
        // 清空所有槽位
        for (int i = 0; i < 27; i++) {
            inventory.setItem(i, new ItemStack(org.bukkit.Material.AIR));
        }

        // 只显示已搜索出来的物品（玩家可以拿走）
        for (Map.Entry<Integer, ItemStack> entry : treasureItems.entrySet()) {
            int slot = entry.getKey();
            if (searchedSlots.contains(slot)) {
                // 已搜索的槽位显示实际物品，玩家可以拿走
                inventory.setItem(slot, entry.getValue());
            }
            // 注意：未搜索的槽位不显示任何东西（因为已经搜索完毕了）
        }
    }

    /**
     * 生成战利品物品
     */
    private void generateTreasureItems(int count) {
        // 获取摸金箱种类
        String chestType = getChestTypeFromLocation(chestLocation);

        // 生成随机槽位列表
        java.util.List<Integer> availableSlots = new java.util.ArrayList<>();
        for (int i = 0; i < 27; i++) {
            availableSlots.add(i);
        }
        java.util.Collections.shuffle(availableSlots);

        int successCount = 0;
        int attempts = 0;
        int maxAttempts = count * 3; // 最多尝试3倍的次数，避免无限循环

        while (successCount < count && attempts < maxAttempts && successCount < availableSlots.size()) {
            int slot = availableSlots.get(successCount);

            // 根据摸金箱种类获取随机物品
            Object randomItem = plugin.getTreasureItemManager().getRandomItem(chestType);

            if (randomItem != null) {
                ItemStack item = plugin.getTreasureItemManager().createItemStack(randomItem);
                if (item != null) {
                    treasureItems.put(slot, item);
                    treasureItemData.put(slot, randomItem);
                    successCount++;
                }
            }

            attempts++;
        }
    }

    /**
     * 加载已存在的摸金箱数据
     */
    private void loadExistingData(com.hang.plugin.listeners.PlayerListener.TreasureChestData data, int itemCount) {
        // 加载物品数据
        treasureItems.putAll(data.getItems());
        treasureItemData.putAll(data.getItemData());
        searchedSlots.addAll(data.getSearchedSlots());
    }

    /**
     * 保存当前摸金箱数据
     */
    private void saveCurrentData() {
        com.hang.plugin.listeners.PlayerListener.TreasureChestData existingData =
            plugin.getPlayerListener().getTreasureChestData(chestLocation);

        com.hang.plugin.listeners.PlayerListener.TreasureChestData data;
        if (existingData != null) {
            data = existingData;
        } else {
            // 修复：创建新数据时保持摸金箱类型
            String chestType = getChestTypeFromLocation(chestLocation);
            data = new com.hang.plugin.listeners.PlayerListener.TreasureChestData(chestType);
        }

        // 修复：确保原始物品数量正确设置
        if (data.getOriginalItemCount() <= 0 && !treasureItems.isEmpty()) {
            data.setOriginalItemCount(treasureItems.size());
        }

        // 设置首次开启时间（用于自动刷新检查）
        if (data.getOpenTime() == 0) {
            data.setOpenTime(System.currentTimeMillis());
        }

        // 清空并重新保存物品数据
        data.getItems().clear();
        data.getItemData().clear();
        for (Map.Entry<Integer, ItemStack> entry : treasureItems.entrySet()) {
            Object itemInfo = treasureItemData.get(entry.getKey());
            data.setItem(entry.getKey(), entry.getValue(), itemInfo);
        }

        // 保存已搜索槽位
        data.getSearchedSlots().clear();
        for (Integer slot : searchedSlots) {
            data.markSlotSearched(slot);
        }

        // 保存到PlayerListener
        plugin.getPlayerListener().saveTreasureChestData(chestLocation, data);
    }

    /**
     * 更新GUI显示状态
     */
    private void updateGUIDisplay(int itemCount) {
        // 清空所有槽位
        for (int i = 0; i < 27; i++) {
            inventory.setItem(i, new ItemStack(org.bukkit.Material.AIR));
        }

        // 设置有物品的槽位
        for (Map.Entry<Integer, ItemStack> entry : treasureItems.entrySet()) {
            int slot = entry.getKey();
            if (searchedSlots.contains(slot)) {
                // 已搜索的槽位显示实际物品
                inventory.setItem(slot, entry.getValue());
            } else {
                // 未搜索的槽位显示灰色玻璃板
                inventory.setItem(slot, createUnSearchedItem());
            }
        }


    }

    /**
     * 创建未搜索的物品 (灰色玻璃板)
     * 修复：根据搜索模式显示不同的lore
     */
    private ItemStack createUnSearchedItem() {
        String materialName = plugin.getConfig().getString("items.unsearched-item.material", "STAINED_GLASS_PANE");
        int data = plugin.getConfig().getInt("items.unsearched-item.data", 7); // 7 = 灰色，8 = 淡灰色

        // 修复：使用新的颜色创建方法
        ItemStack item = createColoredGlassPane(materialName, data);
        ItemMeta meta = item.getItemMeta();

        meta.setDisplayName(plugin.getConfig().getString("items.unsearched-item.name", "§7未搜索"));

        // 修复：根据搜索模式显示不同的lore
        if (isManualSearchEnabled()) {
            // 手动搜索模式：显示"等待手动搜索"
            java.util.List<String> manualLore = new java.util.ArrayList<>();
            manualLore.add("§7等待手动搜索");
            meta.setLore(manualLore);
        } else {
            // 自动搜索模式：显示"等待自动搜索"
            meta.setLore(plugin.getConfig().getStringList("items.unsearched-item.lore"));
        }

        item.setItemMeta(meta);
        return item;
    }

    /**
     * 创建带颜色的玻璃板（1.12.2兼容）
     */
    private ItemStack createColoredGlassPane(String materialName, int colorData) {
        Material material = VersionUtils.getCompatibleMaterial(materialName);
        ItemStack item = new ItemStack(material, 1);

        // 在1.12.2及以下版本中，需要设置durability来指定颜色
        if (VersionUtils.isVersionAtMost(1, 12)) {
            item.setDurability((short) colorData);
        } else {
            // 1.13+版本使用新的彩色材料系统
            material = VersionUtils.getCompatibleColoredMaterial(materialName, colorData);
            item = new ItemStack(material, 1);
        }

        return item;
    }





    /**
     * 开始搜索物品
     */
    private void startSearch(int slot) {
        // 修复：不在开始时设置冷却时间，而是在完成时设置
        // 获取物品的搜索速度（支持模组物品）
        Object itemData = treasureItemData.get(slot);
        int searchSpeed = plugin.getTreasureItemManager().getItemSearchSpeed(itemData);

        // 开始进度条搜索
        startProgressSearch(slot, searchSpeed);
    }

    /**
     * 开始进度条搜索
     */
    private void startProgressSearch(int slot, int searchSpeed) {
        // 播放搜索开始音效
        playSearchStartSound();

        // 获取配置
        int updateInterval = plugin.getConfig().getInt("treasure-chest.animation.update-interval", 3);
        String progressMode = plugin.getConfig().getString("treasure-chest.animation.progress-direction.mode", "countup");

        // 计算进度更新参数
        int totalTicks = searchSpeed * 20; // 搜索时间转换为tick
        int stepInterval = Math.max(1, updateInterval); // 使用配置的更新间隔，至少1 tick
        int totalSteps = Math.max(1, totalTicks / stepInterval); // 根据总时间和间隔计算步数

        // 创建进度条任务
        BukkitTask progressTask = new BukkitRunnable() {
            private int currentStep = progressMode.equals("countdown") ? totalSteps : 0;

            @Override
            public void run() {
                // 计算百分比进度
                int progressPercent;
                if (progressMode.equals("countdown")) {
                    progressPercent = (currentStep * 100) / totalSteps;
                } else { // countup
                    progressPercent = (currentStep * 100) / totalSteps;
                }

                // 更新进度条显示
                updateProgressDisplay(slot, progressPercent);

                // 不再播放进度音效

                // 更新步数
                if (progressMode.equals("countdown")) {
                    currentStep--;
                    if (currentStep < 0) {
                        // 搜索完成
                        completeProgressSearch(slot);
                        searchTasks.remove(slot);
                        this.cancel();
                    }
                } else { // countup
                    currentStep++;
                    if (currentStep > totalSteps) {
                        // 搜索完成
                        completeProgressSearch(slot);
                        searchTasks.remove(slot);
                        this.cancel();
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, stepInterval);

        // 存储任务以便后续取消
        searchTasks.put(slot, progressTask);
    }

    /**
     * 更新进度条显示
     */
    private void updateProgressDisplay(int slot, int progress) {
        // 🎨 新的配置驱动的进度显示系统
        String materialName = plugin.getConfig().getString("items.searching-progress.material", "STAINED_GLASS_PANE");

        // 根据进度确定当前阶段
        String stageName = getProgressStage(progress);
        String stagePath = "items.searching-progress.stages." + stageName;

        // 从配置读取当前阶段的设置
        int stageData = plugin.getConfig().getInt(stagePath + ".data", 14);
        String stageName_display = plugin.getConfig().getString(stagePath + ".name", "§e正在搜索... {progress}%");
        List<String> stageLore = plugin.getConfig().getStringList(stagePath + ".lore");

        // 创建进度物品
        ItemStack progressItem = createColoredGlassPane(materialName, stageData);
        ItemMeta meta = progressItem.getItemMeta();

        // 设置显示名称，替换占位符
        String displayName = stageName_display.replace("{progress}", String.valueOf(progress));
        meta.setDisplayName(displayName);

        // 设置Lore，替换占位符
        List<String> processedLore = new ArrayList<>();
        for (String loreLine : stageLore) {
            processedLore.add(loreLine.replace("{progress}", String.valueOf(progress)));
        }
        meta.setLore(processedLore);

        progressItem.setItemMeta(meta);

        // 为搜索中的玻璃板设置 CustomModelData (仅在高版本生效)
        int customModelData = plugin.getConfig().getInt("items.searching-progress.custom_model_data", 0);
        if (customModelData > 0) {
            progressItem = VersionUtils.setCustomModelData(progressItem, customModelData);
        }

        inventory.setItem(slot, progressItem);
    }

    /**
     * 根据进度百分比确定当前阶段
     */
    private String getProgressStage(int progress) {
        // 检查每个阶段的进度范围
        for (String stageKey : plugin.getConfig().getConfigurationSection("items.searching-progress.stages").getKeys(false)) {
            String stagePath = "items.searching-progress.stages." + stageKey;
            List<Integer> range = plugin.getConfig().getIntegerList(stagePath + ".progress_range");

            if (range.size() >= 2) {
                int minProgress = range.get(0);
                int maxProgress = range.get(1);

                if (progress >= minProgress && progress <= maxProgress) {
                    return stageKey;
                }
            }
        }

        // 默认返回第一个阶段
        return "stage_1";
    }

    /**
     * 播放搜索开始音效
     */
    private void playSearchStartSound() {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.search-start.enabled", true)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.search-start.sound", "entity.experience_orb.pickup");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-start.volume", 0.5);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-start.pitch", 1.0);

            // 使用兼容的音效播放方法
            VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 播放搜索成功音效
     */
    private void playSearchSuccessSound() {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.search-success.enabled", true)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.search-success.sound", "entity.player.levelup");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-success.volume", 0.8);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-success.pitch", 1.2);

            // 使用兼容的音效播放方法
            VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 检查自动刷新功能
     */
    private void checkAutoRefresh(com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
        // 检查自动刷新功能是否启用
        if (!plugin.getConfig().getBoolean("treasure-chest.auto_refresh.enabled", true)) {
            return;
        }

        // 如果数据为空或已经在刷新倒计时中，跳过检查
        if (data == null || data.getNextRefreshTime() > 0) {
            return;
        }

        // 检查摸金箱是否已开启
        long openTime = data.getOpenTime();
        if (openTime == 0) {
            return; // 还未开启，无需检查
        }

        // 获取空闲时间配置（分钟）
        int idleMinutes = plugin.getConfig().getInt("treasure-chest.auto_refresh.idle_minutes", 30);
        if (idleMinutes <= 0) {
            return; // 设置为0或负数表示禁用
        }

        // 计算空闲时间
        long currentTime = System.currentTimeMillis();
        long idleTime = currentTime - openTime;
        long idleMinutesActual = idleTime / (60 * 1000L);

        // 如果超过空闲时间，自动进入刷新倒计时（无论是否搜索完毕）
        if (idleMinutesActual >= idleMinutes) {
            // 根据摸金箱种类设置刷新时间
            String chestType = getChestTypeFromLocation(chestLocation);
            int refreshMinutes = plugin.getTreasureItemManager().getRefreshTime(chestType);
            long refreshTime = refreshMinutes * 60 * 1000L; // 转换为毫秒
            data.setNextRefreshTime(currentTime + refreshTime);

            // 保存数据
            plugin.getPlayerListener().saveTreasureChestData(chestLocation, data);

            // 调试日志
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("摸金箱自动进入刷新倒计时: " + chestLocation +
                    " (开启时间: " + idleMinutesActual + "分钟, 刷新时间: " + refreshMinutes + "分钟)");
            }
        }
    }

    /**
     * 播放搜索进行中音效
     */
    private void playSearchProgressSound() {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.search-progress.enabled", false)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.search-progress.sound", "block.note_block.pling");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-progress.volume", 0.3);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-progress.pitch", 1.5);

            // 使用兼容的音效播放方法
            VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 播放摸金箱打开音效
     */
    private void playChestOpenSound() {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.chest-open.enabled", true)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.chest-open.sound", "block.chest.open");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.chest-open.volume", 1.0);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.chest-open.pitch", 1.2);

            // 使用兼容的音效播放方法
            VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 播放摸金箱关闭音效
     */
    private void playChestCloseSound() {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.chest-close.enabled", true)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.chest-close.sound", "block.chest.close");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.chest-close.volume", 0.8);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.chest-close.pitch", 1.0);

            // 使用兼容的音效播放方法
            VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 播放物品拾取音效
     */
    private void playItemPickupSound() {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.item-pickup.enabled", true)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.item-pickup.sound", "entity.item.pickup");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.item-pickup.volume", 0.6);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.item-pickup.pitch", 1.3);

            // 使用兼容的音效播放方法
            VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 播放搜索失败音效
     */
    private void playSearchFailSound() {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.search-fail.enabled", false)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.search-fail.sound", "entity.villager.no");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-fail.volume", 0.4);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.search-fail.pitch", 0.8);

            // 使用兼容的音效播放方法
            VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 完成进度条搜索
     */
    private void completeProgressSearch(int slot) {
        // 播放搜索成功音效
        playSearchSuccessSound();

        // 完成搜索
        completeSearch(slot);

        // 延迟后开始下一个搜索
        scheduleNextSearch();
    }

    /**
     * 安排下一个搜索
     * 修复：只在自动搜索模式下安排下一个搜索
     */
    private void scheduleNextSearch() {
        // 修复：如果启用了手动搜索，不安排下一个自动搜索
        if (isManualSearchEnabled()) {
            return;
        }

        int nextSearchDelay = plugin.getConfig().getInt("treasure-chest.animation.next-search-delay", 40);

        new BukkitRunnable() {
            @Override
            public void run() {
                // 检查是否还有未搜索的物品
                if (!getUnsearchedSlots().isEmpty() &&
                    player.getOpenInventory().getTopInventory().equals(inventory)) {
                    // 立即尝试搜索下一个物品
                    performAutoSearch();
                }
            }
        }.runTaskLater(plugin, nextSearchDelay);
    }

    /**
     * 完成搜索
     */
    private void completeSearch(int slot) {
        searchedSlots.add(slot);

        // 修复：在搜索完成时设置冷却时间
        long cooldownTime = plugin.getTreasureItemManager().getSearchCooldown() * 1000L;
        searchCooldowns.put(player, System.currentTimeMillis() + cooldownTime);

        // 新增：触发搜索完成事件供等级插件监听
        triggerSearchCompleteEvent();

        // 显示战利品
        ItemStack treasure = treasureItems.get(slot);
        Object itemData = treasureItemData.get(slot);

        if (treasure != null) {
            inventory.setItem(slot, treasure);

            // 修复：使用配置文件中的消息（如果为空则不发送）
            String itemName = plugin.getTreasureItemManager().getItemName(itemData);
            plugin.sendMessageIfNotEmpty(player, "item-search-complete",
                "§a搜索完成！发现了：{item_name}",
                "item_name", itemName);

            // 显示物品，让玩家可以直接拿取
            inventory.setItem(slot, treasure);

            // 执行自定义命令
            plugin.getTreasureItemManager().executeCommands(itemData, player);
        } else {
            // 如果物品为null，显示空槽位或默认物品
            inventory.setItem(slot, new org.bukkit.inventory.ItemStack(org.bukkit.Material.AIR));
            // 修复：使用配置文件中的消息（如果为空则不发送）
            plugin.sendMessageIfNotEmpty(player, "item-search-empty", "§7这里什么都没有...");
        }

        // 修复：先保存搜索状态到持久化数据
        saveCurrentData();

        // 修复：使用GUI中的实时状态检查是否完全搜索完毕
        boolean isFullySearchedInGUI = isAllItemsSearched();

        // 调试：添加详细日志
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("搜索完成调试信息:");
            plugin.getLogger().info("  - 已搜索槽位数: " + searchedSlots.size());
            plugin.getLogger().info("  - 总物品数: " + treasureItems.size());
            plugin.getLogger().info("  - GUI判断已完全搜索: " + isFullySearchedInGUI);

            com.hang.plugin.listeners.PlayerListener.TreasureChestData debugData =
                plugin.getPlayerListener().getTreasureChestData(chestLocation);
            if (debugData != null) {
                plugin.getLogger().info("  - 持久化数据原始物品数: " + debugData.getOriginalItemCount());
                plugin.getLogger().info("  - 持久化数据判断已完全搜索: " + debugData.isFullySearched());
            }
        }

        if (isFullySearchedInGUI) {
            // 获取持久化数据来设置刷新时间
            com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
                plugin.getPlayerListener().getTreasureChestData(chestLocation);

            if (data != null && data.getNextRefreshTime() == 0) {
                // 根据摸金箱种类设置刷新时间
                String chestType = getChestTypeFromLocation(chestLocation);
                int refreshMinutes = plugin.getTreasureItemManager().getRefreshTime(chestType);
                long refreshTime = refreshMinutes * 60 * 1000L; // 转换为毫秒
                data.setNextRefreshTime(System.currentTimeMillis() + refreshTime);
                plugin.getPlayerListener().saveTreasureChestData(chestLocation, data);

                // 修复：使用配置文件中的消息（如果为空则不发送）
                plugin.sendMessageIfNotEmpty(player, "treasure-chest-fully-searched",
                    "§a恭喜！您已搜索完这个摸金箱的所有物品！");
                plugin.sendMessageIfNotEmpty(player, "treasure-chest-refresh-time",
                    "§e箱子将在 {minutes} 分钟后刷新",
                    "minutes", String.valueOf(refreshMinutes));

                // 修复：摸金箱搜索完毕后，清除玩家的搜索冷却时间
                // 这样当摸金箱刷新后，玩家可以立即开始搜索新的物品
                searchCooldowns.remove(player);
            }
        }

        // 修复：确保浮空字使用最新的状态
        updateHologram();
    }

    /**
     * 检查是否在冷却中
     */
    private boolean isOnCooldown() {
        Long cooldownEnd = searchCooldowns.get(player);
        return cooldownEnd != null && System.currentTimeMillis() < cooldownEnd;
    }



    /**
     * 打开GUI
     */
    public void open() {
        // 播放摸金箱打开音效
        playChestOpenSound();

        player.openInventory(inventory);

        // 启动自动搜索
        startAutoSearch();
    }

    /**
     * 获取库存对象
     */
    public Inventory getInventory() {
        return inventory;
    }

    /**
     * 获取玩家对象
     */
    public Player getPlayer() {
        return player;
    }

    /**
     * 检查槽位是否已被搜索
     */
    public boolean isSlotSearched(int slot) {
        return searchedSlots.contains(slot);
    }

    /**
     * 修复：严格检查槽位是否包含真实的已搜索物品
     */
    public boolean hasRealItem(int slot) {
        // 第一层检查：槽位必须已被搜索
        if (!searchedSlots.contains(slot)) {
            return false;
        }

        // 第二层检查：槽位必须在treasureItems中存在
        if (!treasureItems.containsKey(slot)) {
            return false;
        }

        // 第三层检查：GUI中的物品必须存在且不为空
        ItemStack guiItem = inventory.getItem(slot);
        if (guiItem == null || guiItem.getType() == Material.AIR) {
            return false;
        }

        // 第四层检查：GUI中的物品不能是玻璃板（未搜索状态）
        Material itemType = guiItem.getType();
        String typeName = itemType.name();
        if (typeName.contains("GLASS_PANE")) {
            return false;
        }

        // 第五层检查：GUI中的物品必须与treasureItems中的物品匹配
        ItemStack expectedItem = treasureItems.get(slot);
        if (expectedItem == null) {
            return false;
        }

        // 第六层检查：物品类型和基本属性必须匹配
        if (!guiItem.getType().equals(expectedItem.getType()) ||
            guiItem.getAmount() != expectedItem.getAmount()) {
            return false;
        }

        // 第七层检查：如果有自定义名称，必须匹配
        if (expectedItem.hasItemMeta() && expectedItem.getItemMeta().hasDisplayName()) {
            if (!guiItem.hasItemMeta() || !guiItem.getItemMeta().hasDisplayName() ||
                !guiItem.getItemMeta().getDisplayName().equals(expectedItem.getItemMeta().getDisplayName())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 移除指定槽位的物品
     */
    public void removeItem(int slot) {
        // 将槽位设置为空
        inventory.setItem(slot, new ItemStack(Material.AIR));

        // 从数据中移除
        treasureItems.remove(slot);

        // 同步到持久化数据
        saveCurrentData();
    }

    /**
     * 获取摸金箱位置
     */
    public org.bukkit.Location getChestLocation() {
        return chestLocation;
    }

    /**
     * 从位置获取摸金箱种类
     */
    private String getChestTypeFromLocation(org.bukkit.Location location) {
        com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
            plugin.getPlayerListener().getTreasureChestData(location);

        if (data != null) {
            return data.getChestType();
        } else {
            return "common"; // 默认为普通摸金箱
        }
    }

    /**
     * 清理冷却时间
     */
    public static void clearCooldown(Player player) {
        // 这个方法可以在玩家离线时调用来清理数据
    }

    /**
     * 取消所有搜索任务
     */
    public void cancelAllSearchTasks() {
        // 取消自动搜索任务
        if (autoSearchTask != null) {
            autoSearchTask.cancel();
            autoSearchTask = null;
        }

        for (Map.Entry<Integer, BukkitTask> entry : searchTasks.entrySet()) {
            int slot = entry.getKey();
            BukkitTask task = entry.getValue();

            if (task != null) {
                task.cancel();

                // 恢复槽位为未搜索状态
                if (treasureItems.containsKey(slot) && !searchedSlots.contains(slot)) {
                    inventory.setItem(slot, createUnSearchedItem());
                }
            }
        }
        searchTasks.clear();
    }

    /**
     * 启动自动搜索
     */
    private void startAutoSearch() {
        // 检查是否启用自动搜索
        if (!plugin.getConfig().getBoolean("treasure-chest.auto-search.enabled", true)) {
            return;
        }

        // 修复：如果启用了手动搜索，不启动自动搜索
        if (isManualSearchEnabled()) {
            return;
        }

        // 获取配置
        int interval = plugin.getConfig().getInt("treasure-chest.auto-search.interval", 4);

        autoSearchTask = new BukkitRunnable() {
            @Override
            public void run() {
                performAutoSearch();
            }
        }.runTaskTimer(plugin, 20L, interval * 20L); // 1秒后开始，每interval秒执行一次
    }

    /**
     * 执行自动搜索
     * 修复：添加手动搜索模式检查
     */
    private void performAutoSearch() {
        // 修复：如果启用了手动搜索，不执行自动搜索
        if (isManualSearchEnabled()) {
            return;
        }

        // 检查玩家是否还在查看这个GUI
        if (!player.getOpenInventory().getTopInventory().equals(inventory)) {
            // 玩家已关闭GUI，停止自动搜索
            if (autoSearchTask != null) {
                autoSearchTask.cancel();
                autoSearchTask = null;
            }
            return;
        }

        // 检查是否有正在搜索的物品
        if (!searchTasks.isEmpty()) {
            return; // 有物品正在搜索中，等待完成
        }

        // 获取未搜索的槽位
        java.util.List<Integer> unsearchedSlots = getUnsearchedSlots();
        if (unsearchedSlots.isEmpty()) {
            // 没有未搜索的槽位，停止自动搜索
            if (autoSearchTask != null) {
                autoSearchTask.cancel();
                autoSearchTask = null;
            }
            return;
        }

        // 检查冷却时间
        if (isOnCooldown()) {
            return; // 在冷却中，跳过这次搜索
        }

        // 获取搜索模式，一次只搜索一个物品
        String mode = plugin.getConfig().getString("treasure-chest.auto-search.mode", "random");

        // 选择一个槽位进行搜索
        int slotToSearch = selectNextSlot(unsearchedSlots, mode);
        if (slotToSearch != -1) {
            startSearch(slotToSearch);
        }
    }

    /**
     * 获取未搜索的槽位列表
     */
    private java.util.List<Integer> getUnsearchedSlots() {
        java.util.List<Integer> unsearchedSlots = new java.util.ArrayList<>();
        for (int slot : treasureItems.keySet()) {
            if (!searchedSlots.contains(slot) && !searchTasks.containsKey(slot)) {
                unsearchedSlots.add(slot);
            }
        }
        return unsearchedSlots;
    }

    /**
     * 根据模式选择下一个要搜索的槽位
     */
    private int selectNextSlot(java.util.List<Integer> availableSlots, String mode) {
        if (availableSlots.isEmpty()) {
            return -1;
        }

        if ("random".equalsIgnoreCase(mode)) {
            // 随机搜索
            return availableSlots.get(random.nextInt(availableSlots.size()));
        } else if ("sequential".equalsIgnoreCase(mode)) {
            // 按顺序搜索
            java.util.Collections.sort(availableSlots); // 确保按槽位顺序排序

            if (sequentialIndex >= availableSlots.size()) {
                sequentialIndex = 0; // 重置索引
            }

            if (sequentialIndex < availableSlots.size()) {
                int slot = availableSlots.get(sequentialIndex);
                sequentialIndex++;
                return slot;
            }
        }

        // 默认返回第一个
        return availableSlots.get(0);
    }

    /**
     * 更新浮空字
     */
    private void updateHologram() {
        // 修复：检查浮空字是否启用
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            // 如果浮空字被禁用，移除现有的浮空字
            plugin.getHologramManager().removeHologram(chestLocation);
            return;
        }

        com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
            plugin.getPlayerListener().getTreasureChestData(chestLocation);

        if (data != null) {
            String text;

            // 修复：使用GUI中的实时搜索状态而不是持久化数据
            boolean isFullySearchedInGUI = isAllItemsSearched();

            // 新增：额外的安全检查，确保数据一致性
            if (isFullySearchedInGUI && treasureItems.isEmpty()) {
                // 如果GUI显示已搜索完毕但没有物品，说明数据异常，不显示浮空字
                plugin.getHologramManager().removeHologram(chestLocation);
                return;
            }

            if (isFullySearchedInGUI) {
                // 检查是否可以刷新
                long nextRefresh = data.getNextRefreshTime();
                if (nextRefresh > 0 && System.currentTimeMillis() >= nextRefresh) {
                    // 可以刷新 - 使用自定义文本
                    text = plugin.getConfig().getString("treasure-chest.hologram.messages.can_refresh",
                        "§a可以刷新！右键重新生成物品");
                } else if (nextRefresh > 0) {
                    // 显示刷新倒计时 - 使用自定义文本
                    long remaining = (nextRefresh - System.currentTimeMillis()) / 1000;
                    long minutes = remaining / 60;
                    long seconds = remaining % 60;

                    String template = plugin.getConfig().getString("treasure-chest.hologram.messages.refresh_countdown",
                        "§e刷新倒计时: §c{minutes}:{seconds}");

                    text = template
                        .replace("{minutes}", String.valueOf(minutes))
                        .replace("{seconds}", String.format("%02d", seconds));
                } else {
                    // 已搜索完毕 - 使用自定义文本
                    text = plugin.getConfig().getString("treasure-chest.hologram.messages.fully_searched",
                        "§a已搜索完毕");
                }
            } else {
                // 修复：使用GUI中的实时未搜索数量和自定义文本
                int unsearched = getUnsearchedCountInGUI();
                int total = treasureItems.size();

                // 新增：额外的安全检查
                if (total <= 0 || unsearched < 0) {
                    // 数据异常，不显示浮空字
                    plugin.getHologramManager().removeHologram(chestLocation);
                    return;
                }

                String template = plugin.getConfig().getString("treasure-chest.hologram.messages.unsearched",
                    "§6还有 §e{count} §6个物品未搜索");

                text = template
                    .replace("{count}", String.valueOf(unsearched))
                    .replace("{total}", String.valueOf(total));
            }

            plugin.getHologramManager().createOrUpdateHologram(chestLocation, text);
        }
    }

    /**
     * 新增：检查GUI中是否所有物品都已搜索
     */
    private boolean isAllItemsSearched() {
        return searchedSlots.size() >= treasureItems.size();
    }

    /**
     * 新增：获取GUI中的未搜索物品数量
     */
    private int getUnsearchedCountInGUI() {
        int totalItems = treasureItems.size();
        int searchedItems = searchedSlots.size();
        return Math.max(0, totalItems - searchedItems);
    }

    /**
     * 设置当前搜索者
     */
    private void setCurrentSearcher() {
        com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
            plugin.getPlayerListener().getTreasureChestData(chestLocation);

        if (data != null) {
            data.setCurrentSearcher(player.getUniqueId());
            plugin.getPlayerListener().saveTreasureChestData(chestLocation, data);
        }
    }

    /**
     * 清除当前搜索者
     */
    public void clearCurrentSearcher() {
        com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
            plugin.getPlayerListener().getTreasureChestData(chestLocation);

        if (data != null && data.isSearchedBy(player.getUniqueId())) {
            data.clearSearcher();
            plugin.getPlayerListener().saveTreasureChestData(chestLocation, data);
        }
    }

    /**
     * 新增：检查是否启用手动搜索
     */
    private boolean isManualSearchEnabled() {
        return plugin.getConfig().getBoolean("treasure-chest.manual-search.enabled", false);
    }



    /**
     * 新增：触发搜索完成事件
     */
    private void triggerSearchCompleteEvent() {
        // 直接调用合并后的等级管理器
        plugin.getServer().getScheduler().runTask(plugin, new Runnable() {
            @Override
            public void run() {
                // 调用等级管理器增加搜索次数
                if (plugin.getLevelManager() != null) {
                    plugin.getLevelManager().addPlayerSearch(player);
                }
            }
        });
    }

    /**
     * 新增：处理手动搜索点击
     */
    public void handleManualSearchClick(int slot) {
        // 检查是否启用手动搜索
        if (!isManualSearchEnabled()) {
            return;
        }

        // 检查槽位是否有物品且未被搜索
        if (!treasureItems.containsKey(slot)) {
            return; // 没有物品的槽位
        }

        if (searchedSlots.contains(slot)) {
            // 修复：使用配置文件中的消息（如果为空则不发送）
            plugin.sendMessageIfNotEmpty(player, "manual-search-already-searched",
                "§c这个物品已经被搜索过了！");
            return;
        }

        if (searchTasks.containsKey(slot)) {
            // 修复：使用配置文件中的消息（如果为空则不发送）
            plugin.sendMessageIfNotEmpty(player, "manual-search-in-progress",
                "§e这个物品正在搜索中！");
            return;
        }

        // 修复：手动搜索模式下必须等待当前物品搜索完成
        if (!searchTasks.isEmpty()) {
            plugin.sendMessageIfNotEmpty(player, "manual-search-wait-current",
                "§e请等待当前物品搜索完成后再搜索其他物品！");
            return;
        }

        // 检查冷却时间
        if (isOnCooldown()) {
            Long cooldownEnd = searchCooldowns.get(player);
            long remaining = (cooldownEnd - System.currentTimeMillis()) / 1000;
            plugin.sendMessageIfNotEmpty(player, "search-cooldown-remaining",
                "§c搜索冷却中，请等待 {seconds} 秒",
                "seconds", String.valueOf(remaining));
            return;
        }

        // 开始手动搜索
        plugin.sendMessageIfNotEmpty(player, "manual-search-start",
            "§a开始手动搜索物品...");
        startSearch(slot);
    }
}
