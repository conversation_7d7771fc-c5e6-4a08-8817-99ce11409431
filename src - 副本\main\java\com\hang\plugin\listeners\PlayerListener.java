package com.hang.plugin.listeners;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.gui.TreasureChestGUI;
import com.hang.plugin.gui.TreasureManagementGUI;
import com.hang.plugin.gui.TreasureEditGUI;
import com.hang.plugin.gui.CommandEditGUI;

// import com.hang.plugin.utils.VersionUtils;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryAction;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.Material;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.bukkit.event.block.Action;
import org.bukkit.Location;
import org.bukkit.event.world.ChunkLoadEvent;
import org.bukkit.event.world.ChunkUnloadEvent;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 玩家事件监听器
 *
 * <AUTHOR>
 */
public class PlayerListener implements Listener {

    private final HangPlugin plugin;
    private final Map<UUID, TreasureChestGUI> openGUIs;
    private final Map<UUID, TreasureManagementGUI> openManagementGUIs;
    private final Map<UUID, TreasureEditGUI> openEditGUIs;
    private final Map<UUID, com.hang.plugin.gui.ChestTypeSelectionGUI> openChestTypeSelectionGUIs;
    private final Map<UUID, CommandEditGUI> openCommandEditGUIs;

    private final Map<UUID, CommandEditGUI.CommandInputListener> commandInputListeners;

    private final Map<String, TreasureChestData> treasureChestData;

    // 位置缓存：避免重复解析字符串，提升性能
    private final Map<String, org.bukkit.Location> locationCache;

    // 浮空字更新任务控制
    private BukkitTask hologramUpdateTask;

    // 插件关闭标志，防止关服时异步任务继续运行
    private volatile boolean isShuttingDown = false;

    // 撤离功能相关字段
    private final Map<UUID, Location> firstPositions = new HashMap<>();
    private final Map<UUID, Location> secondPositions = new HashMap<>();

    public PlayerListener(HangPlugin plugin) {
        this.plugin = plugin;
        this.openGUIs = new HashMap<>();
        this.openManagementGUIs = new HashMap<>();
        this.openEditGUIs = new HashMap<>();
        this.openChestTypeSelectionGUIs = new HashMap<>();
        this.openCommandEditGUIs = new HashMap<>();

        this.commandInputListeners = new HashMap<>();
        this.treasureChestData = new HashMap<>();
        this.locationCache = new ConcurrentHashMap<>();

        // 启动搜索者清理任务
        startSearcherCleanupTask();

        // 优化：只在浮空字启用时启动更新任务
        checkAndStartHologramUpdateTask();
    }

    /**
     * 玩家加入事件
     */
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // 清理可能存在的数据
        openGUIs.remove(player.getUniqueId());
    }

    /**
     * 玩家离开事件
     */
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 取消搜索任务并清理GUI数据
        TreasureChestGUI gui = openGUIs.get(playerId);
        if (gui != null) {
            gui.cancelAllSearchTasks();
            // 清除当前搜索者
            gui.clearCurrentSearcher();
        }
        openGUIs.remove(playerId);

        // 清理其他GUI映射
        openManagementGUIs.remove(playerId);
        openEditGUIs.remove(playerId);
        openChestTypeSelectionGUIs.remove(playerId);
        openCommandEditGUIs.remove(playerId);

        commandInputListeners.remove(playerId);

        // 清理摸金箱冷却时间
        TreasureChestGUI.clearCooldown(player);
    }

    /**
     * 玩家切换世界事件
     * 修复：处理跨世界摸金箱浮空字卡住问题
     */
    @EventHandler
    public void onPlayerChangedWorld(org.bukkit.event.player.PlayerChangedWorldEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 如果玩家有打开的摸金箱GUI，关闭它
        TreasureChestGUI gui = openGUIs.get(playerId);
        if (gui != null) {
            // 取消所有搜索任务
            gui.cancelAllSearchTasks();
            // 清除当前搜索者
            gui.clearCurrentSearcher();
            // 关闭GUI
            player.closeInventory();
            // 注销GUI
            unregisterTreasureGUI(player);
        }

        // 清理其他GUI
        if (openManagementGUIs.containsKey(playerId)) {
            player.closeInventory();
            openManagementGUIs.remove(playerId);
        }
        if (openEditGUIs.containsKey(playerId)) {
            player.closeInventory();
            openEditGUIs.remove(playerId);
        }
        if (openChestTypeSelectionGUIs.containsKey(playerId)) {
            player.closeInventory();
            openChestTypeSelectionGUIs.remove(playerId);
        }


    }

    /**
     * 玩家打开容器事件 (处理箱子替换为摸金箱)
     */
    @EventHandler
    public void onInventoryOpen(org.bukkit.event.inventory.InventoryOpenEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();
        org.bukkit.inventory.Inventory inventory = event.getInventory();

        // 检查是否是方块容器（支持所有类型的容器，包括模组容器）
        if (inventory.getHolder() instanceof org.bukkit.block.BlockState) {

            // 获取容器方块
            final org.bukkit.block.Block block = ((org.bukkit.block.BlockState) inventory.getHolder()).getBlock();

            if (block != null) {
                // 检查是否应该替换为摸金箱
                if (plugin.getChestReplacementManager() != null &&
                    plugin.getChestReplacementManager().shouldReplaceChest(player, block)) {

                    // 取消原始的打开事件
                    event.setCancelled(true);

                    // 延迟执行替换，避免事件冲突
                    plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
                        @Override
                        public void run() {
                            // 执行替换
                            if (plugin.getChestReplacementManager().replaceChest(player, block)) {
                                // 替换成功后，让玩家重新打开（现在是摸金箱了）
                                plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
                                    @Override
                                    public void run() {
                                        // 触发摸金箱打开逻辑
                                        org.bukkit.inventory.ItemStack itemInHand = player.getInventory().getItemInHand();

                                        PlayerInteractEvent fakeEvent = new PlayerInteractEvent(
                                            player,
                                            org.bukkit.event.block.Action.RIGHT_CLICK_BLOCK,
                                            itemInHand,
                                            block,
                                            org.bukkit.block.BlockFace.UP
                                        );
                                        onPlayerInteract(fakeEvent);
                                    }
                                }, 1L);
                            }
                        }
                    }, 1L);
                }
            }
        }
    }

    /**
     * 玩家交互事件 (处理摸金箱交互和箱子替换)
     */
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // 检查是否是撤离点选择工具
        ItemStack item = event.getItem();
        if (item != null && item.getType() == Material.BLAZE_ROD && item.hasItemMeta()) {
            ItemMeta meta = item.getItemMeta();
            if (meta.hasDisplayName() && meta.getDisplayName().equals("§6撤离点选择工具")) {
                // 处理撤离点选择
                handleEvacuationSelection(event);
                return;
            }
        }

        // 检查箱子替换功能（只在配置的世界中检测）
        if (event.getAction() == org.bukkit.event.block.Action.RIGHT_CLICK_BLOCK &&
            event.getClickedBlock() != null) {

            org.bukkit.block.Block clickedBlock = event.getClickedBlock();

            // 只在配置的世界中检查箱子替换
            if (plugin.getChestReplacementManager() != null &&
                plugin.getChestReplacementManager().shouldReplaceChest(player, clickedBlock)) {

                // 取消原始的交互事件
                event.setCancelled(true);

                // 延迟执行替换，避免事件冲突
                plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
                    @Override
                    public void run() {
                        // 执行替换
                        if (plugin.getChestReplacementManager().replaceChest(player, clickedBlock)) {
                            // 替换成功后，让玩家重新交互（现在是摸金箱了）
                            plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
                                @Override
                                public void run() {
                                    // 重新触发交互事件，但这次是摸金箱
                                    handleTreasureChestInteraction(player, clickedBlock, null);
                                }
                            }, 1L);
                        }
                    }
                }, 1L);

                return; // 不继续处理原始的摸金箱逻辑
            }
        }

        // 处理摸金箱交互
        if (event.getAction() == org.bukkit.event.block.Action.RIGHT_CLICK_BLOCK &&
            event.getClickedBlock() != null) {

            // 优化：在handleTreasureChestInteraction中统一处理检测和事件取消
            handleTreasureChestInteraction(player, event.getClickedBlock(), event);
        } else {
            // 对于非右键方块的事件，不传递event参数
            handleTreasureChestInteraction(player, event.getClickedBlock(), null);
        }
    }

    /**
     * 处理摸金箱交互逻辑
     * 修复：优先检查摸金箱数据，支持任何材质的摸金箱（包括非容器模组物品）
     */
    private void handleTreasureChestInteraction(Player player, org.bukkit.block.Block clickedBlock, PlayerInteractEvent event) {
        if (clickedBlock == null) {
            return;
        }

        // 获取方块位置
        org.bukkit.Location chestLocation = clickedBlock.getLocation();

        // 修复：优先检查是否是摸金箱，而不是先检查容器类型
        // 这样可以支持非容器格式的模组物品作为摸金箱
        TreasureChestData existingData = getTreasureChestData(chestLocation);

        if (existingData == null) {
            // 不是摸金箱，检查是否为容器类型以决定是否需要进一步处理
            if (!isPotentialContainer(clickedBlock.getType()) &&
                !isRuntimeContainer(clickedBlock)) {
                // 既不是摸金箱也不是容器，直接返回
                return;
            }
            // 这是一个普通容器，不进行任何处理，让原版逻辑处理
            return;
        }

        // 到这里说明这是一个摸金箱（有摸金箱数据）
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("检测到摸金箱交互: " + locationToString(chestLocation) +
                " 类型: " + existingData.getChestType());
        }

        // 重要：取消原版容器打开事件，防止同时打开两个界面
        if (event != null) {
            event.setCancelled(true);
        }

        // 重要：取消原版容器打开事件，防止同时打开两个界面
        if (event != null) {
            event.setCancelled(true);
        }

        // 检查是否有权限使用摸金箱
        if (!player.hasPermission("evacuation.chest")) {
            player.sendMessage(plugin.getMessage("no-permission"));
            return;
        }

        // 重要：取消原版箱子打开事件，防止同时打开两个界面
        // 需要通过调用者传递事件来取消

        // 检查是否需要刷新
        checkAndRefreshChest(chestLocation);

        // 修复：移除禁止打开的逻辑，改为允许打开但显示空箱子
        // 检查摸金箱状态，但不禁止打开，让玩家可以查看空箱子
        TreasureChestData data = getTreasureChestData(chestLocation);
        // 注释掉原有的禁止打开逻辑，现在允许玩家随时打开摸金箱
        /*
        if (data != null && data.isFullySearched()) {
            long nextRefresh = data.getNextRefreshTime();
            if (nextRefresh > 0 && System.currentTimeMillis() < nextRefresh) {
                // 摸金箱已搜索完毕且还在冷却中，禁止打开
                long remaining = (nextRefresh - System.currentTimeMillis()) / 1000;
                long minutes = remaining / 60;
                long seconds = remaining % 60;
                player.sendMessage(String.format("§c摸金箱已搜索完毕！刷新倒计时: §e%d:%02d", minutes, seconds));
                return;
            } else if (nextRefresh <= 0) {
                // 没有设置刷新时间，永久禁用
                player.sendMessage("§c这个摸金箱已经搜索完毕，无法再次打开！");
                return;
            }
        }
        */

        // 检查是否有其他玩家正在搜索这个摸金箱
        if (data != null && data.isBeingSearched() && !data.isSearchedBy(player.getUniqueId())) {
            // 获取正在搜索的玩家名称
            Player currentSearcher = plugin.getServer().getPlayer(data.getCurrentSearcher());
            String searcherName = currentSearcher != null ? currentSearcher.getName() : "未知玩家";
            player.sendMessage("§c这个摸金箱正在被 §e" + searcherName + " §c搜索中，请稍后再试！");
            return;
        }

        // 打开摸金箱GUI，传递方块位置
        TreasureChestGUI gui = new TreasureChestGUI(plugin, player, chestLocation);

        // 注册GUI到监听器
        registerTreasureGUI(player, gui);

        gui.open();

            // 修复：根据搜索模式显示不同的提示消息，支持摸金箱名称变量
            boolean manualSearchEnabled = plugin.getConfig().getBoolean("treasure-chest.manual-search.enabled", false);

            // 获取摸金箱名称
            String chestType = getChestTypeFromLocation(chestLocation);
            String chestName = plugin.getTreasureItemManager().getChestName(chestType);

            if (manualSearchEnabled) {
                String message = plugin.getConfig().getString("messages.treasure-chest-opened-manual", "§6{chest_name}已打开！§e点击未搜索的物品进行手动搜索！");
                message = message.replace("{chest_name}", chestName);
                player.sendMessage(message);
            } else {
                String message = plugin.getConfig().getString("messages.treasure-chest-opened-auto", "§6{chest_name}已打开！正在自动搜索宝藏...");
                message = message.replace("{chest_name}", chestName);
                player.sendMessage(message);
            }
    }

    /**
     * 方块放置事件 (处理摸金箱放置)
     * 新增：只有OP才能放置摸金箱
     */
    @EventHandler
    public void onBlockPlace(org.bukkit.event.block.BlockPlaceEvent event) {
        Player player = event.getPlayer();
        org.bukkit.inventory.ItemStack item = event.getItemInHand();

        // 检查是否放置的是摸金箱
        boolean isTreasureChest = com.hang.plugin.items.TreasureChestItem.isTreasureChest(item);

        if (isTreasureChest) {
            // 新增：检查摸金箱放置权限
            if (!canPlaceTreasureChest(player)) {
                // 玩家没有权限放置摸金箱，取消事件并提示
                event.setCancelled(true);
                sendPlacementDeniedMessage(player);
                return;
            }

            org.bukkit.Location chestLocation = event.getBlock().getLocation();

            // 修复：添加异常处理，防止LinkageError
            String chestType = null;
            try {
                // 从物品中识别摸金箱种类
                chestType = plugin.getChestTypeManager().getChestTypeFromItem(item);
            } catch (LinkageError | Exception e) {
                plugin.getLogger().warning("获取摸金箱种类时出错，使用默认种类: " + e.getMessage());
            }

            if (chestType == null) {
                chestType = "common"; // 默认为普通摸金箱
            }

            // 创建新的摸金箱数据，包含种类信息
            PlayerListener.TreasureChestData data = new PlayerListener.TreasureChestData(chestType);
            saveTreasureChestData(chestLocation, data);

            // 修复：获取种类显示名称 - 添加异常处理
            String typeName;
            if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                typeName = item.getItemMeta().getDisplayName();
            } else {
                try {
                    com.hang.plugin.manager.ChestTypeManager.ChestType type = plugin.getChestTypeManager().getChestType(chestType);
                    typeName = type != null ? type.getDisplayName() : "§6摸金箱";
                } catch (LinkageError | Exception e) {
                    plugin.getLogger().warning("获取摸金箱种类显示名称时出错: " + e.getMessage());
                    typeName = "§6摸金箱"; // 使用默认名称
                }
            }

            player.sendMessage("§a" + typeName + " §a已放置！右键点击开始搜索宝藏！");
            plugin.getLogger().info("管理员 " + player.getName() + " 放置了摸金箱: " + typeName + " 在位置 " +
                chestLocation.getWorld().getName() + " " + chestLocation.getBlockX() + "," +
                chestLocation.getBlockY() + "," + chestLocation.getBlockZ());
        }
    }

    /**
     * 方块破坏事件 (处理摸金箱被破坏)
     */
    @EventHandler
    public void onBlockBreak(org.bukkit.event.block.BlockBreakEvent event) {
        org.bukkit.Location chestLocation = event.getBlock().getLocation();

        // 检查这个位置是否有摸金箱数据（支持任何材质的摸金箱）
        String key = locationToString(chestLocation);
        if (treasureChestData.containsKey(key)) {
            // 修复：先关闭所有相关的GUI，防止延迟任务重新创建浮空字
            for (Map.Entry<UUID, TreasureChestGUI> entry : openGUIs.entrySet()) {
                TreasureChestGUI gui = entry.getValue();
                if (gui != null && gui.getChestLocation().equals(chestLocation)) {
                    Player player = plugin.getServer().getPlayer(entry.getKey());
                    if (player != null && player.isOnline()) {
                        // 取消所有搜索任务
                        gui.cancelAllSearchTasks();
                        // 清除当前搜索者
                        gui.clearCurrentSearcher();
                        // 关闭GUI
                        player.closeInventory();
                    }
                    // 注销GUI
                    openGUIs.remove(entry.getKey());
                    break;
                }
            }

            // 移除摸金箱数据
            treasureChestData.remove(key);

            // 同时从文件中删除
            plugin.getChestManager().removeChestData(chestLocation);

            // 修复：立即移除浮空字，并确保备份数据也被清理
            plugin.getHologramManager().removeHologram(chestLocation);

            // 额外保险：延迟再次检查并移除可能残留的浮空字
            plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
                @Override
                public void run() {
                    plugin.getHologramManager().removeHologram(chestLocation);
                }
            }, 5L); // 延迟5tick（0.25秒）

            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("摸金箱被破坏，已清理所有相关数据: " + key);
            }
        }
    }

    /**
     * 库存关闭事件 (处理摸金箱GUI关闭时同步物品状态)
     */
    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 检查是否是摸金箱GUI
        TreasureChestGUI gui = openGUIs.get(playerId);
        if (gui != null && event.getInventory().equals(gui.getInventory())) {
            // 播放摸金箱关闭音效
            playChestCloseSound(player);

            // 取消所有搜索任务
            gui.cancelAllSearchTasks();

            // 清除当前搜索者
            gui.clearCurrentSearcher();

            // 同步摸金箱状态
            syncTreasureChestState(gui);

            // 修复：只有在全息图启用时才更新浮空字
            if (plugin.getTreasureItemManager().isHologramEnabled()) {
                org.bukkit.Location chestLocation = gui.getChestLocation();
                TreasureChestData data = getTreasureChestData(chestLocation);
                if (data != null) {
                    // 延迟一点时间确保数据同步完成
                    plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
                        @Override
                        public void run() {
                            // 修复：再次检查全息图是否启用（防止延迟期间被关闭）
                            if (!plugin.getTreasureItemManager().isHologramEnabled()) {
                                return;
                            }

                            // 修复：在延迟更新前再次检查摸金箱是否还存在
                            TreasureChestData currentData = getTreasureChestData(chestLocation);
                            if (currentData != null) {
                                updateChestHologram(chestLocation, currentData);
                            } else {
                                // 摸金箱已被移除，确保浮空字也被移除
                                plugin.getHologramManager().removeHologram(chestLocation);
                            }
                        }
                    }, 2L); // 延迟2tick（0.1秒）
                }
            }

            // 注销GUI
            unregisterTreasureGUI(player);
            return;
        }

        // 检查是否是管理GUI
        TreasureManagementGUI managementGUI = openManagementGUIs.get(playerId);
        if (managementGUI != null && event.getInventory().equals(managementGUI.getInventory())) {
            // 注销管理GUI
            openManagementGUIs.remove(playerId);
            return;
        }



        // 检查是否是编辑GUI
        TreasureEditGUI editGUI = openEditGUIs.get(playerId);
        if (editGUI != null && event.getInventory().equals(editGUI.getInventory())) {
            // 注销编辑GUI
            openEditGUIs.remove(playerId);
            return;
        }

        // 检查是否是摸金箱种类选择GUI
        com.hang.plugin.gui.ChestTypeSelectionGUI chestTypeSelectionGUI = openChestTypeSelectionGUIs.get(playerId);
        if (chestTypeSelectionGUI != null && event.getInventory().equals(chestTypeSelectionGUI.getInventory())) {
            // 注销摸金箱种类选择GUI
            openChestTypeSelectionGUIs.remove(playerId);
            return;
        }
    }

    /**
     * 库存点击事件
     */
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        UUID playerId = player.getUniqueId();

        // 修复：如果是管理GUI，不要处理模组物品拿取逻辑
        TreasureManagementGUI managementGUI = openManagementGUIs.get(playerId);
        if (managementGUI != null) {
            // 检查是否点击的是管理GUI本身
            if (event.getClickedInventory() != null && event.getClickedInventory().equals(managementGUI.getInventory())) {
                // 点击管理GUI本身
                event.setCancelled(true);
                managementGUI.handleClick(event.getSlot(), event.isRightClick(), event.isShiftClick());
                return;
            }
            // 检查是否点击的是玩家背包（在管理GUI打开时）
            else if (event.getClickedInventory() != null && event.getClickedInventory().equals(player.getInventory())) {
                // 点击玩家背包 - 新功能：直接添加物品到战利品
                handlePlayerInventoryClickInManagementGUI(event, managementGUI, player);
                return;
            }
        }

        // 检查是否是拿取模组物品代表（仅在非管理GUI中）
        if (event.getAction() == InventoryAction.PICKUP_ALL ||
            event.getAction() == InventoryAction.PICKUP_HALF ||
            event.getAction() == InventoryAction.PICKUP_ONE ||
            event.getAction() == InventoryAction.PICKUP_SOME) {

            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem != null && isModItemRepresentative(clickedItem)) {
                // 这是模组物品代表，需要转换为真实模组物品
                handleModItemPickup(event, clickedItem);
                return;
            }
        }


        // 检查是否是摸金箱GUI
        TreasureChestGUI gui = openGUIs.get(playerId);
        if (gui != null && event.getInventory().equals(gui.getInventory())) {
            // 取消所有点击事件，摸金箱变为纯展示界面
            event.setCancelled(true);

            int slot = event.getSlot();

            // 只处理摸金箱区域的点击
            if (slot >= 0 && slot < gui.getInventory().getSize()) {
                // 检查点击的物品
                org.bukkit.inventory.ItemStack clickedItem = event.getCurrentItem();

                // 修复：检查是否启用手动搜索
                boolean manualSearchEnabled = plugin.getConfig().getBoolean("treasure-chest.manual-search.enabled", false);

                // 如果点击的是玻璃板（未搜索或搜索中）
                if (clickedItem != null && isGlassPane(clickedItem.getType())) {
                    if (manualSearchEnabled) {
                        // 手动搜索模式：处理点击事件
                        gui.handleManualSearchClick(slot);
                    } else {
                        // 自动搜索模式：提示玩家使用自动搜索
                        if (clickedItem.hasItemMeta() &&
                            clickedItem.getItemMeta().hasDisplayName() &&
                            clickedItem.getItemMeta().getDisplayName().contains("未搜索")) {
                            player.sendMessage("§e请等待自动搜索，无需手动点击！");
                        }
                    }
                    return;
                }



                // 修复：严格验证点击的物品是否为合法的已搜索物品
                if (clickedItem != null && clickedItem.getType() != org.bukkit.Material.AIR) {
                    // 第一层检查：槽位必须已被搜索
                    if (!gui.isSlotSearched(slot)) {
                        // 安全日志：记录可疑的未搜索物品点击
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().warning("玩家 " + player.getName() + " 尝试点击未搜索的槽位 " + slot);
                        }
                        return;
                    }

                    // 第二层检查：槽位必须包含真实物品（多重验证）
                    if (!gui.hasRealItem(slot)) {
                        // 安全日志：记录可疑的非真实物品点击
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().warning("玩家 " + player.getName() + " 尝试点击非真实物品，槽位 " + slot);
                        }
                        return;
                    }

                    // 第三层检查：验证点击的物品类型不是玻璃板
                    String typeName = clickedItem.getType().name();
                    if (typeName.contains("GLASS_PANE")) {
                        // 安全日志：记录可疑的玻璃板点击
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().warning("玩家 " + player.getName() + " 尝试点击玻璃板，槽位 " + slot);
                        }
                        return;
                    }

                    // 第四层检查：验证事件类型，防止特殊点击动作
                    org.bukkit.event.inventory.InventoryAction action = event.getAction();
                    if (action == org.bukkit.event.inventory.InventoryAction.MOVE_TO_OTHER_INVENTORY ||
                        action == org.bukkit.event.inventory.InventoryAction.COLLECT_TO_CURSOR ||
                        action == org.bukkit.event.inventory.InventoryAction.UNKNOWN) {
                        // 安全日志：记录可疑的特殊点击动作
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().warning("玩家 " + player.getName() + " 使用了特殊点击动作: " + action + "，槽位 " + slot);
                        }
                        return;
                    }

                    // 通过所有安全检查，允许拿取物品
                    try {
                        // 尝试将物品添加到玩家背包
                        java.util.HashMap<Integer, org.bukkit.inventory.ItemStack> leftover =
                            player.getInventory().addItem(clickedItem.clone());

                        if (leftover.isEmpty()) {
                            // 成功添加到背包，从摸金箱中移除
                            gui.removeItem(slot);

                            // 获取物品名称
                            String itemName = clickedItem.hasItemMeta() && clickedItem.getItemMeta().hasDisplayName()
                                ? clickedItem.getItemMeta().getDisplayName()
                                : clickedItem.getType().name();

                            // 使用配置文件中的消息
                            plugin.sendMessageIfNotEmpty(player, "item-obtained",
                                "§a获得物品: §f{item_name} §ax{amount}",
                                "item_name", itemName,
                                "amount", String.valueOf(clickedItem.getAmount()));

                            // 安全日志：记录合法的物品拿取
                            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                plugin.getLogger().info("玩家 " + player.getName() + " 合法拿取物品，槽位 " + slot + "，物品: " + clickedItem.getType());
                            }
                        } else {
                            // 背包满了 - 使用配置文件中的消息
                            plugin.sendMessageIfNotEmpty(player, "inventory-full", "§c背包空间不足！");
                        }
                    } catch (Exception e) {
                        // 异常处理：防止任何意外错误
                        plugin.getLogger().severe("玩家 " + player.getName() + " 拿取物品时出错: " + e.getMessage());
                        player.sendMessage("§c拿取物品时出错，请联系管理员！");
                    }
                }
            }
            return;
        }



        // 检查是否是管理GUI中的摸金箱种类选择GUI
        if (event.getView().getTitle().equals("§6添加物品 - 选择摸金箱种类")) {
            event.setCancelled(true);
            // 找到对应的TreasureManagementGUI实例
            if (managementGUI != null) {
                managementGUI.handleChestTypeSelection(event);
            } else {
                // 如果没有找到管理GUI，可能是从其他地方打开的种类选择GUI
                // 尝试创建一个临时的处理逻辑
                handleStandaloneChestTypeSelection(event);
            }
            return;
        }



        // 检查是否是编辑GUI
        TreasureEditGUI editGUI = openEditGUIs.get(playerId);
        if (editGUI != null && event.getInventory().equals(editGUI.getInventory())) {
            event.setCancelled(true); // 取消所有点击事件
            editGUI.handleClick(event.getSlot());
            return;
        }

        // 检查是否是摸金箱种类选择GUI
        com.hang.plugin.gui.ChestTypeSelectionGUI chestTypeSelectionGUI = openChestTypeSelectionGUIs.get(playerId);
        if (chestTypeSelectionGUI != null && event.getInventory().equals(chestTypeSelectionGUI.getInventory())) {
            event.setCancelled(true); // 取消所有点击事件
            chestTypeSelectionGUI.handleClick(event.getSlot(), event.isRightClick());
            return;
        }

        // 检查是否是命令编辑GUI
        CommandEditGUI commandEditGUI = openCommandEditGUIs.get(playerId);
        if (commandEditGUI != null && event.getInventory().equals(commandEditGUI.getInventory())) {
            event.setCancelled(true); // 取消所有点击事件
            commandEditGUI.handleClick(event.getSlot(), event.isRightClick(), event.isShiftClick());
            return;
        }


    }







    /**
     * 启动搜索者清理任务
     * 定期清理长时间未活动的搜索者，防止摸金箱被永久锁定
     */
    private void startSearcherCleanupTask() {
        // 每5分钟检查一次
        long cleanupInterval = 5 * 60 * 20L; // 5分钟 = 5 * 60 * 20 ticks
        // 搜索者超时时间：10分钟
        long searcherTimeout = 10 * 60 * 1000L; // 10分钟 = 10 * 60 * 1000 毫秒

        new BukkitRunnable() {
            @Override
            public void run() {
                long currentTime = System.currentTimeMillis();

                // 遍历所有摸金箱数据
                for (TreasureChestData data : treasureChestData.values()) {
                    if (data.isBeingSearched()) {
                        // 检查搜索者是否还在线
                        Player searcher = plugin.getServer().getPlayer(data.getCurrentSearcher());

                        // 如果搜索者离线或者搜索时间超时
                        if (searcher == null || !searcher.isOnline() ||
                            (currentTime - data.getSearchStartTime()) > searcherTimeout) {

                            // 清除搜索者信息
                            data.clearSearcher();
                            plugin.getLogger().info("清理了超时的摸金箱搜索者: " +
                                (searcher != null ? searcher.getName() : "离线玩家"));
                        }
                    }
                }
            }
        }.runTaskTimer(plugin, cleanupInterval, cleanupInterval);
    }

    /**
     * 检查并启动浮空字更新任务
     */
    private void checkAndStartHologramUpdateTask() {
        // 检查浮空字是否启用
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            plugin.getLogger().info("浮空字已禁用，跳过浮空字更新任务");
            // 修复：确保停止现有的浮空字更新任务
            stopHologramUpdateTask();
            return;
        }

        // 启动浮空字更新任务
        startHologramUpdateTask();
    }

    /**
     * 停止浮空字更新任务
     */
    private void stopHologramUpdateTask() {
        if (hologramUpdateTask != null) {
            try {
                hologramUpdateTask.cancel();
                hologramUpdateTask = null;
                plugin.getLogger().info("浮空字更新任务已停止");
            } catch (Exception e) {
                plugin.getLogger().warning("停止浮空字更新任务时出错: " + e.getMessage());
            }
        }
    }

    /**
     * 新增：重新检查全息图任务状态（用于配置重载）
     */
    public void recheckHologramTaskStatus() {
        if (plugin.getTreasureItemManager().isHologramEnabled()) {
            // 全息图启用，确保任务运行
            if (hologramUpdateTask == null) {
                checkAndStartHologramUpdateTask();
            }
        } else {
            // 全息图禁用，停止任务
            stopHologramUpdateTask();
            // 清理所有现有的全息图
            plugin.getHologramManager().removeAllHolograms();
        }
    }

    /**
     * 启动浮空字更新任务
     * 定期更新摸金箱浮空字的倒计时显示
     * 修复：添加世界玩家检查，避免跨世界浮空字卡住
     * 1.16.5修复：增强版本兼容性和实体检查
     */
    private void startHologramUpdateTask() {
        // 优化：从配置文件读取更新频率
        long updateInterval = plugin.getConfig().getLong("treasure-chest.hologram.update_interval", 20L);

        // 1.16.5修复：对于1.16.x版本，如果配置是默认值，则使用更频繁的更新
        // if (VersionUtils.isVersionAtLeast(1, 16) && !VersionUtils.isVersionAtLeast(1, 17)) {
        //     if (updateInterval == 20L) {
        //         // 只有当配置是默认值时才调整
        //         updateInterval = 10L; // 0.5秒 = 10 ticks
        //         plugin.getLogger().info("检测到1.16.x版本，使用增强的浮空字更新频率 (0.5秒)");
        //     }
        // }

        // 只在调试模式下显示浮空字更新频率信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("浮空字更新频率: " + updateInterval + " ticks (" + (updateInterval / 20.0) + "秒)");
        }

        // 优化：停止现有任务（如果有）
        stopHologramUpdateTask();

        // 优化：创建并保存新任务
        hologramUpdateTask = new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    // 优化：检查插件是否正在关闭
                    if (isShuttingDown) {
                        // 插件正在关闭，立即停止任务
                        return;
                    }

                    // 优化：再次检查浮空字是否仍然启用
                    if (!plugin.getTreasureItemManager().isHologramEnabled()) {
                        // 浮空字被禁用，停止任务
                        stopHologramUpdateTask();
                        return;
                    }

                    // 修复：如果没有在线玩家，跳过浮空字更新以节省性能
                    if (plugin.getServer().getOnlinePlayers().isEmpty()) {
                        return;
                    }

                    // 遍历所有摸金箱数据，更新需要倒计时的浮空字
                    for (Map.Entry<String, TreasureChestData> entry : treasureChestData.entrySet()) {
                        TreasureChestData data = entry.getValue();

                        // 从key解析位置
                        org.bukkit.Location location = parseLocationFromString(entry.getKey());
                        if (location == null) {
                            continue;
                        }

                        // 修复：检查世界是否存在
                        org.bukkit.World world = location.getWorld();
                        if (world == null) {
                            continue;
                        }

                        // 优化：再次检查插件是否正在关闭
                        if (isShuttingDown) {
                            return;
                        }

                        // 修复：安全检查区块是否加载
                        try {
                            if (!location.getChunk().isLoaded()) {
                                // 区块未加载，跳过更新
                                continue;
                            }
                        } catch (Exception e) {
                            // 优化：关服时可能出现异常，直接跳过
                            if (isShuttingDown) {
                                return;
                            }
                            continue;
                        }

                        // 修复：检查世界中是否有玩家
                        // 如果世界中没有玩家，跳过浮空字更新以避免卡住
                        boolean hasPlayersInWorld = false;
                        for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
                            if (onlinePlayer.getWorld().equals(world)) {
                                hasPlayersInWorld = true;
                                break;
                            }
                        }

                        // 如果世界中没有玩家，移除浮空字以避免卡住
                        if (!hasPlayersInWorld) {
                            plugin.getHologramManager().removeHologram(location);
                            continue;
                        }

                        // 检查玩家距离，实现智能浮空字管理
                        double autoRemoveDistance = plugin.getConfig().getDouble("treasure-chest.hologram.auto_remove_distance", 32.0);
                        double detectionRange = plugin.getConfig().getDouble("treasure-chest.hologram.chunk_protection.player_detection_range", 64.0);

                        // 检查是否有玩家在附近
                        boolean hasPlayersNearby = false;
                        boolean hasPlayersInRemoveRange = false;

                        for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
                            if (onlinePlayer.getWorld().equals(world)) {
                                double distance = onlinePlayer.getLocation().distance(location);
                                if (distance <= detectionRange) {
                                    hasPlayersNearby = true;
                                }
                                if (distance <= autoRemoveDistance) {
                                    hasPlayersInRemoveRange = true;
                                }
                                // 如果两个条件都满足，可以提前退出循环
                                if (hasPlayersNearby && hasPlayersInRemoveRange) {
                                    break;
                                }
                            }
                        }

                        // 新增：如果浮空字存在但没有玩家在移除距离内，删除浮空字
                        if (plugin.getHologramManager().hasHologram(location)) {
                            if (!hasPlayersInRemoveRange) {
                                plugin.getHologramManager().removeHologram(location);
                                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                    plugin.getLogger().info("玩家远离摸金箱超过" + autoRemoveDistance + "格，已移除浮空字: " + locationToString(location));
                                }
                                continue;
                            }
                        } else {
                            // 检查浮空字是否存在，如果不存在且有玩家在附近则重建
                            // 如果有玩家在附近，重建浮空字
                            if (hasPlayersNearby) {
                                String newText = generateHologramText(data);
                                if (newText != null) {
                                    plugin.getHologramManager().createOrUpdateHologram(location, newText);

                                    // 调试模式下显示重建日志
                                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                        plugin.getLogger().info("在更新任务中重建了丢失的浮空字: " + locationToString(location));
                                    }
                                }
                            }
                            continue;
                        }

                        // 修复：检查摸金箱数据是否有效
                        if (data.getOriginalItemCount() <= 0 && data.getItems().isEmpty()) {
                            // 数据异常的摸金箱，移除浮空字
                            plugin.getHologramManager().removeHologram(location);
                            continue;
                        }

                        // 修复：检查浮空字是否启用
                        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
                            // 如果浮空字被禁用，移除现有的浮空字
                            plugin.getHologramManager().removeHologram(location);
                            continue;
                        }

                        // 生成新的浮空字文本
                        String newText = generateHologramText(data);

                        if (newText != null) {
                            // 更新浮空字
                            plugin.getHologramManager().createOrUpdateHologram(location, newText);
                        } else {
                            // 如果返回null，移除浮空字
                            plugin.getHologramManager().removeHologram(location);
                        }
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("浮空字更新任务出错: " + e.getMessage());
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        e.printStackTrace();
                    }
                }
            }
        }.runTaskTimer(plugin, updateInterval, updateInterval);
    }

    /**
     * 从字符串解析位置
     * 优化：使用缓存避免重复解析，大幅提升性能
     */
    public org.bukkit.Location parseLocationFromString(String locationString) {
        // 优化：首先检查缓存
        org.bukkit.Location cachedLocation = locationCache.get(locationString);
        if (cachedLocation != null) {
            // 验证缓存的位置是否仍然有效
            if (cachedLocation.getWorld() != null) {
                return cachedLocation;
            } else {
                // 世界不存在，从缓存中移除
                locationCache.remove(locationString);
            }
        }

        // 缓存未命中，解析位置
        org.bukkit.Location location = parseLocationFromStringInternal(locationString);

        // 优化：将解析结果加入缓存
        if (location != null) {
            locationCache.put(locationString, location);
        }

        return location;
    }

    /**
     * 内部位置解析方法
     * 修复：统一使用下划线格式，与ChestManager保持一致
     */
    private org.bukkit.Location parseLocationFromStringInternal(String locationString) {
        try {
            // 首先尝试新格式（下划线分隔）
            String[] parts = locationString.split("_");
            if (parts.length == 4) {
                String worldName = parts[0];
                int x = Integer.parseInt(parts[1]);
                int y = Integer.parseInt(parts[2]);
                int z = Integer.parseInt(parts[3]);

                org.bukkit.World world = plugin.getServer().getWorld(worldName);
                if (world != null) {
                    return new org.bukkit.Location(world, x, y, z);
                }
            }

            // 兼容旧格式（冒号分隔）
            parts = locationString.split(":");
            if (parts.length == 4) {
                String worldName = parts[0];
                int x = Integer.parseInt(parts[1]);
                int y = Integer.parseInt(parts[2]);
                int z = Integer.parseInt(parts[3]);

                org.bukkit.World world = plugin.getServer().getWorld(worldName);
                if (world != null) {
                    return new org.bukkit.Location(world, x, y, z);
                }
            }
        } catch (NumberFormatException e) {
            // 解析失败，忽略
        }
        return null;
    }

    /**
     * 清理位置缓存
     */
    public void clearLocationCache() {
        locationCache.clear();
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("🧹 位置缓存已清理");
        }
    }

    /**
     * 获取位置缓存统计信息
     */
    public String getLocationCacheStats() {
        return "位置缓存: " + locationCache.size() + " 个位置";
    }

    /**
     * 插件关闭时的清理方法
     */
    public void shutdown() {
        // 设置关闭标志，停止所有异步任务
        isShuttingDown = true;

        // 停止浮空字更新任务
        stopHologramUpdateTask();

        // 清理位置缓存
        clearLocationCache();

        plugin.getLogger().info("PlayerListener 已安全关闭");
    }

    /**
     * 注册摸金箱GUI
     *
     * @param player 玩家
     * @param gui GUI实例
     */
    public void registerTreasureGUI(Player player, TreasureChestGUI gui) {
        openGUIs.put(player.getUniqueId(), gui);
    }

    /**
     * 注销摸金箱GUI
     *
     * @param player 玩家
     */
    public void unregisterTreasureGUI(Player player) {
        openGUIs.remove(player.getUniqueId());
    }

    /**
     * 注册管理GUI
     *
     * @param player 玩家
     * @param gui GUI实例
     */
    public void registerManagementGUI(Player player, TreasureManagementGUI gui) {
        openManagementGUIs.put(player.getUniqueId(), gui);
    }



    /**
     * 注册编辑GUI
     *
     * @param player 玩家
     * @param gui GUI实例
     */
    public void registerEditGUI(Player player, TreasureEditGUI gui) {
        openEditGUIs.put(player.getUniqueId(), gui);
    }

    /**
     * 注册摸金箱种类选择GUI
     *
     * @param player 玩家
     * @param gui GUI实例
     */
    public void registerChestTypeSelectionGUI(Player player, com.hang.plugin.gui.ChestTypeSelectionGUI gui) {
        openChestTypeSelectionGUIs.put(player.getUniqueId(), gui);
    }

    /**
     * 注册命令编辑GUI
     */
    public void registerCommandEditGUI(Player player, CommandEditGUI gui) {
        openCommandEditGUIs.put(player.getUniqueId(), gui);
    }



    /**
     * 设置命令输入监听器
     */
    public void setCommandInputListener(Player player, CommandEditGUI.CommandInputListener listener) {
        commandInputListeners.put(player.getUniqueId(), listener);
    }

    /**
     * 处理聊天事件 - 用于命令输入
     */
    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        UUID playerId = player.getUniqueId();

        // 检查是否有命令输入监听器
        CommandEditGUI.CommandInputListener listener = commandInputListeners.get(playerId);
        if (listener != null) {
            // 取消聊天事件
            event.setCancelled(true);

            // 移除监听器
            commandInputListeners.remove(playerId);

            // 在主线程中处理输入
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                listener.onCommandInput(event.getMessage());
            });
        }
    }



    /**
     * 检查并刷新摸金箱
     */
    private void checkAndRefreshChest(org.bukkit.Location chestLocation) {
        TreasureChestData data = getTreasureChestData(chestLocation);

        if (data != null) {
            long nextRefresh = data.getNextRefreshTime();

            // 修复：检查是否到了刷新时间（无论是否完全搜索）
            if (nextRefresh > 0 && System.currentTimeMillis() >= nextRefresh) {
                // 调试日志
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("摸金箱刷新: " + locationToString(chestLocation) +
                        " (倒计时已结束，重置摸金箱状态)");
                }

                // 刷新摸金箱
                data.reset();
                saveTreasureChestData(chestLocation, data);

                // 移除浮空字，让新的GUI重新创建
                plugin.getHologramManager().removeHologram(chestLocation);
            }
        }
    }

    /**
     * 同步摸金箱状态 (检查物品是否被拿走)
     */
    private void syncTreasureChestState(TreasureChestGUI gui) {
        org.bukkit.Location chestLocation = gui.getChestLocation();
        TreasureChestData data = getTreasureChestData(chestLocation);

        if (data == null) {
            return;
        }

        // 检查GUI中的物品状态
        org.bukkit.inventory.Inventory inventory = gui.getInventory();
        boolean hasChanges = false;

        // 遍历所有已搜索的槽位
        for (Integer slot : new java.util.HashSet<>(data.getSearchedSlots())) {
            org.bukkit.inventory.ItemStack currentItem = inventory.getItem(slot);
            org.bukkit.inventory.ItemStack originalItem = data.getItems().get(slot);

            // 如果物品被拿走了（槽位为空或者是空气）
            if (currentItem == null || currentItem.getType() == org.bukkit.Material.AIR) {
                if (originalItem != null && originalItem.getType() != org.bukkit.Material.AIR) {
                    // 播放物品拾取音效
                    playItemPickupSound(gui.getPlayer());

                    // 物品被拿走，从数据中移除
                    data.getItems().remove(slot);
                    data.getItemData().remove(slot);
                    hasChanges = true;
                }
            }
        }

        if (hasChanges) {
            // 保存更新后的数据
            saveTreasureChestData(chestLocation, data);

            // 修复：不在这里立即更新浮空字，让onInventoryClose中的延迟更新处理
            // 这样可以避免竞态条件和重复更新
        }
    }

    /**
     * 新增：更新指定摸金箱的浮空字
     */
    private void updateChestHologram(org.bukkit.Location chestLocation, TreasureChestData data) {
        // 检查浮空字是否启用
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            plugin.getHologramManager().removeHologram(chestLocation);
            return;
        }

        // 生成浮空字文本
        String newText = generateHologramText(data);

        if (newText != null) {
            // 更新浮空字
            plugin.getHologramManager().createOrUpdateHologram(chestLocation, newText);
        } else {
            // 如果返回null，移除浮空字
            plugin.getHologramManager().removeHologram(chestLocation);
        }
    }

    /**
     * 生成浮空字文本
     * 新增：支持自定义文本配置
     */
    private String generateHologramText(TreasureChestData data) {
        // 检查是否有玩家正在搜索
        if (data.isBeingSearched()) {
            Player currentSearcher = plugin.getServer().getPlayer(data.getCurrentSearcher());
            if (currentSearcher != null && currentSearcher.isOnline()) {
                // 搜索中状态 - 使用自定义文本
                String template = plugin.getConfig().getString("treasure-chest.hologram.messages.searching",
                    "§b{player} §7正在搜索中...");

                return template.replace("{player}", currentSearcher.getName());
            } else {
                // 搜索者已离线，清除搜索状态
                data.clearSearcher();
            }
        }

        // 修复：优先检查倒计时状态，无论是否完全搜索
        long nextRefresh = data.getNextRefreshTime();
        if (nextRefresh > 0) {
            if (System.currentTimeMillis() >= nextRefresh) {
                // 修复：倒计时已结束，应该触发刷新而不是显示"可以刷新"
                // 这种情况下应该立即刷新摸金箱，然后返回null让浮空字消失
                data.reset();

                // 调试日志
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("浮空字检测到倒计时结束，自动刷新摸金箱并移除浮空字");
                }

                return null; // 返回null让浮空字消失，等待玩家重新打开
            } else {
                // 显示刷新倒计时 - 使用自定义文本
                long remaining = (nextRefresh - System.currentTimeMillis()) / 1000;
                long minutes = remaining / 60;
                long seconds = remaining % 60;

                String template = plugin.getConfig().getString("treasure-chest.hologram.messages.refresh_countdown",
                    "§e刷新倒计时: §c{minutes}:{seconds}");

                return template
                    .replace("{minutes}", String.valueOf(minutes))
                    .replace("{seconds}", String.format("%02d", seconds));
            }
        }

        // 如果没有倒计时，再检查搜索状态
        if (data.isFullySearched()) {
            // 已搜索完毕 - 使用自定义文本
            return plugin.getConfig().getString("treasure-chest.hologram.messages.fully_searched",
                "§a已搜索完毕");
        } else {
            // 修复：检查摸金箱是否有效，避免显示"0个物品未搜索"
            int unsearched = data.getUnsearchedCount();
            int originalCount = data.getOriginalItemCount();

            // 如果原始物品数量为0或负数，说明摸金箱数据异常，不显示浮空字
            if (originalCount <= 0) {
                return null; // 返回null表示不显示浮空字
            }

            // 如果未搜索数量为0但不是完全搜索状态，说明数据异常
            if (unsearched <= 0) {
                return null; // 返回null表示不显示浮空字
            }

            // 未搜索状态 - 使用自定义文本
            String template = plugin.getConfig().getString("treasure-chest.hologram.messages.unsearched",
                "§6还有 §e{count} §6个物品未搜索");

            return template
                .replace("{count}", String.valueOf(unsearched))
                .replace("{total}", String.valueOf(originalCount));
        }
    }

    /**
     * 获取摸金箱数据
     */
    public TreasureChestData getTreasureChestData(org.bukkit.Location location) {
        String key = locationToString(location);
        return treasureChestData.get(key);
    }

    /**
     * 获取内存中摸金箱数据的数量
     */
    public int getTreasureChestDataSize() {
        return treasureChestData.size();
    }

    /**
     * 获取摸金箱数据映射（用于自动刷新检查）
     */
    public java.util.Map<String, TreasureChestData> getTreasureChestDataMap() {
        return new java.util.HashMap<>(treasureChestData);
    }

    /**
     * 获取所有摸金箱数据（用于刷新指令）
     */
    public Map<String, TreasureChestData> getAllTreasureChestData() {
        return new HashMap<>(treasureChestData); // 返回副本以避免并发修改
    }

    /**
     * 刷新指定摸金箱（用于刷新指令）
     */
    public void refreshTreasureChest(org.bukkit.Location location, TreasureChestData data) {
        try {
            // 正确逻辑：刷新时不立即生成物品，让摸金箱保持空状态
            // 重置摸金箱数据
            data.reset();

            // 正确逻辑：不立即生成物品，等玩家打开时再生成
            // generateTreasureItemsForChest(location, data);

            // 保存数据
            saveTreasureChestData(location, data);

            // 正确逻辑：移除浮空字（刷新后不显示浮空字是正确的）
            if (plugin.getTreasureItemManager().isHologramEnabled()) {
                plugin.getHologramManager().removeHologram(location);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("刷新摸金箱时出错: " + locationToString(location) + " - " + e.getMessage());
        }
    }



    /**
     * 创建空的摸金箱数据（用于箱子替换功能）
     */
    public void createEmptyTreasureChest(org.bukkit.Location location, String chestType) {
        // 创建新的摸金箱数据
        TreasureChestData data = new TreasureChestData(chestType);

        // 保存数据
        saveTreasureChestData(location, data);

        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("已创建空摸金箱: " + chestType + " 位置: " + locationToString(location));
        }
    }

    /**
     * 保存摸金箱数据
     * 优化：优先保存到内存缓存，提升性能
     */
    public void saveTreasureChestData(org.bukkit.Location location, TreasureChestData data) {
        String key = locationToString(location);
        treasureChestData.put(key, data);

        // 优化：检查是否启用内存缓存
        boolean memoryCacheEnabled = plugin.getConfig().getBoolean("performance.memory_cache.enabled", true);

        if (memoryCacheEnabled) {
            // 优先保存到内存缓存
            plugin.getMemoryCacheManager().saveToCache(key, data);

            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("已保存到内存缓存: " + key +
                    " (类型: " + data.getChestType() +
                    ", 物品数: " + data.getItems().size() +
                    ", 已搜索: " + data.getSearchedSlots().size() + ")");
            }
        } else {
            // 传统方式：异步保存到文件
            try {
                plugin.getChestManager().saveChestDataAsync(location, data);

                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("已添加摸金箱到保存队列: " + key +
                        " (类型: " + data.getChestType() +
                        ", 物品数: " + data.getItems().size() +
                        ", 已搜索: " + data.getSearchedSlots().size() + ")");
                }
            } catch (Exception e) {
                plugin.getLogger().severe("错误：添加摸金箱到保存队列失败 (" + key + "): " + e.getMessage());
                e.printStackTrace();
            }
        }
    }

    /**
     * 从内存中移除摸金箱数据
     * 用于完全清空摸金箱
     */
    public void removeTreasureChestData(String locationKey) {
        treasureChestData.remove(locationKey);

        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("已从内存中移除摸金箱数据: " + locationKey);
        }
    }

    /**
     * 保存所有摸金箱数据到文件
     * 在插件关闭时调用
     */
    public void saveAllChestDataToFile() {
        try {
            long startTime = System.currentTimeMillis();
            int savedCount = 0;
            int totalCount = treasureChestData.size();

            // 优化：只在启用日志时显示保存进度
            if (plugin.getConfig().getBoolean("performance.auto_save.show_logs", true)) {
                plugin.getLogger().info("正在保存所有数据... 当前内存中有 " + totalCount + " 个摸金箱");
            }

            // 优化：从配置读取保存设置
            boolean showProgress = plugin.getConfig().getBoolean("performance.shutdown_save.show_progress", true);
            int progressInterval = plugin.getConfig().getInt("performance.shutdown_save.progress_interval", 100);
            boolean batchMode = plugin.getConfig().getBoolean("performance.shutdown_save.batch_mode", true);

            // 优化：批量保存到内存配置，最后一次性写入文件
            for (Map.Entry<String, TreasureChestData> entry : treasureChestData.entrySet()) {
                try {
                    // 从字符串键解析位置
                    org.bukkit.Location location = parseLocationFromKey(entry.getKey());
                    if (location != null) {
                        // 优化：根据配置选择保存方式
                        if (batchMode) {
                            // 批量模式：只保存到内存配置，不立即写文件
                            plugin.getChestManager().saveChestDataBatch(location, entry.getValue());
                        } else {
                            // 传统模式：每个摸金箱都立即写文件（较慢）
                            plugin.getChestManager().saveChestData(location, entry.getValue());
                        }
                        savedCount++;

                        // 优化：根据配置显示进度，同时检查show_logs
                        if (showProgress && savedCount % progressInterval == 0 && plugin.getConfig().getBoolean("performance.auto_save.show_logs", true)) {
                            plugin.getLogger().info("保存进度: " + savedCount + "/" + totalCount + " (" +
                                String.format("%.1f", (savedCount * 100.0 / totalCount)) + "%)");
                        }

                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().info("已保存摸金箱: " + entry.getKey() + " (类型: " + entry.getValue().getChestType() + ")");
                        }
                    } else {
                        plugin.getLogger().warning("无法解析摸金箱位置: " + entry.getKey());
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("保存摸金箱数据时出错 (" + entry.getKey() + "): " + e.getMessage());
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        e.printStackTrace();
                    }
                }
            }

            // 优化：只在批量模式下才需要最后写入文件
            if (batchMode) {
                try {
                    if (plugin.getConfig().getBoolean("performance.auto_save.show_logs", true)) {
                        plugin.getLogger().info("正在写入配置文件...");
                    }
                    plugin.getChestManager().saveConfigFile();

                    long duration = System.currentTimeMillis() - startTime;
                    if (plugin.getConfig().getBoolean("performance.auto_save.show_logs", true)) {
                        plugin.getLogger().info("数据保存完成! 保存了 " + savedCount + "/" + totalCount + " 个摸金箱 (耗时: " + duration + "ms)");
                    }

                } catch (Exception e) {
                    plugin.getLogger().severe("写入配置文件失败: " + e.getMessage());
                    e.printStackTrace();
                }
            } else {
                // 传统模式下，文件已经在循环中写入了
                long duration = System.currentTimeMillis() - startTime;
                if (plugin.getConfig().getBoolean("performance.auto_save.show_logs", true)) {
                    plugin.getLogger().info("数据保存完成! 保存了 " + savedCount + "/" + totalCount + " 个摸金箱 (耗时: " + duration + "ms)");
                }
            }

        } catch (Exception e) {
            plugin.getLogger().severe("保存所有摸金箱数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 从字符串键解析位置
     * 修复：使用与ChestManager一致的格式
     */
    private org.bukkit.Location parseLocationFromKey(String key) {
        try {
            String[] parts = key.split("_");
            if (parts.length != 4) {
                return null;
            }

            String worldName = parts[0];
            int x = Integer.parseInt(parts[1]);
            int y = Integer.parseInt(parts[2]);
            int z = Integer.parseInt(parts[3]);

            org.bukkit.World world = plugin.getServer().getWorld(worldName);
            if (world == null) {
                return null;
            }

            return new org.bukkit.Location(world, x, y, z);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 处理独立的摸金箱种类选择（当没有关联的管理GUI时）
     */
    private void handleStandaloneChestTypeSelection(InventoryClickEvent event) {
        Player player = (Player) event.getWhoClicked();

        if (event.getClickedInventory() == null || event.getCurrentItem() == null) {
            return;
        }

        ItemStack clicked = event.getCurrentItem();
        if (!clicked.hasItemMeta() || !clicked.getItemMeta().hasDisplayName()) {
            return;
        }

        String displayName = clicked.getItemMeta().getDisplayName();

        // 处理取消
        if (displayName.equals("§c取消")) {
            player.removeMetadata("pending_item", plugin);
            player.closeInventory();
            return;
        }

        // 获取待添加的物品
        if (!player.hasMetadata("pending_item")) {
            player.sendMessage("§c错误：未找到待添加的物品");
            return;
        }

        ItemStack itemToAdd = (ItemStack) player.getMetadata("pending_item").get(0).value();
        player.removeMetadata("pending_item", plugin);

        // 确定选择的种类
        String selectedType = null;
        int slot = event.getSlot();

        // 调试信息
        player.sendMessage("§7[调试] 独立处理 - 点击槽位: " + slot);

        // 从配置获取摸金箱种类
        java.util.Collection<com.hang.plugin.manager.ChestTypeManager.ChestType> availableTypes =
            plugin.getChestTypeManager().getAllChestTypes();

        if (availableTypes.isEmpty()) {
            // 使用默认种类
            String[] chestTypes = {"common", "weapon", "ammo", "medical", "supply", "equipment"};
            if (slot >= 10 && slot <= 15) {
                selectedType = chestTypes[slot - 10];
                player.sendMessage("§7[调试] 独立处理 - 使用默认种类: " + selectedType);
            }
        } else {
            // 从配置中的种类获取 - 修复槽位计算
            if (slot >= 10 && slot < 25) {
                // 计算在种类列表中的索引
                int index = slot - 10;
                java.util.List<com.hang.plugin.manager.ChestTypeManager.ChestType> typesList =
                    new java.util.ArrayList<>(availableTypes);

                if (index >= 0 && index < typesList.size()) {
                    selectedType = typesList.get(index).getTypeId();
                    player.sendMessage("§7[调试] 独立处理 - 使用配置种类: " + selectedType + " (索引: " + index + ")");
                } else {
                    player.sendMessage("§7[调试] 独立处理 - 索引超出范围: " + index + ", 总数: " + typesList.size());
                }
            }
        }

        if (selectedType != null) {
            // 创建新的战利品物品，指定摸金箱种类
            String newId = "custom_" + System.currentTimeMillis();
            com.hang.plugin.manager.TreasureItemManager.TreasureItem newItem = plugin.getTreasureItemManager()
                .createFromItemStackWithChestType(newId, itemToAdd, selectedType);
            plugin.getTreasureItemManager().addItem(newItem);

            player.sendMessage("§a已添加新物品到 " + displayName + ": §f" + newId);
            player.closeInventory();
        } else {
            player.sendMessage("§c无效的选择");
        }
    }

    /**
     * 检查材料是否为玻璃板（兼容不同版本）
     */
    private boolean isGlassPane(org.bukkit.Material material) {
        String materialName = material.name();
        return materialName.contains("GLASS_PANE");
    }

    /**
     * 将位置转换为字符串键
     * 修复：使用与ChestManager一致的格式
     */
    private String locationToString(org.bukkit.Location location) {
        return location.getWorld().getName() + "_" +
               location.getBlockX() + "_" +
               location.getBlockY() + "_" +
               location.getBlockZ();
    }

    /**
     * 从位置获取摸金箱种类
     */
    private String getChestTypeFromLocation(org.bukkit.Location location) {
        TreasureChestData data = getTreasureChestData(location);
        if (data != null) {
            return data.getChestType();
        } else {
            return "common"; // 默认为普通摸金箱
        }
    }

    /**
     * 新增：检查玩家是否可以放置摸金箱
     */
    private boolean canPlaceTreasureChest(Player player) {
        // 检查是否启用OP限制
        boolean opOnly = plugin.getConfig().getBoolean("treasure-chest.placement-restrictions.op-only", true);

        // 检查是否有自定义权限节点
        String permission = plugin.getConfig().getString("treasure-chest.placement-restrictions.permission", "");

        if (!permission.isEmpty()) {
            // 使用自定义权限节点
            return player.hasPermission(permission);
        } else if (opOnly) {
            // 使用OP检查
            return player.isOp();
        } else {
            // 不限制，所有玩家都可以放置
            return true;
        }
    }

    /**
     * 新增：发送摸金箱放置权限被拒绝的消息
     */
    private void sendPlacementDeniedMessage(Player player) {
        String permission = plugin.getConfig().getString("treasure-chest.placement-restrictions.permission", "");

        if (!permission.isEmpty()) {
            // 使用权限节点的错误消息
            String message = plugin.getConfig().getString("messages.treasure-chest-place-denied-permission",
                "§c您没有权限放置摸金箱！需要权限: {permission}");
            message = message.replace("{permission}", permission);
            player.sendMessage(message);
        } else {
            // 使用OP的错误消息
            String message = plugin.getConfig().getString("messages.treasure-chest-place-denied",
                "§c只有管理员才能放置摸金箱！");
            player.sendMessage(message);

            String helpMessage = plugin.getConfig().getString("messages.treasure-chest-place-help",
                "§7如果您是管理员，请使用 /op {player} 获取权限");
            helpMessage = helpMessage.replace("{player}", player.getName());
            player.sendMessage(helpMessage);
        }
    }

    /**
     * 摸金箱数据类
     */
    public static class TreasureChestData {
        private final Map<Integer, org.bukkit.inventory.ItemStack> items;
        private final Map<Integer, Object> itemData;
        private final java.util.Set<Integer> searchedSlots;
        private long lastRefreshTime;
        private long nextRefreshTime;
        private UUID currentSearcher; // 当前正在搜索的玩家
        private long searchStartTime; // 搜索开始时间
        private String chestType; // 摸金箱种类
        private long openTime; // 摸金箱首次开启时间（用于自动刷新检查）

        public TreasureChestData() {
            this.items = new HashMap<>();
            this.itemData = new HashMap<>();
            this.searchedSlots = new java.util.HashSet<>();
            this.lastRefreshTime = System.currentTimeMillis();
            this.nextRefreshTime = 0; // 初始不设置刷新时间
            this.chestType = "common"; // 默认为普通摸金箱
            this.openTime = 0; // 初始未开启
        }

        public TreasureChestData(String chestType) {
            this.items = new HashMap<>();
            this.itemData = new HashMap<>();
            this.searchedSlots = new java.util.HashSet<>();
            this.lastRefreshTime = System.currentTimeMillis();
            this.nextRefreshTime = 0; // 初始不设置刷新时间
            this.chestType = chestType != null ? chestType : "common";
            this.openTime = 0; // 初始未开启
        }

        public Map<Integer, org.bukkit.inventory.ItemStack> getItems() {
            return items;
        }

        public Map<Integer, Object> getItemData() {
            return itemData;
        }

        public java.util.Set<Integer> getSearchedSlots() {
            return searchedSlots;
        }

        public void setItem(int slot, org.bukkit.inventory.ItemStack item,
                           com.hang.plugin.manager.TreasureItemManager.TreasureItem itemInfo) {
            items.put(slot, item);
            if (itemInfo != null) {
                itemData.put(slot, (Object) itemInfo);
            }
        }

        /**
         * 设置物品（支持模组物品）
         */
        public void setItem(int slot, org.bukkit.inventory.ItemStack item, Object itemInfo) {
            items.put(slot, item);
            if (itemInfo != null) {
                itemData.put(slot, itemInfo);
            }
        }

        public void markSlotSearched(int slot) {
            searchedSlots.add(slot);
        }

        public boolean isSlotSearched(int slot) {
            return searchedSlots.contains(slot);
        }

        public long getLastRefreshTime() {
            return lastRefreshTime;
        }

        public void setLastRefreshTime(long time) {
            this.lastRefreshTime = time;
        }

        public long getNextRefreshTime() {
            return nextRefreshTime;
        }

        public void setNextRefreshTime(long time) {
            this.nextRefreshTime = time;
        }

        public boolean isFullySearched() {
            // 如果是新创建的摸金箱（没有物品），则不算搜索完毕
            if (items.isEmpty() && searchedSlots.isEmpty()) {
                return false;
            }

            // 如果原始物品数量为0或未设置，则不算搜索完毕
            int originalCount = getOriginalItemCount();
            if (originalCount <= 0) {
                return false;
            }

            // 检查是否所有原始槽位都已被搜索
            // 注意：items可能会因为玩家拿走物品而减少，所以我们需要记录原始的物品数量
            return searchedSlots.size() >= originalCount;
        }

        private int originalItemCount = -1;

        public int getOriginalItemCount() {
            if (originalItemCount == -1) {
                // 只有当items不为空时才设置originalItemCount
                // 这样可以避免在摸金箱还没有生成物品时就被标记为搜索完毕
                if (!items.isEmpty()) {
                    originalItemCount = items.size();
                } else {
                    return 0; // 返回0但不设置originalItemCount，保持-1状态
                }
            }
            return originalItemCount;
        }

        public void setOriginalItemCount(int count) {
            this.originalItemCount = count;
        }

        public int getUnsearchedCount() {
            // 使用原始物品数量而不是当前物品数量，避免玩家拿走物品后出现负数
            int originalCount = getOriginalItemCount();
            if (originalCount <= 0) {
                return 0; // 如果没有原始物品，返回0
            }

            int unsearched = originalCount - searchedSlots.size();
            return Math.max(0, unsearched); // 确保不返回负数
        }

        public UUID getCurrentSearcher() {
            return currentSearcher;
        }

        public void setCurrentSearcher(UUID searcherId) {
            this.currentSearcher = searcherId;
            this.searchStartTime = System.currentTimeMillis();
        }

        public long getSearchStartTime() {
            return searchStartTime;
        }

        public boolean isBeingSearched() {
            return currentSearcher != null;
        }

        public boolean isSearchedBy(UUID playerId) {
            return currentSearcher != null && currentSearcher.equals(playerId);
        }

        public void clearSearcher() {
            this.currentSearcher = null;
            this.searchStartTime = 0;
        }

        public String getChestType() {
            return chestType;
        }

        public void setChestType(String chestType) {
            this.chestType = chestType != null ? chestType : "common";
        }

        public long getOpenTime() {
            return openTime;
        }

        public void setOpenTime(long openTime) {
            this.openTime = openTime;
        }

        public void reset() {
            items.clear();
            itemData.clear();
            searchedSlots.clear();
            lastRefreshTime = System.currentTimeMillis();
            nextRefreshTime = 0;
            // 修复：重置originalItemCount，让新生成的物品重新计算原始数量
            // 这样可以确保刷新后的摸金箱能正确设置刷新时间
            originalItemCount = -1;
            clearSearcher(); // 重置时清除搜索者信息
            openTime = 0; // 重置开启时间，等待下次开启
            // 注意：不重置chestType，保持摸金箱的种类
        }
    }

    /**
     * 检查方块类型是否可能是容器
     */
    private boolean isPotentialContainer(Material material) {
        String materialName = material.name().toUpperCase();

        // 首先检查是否实现了 InventoryHolder 接口（更准确的方法）
        try {
            // 尝试检查是否为容器类型（兼容不同版本）
            // 在 1.14+ 版本中有 Container 接口，1.12.2 中没有
            Class.forName("org.bukkit.block.Container");
            // 如果能找到 Container 类，说明是新版本，可以使用更精确的检测
        } catch (ClassNotFoundException e) {
            // 1.12.2 等旧版本，使用字符串匹配
        }

        // 修复：首先检查配置文件中的自定义容器关键词
        if (plugin.getConfig().getBoolean("mod_compatibility.enable_mod_containers", true)) {
            java.util.List<String> customKeywords = plugin.getConfig().getStringList("mod_compatibility.custom_container_keywords");
            for (String keyword : customKeywords) {
                if (materialName.contains(keyword.toUpperCase())) {
                    return true;
                }
            }
        }

        // 检查常见的容器类型（基于关键词）
        return materialName.contains("CHEST") ||           // 各种箱子
               materialName.contains("SHULKER") ||         // 潜影盒
               materialName.contains("BARREL") ||          // 桶
               materialName.contains("HOPPER") ||          // 漏斗
               materialName.contains("DISPENSER") ||       // 发射器
               materialName.contains("DROPPER") ||         // 投掷器
               materialName.contains("FURNACE") ||         // 熔炉
               materialName.contains("BREWING_STAND") ||   // 酿造台
               materialName.contains("BEACON") ||          // 信标
               materialName.contains("ANVIL") ||           // 铁砧
               materialName.contains("ENCHANTING_TABLE") ||// 附魔台
               materialName.contains("ENDER_CHEST") ||     // 末影箱
               // 模组容器类型（更全面）
               materialName.contains("CRATE") ||           // 板条箱
               materialName.contains("BOX") ||             // 各种盒子
               materialName.contains("CONTAINER") ||       // 容器
               materialName.contains("STORAGE") ||         // 存储
               materialName.contains("TANK") ||            // 储罐
               materialName.contains("CABINET") ||         // 柜子
               materialName.contains("LOCKER") ||          // 储物柜
               materialName.contains("SAFE") ||            // 保险箱
               materialName.contains("VAULT") ||           // 金库
               materialName.contains("DRAWER") ||          // 抽屉
               materialName.contains("RACK") ||            // 架子
               materialName.contains("SHELF") ||           // 货架
               materialName.contains("WARDROBE") ||        // 衣柜
               materialName.contains("CUPBOARD") ||        // 橱柜
               materialName.contains("TOOLBOX") ||         // 工具箱
               materialName.contains("INVENTORY") ||       // 库存
               materialName.contains("DEPOT") ||           // 仓库
               materialName.contains("WAREHOUSE") ||       // 仓储
               materialName.contains("SILO") ||            // 筒仓
               materialName.contains("BIN") ||             // 垃圾箱/储物箱
               materialName.contains("CASE") ||            // 箱子/盒子
               materialName.contains("TRUNK");             // 行李箱
    }

    /**
     * 运行时检测方块是否为容器（更准确的方法）
     */
    private boolean isRuntimeContainer(org.bukkit.block.Block block) {
        try {
            // 方法1: 检查方块状态是否实现了 InventoryHolder
            org.bukkit.block.BlockState state = block.getState();
            if (state instanceof org.bukkit.inventory.InventoryHolder) {
                return true;
            }

            // 方法2: 尝试获取方块的库存（适用于模组容器）
            try {
                if (state.getClass().getMethod("getInventory") != null) {
                    return true;
                }
            } catch (NoSuchMethodException e) {
                // 没有 getInventory 方法，不是容器
            }

            // 方法3: 检查是否为 Container 类型（1.14+）
            try {
                Class<?> containerClass = Class.forName("org.bukkit.block.Container");
                if (containerClass.isInstance(state)) {
                    return true;
                }
            } catch (ClassNotFoundException e) {
                // 旧版本没有 Container 接口
            }

        } catch (Exception e) {
            // 检测失败，返回 false
        }

        return false;
    }

    /**
     * 检查物品是否为模组物品代表
     */
    private boolean isModItemRepresentative(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();

        // 方法1: 检查PersistentDataContainer (1.14+)
        try {
            Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
            Class<?> persistentDataTypeClass = Class.forName("org.bukkit.persistence.PersistentDataType");

            Object namespacedKey = namespacedKeyClass.getConstructor(org.bukkit.plugin.Plugin.class, String.class)
                .newInstance(plugin, "mod_item_id");
            Object stringType = persistentDataTypeClass.getField("STRING").get(null);

            Object container = meta.getClass().getMethod("getPersistentDataContainer").invoke(meta);
            Object value = container.getClass().getMethod("get", namespacedKeyClass, persistentDataTypeClass)
                .invoke(container, namespacedKey, stringType);

            return value != null;
        } catch (Exception e) {
            // 1.14以下版本，检查Lore
        }

        // 方法2: 检查Lore中的标识
        if (meta.hasLore()) {
            for (String line : meta.getLore()) {
                if (line.contains("[模组物品代表]") || line.contains("拿取时将转换为真实物品")) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 处理模组物品代表的拿取
     */
    private void handleModItemPickup(InventoryClickEvent event, ItemStack representativeItem) {
        Player player = (Player) event.getWhoClicked();

        // 获取模组物品ID
        String modItemId = getModItemIdFromRepresentative(representativeItem);
        if (modItemId == null) {
            player.sendMessage("§c无法识别模组物品ID");
            return;
        }

        // 尝试创建真实的模组物品
        ItemStack realModItem = createRealModItemForPlayer(modItemId, representativeItem.getAmount());

        if (realModItem != null) {
            // 成功创建真实模组物品
            event.setCurrentItem(realModItem);
            plugin.sendMessageIfNotEmpty(player, "mod-item-converted",
                "§a已转换为真实模组物品: {mod_item_id}",
                "mod_item_id", modItemId);
        } else {
            // 无法创建真实模组物品，使用命令给予
            event.setCancelled(true);
            event.setCurrentItem(new ItemStack(Material.AIR));

            // 通过命令给予
            giveModItemByCommand(player, modItemId, representativeItem.getAmount());
            plugin.sendMessageIfNotEmpty(player, "mod-item-given-by-command",
                "§e模组物品已通过命令给予到您的背包");
        }
    }

    /**
     * 从代表物品获取模组物品ID
     */
    private String getModItemIdFromRepresentative(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return null;
        }

        ItemMeta meta = item.getItemMeta();

        // 方法1: 从PersistentDataContainer获取 (1.14+)
        try {
            Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
            Class<?> persistentDataTypeClass = Class.forName("org.bukkit.persistence.PersistentDataType");

            Object namespacedKey = namespacedKeyClass.getConstructor(org.bukkit.plugin.Plugin.class, String.class)
                .newInstance(plugin, "mod_item_id");
            Object stringType = persistentDataTypeClass.getField("STRING").get(null);

            Object container = meta.getClass().getMethod("getPersistentDataContainer").invoke(meta);
            Object value = container.getClass().getMethod("get", namespacedKeyClass, persistentDataTypeClass)
                .invoke(container, namespacedKey, stringType);

            if (value != null) {
                return (String) value;
            }
        } catch (Exception e) {
            // 继续尝试其他方法
        }

        // 方法2: 从Lore中解析
        if (meta.hasLore()) {
            for (String line : meta.getLore()) {
                if (line.contains("物品ID: §7")) {
                    String[] parts = line.split("物品ID: §7");
                    if (parts.length > 1) {
                        String itemId = parts[1].trim();
                        // 还需要获取模组ID
                        for (String loreLine : meta.getLore()) {
                            if (loreLine.contains("模组: §7")) {
                                String[] modParts = loreLine.split("模组: §7");
                                if (modParts.length > 1) {
                                    String modId = modParts[1].trim();
                                    return modId + ":" + itemId;
                                }
                            }
                        }
                    }
                }
            }
        }

        return null;
    }

    /**
     * 为玩家创建真实的模组物品
     */
    private ItemStack createRealModItemForPlayer(String modItemId, int amount) {
        try {
            // 尝试通过NMS创建
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                ItemStack realItem = plugin.getNMSManager().createItemFromString(modItemId, amount);
                if (realItem != null) {
                    return realItem;
                }
            }

            // 尝试通过Bukkit API创建
            String[] parts = modItemId.split(":");
            if (parts.length == 2) {
                try {
                    Material material = Material.matchMaterial(modItemId);
                    if (material != null) {
                        return new ItemStack(material, amount);
                    }
                } catch (Exception e) {
                    // 继续尝试其他方法
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("创建真实模组物品失败: " + modItemId + " - " + e.getMessage());
        }

        return null;
    }

    /**
     * 通过命令给予模组物品
     */
    private void giveModItemByCommand(Player player, String modItemId, int amount) {
        // 尝试多种命令格式（适配不同的模组和服务端）
        String[] commandVariants = {
            // 标准格式
            "give " + player.getName() + " " + modItemId + " " + amount,
            // Minecraft 命名空间
            "minecraft:give " + player.getName() + " " + modItemId + " " + amount,
            // 模组特定格式
            "give " + player.getName() + " \"" + modItemId + "\" " + amount,
            // Forge 格式
            "/give " + player.getName() + " " + modItemId + " " + amount,
            // 其他可能的格式
            "item give " + player.getName() + " " + modItemId + " " + amount,
            "give @p " + modItemId + " " + amount,
            // 尝试不带数量（某些模组默认为1）
            "give " + player.getName() + " " + modItemId
        };

        boolean success = false;
        String lastError = "";

        for (String cmd : commandVariants) {
            try {
                // 移除可能的前导斜杠
                String cleanCmd = cmd.startsWith("/") ? cmd.substring(1) : cmd;

                plugin.getServer().dispatchCommand(plugin.getServer().getConsoleSender(), cleanCmd);
                success = true;
                break;
            } catch (Exception e) {
                lastError = e.getMessage();
                // 继续尝试下一个格式
            }
        }

        if (!success) {
            player.sendMessage("§c无法给予模组物品: " + modItemId);
            if (!lastError.isEmpty()) {
                plugin.getLogger().warning("模组物品给予失败: " + modItemId + " - " + lastError);
            }
        }
    }

    /**
     * 新增：区块加载事件 - 重建该区块中的浮空字
     */
    @EventHandler
    public void onChunkLoad(ChunkLoadEvent event) {
        // 优化：如果插件正在关闭，跳过区块加载处理
        if (isShuttingDown) {
            return;
        }

        // 修复：如果全息图功能关闭，完全跳过区块加载处理
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            return;
        }

        if (!plugin.getConfig().getBoolean("treasure-chest.hologram.chunk_protection.enabled", true)) {
            return;
        }

        org.bukkit.Chunk chunk = event.getChunk();

        // 延迟处理，确保区块完全加载
        plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
            @Override
            public void run() {
                try {
                    // 修复：再次检查全息图是否启用（防止延迟期间被关闭）
                    if (!plugin.getTreasureItemManager().isHologramEnabled()) {
                        return;
                    }

                    int recreatedCount = 0;

                    // 检查该区块中是否有摸金箱需要重建浮空字
                    for (Map.Entry<String, TreasureChestData> entry : treasureChestData.entrySet()) {
                        org.bukkit.Location location = parseLocationFromString(entry.getKey());
                        if (location == null || !location.getWorld().equals(chunk.getWorld())) {
                            continue;
                        }

                        // 检查是否在当前区块中
                        if (location.getChunk().getX() == chunk.getX() &&
                            location.getChunk().getZ() == chunk.getZ()) {

                            // 检查浮空字是否存在
                            if (!plugin.getHologramManager().hasHologram(location)) {
                                // 重建浮空字
                                TreasureChestData data = entry.getValue();
                                String newText = generateHologramText(data);
                                if (newText != null) {
                                    plugin.getHologramManager().createOrUpdateHologram(location, newText);
                                    recreatedCount++;
                                }
                            }
                        }
                    }

                    if (recreatedCount > 0) {
                        // 调试模式下显示区块重建日志
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().info("区块加载时重建了 " + recreatedCount + " 个浮空字 (区块: " +
                                chunk.getX() + "," + chunk.getZ() + ")");
                        }
                    }

                } catch (Exception e) {
                    plugin.getLogger().warning("区块加载时重建浮空字失败: " + e.getMessage());
                }
            }
        }, 5L); // 延迟5tick
    }

    /**
     * 新增：区块卸载事件 - 备份该区块中的浮空字信息
     * 修复：防止StackOverflowError的安全版本
     */
    @EventHandler
    public void onChunkUnload(ChunkUnloadEvent event) {
        // 优化：如果插件正在关闭，跳过区块卸载处理
        if (isShuttingDown) {
            return;
        }

        // 修复：如果全息图功能关闭，完全跳过区块卸载处理
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            return;
        }

        if (!plugin.getConfig().getBoolean("treasure-chest.hologram.chunk_protection.enabled", true)) {
            return;
        }

        org.bukkit.Chunk chunk = event.getChunk();

        // 修复：使用异步任务处理区块卸载，避免递归调用导致StackOverflowError
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, new Runnable() {
            @Override
            public void run() {
                try {
                    int backedUpCount = 0;

                    // 修复：创建副本避免并发修改异常
                    Map<String, TreasureChestData> dataCopy = new HashMap<>(treasureChestData);

                    // 备份该区块中的浮空字信息
                    for (Map.Entry<String, TreasureChestData> entry : dataCopy.entrySet()) {
                        try {
                            org.bukkit.Location location = parseLocationFromString(entry.getKey());
                            if (location == null || !location.getWorld().equals(chunk.getWorld())) {
                                continue;
                            }

                            // 检查是否在当前区块中
                            if (location.getChunk().getX() == chunk.getX() &&
                                location.getChunk().getZ() == chunk.getZ()) {

                                // 修复：在主线程中安全检查浮空字
                                plugin.getServer().getScheduler().runTask(plugin, new Runnable() {
                                    @Override
                                    public void run() {
                                        try {
                                            // 确保浮空字管理器有备份
                                            if (plugin.getHologramManager().hasHologram(location)) {
                                                TreasureChestData data = entry.getValue();

                                                // 修复：直接使用简单的文本生成，避免复杂的递归调用
                                                String currentText = generateSimpleHologramText(data);
                                                if (currentText != null) {
                                                    // 强制备份当前状态
                                                    plugin.getHologramManager().backupHologram(location, currentText);
                                                }
                                            }
                                        } catch (Exception e) {
                                            // 修复：单个浮空字备份失败不影响其他
                                            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                                plugin.getLogger().warning("备份单个浮空字失败: " + e.getMessage());
                                            }
                                        }
                                    }
                                });
                                backedUpCount++;
                            }
                        } catch (Exception e) {
                            // 修复：单个条目处理失败不影响其他
                            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                plugin.getLogger().warning("处理区块卸载条目失败: " + e.getMessage());
                            }
                        }
                    }

                    if (backedUpCount > 0) {
                        // 调试模式下显示区块备份日志
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().info("区块卸载时备份了 " + backedUpCount + " 个浮空字 (区块: " +
                                chunk.getX() + "," + chunk.getZ() + ")");
                        }
                    }

                } catch (Exception e) {
                    plugin.getLogger().warning("区块卸载时备份浮空字失败: " + e.getMessage());
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        e.printStackTrace();
                    }
                }
            }
        });
    }

    /**
     * 生成简单的浮空字文本（用于区块卸载备份，避免复杂递归）
     */
    private String generateSimpleHologramText(TreasureChestData data) {
        try {
            // 修复：简化的文本生成，避免复杂的状态检查
            if (data.isBeingSearched()) {
                return "§7搜索中...";
            }

            if (data.isFullySearched()) {
                long nextRefresh = data.getNextRefreshTime();
                if (nextRefresh > 0 && System.currentTimeMillis() >= nextRefresh) {
                    return "§a可以刷新！";
                } else if (nextRefresh > 0) {
                    return "§e刷新倒计时中...";
                } else {
                    return "§a已搜索完毕";
                }
            } else {
                int unsearched = data.getUnsearchedCount();
                int originalCount = data.getOriginalItemCount();

                // 简单的数据验证
                if (originalCount <= 0 || unsearched <= 0) {
                    return null;
                }

                return "§6还有 §e" + unsearched + " §6个物品未搜索";
            }
        } catch (Exception e) {
            // 修复：如果生成失败，返回默认文本
            return "§7摸金箱";
        }
    }

    /**
     * 播放摸金箱关闭音效
     */
    private void playChestCloseSound(Player player) {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.chest-close.enabled", true)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.chest-close.sound", "block.chest.close");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.chest-close.volume", 0.8);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.chest-close.pitch", 1.0);

            // 使用兼容的音效播放方法
            com.hang.plugin.utils.VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 播放物品拾取音效
     */
    private void playItemPickupSound(Player player) {
        if (!plugin.getConfig().getBoolean("treasure-chest.animation.sounds.item-pickup.enabled", true)) {
            return;
        }

        try {
            String soundName = plugin.getConfig().getString("treasure-chest.animation.sounds.item-pickup.sound", "entity.item.pickup");
            float volume = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.item-pickup.volume", 0.6);
            float pitch = (float) plugin.getConfig().getDouble("treasure-chest.animation.sounds.item-pickup.pitch", 1.3);

            // 使用兼容的音效播放方法
            com.hang.plugin.utils.VersionUtils.playCompatibleSound(player, soundName, volume, pitch);
        } catch (Exception e) {
            // 静默处理音效错误
        }
    }

    /**
     * 处理撤离点选择
     */
    private void handleEvacuationSelection(PlayerInteractEvent event) {
        event.setCancelled(true);
        Player player = event.getPlayer();

        if (event.getAction() == Action.LEFT_CLICK_BLOCK) {
            // 左键选择第一个点
            Location location = event.getClickedBlock().getLocation();
            firstPositions.put(player.getUniqueId(), location);
            player.sendMessage("§a第一个坐标已选择: " + formatLocation(location));
        } else if (event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            // 右键选择第二个点
            Location location = event.getClickedBlock().getLocation();
            secondPositions.put(player.getUniqueId(), location);
            player.sendMessage("§a第二个坐标已选择: " + formatLocation(location));

            // 检查是否两个点都已选择
            if (firstPositions.containsKey(player.getUniqueId())) {
                player.sendMessage("§e两个坐标都已选择，现在可以使用 §6/evac create <名称> §e创建撤离区域");
            }
        }
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(Location loc) {
        return String.format("%s: %d, %d, %d",
            loc.getWorld().getName(),
            loc.getBlockX(),
            loc.getBlockY(),
            loc.getBlockZ());
    }

    /**
     * 玩家移动事件 - 处理撤离区域检测
     */
    @EventHandler
    public void onPlayerMove(PlayerMoveEvent event) {
        Player player = event.getPlayer();

        // 检查玩家是否在撤离区域内
        String evacuationZone = plugin.getEvacuationSystem().checkPlayerInEvacuationZone(player);

        if (evacuationZone != null) {
            // 玩家在撤离区域内
            if (!plugin.getCountdownManager().hasActiveCountdown(player.getUniqueId())) {
                // 开始撤离倒计时
                plugin.getCountdownManager().startEvacuationCountdown(player, evacuationZone);
            }
        } else {
            // 玩家不在撤离区域内
            if (plugin.getCountdownManager().hasActiveCountdown(player.getUniqueId())) {
                // 取消撤离倒计时
                plugin.getCountdownManager().cancelCountdown(player.getUniqueId());
            }
        }
    }

    /**
     * 获取玩家的第一个选择位置
     */
    public Location getFirstPosition(Player player) {
        return firstPositions.get(player.getUniqueId());
    }

    /**
     * 获取玩家的第二个选择位置
     */
    public Location getSecondPosition(Player player) {
        return secondPositions.get(player.getUniqueId());
    }

    /**
     * 清除玩家的选择
     */
    public void clearPlayerSelection(Player player) {
        firstPositions.remove(player.getUniqueId());
        secondPositions.remove(player.getUniqueId());
    }

    /**
     * 处理在战利品管理GUI中点击玩家背包的事件
     */
    private void handlePlayerInventoryClickInManagementGUI(InventoryClickEvent event, TreasureManagementGUI managementGUI, Player player) {
        // 取消事件，防止物品被移动
        event.setCancelled(true);

        // 直接获取点击的物品（event.getSlot()就是背包槽位）
        int inventorySlot = event.getSlot();
        ItemStack clickedItem = event.getCurrentItem();

        if (clickedItem == null || clickedItem.getType() == Material.AIR) {
            return;
        }

        // 检查权限
        if (!player.hasPermission("evacuation.admin")) {
            player.sendMessage("§c您没有权限添加物品到战利品！");
            return;
        }

        // 添加调试信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("玩家 " + player.getName() + " 在战利品管理GUI中点击背包物品: " + clickedItem.getType().name() +
                " (背包槽位: " + inventorySlot + ")");
        }

        // 创建物品副本，避免修改原物品
        ItemStack itemToAdd = clickedItem.clone();

        // 调用管理GUI的添加物品方法
        managementGUI.addItemFromInventoryClick(itemToAdd);

        // 给玩家反馈
        String itemName = getItemDisplayName(itemToAdd);
        player.sendMessage("§a正在添加物品: §f" + itemName);
    }

    /**
     * 获取物品的显示名称
     */
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        return item.getType().name();
    }
}
