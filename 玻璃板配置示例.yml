# 🎨 HangEvacuation 玻璃板颜色配置示例
# 
# 这个文件展示了各种玻璃板颜色配置的示例
# 适用于 Minecraft 1.8.8-1.21.4 版本

# ===============================================
#           基础配置 (默认四阶段)
# ===============================================

# 默认配置 - 红橙黄绿渐变
items:
  unsearched-item:
    material: STAINED_GLASS_PANE
    data: 7  # 灰色
    name: "§7未搜索"
    lore:
      - "§7等待自动搜索"

  searching-progress:
    material: STAINED_GLASS_PANE
    custom_model_data: 0
    stages:
      stage_1:
        progress_range: [0, 25]
        data: 14  # 红色
        name: "§c正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §c{progress}%"
          - "§7状态: §c开始搜索"
      stage_2:
        progress_range: [26, 50]
        data: 1   # 橙色
        name: "§6正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §6{progress}%"
          - "§7状态: §6搜索中"
      stage_3:
        progress_range: [51, 75]
        data: 4   # 黄色
        name: "§e正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §e{progress}%"
          - "§7状态: §e即将完成"
      stage_4:
        progress_range: [76, 100]
        data: 5   # 浅绿色
        name: "§a正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §a{progress}%"
          - "§7状态: §a马上完成"

# ===============================================
#           示例1: 彩虹进度条 (6阶段)
# ===============================================

# 彩虹配置 - 红橙黄绿蓝紫渐变
items_rainbow:
  searching-progress:
    material: STAINED_GLASS_PANE
    custom_model_data: 0
    stages:
      rainbow_red:
        progress_range: [0, 16]
        data: 14  # 红色
        name: "§c🌈 搜索中 {progress}%"
        lore:
          - "§c▓§7▓▓▓▓▓ §7{progress}%"
          - "§7彩虹搜索模式"
      rainbow_orange:
        progress_range: [17, 33]
        data: 1   # 橙色
        name: "§6🌈 搜索中 {progress}%"
        lore:
          - "§c▓§6▓§7▓▓▓▓ §7{progress}%"
          - "§7彩虹搜索模式"
      rainbow_yellow:
        progress_range: [34, 50]
        data: 4   # 黄色
        name: "§e🌈 搜索中 {progress}%"
        lore:
          - "§c▓§6▓§e▓§7▓▓▓ §7{progress}%"
          - "§7彩虹搜索模式"
      rainbow_green:
        progress_range: [51, 66]
        data: 5   # 浅绿色
        name: "§a🌈 搜索中 {progress}%"
        lore:
          - "§c▓§6▓§e▓§a▓§7▓▓ §7{progress}%"
          - "§7彩虹搜索模式"
      rainbow_blue:
        progress_range: [67, 83]
        data: 11  # 蓝色
        name: "§9🌈 搜索中 {progress}%"
        lore:
          - "§c▓§6▓§e▓§a▓§9▓§7▓ §7{progress}%"
          - "§7彩虹搜索模式"
      rainbow_purple:
        progress_range: [84, 100]
        data: 10  # 紫色
        name: "§d🌈 搜索中 {progress}%"
        lore:
          - "§c▓§6▓§e▓§a▓§9▓§d▓ §7{progress}%"
          - "§7彩虹搜索完成！"

# ===============================================
#           示例2: 简化双色模式
# ===============================================

# 简化配置 - 只有搜索中和即将完成两个状态
items_simple:
  searching-progress:
    material: STAINED_GLASS_PANE
    custom_model_data: 0
    stages:
      searching:
        progress_range: [0, 90]
        data: 14  # 红色
        name: "§c⏳ 正在搜索... {progress}%"
        lore:
          - "§7请耐心等待..."
          - "§7进度: §c{progress}%"
      completing:
        progress_range: [91, 100]
        data: 5   # 绿色
        name: "§a✓ 即将完成! {progress}%"
        lore:
          - "§a马上就好了！"
          - "§7进度: §a{progress}%"

# ===============================================
#           示例3: 蓝色主题
# ===============================================

# 蓝色主题 - 深蓝到浅蓝渐变
items_blue_theme:
  unsearched-item:
    material: STAINED_GLASS_PANE
    data: 8   # 淡灰色
    name: "§8未搜索"
    lore:
      - "§8等待搜索..."

  searching-progress:
    material: STAINED_GLASS_PANE
    custom_model_data: 0
    stages:
      blue_dark:
        progress_range: [0, 33]
        data: 11  # 蓝色
        name: "§1🔍 深度搜索 {progress}%"
        lore:
          - "§1▓▓▓ §7{progress}%"
          - "§7深度扫描中..."
      blue_medium:
        progress_range: [34, 66]
        data: 9   # 青色
        name: "§b🔍 搜索中 {progress}%"
        lore:
          - "§1▓§b▓▓ §7{progress}%"
          - "§7数据分析中..."
      blue_light:
        progress_range: [67, 100]
        data: 3   # 淡蓝色
        name: "§b🔍 即将完成 {progress}%"
        lore:
          - "§1▓§b▓§3▓ §7{progress}%"
          - "§7最后处理中..."

# ===============================================
#           示例4: 火焰主题
# ===============================================

# 火焰主题 - 黑红橙黄渐变
items_fire_theme:
  unsearched-item:
    material: STAINED_GLASS_PANE
    data: 15  # 黑色
    name: "§0未点燃"
    lore:
      - "§8等待点火..."

  searching-progress:
    material: STAINED_GLASS_PANE
    custom_model_data: 0
    stages:
      fire_spark:
        progress_range: [0, 25]
        data: 15  # 黑色
        name: "§8🔥 点火中 {progress}%"
        lore:
          - "§8火花四溅..."
          - "§7进度: {progress}%"
      fire_ignite:
        progress_range: [26, 50]
        data: 14  # 红色
        name: "§c🔥 燃烧中 {progress}%"
        lore:
          - "§c火焰升起..."
          - "§7进度: {progress}%"
      fire_blaze:
        progress_range: [51, 75]
        data: 1   # 橙色
        name: "§6🔥 烈焰中 {progress}%"
        lore:
          - "§6烈火熊熊..."
          - "§7进度: {progress}%"
      fire_inferno:
        progress_range: [76, 100]
        data: 4   # 黄色
        name: "§e🔥 炽热中 {progress}%"
        lore:
          - "§e白热化状态！"
          - "§7进度: {progress}%"

# ===============================================
#           示例5: 自然主题
# ===============================================

# 自然主题 - 棕绿色调
items_nature_theme:
  unsearched-item:
    material: STAINED_GLASS_PANE
    data: 12  # 棕色
    name: "§6🌱 种子"
    lore:
      - "§7等待发芽..."

  searching-progress:
    material: STAINED_GLASS_PANE
    custom_model_data: 0
    stages:
      seed:
        progress_range: [0, 25]
        data: 12  # 棕色
        name: "§6🌱 发芽中 {progress}%"
        lore:
          - "§7种子正在萌芽..."
      sprout:
        progress_range: [26, 50]
        data: 13  # 绿色
        name: "§2🌿 生长中 {progress}%"
        lore:
          - "§2嫩芽破土而出..."
      grow:
        progress_range: [51, 75]
        data: 5   # 浅绿色
        name: "§a🌳 茁壮中 {progress}%"
        lore:
          - "§a快速生长中..."
      bloom:
        progress_range: [76, 100]
        data: 4   # 黄色
        name: "§e🌻 开花中 {progress}%"
        lore:
          - "§e即将开花结果！"
