# 🔍 摸金箱搜索配置详解

## 📋 **配置项总览**

| 配置项 | 文件位置 | 作用 | 默认值 | 状态 |
|--------|----------|------|--------|------|
| `treasure-chest.search-cooldown` | `config.yml` | 手动搜索冷却时间 | 1秒 | ✅ 使用中 |
| `treasure-chest.auto-search.interval` | `config.yml` | 自动搜索检查间隔 | 1秒 | ✅ 使用中 |
| `chest_settings.search_cooldown` | `treasure_items.yml` | 默认搜索冷却时间 | 1秒 | ✅ 使用中 |

## 🔧 **详细配置说明**

### **1. 手动搜索冷却时间**

```yaml
# config.yml
treasure-chest:
  search-cooldown: 1  # 手动点击搜索的冷却时间（秒）
```

**作用**：
- 防止玩家频繁点击搜索按钮
- 只在手动搜索模式下生效
- 在自动搜索模式下基本不起作用

**使用场景**：
- 玩家手动点击物品进行搜索
- 防止误操作和服务器压力

### **2. 自动搜索检查间隔**

```yaml
# config.yml
treasure-chest:
  auto-search:
    interval: 1  # 自动搜索检查间隔（秒）
```

**作用**：
- 控制自动搜索系统的检查频率
- 每隔指定秒数检查是否可以开始下一个搜索
- **不是搜索时间本身**

**工作原理**：
```
时间轴: 0s -> 1s -> 2s -> 3s -> 4s -> 5s
检查:   ✓     ✓     ✓     ✓     ✓     ✓
搜索:   开始  进行中 进行中 完成   开始  进行中
```

### **3. 默认搜索冷却时间（向下兼容）**

```yaml
# treasure_items.yml
chest_settings:
  search_cooldown: 1  # 默认搜索冷却时间（秒）
```

**作用**：
- 提供向下兼容性
- 当 `config.yml` 中没有配置时使用此值
- 功能与 `config.yml` 中的 `search-cooldown` 相同

## 🎯 **搜索时间配置**

现在每个物品的搜索时间由物品自身的 `search_speed` 属性决定，支持不同物品有不同的搜索时间，更加灵活和个性化。

**搜索时间配置**：
```yaml
# treasure_items.yml
items:
  coal:
    search_speed: 2    # 煤炭搜索2秒
  diamond:
    search_speed: 8    # 钻石搜索8秒
  iron_ingot:
    search_speed: 4    # 铁锭搜索4秒
```

## 🎯 **实际搜索流程**

### **自动搜索模式**

1. **开始搜索**：玩家打开摸金箱
2. **检查间隔**：每 `interval` 秒检查一次
3. **开始搜索物品**：找到未搜索的物品
4. **搜索时间**：根据物品的 `search_speed` 进行搜索
5. **完成搜索**：物品出现在箱子中
6. **继续循环**：重复步骤2-5

### **手动搜索模式**

1. **玩家点击**：点击未搜索的物品
2. **冷却检查**：检查是否在 `search-cooldown` 冷却中
3. **开始搜索**：根据物品的 `search_speed` 进行搜索
4. **完成搜索**：物品出现，进入冷却状态

## 📊 **配置建议**

### **高性能服务器**
```yaml
# config.yml
treasure-chest:
  search-cooldown: 0.5    # 更短的冷却时间
  auto-search:
    interval: 0.5         # 更频繁的检查
```

### **低性能服务器**
```yaml
# config.yml
treasure-chest:
  search-cooldown: 2      # 更长的冷却时间
  auto-search:
    interval: 2           # 较少的检查频率
```

### **平衡设置（推荐）**
```yaml
# config.yml
treasure-chest:
  search-cooldown: 1      # 标准冷却时间
  auto-search:
    interval: 1           # 标准检查间隔
```

## 🔄 **配置优化建议**

### **1. 统一配置位置**
建议将所有搜索相关配置都放在 `config.yml` 中，减少配置分散。

### **2. 清理废弃配置**
已经完全移除了废弃的 `search-time` 配置，避免混淆。

### **3. 改进注释**
添加了详细的注释说明，明确每个配置的作用。

### **4. 配置验证**
建议添加配置验证，确保数值在合理范围内：
- `search-cooldown`: 0.1-10秒
- `interval`: 0.5-5秒

## 📝 **总结**

**配置项说明**：现在的配置项都有明确的作用，不再有重复：

- `interval`: 控制检查频率（多久检查一次）
- `search-cooldown`: 控制冷却时间（防止频繁操作）
- 搜索时间: 现在由每个物品的 `search_speed` 属性决定

每个配置都有其独特的用途，共同构成了完整的摸金箱搜索系统！
