# ⌨️ Tab补全功能详细说明

## 🎯 **功能概述**

现在所有刷新指令都支持完整的Tab补全功能，包括子命令、世界名和参数补全，大大提升了使用体验。

---

## 📋 **支持的Tab补全**

### **1. `/evac` 命令Tab补全**

#### **第一级：主要子命令**
```bash
/evac <TAB>
```
**补全选项**：
- `give` - 给予摸金箱
- `gui` - 打开GUI界面
- `refresh` - 刷新功能 ⭐
- `hologram` / `holo` - 浮空字管理
- `save` - 保存数据
- `reload` - 重载配置
- `nms` / `version` - 查看版本信息
- `addtype` - 添加摸金箱种类
- `removetype` - 移除摸金箱种类
- `listtypes` - 列出摸金箱种类

#### **第二级：refresh子命令**
```bash
/evac refresh <TAB>
```
**补全选项**：
- `all` - 刷新所有内容
- `holograms` - 仅刷新浮空字
- `chests` - 仅刷新摸金箱
- `items` - 重新生成物品

#### **第三级：世界名补全**
```bash
/evac refresh all <TAB>
/evac refresh holograms <TAB>
/evac refresh chests <TAB>
/evac refresh items <TAB>
```
**补全选项**：
- `world` - 主世界
- `world_nether` - 地狱世界
- `world_the_end` - 末地世界
- `[自定义世界名]` - 服务器上的其他世界

#### **第四级：确认参数**
```bash
/evac refresh items world <TAB>
```
**补全选项**：
- `confirm` - 确认执行

---

### **2. `/evacuation` 命令Tab补全**

#### **第一级：主要子命令**
```bash
/evacuation <TAB>
```
**补全选项**：
- `reload` - 重载配置
- `help` - 显示帮助
- `debug` - 调试信息
- `save` - 保存数据
- `refresh` - 刷新功能 ⭐

#### **第二级：refresh子命令**
```bash
/evacuation refresh <TAB>
```
**补全选项**：
- `all` - 刷新所有内容
- `holograms` - 仅刷新浮空字
- `chests` - 仅刷新摸金箱
- `items` - 重新生成物品

#### **第三级：世界名补全**
```bash
/evacuation refresh all <TAB>
```
**补全选项**：
- 所有服务器世界名（动态获取）

#### **第四级：确认参数**
```bash
/evacuation refresh items world <TAB>
```
**补全选项**：
- `confirm` - 确认执行

---

## 🌍 **世界名补全详解**

### **动态获取**
世界名补全是**动态获取**的，会自动包含：
- ✅ 服务器上所有已加载的世界
- ✅ 主世界、地狱、末地
- ✅ 自定义世界和插件世界
- ✅ 实时更新（新加载的世界会自动出现）

### **智能过滤**
Tab补全支持**智能过滤**：
```bash
# 输入部分世界名会自动过滤
/evac refresh all wo<TAB>  → 只显示以"wo"开头的世界
/evac refresh all world_<TAB>  → 只显示以"world_"开头的世界
```

---

## 🎮 **使用示例**

### **完整的Tab补全流程**

#### **示例1：刷新主世界的所有内容**
```bash
1. /evac <TAB>           → 选择 refresh
2. /evac refresh <TAB>   → 选择 all  
3. /evac refresh all <TAB> → 选择 world
4. /evac refresh all world  → 执行
```

#### **示例2：重新生成地狱世界的物品**
```bash
1. /evac <TAB>                    → 选择 refresh
2. /evac refresh <TAB>            → 选择 items
3. /evac refresh items <TAB>      → 选择 world_nether
4. /evac refresh items world_nether <TAB> → 选择 confirm
5. /evac refresh items world_nether confirm → 执行
```

#### **示例3：刷新特定世界的浮空字**
```bash
1. /evacuation <TAB>              → 选择 refresh
2. /evacuation refresh <TAB>      → 选择 holograms
3. /evacuation refresh holograms <TAB> → 选择目标世界
4. /evacuation refresh holograms [世界名] → 执行
```

---

## 🔧 **其他Tab补全功能**

### **摸金箱种类补全**
```bash
/evac give <TAB>
```
**补全选项**：
- `common` - 普通摸金箱
- `weapon` - 武器箱
- `ammo` - 弹药箱
- `medical` - 医疗箱
- `supply` - 补给箱
- `equipment` - 装备箱

### **玩家名补全**
```bash
/evac give common <TAB>
```
**补全选项**：
- 所有在线玩家的名称

### **浮空字管理补全**
```bash
/evac hologram <TAB>
```
**补全选项**：
- `stats` - 查看统计
- `rebuild` - 重建浮空字
- `check` - 检查状态
- `clear` - 清理浮空字
- `cleanup` - 清理孤立浮空字
- `distance` - 距离清理

### **距离参数补全**
```bash
/evac hologram distance <TAB>
```
**补全选项**：
- `16` - 16格距离
- `32` - 32格距离
- `48` - 48格距离
- `64` - 64格距离

---

## ⚡ **性能优化**

### **智能过滤**
- ✅ 只显示匹配的补全选项
- ✅ 大小写不敏感匹配
- ✅ 前缀匹配算法

### **动态加载**
- ✅ 世界列表实时获取
- ✅ 玩家列表实时更新
- ✅ 摸金箱种类动态加载

### **内存优化**
- ✅ 临时列表，用完即释放
- ✅ 避免重复计算
- ✅ 高效的字符串匹配

---

## 🎯 **用户体验提升**

### **操作便捷性**
- 🚀 **减少输入错误** - Tab补全避免拼写错误
- 🚀 **提高输入速度** - 快速选择而非手动输入
- 🚀 **发现功能** - 通过Tab补全了解可用选项

### **学习成本降低**
- 📚 **自动提示** - 不需要记住所有参数
- 📚 **即时反馈** - 立即看到可用选项
- 📚 **渐进式引导** - 逐步引导完成复杂命令

### **错误预防**
- ⚠️ **世界名验证** - 只显示存在的世界
- ⚠️ **参数验证** - 只显示有效的参数选项
- ⚠️ **语法提示** - 通过补全了解正确语法

---

## 🔍 **技术实现**

### **核心方法**
```java
@Override
public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
    // 智能补全逻辑
}

private void addWorldCompletions(List<String> completions) {
    // 动态获取世界列表
    for (World world : plugin.getServer().getWorlds()) {
        completions.add(world.getName());
    }
}
```

### **过滤算法**
```java
// 智能过滤匹配的补全选项
String input = args.length > 0 ? args[args.length - 1].toLowerCase() : "";
completions.removeIf(completion -> !completion.toLowerCase().startsWith(input));
```

现在refresh指令拥有完整的Tab补全支持，包括世界名补充，大大提升了使用体验！
