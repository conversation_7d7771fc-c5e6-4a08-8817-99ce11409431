===============================================
  HangEvacuation 摸金箱种类系统更新 - v1.6.0
===============================================

🎮 插件名称: HangEvacuation (Hang摸金撤离)
📅 更新日期: 2024-12-19
🔧 版本号: 1.6.0
👨‍💻 作者: hangzong(航总)
📞 技术支持: 微信 hang060217

===============================================
            🆕 摸金箱种类系统功能
===============================================

🎯 【核心功能】
✅ 新增摸金箱种类管理系统 (ChestTypeManager)
✅ 支持多种摸金箱类型：武器箱、弹药箱、医疗箱、补给箱、装备箱
✅ 新增摸金箱种类选择GUI (ChestTypeSelectionGUI)
✅ 新增 mojin.yml 摸金箱种类配置文件
✅ 支持每种摸金箱的独立配置和外观

🔧 【命令系统增强】
✅ 升级 /evac give 命令支持摸金箱种类
✅ 新格式: /evac give [种类] [玩家] [数量]
✅ 支持种类参数：common, weapon, ammo, medical, supply, equipment
✅ 向下兼容原有命令格式
✅ 新增 /evac gui 打开摸金箱种类选择界面

🎨 【GUI系统重构】
✅ 重构管理GUI支持按种类分类管理
✅ 新增摸金箱种类选择界面
✅ 支持左键管理、右键获取摸金箱
✅ 优化界面布局和用户体验
✅ 支持分页显示多种摸金箱类型

===============================================
              📝 摸金箱种类配置
===============================================

🎯 【默认种类】
📦 common (普通摸金箱) - 材质: CHEST
🔫 weapon (武器箱) - 材质: TRAPPED_CHEST
💥 ammo (弹药箱) - 材质: BARREL  
🏥 medical (医疗箱) - 材质: WHITE_SHULKER_BOX
📦 supply (补给箱) - 材质: BLUE_SHULKER_BOX
⚙️ equipment (装备箱) - 材质: ENDER_CHEST

🔧 【配置特性】
✅ 每种摸金箱独立的名称、描述、材质
✅ 支持自定义模型数据 (1.14+版本)
✅ 支持稀有度等级和颜色配置
✅ 支持启用/禁用特定种类
✅ 支持权限系统控制访问

===============================================
              🎮 使用指南
===============================================

📋 【基础命令】
- /evac give - 给自己普通摸金箱
- /evac give weapon - 给自己武器箱
- /evac give weapon player1 - 给player1武器箱
- /evac give ammo player1 5 - 给player1 5个弹药箱
- /evac gui - 打开摸金箱种类选择界面

🛠️【管理流程】
1. 使用 /evac gui 打开种类选择界面
2. 左键点击种类 - 管理该种类的战利品
3. 右键点击种类 - 获得该种类的摸金箱
4. 在管理界面中配置物品概率等参数

🎯 【配置管理】
- 编辑 mojin.yml 自定义摸金箱种类
- 使用 /evac reload 重载配置
- 支持热重载，无需重启服务器

===============================================
              🔧 技术细节
===============================================

📊 【系统架构】
✅ 新增 ChestTypeManager 摸金箱种类管理器
✅ 新增 ChestTypeSelectionGUI 种类选择界面
✅ 重构 TreasureManagementGUI 支持种类分类
✅ 升级命令处理系统支持种类参数
✅ 优化PlayerListener支持新GUI系统

🛠️【代码优化】
✅ 使用反射兼容1.12.2版本的自定义模型数据
✅ 统一GUI管理接口和事件处理
✅ 优化内存使用和性能表现
✅ 改进错误处理和日志输出

🔄 【兼容性】
✅ 完全向下兼容现有摸金箱系统
✅ 支持 1.12.2 版本的所有功能
✅ 兼容现有的战利品配置文件
✅ 保持与模组物品系统的兼容性

===============================================
              🎯 配置示例
===============================================

🔧 【mojin.yml 配置示例】
```yaml
chest_types:
  weapon:
    name: "§c武器箱"
    display_name: "§c军用武器箱"
    description:
      - "§7军用级别的武器箱"
      - "§7包含各种强力武器"
    material: "TRAPPED_CHEST"
    custom_model_data: 1001
    rarity: "§c稀有"
    enabled: true
```

🎮 【命令示例】
```
/evac give weapon - 给自己武器箱
/evac give ammo player1 3 - 给player1 3个弹药箱
/evac gui - 打开种类选择界面
```

===============================================
              🔍 故障排除
===============================================

❓ 【常见问题】
Q: 如何添加新的摸金箱种类？
A: 编辑 mojin.yml 文件，在 chest_types 节点下添加新种类配置

Q: 摸金箱种类不显示怎么办？
A: 检查 mojin.yml 中 enabled 是否为 true，使用 /evac reload 重载配置

Q: 自定义模型数据不生效？
A: 自定义模型数据仅在1.14+版本支持，1.12.2版本会自动忽略

Q: 权限系统如何配置？
A: 在 mojin.yml 中启用权限检查，配置对应的权限节点

===============================================
              📦 文件结构
===============================================

📁 【新增文件】
- mojin.yml - 摸金箱种类配置文件
- ChestTypeManager.java - 种类管理器
- ChestTypeSelectionGUI.java - 种类选择界面

📁 【修改文件】
- HangPlugin.java - 添加种类管理器支持
- HangCommand.java - 升级命令系统
- PlayerListener.java - 支持新GUI系统
- TreasureManagementGUI.java - 支持种类分类

===============================================
              🎉 更新总结
===============================================

🎯 本次更新为HangEvacuation插件带来了完整的摸金箱种类系统，
   让服务器管理员可以创建多种不同类型的摸金箱，每种都有
   独特的外观、名称和配置。玩家可以通过直观的GUI界面
   选择和管理不同种类的摸金箱，大大提升了游戏体验。

🔧 技术支持: 微信 hang060217
🎮 交流Q群: 361919269
👨‍💻 作者: hangzong(航总)
🏷️ 标签: Hang系列插件
