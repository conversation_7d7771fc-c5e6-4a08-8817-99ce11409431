# 摸金箱物品配置文件
# 配置摸金箱中可能出现的物品及其概率

# 物品配置格式说明:
# items:
#   物品ID:
#     material: 物品材质 (必填)
#     amount: 物品数量 (可选，默认1，支持范围随机如 "1-5")
#     data: 物品数据值 (可选，用于1.12.2的子类型)
#     name: 自定义名称 (可选)
#     lore: 物品描述 (可选，列表格式)
#     probability: 出现概率 (必填，0.0-1.0)
#     search_speed: 搜索速度/秒 (可选，默认3秒，物品越稀有建议时间越长)
#     commands: 获得物品时执行的命令 (可选，列表格式)
#               使用 {player} 代表玩家名称
#     chest_types: 适用的摸金箱种类 (新增，列表格式)
#                  可选值: common, weapon, ammo, medical, supply, equipment
#                  如果不指定，默认只在 common 中出现
#
# 数量范围随机功能：
# - 固定数量：amount: 5 （总是给5个）
# - 范围随机：amount: "3-8" （随机给3到8个）
# - 单个随机：amount: "1-1" （等同于固定1个）

items:
  # 钻石 - 稀有物品，搜索时间较长
  diamond:
    material: DIAMOND
    amount: "1-2"  # 范围随机：1到2个钻石
    name: "§b闪亮的钻石"
    lore:
      - "§7一颗珍贵的钻石"
      - "§7价值连城"
    probability: 0.05
    search_speed: 8
    commands:
      - "tellraw {player} [\"§a恭喜获得钻石！\"]"
    chest_types:
      - equipment  # 装备箱
      - common     # 普通摸金箱

  # 绿宝石 - 稀有物品
  emerald:
    material: EMERALD
    amount: "1-3"  # 范围随机：1到3个绿宝石
    name: "§a老村的绿宝石"
    lore:
      - "§7村民最爱的货币"
    probability: 0.08
    search_speed: 6
    commands:
      - "tellraw {player} [\"§a获得了绿宝石！\"]"
    chest_types:
      - equipment  # 装备箱
      - common     # 普通摸金箱

  # 金锭 - 中等稀有
  gold_ingot:
    material: GOLD_INGOT
    amount: "2-5"  # 范围随机：2到5个金锭
    name: "§6金锭"
    probability: 0.12
    search_speed: 5
    chest_types:
      - equipment  # 装备箱
      - supply     # 补给箱
      - common     # 普通摸金箱

  # 铁锭 - 普通物品
  iron_ingot:
    material: IRON_INGOT
    amount: "3-8"  # 范围随机：3到8个铁锭
    name: "§7铁锭"
    probability: 0.15
    search_speed: 3
    chest_types:
      - supply     # 补给箱
      - weapon     # 武器箱（制作武器用）
      - common     # 普通摸金箱

  # 煤炭 - 常见物品，搜索快
  coal:
    material: COAL
    amount: "5-12"  # 范围随机：5到12个煤炭
    probability: 0.20
    search_speed: 2
    chest_types:
      - supply     # 补给箱
      - common     # 普通摸金箱

  # 红石 - 常见物品
  redstone:
    material: REDSTONE
    amount: 10
    probability: 0.18
    search_speed: 2
    chest_types:
      - supply     # 补给箱
      - equipment  # 装备箱（电路用）
      - common     # 普通摸金箱

  # 青金石 - 普通物品
  lapis:
    material: LAPIS_LAZULI
    amount: 4
    name: "§1青金石"
    probability: 0.15
    search_speed: 3
    chest_types:
      - supply     # 补给箱
      - equipment  # 装备箱（附魔用）
      - common     # 普通摸金箱

  # 经验瓶 - 中等稀有
  exp_bottle:
    material: EXPERIENCE_BOTTLE
    amount: 3
    name: "§d经验之瓶"
    lore:
      - "§7蕴含着丰富的经验"
    probability: 0.10
    search_speed: 4
    commands:
      - "xp add {player} 100"
    chest_types:
      - medical    # 医疗箱
      - equipment  # 装备箱
      - common     # 普通摸金箱

  # 末影珍珠 - 稀有物品
  ender_pearl:
    material: ENDER_PEARL
    amount: 1
    name: "§5末影珍珠"
    lore:
      - "§7传送的神秘力量"
    probability: 0.06
    search_speed: 7
    chest_types:
      - equipment  # 装备箱
      - common     # 普通摸金箱

  # 烈焰棒 - 稀有物品
  blaze_rod:
    material: BLAZE_ROD
    amount: 2
    name: "§c热热的棒子"
    probability: 0.08
    search_speed: 6
    chest_types:
      - ammo       # 弹药箱
      - weapon     # 武器箱（制作用）
      - common     # 普通摸金箱

  # 特殊奖励 - 钻石剑 (传说级物品，搜索时间最长)
  diamond_sword:
    material: DIAMOND_SWORD
    amount: 1
    name: "§b摸金者之剑"
    lore:
      - "§7传说中的武器"
      - "§7锋利无比"
    probability: 0.02
    search_speed: 15
    commands:
      - "enchant {player} sharpness 3"
      - "tellraw {player} [\"§6恭喜获得传说武器！\"]"
    chest_types:
      - weapon     # 武器箱
      - equipment  # 装备箱
      - common     # 普通摸金箱

  # 空物品 (什么都没有) - 瞬间搜索
  empty:
    material: AIR
    amount: 0
    name: "§7空无一物"
    probability: 0.25
    search_speed: 1
    chest_types:
      - common     # 只在普通摸金箱中出现
      - weapon     # 武器箱
      - ammo       # 弹药箱
      - medical    # 医疗箱
      - supply     # 补给箱
      - equipment  # 装备箱

  # === 武器箱专属物品 ===
  # 铁剑 - 武器箱专属
  iron_sword:
    material: IRON_SWORD
    amount: 1
    name: "§7锋利的铁剑"
    lore:
      - "§7一把普通的铁剑"
      - "§7适合新手使用"
    probability: 0.20
    search_speed: 4
    chest_types:
      - weapon     # 只在武器箱中出现

  # 弓 - 武器箱专属
  bow:
    material: BOW
    amount: 1
    name: "§6猎人之弓"
    lore:
      - "§7经验丰富的猎人使用的弓"
      - "§7射程远，威力大"
    probability: 0.15
    search_speed: 5
    chest_types:
      - weapon     # 只在武器箱中出现

  # === 弹药箱专属物品 ===
  # 箭 - 弹药箱专属
  arrow:
    material: ARROW
    amount: 16
    name: "§f精制箭矢"
    lore:
      - "§7制作精良的箭矢"
      - "§7命中率高"
    probability: 0.30
    search_speed: 2
    chest_types:
      - ammo       # 只在弹药箱中出现

  # TNT - 弹药箱专属
  tnt:
    material: TNT
    amount: 2
    name: "§c爆破专用TNT"
    lore:
      - "§7威力巨大的爆炸物"
      - "§c小心使用！"
    probability: 0.10
    search_speed: 6
    chest_types:
      - ammo       # 只在弹药箱中出现

  # === 医疗箱专属物品 ===
  # 治疗药水 - 医疗箱专属
  healing_potion:
    material: POTION
    amount: 2
    data: 8261  # 治疗药水II的数据值
    name: "§d强效治疗药水"
    lore:
      - "§7能够快速恢复生命值"
      - "§7紧急情况下的救命药"
    probability: 0.25
    search_speed: 4
    chest_types:
      - medical    # 只在医疗箱中出现

  # 金苹果 - 医疗箱专属
  golden_apple:
    material: GOLDEN_APPLE
    amount: 1
    name: "§6神圣金苹果"
    lore:
      - "§7传说中的治疗圣品"
      - "§7能够提供强大的恢复效果"
    probability: 0.08
    search_speed: 8
    chest_types:
      - medical    # 只在医疗箱中出现

  # === 补给箱专属物品 ===
  # 面包 - 补给箱专属
  bread:
    material: BREAD
    amount: 8
    name: "§e新鲜面包"
    lore:
      - "§7刚出炉的面包"
      - "§7能够快速恢复饥饿值"
    probability: 0.35
    search_speed: 2
    chest_types:
      - supply     # 只在补给箱中出现

  # 熟牛肉 - 补给箱专属
  cooked_beef:
    material: COOKED_BEEF
    amount: 6
    name: "§c烤牛肉"
    lore:
      - "§7香喷喷的烤牛肉"
      - "§7营养丰富"
    probability: 0.25
    search_speed: 3
    chest_types:
      - supply     # 只在补给箱中出现

# 注意：
# - 搜索时间: 在上面的 items 配置中每个物品单独设置 search_speed
# - 刷新时间: 在主配置文件 config.yml 中的 treasure-chest.refresh-time 配置
# - 搜索冷却时间: 在主配置文件 config.yml 中的 treasure-chest.search-cooldown 配置
# - 浮空字启用: 在主配置文件 config.yml 中的 treasure-chest.hologram_enabled 配置
# - 槽位数量: 在 mojin.yml 中每个摸金箱种类单独配置
