handler=Block #C, types=[Ljava/lang/IllegalArgumentException;], range=[#B...#B]
handler=Block #AJ, types=[Ljava/lang/RuntimeException;], range=[Block #AI, Block #AH]
handler=Block #AM, types=[Ljava/io/IOException;], range=[Block #AL, Block #AK]
handler=Block #AP, types=[Ljava/lang/RuntimeException;], range=[Block #AO, Block #AN]
handler=Block #AS, types=[Ljava/lang/RuntimeException;], range=[Block #AR, Block #AQ]
handler=Block #AV, types=[Ljava/lang/IllegalAccessException;], range=[Block #AU, Block #AT]
handler=Block #AY, types=[Ljava/io/IOException;], range=[Block #AX, Block #AW]
===#Block A(size=3, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1703993696)
      goto BK
      -> ConditionalJump[IF_ICMPNE] #A -> #BK
      -> Immediate #A -> #B
===#Block B(size=4, flags=0)===
   0. lvar3 = lvar1;
   1. lvar10 = lvar3.toUpperCase();
   2. lvar11 = org.bukkit.Material.valueOf(lvar10);
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1510108390)
      goto BT
      -> Immediate #B -> #AG
      -> ConditionalJump[IF_ICMPNE] #B -> #BT
      -> TryCatch range: [B...B] -> C ([Ljava/lang/IllegalArgumentException;])
      <- Immediate #A -> #B
===#Block C(size=4, flags=0)===
   0. lvar12 = catch();
   1. // Frame: locals[0] [] stack[1] [java/lang/IllegalArgumentException]
   2. lvar5 = lvar12;
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2105754288)
      goto BL
      -> Immediate #C -> #D
      -> ConditionalJump[IF_ICMPNE] #C -> #BL
      <- TryCatch range: [B...B] -> C ([Ljava/lang/IllegalArgumentException;])
===#Block BL(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2105754288)
      goto BL
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1961494076 ^ lvar78})
      goto BL
   2. _consume({1994296950 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BL -> #BL
      <- ConditionalJump[IF_ICMPNE] #C -> #BL
      <- ConditionalJump[IF_ICMPNE] #BL -> #BL
===#Block D(size=4, flags=0)===
   0. lvar13 = lvar1;
   1. lvar14 = lvar13.toUpperCase();
   2. lvar6 = lvar14;
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1511030039)
      goto BG
      -> ConditionalJump[IF_ICMPNE] #D -> #BG
      -> Immediate #D -> #E
      <- Immediate #C -> #D
===#Block E(size=9, flags=0)===
   0. lvar15 = lvar6;
   1. lvar7 = lvar15;
   2. lvar16 = {-1364590018 ^ lvar78};
   3. lvar8 = lvar16;
   4. lvar17 = lvar7;
   5. lvar18 = lvar17.hashCode();
   6. svar80 = {lvar18 ^ lvar78};
   7. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(svar80)) {
      case 24472792:
      	 goto	#R
      case 102479455:
      	 goto	#H
      case 110867538:
      	 goto	#P
      case 156907340:
      	 goto	#N
      case 197868356:
      	 goto	#F
      case 227817807:
      	 goto	#L
      case 232973276:
      	 goto	#J
      default:
      	 goto	#T
   }
   8. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1968181750)
      goto BD
      -> Switch[24472792] #E -> #R
      -> DefaultSwitch #E -> #T
      -> Switch[110867538] #E -> #P
      -> ConditionalJump[IF_ICMPNE] #E -> #BD
      -> Switch[156907340] #E -> #N
      -> Switch[227817807] #E -> #L
      -> Switch[232973276] #E -> #J
      -> Switch[102479455] #E -> #H
      -> Switch[197868356] #E -> #F
      <- Immediate #D -> #E
===#Block F(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar19 = lvar7;
   2. lvar4 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.xaqnqktvbkqgxqr(), lvar78);
   3. lvar20 = lvar19.equals(lvar4);
   4. if (lvar20 == {1314928717 ^ lvar78})
      goto T
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1929495146)
      goto BC
      -> ConditionalJump[IF_ICMPEQ] #F -> #T
      -> Immediate #F -> #G
      -> ConditionalJump[IF_ICMPNE] #F -> #BC
      <- Switch[197868356] #E -> #F
===#Block G(size=4, flags=0)===
   0. lvar21 = {1131040627 ^ lvar78};
   1. lvar8 = lvar21;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 458390410)
      goto BJ
   3. goto AL
      -> UnconditionalJump[GOTO] #G -> #AL
      -> ConditionalJump[IF_ICMPNE] #G -> #BJ
      <- Immediate #F -> #G
===#Block AL(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 139969599)
      goto AK
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 854964034)
      goto BH
      -> ConditionalJump[IF_ICMPNE] #AL -> #BH
      -> TryCatch range: [AL...AK] -> AM ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #AL -> #AK
      <- UnconditionalJump[GOTO] #G -> #AL
===#Block AK(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486359395)
      goto CC
      -> ConditionalJump[IF_ICMPNE] #AK -> #CC
      -> TryCatch range: [AL...AK] -> AM ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AL -> #AK
===#Block CC(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486359395)
      goto CC
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2065479897 ^ lvar78})
      goto CC
   2. _consume({63045588 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #CC -> #CC
      <- ConditionalJump[IF_ICMPNE] #CC -> #CC
      <- ConditionalJump[IF_ICMPNE] #AK -> #CC
===#Block AM(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1028083891)
      goto BZ
   2. goto T
      -> ConditionalJump[IF_ICMPNE] #AM -> #BZ
      -> UnconditionalJump[GOTO] #AM -> #T
      <- TryCatch range: [AL...AK] -> AM ([Ljava/io/IOException;])
      <- TryCatch range: [AL...AK] -> AM ([Ljava/io/IOException;])
===#Block BZ(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1028083891)
      goto BZ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {121317528 ^ lvar78})
      goto BZ
   2. _consume({1921197660 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BZ -> #BZ
      <- ConditionalJump[IF_ICMPNE] #BZ -> #BZ
      <- ConditionalJump[IF_ICMPNE] #AM -> #BZ
===#Block H(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar45 = lvar7;
   2. lvar68 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yovpiklqomirgvh(), lvar78);
   3. lvar46 = lvar45.equals(lvar68);
   4. if (lvar46 == {1988704460 ^ lvar78})
      goto T
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1270233501)
      goto BB
      -> Immediate #H -> #I
      -> ConditionalJump[IF_ICMPEQ] #H -> #T
      -> ConditionalJump[IF_ICMPNE] #H -> #BB
      <- Switch[102479455] #E -> #H
===#Block I(size=3, flags=0)===
   0. lvar47 = {350942479 ^ lvar78};
   1. lvar8 = lvar47;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1487427512)
      goto AZ
      -> Immediate #I -> #T
      -> ConditionalJump[IF_ICMPNE] #I -> #AZ
      <- Immediate #H -> #I
===#Block J(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar48 = lvar7;
   2. lvar69 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.uugjldhzkttpuzb(), lvar78);
   3. lvar49 = lvar48.equals(lvar69);
   4. if (lvar49 == {54218633 ^ lvar78})
      goto T
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 433749064)
      goto BC
      -> ConditionalJump[IF_ICMPNE] #J -> #BC
      -> ConditionalJump[IF_ICMPEQ] #J -> #T
      -> Immediate #J -> #K
      <- Switch[232973276] #E -> #J
===#Block K(size=4, flags=0)===
   0. lvar50 = {836190228 ^ lvar78};
   1. lvar8 = lvar50;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1900412743)
      goto BN
   3. goto AX
      -> ConditionalJump[IF_ICMPNE] #K -> #BN
      -> UnconditionalJump[GOTO] #K -> #AX
      <- Immediate #J -> #K
===#Block AX(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 182694466)
      goto AW
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2001254513)
      goto BP
      -> TryCatch range: [AX...AW] -> AY ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AX -> #BP
      -> ConditionalJump[IF_ICMPEQ] #AX -> #AW
      <- UnconditionalJump[GOTO] #K -> #AX
===#Block AW(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -256907456)
      goto CD
      -> ConditionalJump[IF_ICMPNE] #AW -> #CD
      -> TryCatch range: [AX...AW] -> AY ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AX -> #AW
===#Block CD(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -256907456)
      goto CD
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {167772058 ^ lvar78})
      goto CD
   2. _consume({1804540114 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #CD -> #CD
      <- ConditionalJump[IF_ICMPNE] #CD -> #CD
      <- ConditionalJump[IF_ICMPNE] #AW -> #CD
===#Block BP(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2001254513)
      goto BP
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {3946138 ^ lvar78})
      goto BP
   2. _consume({1161545956 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BP -> #BP
      <- ConditionalJump[IF_ICMPNE] #BP -> #BP
      <- ConditionalJump[IF_ICMPNE] #AX -> #BP
===#Block AY(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1558449075)
      goto BQ
   2. goto T
      -> UnconditionalJump[GOTO] #AY -> #T
      -> ConditionalJump[IF_ICMPNE] #AY -> #BQ
      <- TryCatch range: [AX...AW] -> AY ([Ljava/io/IOException;])
      <- TryCatch range: [AX...AW] -> AY ([Ljava/io/IOException;])
===#Block L(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar51 = lvar7;
   2. lvar70 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yhkazdalezultca(), lvar78);
   3. lvar52 = lvar51.equals(lvar70);
   4. if (lvar52 == {454434856 ^ lvar78})
      goto T
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -659488448)
      goto CB
      -> Immediate #L -> #M
      -> ConditionalJump[IF_ICMPEQ] #L -> #T
      -> ConditionalJump[IF_ICMPNE] #L -> #CB
      <- Switch[227817807] #E -> #L
===#Block CB(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -659488448)
      goto CB
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1662068816 ^ lvar78})
      goto CB
   2. _consume({919460236 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #CB -> #CB
      <- ConditionalJump[IF_ICMPNE] #CB -> #CB
      <- ConditionalJump[IF_ICMPNE] #L -> #CB
===#Block M(size=4, flags=0)===
   0. lvar53 = {495822276 ^ lvar78};
   1. lvar8 = lvar53;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -328389120)
      goto BO
   3. goto AI
      -> UnconditionalJump[GOTO] #M -> #AI
      -> ConditionalJump[IF_ICMPNE] #M -> #BO
      <- Immediate #L -> #M
===#Block AI(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 10889821)
      goto AH
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -647697102)
      goto BH
      -> ConditionalJump[IF_ICMPEQ] #AI -> #AH
      -> TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPNE] #AI -> #BH
      <- UnconditionalJump[GOTO] #M -> #AI
===#Block AH(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 856142680)
      goto BV
      -> TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPNE] #AH -> #BV
      <- ConditionalJump[IF_ICMPEQ] #AI -> #AH
===#Block BV(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 856142680)
      goto BV
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1413532324 ^ lvar78})
      goto BV
   2. _consume({1900707578 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BV -> #BV
      <- ConditionalJump[IF_ICMPNE] #BV -> #BV
      <- ConditionalJump[IF_ICMPNE] #AH -> #BV
===#Block AJ(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486043016)
      goto BW
   2. goto T
      -> ConditionalJump[IF_ICMPNE] #AJ -> #BW
      -> UnconditionalJump[GOTO] #AJ -> #T
      <- TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
===#Block BW(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486043016)
      goto BW
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {904193609 ^ lvar78})
      goto BW
   2. _consume({415347106 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BW -> #BW
      <- ConditionalJump[IF_ICMPNE] #AJ -> #BW
      <- ConditionalJump[IF_ICMPNE] #BW -> #BW
===#Block N(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar54 = lvar7;
   2. lvar71 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yjxsfpuegqomqtc(), lvar78);
   3. lvar55 = lvar54.equals(lvar71);
   4. if (lvar55 == {1725606299 ^ lvar78})
      goto T
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 919948507)
      goto BE
      -> ConditionalJump[IF_ICMPNE] #N -> #BE
      -> ConditionalJump[IF_ICMPEQ] #N -> #T
      -> Immediate #N -> #O
      <- Switch[156907340] #E -> #N
===#Block O(size=4, flags=0)===
   0. lvar56 = {486010004 ^ lvar78};
   1. lvar8 = lvar56;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -406887256)
      goto BB
   3. goto AR
      -> UnconditionalJump[GOTO] #O -> #AR
      -> ConditionalJump[IF_ICMPNE] #O -> #BB
      <- Immediate #N -> #O
===#Block AR(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 65816982)
      goto AQ
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1518713057)
      goto BQ
      -> ConditionalJump[IF_ICMPEQ] #AR -> #AQ
      -> ConditionalJump[IF_ICMPNE] #AR -> #BQ
      -> TryCatch range: [AR...AQ] -> AS ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #O -> #AR
===#Block BQ(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1558449075)
      goto BQ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {341426397 ^ lvar78})
      goto BQ
   2. _consume({368654051 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1518713057)
      goto BQ
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1905219472 ^ lvar78})
      goto BQ
   5. _consume({1402203533 ^ lvar78});
   6. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BQ -> #BQ
      <- ConditionalJump[IF_ICMPNE] #BQ -> #BQ
      <- ConditionalJump[IF_ICMPNE] #AY -> #BQ
      <- ConditionalJump[IF_ICMPNE] #AR -> #BQ
===#Block AQ(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1591771375)
      goto BB
      -> TryCatch range: [AR...AQ] -> AS ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPNE] #AQ -> #BB
      <- ConditionalJump[IF_ICMPEQ] #AR -> #AQ
===#Block BB(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1591771375)
      goto BB
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {558263189 ^ lvar78})
      goto BB
   2. _consume({336583340 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -406887256)
      goto BB
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {495698447 ^ lvar78})
      goto BB
   5. _consume({754669794 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1270233501)
      goto BB
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1300752702 ^ lvar78})
      goto BB
   8. _consume({1160696589 ^ lvar78});
   9. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #O -> #BB
      <- ConditionalJump[IF_ICMPNE] #AQ -> #BB
      <- ConditionalJump[IF_ICMPNE] #H -> #BB
===#Block AS(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -150142167)
      goto AZ
   2. goto T
      -> UnconditionalJump[GOTO] #AS -> #T
      -> ConditionalJump[IF_ICMPNE] #AS -> #AZ
      <- TryCatch range: [AR...AQ] -> AS ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AR...AQ] -> AS ([Ljava/lang/RuntimeException;])
===#Block BE(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 141206393)
      goto BE
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {443605690 ^ lvar78})
      goto BE
   2. _consume({63999251 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 919948507)
      goto BE
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {170860988 ^ lvar78})
      goto BE
   5. _consume({1445069689 ^ lvar78});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BE -> #BE
      <- ConditionalJump[IF_ICMPNE] #N -> #BE
      <- ConditionalJump[IF_ICMPNE] #BE -> #BE
      <- ConditionalJump[IF_ICMPNE] #AC -> #BE
===#Block P(size=6, flags=0)===
   0. // Frame: locals[6] [com/hang/plugin/manager/TreasureItemManager, java/lang/String, java/lang/IllegalArgumentException, java/lang/String, java/lang/String, 1] stack[0] []
   1. lvar57 = lvar7;
   2. lvar72 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.ftzbearmuokvzqe(), lvar78);
   3. lvar58 = lvar57.equals(lvar72);
   4. if (lvar58 == {1462798918 ^ lvar78})
      goto T
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1182510542)
      goto AZ
      -> Immediate #P -> #Q
      -> ConditionalJump[IF_ICMPEQ] #P -> #T
      -> ConditionalJump[IF_ICMPNE] #P -> #AZ
      <- Switch[110867538] #E -> #P
===#Block Q(size=4, flags=0)===
   0. lvar59 = {1815796856 ^ lvar78};
   1. lvar8 = lvar59;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1641472963)
      goto BC
   3. goto AU
      -> ConditionalJump[IF_ICMPNE] #Q -> #BC
      -> UnconditionalJump[GOTO] #Q -> #AU
      <- Immediate #P -> #Q
===#Block AU(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 44193335)
      goto AT
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1480146613)
      goto BD
      -> ConditionalJump[IF_ICMPNE] #AU -> #BD
      -> TryCatch range: [AU...AT] -> AV ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AU -> #AT
      <- UnconditionalJump[GOTO] #Q -> #AU
===#Block AT(size=2, flags=0)===
   0. throw new java/lang/IllegalAccessException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1671324573)
      goto BI
      -> TryCatch range: [AU...AT] -> AV ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPNE] #AT -> #BI
      <- ConditionalJump[IF_ICMPEQ] #AU -> #AT
===#Block BI(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1671324573)
      goto BI
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {713909545 ^ lvar78})
      goto BI
   2. _consume({1156449947 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- ConditionalJump[IF_ICMPNE] #AT -> #BI
===#Block AV(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 752998531)
      goto BO
   2. goto T
      -> ConditionalJump[IF_ICMPNE] #AV -> #BO
      -> UnconditionalJump[GOTO] #AV -> #T
      <- TryCatch range: [AU...AT] -> AV ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AU...AT] -> AV ([Ljava/lang/IllegalAccessException;])
===#Block BO(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 752998531)
      goto BO
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1881659440 ^ lvar78})
      goto BO
   2. _consume({547062424 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -328389120)
      goto BO
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {753325861 ^ lvar78})
      goto BO
   5. _consume({946477553 ^ lvar78});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BO -> #BO
      <- ConditionalJump[IF_ICMPNE] #AV -> #BO
      <- ConditionalJump[IF_ICMPNE] #BO -> #BO
      <- ConditionalJump[IF_ICMPNE] #M -> #BO
===#Block BC(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1929495146)
      goto BC
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {231884989 ^ lvar78})
      goto BC
   2. _consume({1462970804 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1641472963)
      goto BC
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {478421264 ^ lvar78})
      goto BC
   5. _consume({317227234 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 433749064)
      goto BC
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {160018041 ^ lvar78})
      goto BC
   8. _consume({2125906394 ^ lvar78});
   9. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- ConditionalJump[IF_ICMPNE] #J -> #BC
      <- ConditionalJump[IF_ICMPNE] #Q -> #BC
      <- ConditionalJump[IF_ICMPNE] #F -> #BC
===#Block R(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar60 = lvar7;
   2. lvar73 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.bectnpvogneupnu(), lvar78);
   3. lvar61 = lvar60.equals(lvar73);
   4. if (lvar61 == {1936643957 ^ lvar78})
      goto T
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1686717525)
      goto BF
      -> Immediate #R -> #S
      -> ConditionalJump[IF_ICMPEQ] #R -> #T
      -> ConditionalJump[IF_ICMPNE] #R -> #BF
      <- Switch[24472792] #E -> #R
===#Block S(size=4, flags=0)===
   0. lvar62 = {1281536602 ^ lvar78};
   1. lvar8 = lvar62;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1662358266)
      goto BS
   3. goto AO
      -> ConditionalJump[IF_ICMPNE] #S -> #BS
      -> UnconditionalJump[GOTO] #S -> #AO
      <- Immediate #R -> #S
===#Block AO(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 79360532)
      goto AN
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 466195017)
      goto BJ
      -> ConditionalJump[IF_ICMPEQ] #AO -> #AN
      -> ConditionalJump[IF_ICMPNE] #AO -> #BJ
      -> TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #S -> #AO
===#Block AN(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -766768223)
      goto BN
      -> ConditionalJump[IF_ICMPNE] #AN -> #BN
      -> TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AO -> #AN
===#Block AP(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1498308240)
      goto BM
   2. goto T
      -> ConditionalJump[IF_ICMPNE] #AP -> #BM
      -> UnconditionalJump[GOTO] #AP -> #T
      <- TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
===#Block T(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar8;
   2. svar80 = {lvar22 ^ lvar78};
   3. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(svar80)) {
      case 28214768:
      	 goto	#AD
      case 28214769:
      	 goto	#AF
      case 28214770:
      	 goto	#V
      case 28214771:
      	 goto	#W
      case 28214780:
      	 goto	#U
      case 28214782:
      	 goto	#AE
      default:
      	 goto	#X
   }
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 240762280)
      goto BU
      -> Switch[28214769] #T -> #AF
      -> Switch[28214782] #T -> #AE
      -> Switch[28214768] #T -> #AD
      -> DefaultSwitch #T -> #X
      -> Switch[28214771] #T -> #W
      -> Switch[28214770] #T -> #V
      -> Switch[28214780] #T -> #U
      -> ConditionalJump[IF_ICMPNE] #T -> #BU
      <- ConditionalJump[IF_ICMPEQ] #H -> #T
      <- ConditionalJump[IF_ICMPEQ] #R -> #T
      <- UnconditionalJump[GOTO] #AY -> #T
      <- DefaultSwitch #E -> #T
      <- UnconditionalJump[GOTO] #AS -> #T
      <- UnconditionalJump[GOTO] #AP -> #T
      <- Immediate #I -> #T
      <- ConditionalJump[IF_ICMPEQ] #P -> #T
      <- ConditionalJump[IF_ICMPEQ] #N -> #T
      <- UnconditionalJump[GOTO] #AJ -> #T
      <- UnconditionalJump[GOTO] #AV -> #T
      <- ConditionalJump[IF_ICMPEQ] #F -> #T
      <- UnconditionalJump[GOTO] #AM -> #T
      <- ConditionalJump[IF_ICMPEQ] #J -> #T
      <- ConditionalJump[IF_ICMPEQ] #L -> #T
===#Block BU(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 240762280)
      goto BU
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2041776176 ^ lvar78})
      goto BU
   2. _consume({282149557 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BU -> #BU
      <- ConditionalJump[IF_ICMPNE] #BU -> #BU
      <- ConditionalJump[IF_ICMPNE] #T -> #BU
===#Block U(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar23 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.prtwnvioasyjvha(), lvar78);
   2. lvar24 = org.bukkit.Material.valueOf(lvar23);
   3. return lvar24;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 320417289)
      goto AZ
      -> ConditionalJump[IF_ICMPNE] #U -> #AZ
      <- Switch[28214780] #T -> #U
===#Block AZ(size=13, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 320417289)
      goto AZ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {481149474 ^ lvar78})
      goto AZ
   2. _consume({1065549739 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -150142167)
      goto AZ
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1202868137 ^ lvar78})
      goto AZ
   5. _consume({2050735800 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1182510542)
      goto AZ
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {972456556 ^ lvar78})
      goto AZ
   8. _consume({1571224845 ^ lvar78});
   9. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1487427512)
      goto AZ
   10. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {662728743 ^ lvar78})
      goto AZ
   11. _consume({1205739256 ^ lvar78});
   12. throw new java/lang/RuntimeException();
      -> ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AS -> #AZ
      <- ConditionalJump[IF_ICMPNE] #U -> #AZ
      <- ConditionalJump[IF_ICMPNE] #I -> #AZ
      <- ConditionalJump[IF_ICMPNE] #P -> #AZ
===#Block V(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.ovegptpfdypksny(), lvar78);
   2. lvar26 = org.bukkit.Material.valueOf(lvar25);
   3. return lvar26;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 835178306)
      goto BX
      -> ConditionalJump[IF_ICMPNE] #V -> #BX
      <- Switch[28214770] #T -> #V
===#Block BX(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 835178306)
      goto BX
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {70327400 ^ lvar78})
      goto BX
   2. _consume({2060233287 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BX -> #BX
      <- ConditionalJump[IF_ICMPNE] #BX -> #BX
      <- ConditionalJump[IF_ICMPNE] #V -> #BX
===#Block W(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.cqllbpqxzblrrxy(), lvar78);
   2. lvar28 = org.bukkit.Material.valueOf(lvar27);
   3. return lvar28;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1242378807)
      goto CA
      -> ConditionalJump[IF_ICMPNE] #W -> #CA
      <- Switch[28214771] #T -> #W
===#Block CA(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1242378807)
      goto CA
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1275584679 ^ lvar78})
      goto CA
   2. _consume({1531933032 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #CA -> #CA
      <- ConditionalJump[IF_ICMPNE] #CA -> #CA
      <- ConditionalJump[IF_ICMPNE] #W -> #CA
===#Block X(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar1;
   2. lvar30 = org.bukkit.Material.matchMaterial(lvar29);
   3. lvar9 = lvar30;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -523811990)
      goto BJ
      -> Immediate #X -> #Y
      -> ConditionalJump[IF_ICMPNE] #X -> #BJ
      <- DefaultSwitch #T -> #X
===#Block BJ(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -523811990)
      goto BJ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {937614200 ^ lvar78})
      goto BJ
   2. _consume({1153887896 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 458390410)
      goto BJ
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2080180205 ^ lvar78})
      goto BJ
   5. _consume({954370257 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 466195017)
      goto BJ
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1229602279 ^ lvar78})
      goto BJ
   8. _consume({1354336768 ^ lvar78});
   9. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BJ -> #BJ
      <- ConditionalJump[IF_ICMPNE] #BJ -> #BJ
      <- ConditionalJump[IF_ICMPNE] #AO -> #BJ
      <- ConditionalJump[IF_ICMPNE] #G -> #BJ
      <- ConditionalJump[IF_ICMPNE] #X -> #BJ
===#Block Y(size=3, flags=0)===
   0. lvar31 = lvar9;
   1. if (lvar31 == nullconst)
      goto Z
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -138722040)
      goto BH
      -> ConditionalJump[IF_ICMPNE] #Y -> #BH
      -> Immediate #Y -> #AB
      -> ConditionalJump[IFNULL] #Y -> #Z
      <- Immediate #X -> #Y
===#Block Z(size=15, flags=0)===
   0. // Frame: locals[1] [org/bukkit/Material] stack[0] []
   1. lvar32 = lvar0;
   2. lvar33 = lvar32.plugin;
   3. lvar34 = lvar33.getLogger();
   4. lvar63 = new java.lang.StringBuilder;
   5. _consume(lvar63.<init>());
   6. lvar74 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yibvjbwmhuiuhns(), lvar78);
   7. lvar64 = lvar63.append(lvar74);
   8. lvar75 = lvar1;
   9. lvar65 = lvar64.append(lvar75);
   10. lvar76 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.tzqznptxyjijuzc(), lvar78);
   11. lvar66 = lvar65.append(lvar76);
   12. lvar67 = lvar66.toString();
   13. _consume(lvar34.warning(lvar67));
   14. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -187189448)
      goto BF
      -> ConditionalJump[IF_ICMPNE] #Z -> #BF
      -> Immediate #Z -> #AA
      <- ConditionalJump[IFNULL] #Y -> #Z
===#Block AA(size=3, flags=0)===
   0. lvar35 = org.bukkit.Material.STONE;
   1. return lvar35;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -342113063)
      goto CE
      -> ConditionalJump[IF_ICMPNE] #AA -> #CE
      <- Immediate #Z -> #AA
===#Block CE(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -342113063)
      goto CE
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1309035836 ^ lvar78})
      goto CE
   2. _consume({1502736793 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #CE -> #CE
      <- ConditionalJump[IF_ICMPNE] #AA -> #CE
      <- ConditionalJump[IF_ICMPNE] #CE -> #CE
===#Block AB(size=3, flags=0)===
   0. lvar36 = lvar9;
   1. return lvar36;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -227253213)
      goto BA
      -> ConditionalJump[IF_ICMPNE] #AB -> #BA
      <- Immediate #Y -> #AB
===#Block BA(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -227253213)
      goto BA
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1732202856 ^ lvar78})
      goto BA
   2. _consume({1268268387 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #AB -> #BA
===#Block BH(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 854964034)
      goto BH
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {409650150 ^ lvar78})
      goto BH
   2. _consume({311993592 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -138722040)
      goto BH
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1748883515 ^ lvar78})
      goto BH
   5. _consume({2038232330 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -647697102)
      goto BH
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2068093208 ^ lvar78})
      goto BH
   8. _consume({1519443261 ^ lvar78});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BH -> #BH
      <- ConditionalJump[IF_ICMPNE] #AL -> #BH
      <- ConditionalJump[IF_ICMPNE] #Y -> #BH
      <- ConditionalJump[IF_ICMPNE] #BH -> #BH
      <- ConditionalJump[IF_ICMPNE] #AI -> #BH
===#Block AD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar39 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.utdmhhkwoahmkaq(), lvar78);
   2. lvar40 = org.bukkit.Material.valueOf(lvar39);
   3. return lvar40;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2095522984)
      goto BF
      -> ConditionalJump[IF_ICMPNE] #AD -> #BF
      <- Switch[28214768] #T -> #AD
===#Block BF(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2095522984)
      goto BF
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {391487732 ^ lvar78})
      goto BF
   2. _consume({633188083 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -187189448)
      goto BF
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1310160907 ^ lvar78})
      goto BF
   5. _consume({1763035083 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1686717525)
      goto BF
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {142400897 ^ lvar78})
      goto BF
   8. _consume({1117214829 ^ lvar78});
   9. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BF -> #BF
      <- ConditionalJump[IF_ICMPNE] #BF -> #BF
      <- ConditionalJump[IF_ICMPNE] #R -> #BF
      <- ConditionalJump[IF_ICMPNE] #Z -> #BF
      <- ConditionalJump[IF_ICMPNE] #AD -> #BF
===#Block AE(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar41 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.eacruwlloaiuxmf(), lvar78);
   2. lvar42 = org.bukkit.Material.valueOf(lvar41);
   3. return lvar42;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 122835440)
      goto BY
      -> ConditionalJump[IF_ICMPNE] #AE -> #BY
      <- Switch[28214782] #T -> #AE
===#Block BY(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 122835440)
      goto BY
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1518476571 ^ lvar78})
      goto BY
   2. _consume({961816527 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BY -> #BY
      <- ConditionalJump[IF_ICMPNE] #BY -> #BY
      <- ConditionalJump[IF_ICMPNE] #AE -> #BY
===#Block AF(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar43 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.wurcyacdyjmgmou(), lvar78);
   2. lvar44 = org.bukkit.Material.valueOf(lvar43);
   3. return lvar44;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 210995642)
      goto BR
      -> ConditionalJump[IF_ICMPNE] #AF -> #BR
      <- Switch[28214769] #T -> #AF
===#Block BR(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 210995642)
      goto BR
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {661958823 ^ lvar78})
      goto BR
   2. _consume({1272044175 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BR -> #BR
      <- ConditionalJump[IF_ICMPNE] #BR -> #BR
      <- ConditionalJump[IF_ICMPNE] #AF -> #BR
===#Block BM(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1498308240)
      goto BM
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1124462258 ^ lvar78})
      goto BM
   2. _consume({1508363018 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BM -> #BM
      <- ConditionalJump[IF_ICMPNE] #BM -> #BM
      <- ConditionalJump[IF_ICMPNE] #AP -> #BM
===#Block BN(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -766768223)
      goto BN
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {125397084 ^ lvar78})
      goto BN
   2. _consume({1023240847 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1900412743)
      goto BN
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1211717695 ^ lvar78})
      goto BN
   5. _consume({519105134 ^ lvar78});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BN -> #BN
      <- ConditionalJump[IF_ICMPNE] #BN -> #BN
      <- ConditionalJump[IF_ICMPNE] #K -> #BN
      <- ConditionalJump[IF_ICMPNE] #AN -> #BN
===#Block BS(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1662358266)
      goto BS
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {146954481 ^ lvar78})
      goto BS
   2. _consume({286671700 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BS -> #BS
      <- ConditionalJump[IF_ICMPNE] #S -> #BS
      <- ConditionalJump[IF_ICMPNE] #BS -> #BS
===#Block BG(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1511030039)
      goto BG
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1840155363 ^ lvar78})
      goto BG
   2. _consume({2114551706 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- ConditionalJump[IF_ICMPNE] #D -> #BG
===#Block BT(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1510108390)
      goto BT
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {485002264 ^ lvar78})
      goto BT
   2. _consume({2092454279 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BT -> #BT
      <- ConditionalJump[IF_ICMPNE] #B -> #BT
      <- ConditionalJump[IF_ICMPNE] #BT -> #BT
===#Block AG(size=2, flags=0)===
   0. return lvar11;
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1391758064)
      goto BD
      -> ConditionalJump[IF_ICMPNE] #AG -> #BD
      <- Immediate #B -> #AG
===#Block BD(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1968181750)
      goto BD
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1198503486 ^ lvar78})
      goto BD
   2. _consume({1981879217 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1480146613)
      goto BD
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2096896243 ^ lvar78})
      goto BD
   5. _consume({892111720 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1391758064)
      goto BD
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1266179455 ^ lvar78})
      goto BD
   8. _consume({1162172891 ^ lvar78});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BD -> #BD
      <- ConditionalJump[IF_ICMPNE] #BD -> #BD
      <- ConditionalJump[IF_ICMPNE] #AU -> #BD
      <- ConditionalJump[IF_ICMPNE] #E -> #BD
      <- ConditionalJump[IF_ICMPNE] #AG -> #BD
===#Block BK(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1703993696)
      goto BK
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {997869065 ^ lvar78})
      goto BK
   2. _consume({204671798 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BK -> #BK
      <- ConditionalJump[IF_ICMPNE] #A -> #BK
      <- ConditionalJump[IF_ICMPNE] #BK -> #BK
