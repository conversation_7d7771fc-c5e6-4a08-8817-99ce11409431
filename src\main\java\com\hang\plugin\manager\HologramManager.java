package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.utils.VersionUtils;
import org.bukkit.Location;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 浮空字管理器
 * 负责创建、更新和清理浮空字显示
 */
public class HologramManager {

    private final HangPlugin plugin;
    private final Map<String, ArmorStand> holograms = new ConcurrentHashMap<>();
    private final Map<String, BukkitTask> updateTasks = new ConcurrentHashMap<>();

    // 区块卸载保护：存储浮空字的位置和文本信息
    private final Map<String, HologramData> hologramBackups = new ConcurrentHashMap<>();
    private BukkitTask chunkCheckTask;

    public HologramManager(HangPlugin plugin) {
        this.plugin = plugin;

        // 启动区块检查任务，防止浮空字因区块卸载而卡住
        startChunkCheckTask();
    }

    /**
     * 创建浮空字
     * 修复：添加世界检查，避免在无效世界中创建浮空字
     * 新增：支持自定义配置
     */
    public void createHologram(String id, Location location, String text) {
        // 检查世界是否存在
        if (location.getWorld() == null) {
            plugin.getLogger().warning("无法创建浮空字：世界不存在 - " + id);
            return;
        }

        // 修复：检查是否已存在有效的浮空字，避免重复创建
        ArmorStand existingHologram = holograms.get(id);
        if (existingHologram != null && !existingHologram.isDead()) {
            // 浮空字已存在且有效，只更新文本
            updateHologram(id, text);
            return;
        }

        // 清理已存在但无效的浮空字
        removeHologram(id);

        try {
            // 检查区块是否已加载
            if (!location.getChunk().isLoaded()) {
                // 检查是否启用强制加载区块
                if (plugin.getConfig().getBoolean("treasure-chest.hologram.chunk_protection.force_load_chunks", true)) {
                    location.getChunk().load();
                    plugin.getLogger().info("为浮空字强制加载区块: " + id);
                } else {
                    plugin.getLogger().warning("浮空字所在区块未加载，跳过创建: " + id);
                    return;
                }
            }

            // 从配置文件读取位置偏移
            double offsetX = plugin.getConfig().getDouble("treasure-chest.hologram.position.x", 0.5);
            double offsetY = plugin.getConfig().getDouble("treasure-chest.hologram.position.y", 1.5);
            double offsetZ = plugin.getConfig().getDouble("treasure-chest.hologram.position.z", 0.5);

            Location hologramLocation = location.clone().add(offsetX, offsetY, offsetZ);

            // 创建新的浮空字
            ArmorStand armorStand = (ArmorStand) location.getWorld().spawnEntity(
                hologramLocation, EntityType.ARMOR_STAND);

            // 使用浮空字最佳实践设置（固定值）
            armorStand.setVisible(false);        // 隐藏盔甲架本体
            armorStand.setGravity(false);        // 禁用重力
            armorStand.setCanPickupItems(false); // 禁止拾取物品
            armorStand.setCustomName(text);
            armorStand.setCustomNameVisible(true);
            armorStand.setMarker(true);          // 标记模式（无碰撞箱）
            armorStand.setSmall(true);           // 小型盔甲架

            holograms.put(id, armorStand);

            // 备份浮空字数据，防止区块卸载丢失（使用固定的最佳实践值）
            hologramBackups.put(id, new HologramData(hologramLocation, text,
                true, true, true, true, true)); // invisible, noGravity, noPickup, marker, small

        } catch (Exception e) {
            plugin.getLogger().warning("创建浮空字失败 (" + id + "): " + e.getMessage());
        }
    }

    /**
     * 更新浮空字文本
     * 1.16.5修复：增强实体状态检查和文本更新
     */
    public void updateHologram(String id, String text) {
        ArmorStand hologram = holograms.get(id);
        if (hologram != null && !hologram.isDead()) {
            // 1.16.5修复：检查实体是否真的有效
            try {
                // 尝试获取实体位置，如果实体无效会抛出异常
                hologram.getLocation();

                // 1.16.5修复：强制刷新CustomName可见性
                if (VersionUtils.isVersionAtLeast(1, 16) && !VersionUtils.isVersionAtLeast(1, 17)) {
                    hologram.setCustomNameVisible(false);
                    hologram.setCustomName(text);
                    hologram.setCustomNameVisible(true);
                } else {
                    hologram.setCustomName(text);
                }

                // 同时更新备份数据
                HologramData backup = hologramBackups.get(id);
                if (backup != null) {
                    backup.setText(text);
                }
            } catch (Exception e) {
                // 实体无效，移除并尝试重建
                holograms.remove(id);
                HologramData backup = hologramBackups.get(id);
                if (backup != null) {
                    // 调试模式下显示重建日志
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().info("检测到浮空字实体无效，尝试重建: " + id);
                    }
                    recreateHologramFromBackup(id, text);
                }
            }
        } else {
            // 如果浮空字实体丢失，尝试从备份重建
            HologramData backup = hologramBackups.get(id);
            if (backup != null) {
                // 调试模式下显示重建日志
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("检测到浮空字丢失，尝试重建: " + id);
                }
                recreateHologramFromBackup(id, text);
            }
        }
    }

    /**
     * 移除浮空字
     * 修复：增强版本兼容性和清理机制
     */
    public void removeHologram(String id) {
        // 修复：安全移除主要浮空字实体
        ArmorStand hologram = holograms.remove(id);
        if (hologram != null) {
            try {
                // 版本兼容性：检查实体是否仍然有效
                if (!hologram.isDead()) {
                    // 1.16.x版本特殊处理：先隐藏再移除
                    if (com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 16) &&
                        !com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 17)) {
                        try {
                            hologram.setCustomNameVisible(false);
                            hologram.setVisible(false);
                        } catch (Exception e) {
                            // 忽略设置错误
                        }
                    }
                    hologram.remove();
                }
            } catch (Exception e) {
                // 修复：如果主要移除失败，记录但继续清理
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().warning("移除主要浮空字实体时出错: " + e.getMessage());
                }
            }
        }

        // 停止更新任务
        BukkitTask task = updateTasks.remove(id);
        if (task != null) {
            try {
                task.cancel();
            } catch (Exception e) {
                // 忽略任务取消错误
            }
        }

        // 修复：增强的残留实体清理（版本兼容）
        try {
            HologramData backup = hologramBackups.get(id);
            if (backup != null) {
                Location location = backup.getLocation();

                // 版本兼容性：检查世界是否存在
                if (location.getWorld() == null) {
                    plugin.getLogger().warning("浮空字位置的世界不存在，跳过残留清理: " + id);
                } else {
                    // 版本兼容性：使用安全的实体检测方法
                    cleanupRemainingEntities(location, id);
                }
            }
        } catch (Exception e) {
            // 修复：记录清理错误但不影响主要功能
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().warning("清理残留浮空字实体时出错: " + e.getMessage());
            }
        }

        // 同时移除备份数据
        hologramBackups.remove(id);
    }

    /**
     * 清理残留的浮空字实体（版本兼容）
     */
    private void cleanupRemainingEntities(Location location, String id) {
        try {
            // 版本兼容性：使用不同的实体检测方法
            java.util.Collection<org.bukkit.entity.Entity> nearbyEntities;

            if (com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 13)) {
                // 1.13+版本：使用新的API
                nearbyEntities = location.getWorld().getNearbyEntities(location, 1, 1, 1);
            } else {
                // 1.8-1.12版本：使用旧的API
                nearbyEntities = location.getWorld().getEntities();
            }

            for (org.bukkit.entity.Entity entity : nearbyEntities) {
                if (entity instanceof ArmorStand) {
                    ArmorStand armorStand = (ArmorStand) entity;

                    // 版本兼容性：安全检查实体属性
                    try {
                        // 检查是否是浮空字（有自定义名称且可见）
                        if (armorStand.isCustomNameVisible() && armorStand.getCustomName() != null) {
                            // 检查位置是否匹配（允许小误差）
                            double distance = armorStand.getLocation().distance(location);
                            if (distance < 0.5) {
                                // 1.16.x版本特殊处理
                                if (com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 16) &&
                                    !com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 17)) {
                                    armorStand.setCustomNameVisible(false);
                                    armorStand.setVisible(false);
                                }

                                armorStand.remove();

                                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                    plugin.getLogger().info("🧹 清理了残留的浮空字实体: " + id + " (距离: " + String.format("%.2f", distance) + ")");
                                }
                            }
                        }
                    } catch (Exception e) {
                        // 修复：单个实体检查失败不影响其他实体
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().warning("检查残留实体时出错: " + e.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 修复：整体清理失败的处理
            plugin.getLogger().warning("清理残留实体时出错: " + e.getMessage());
        }
    }

    /**
     * 创建带更新任务的浮空字
     */
    public void createUpdatingHologram(String id, Location location, String initialText,
                                     HologramUpdater updater, long updateInterval) {
        createHologram(id, location, initialText);

        BukkitTask task = new BukkitRunnable() {
            @Override
            public void run() {
                ArmorStand hologram = holograms.get(id);
                if (hologram == null || hologram.isDead()) {
                    this.cancel();
                    updateTasks.remove(id);
                    return;
                }

                String newText = updater.getUpdatedText();
                if (newText != null) {
                    updateHologram(id, newText);
                } else {
                    // 如果返回null，移除浮空字
                    removeHologram(id);
                    this.cancel();
                }
            }
        }.runTaskTimer(plugin, updateInterval, updateInterval);

        updateTasks.put(id, task);
    }

    /**
     * 检查浮空字是否存在
     */
    public boolean hasHologram(String id) {
        ArmorStand hologram = holograms.get(id);
        return hologram != null && !hologram.isDead();
    }

    /**
     * 获取浮空字位置
     */
    public Location getHologramLocation(String id) {
        ArmorStand hologram = holograms.get(id);
        if (hologram != null && !hologram.isDead()) {
            return hologram.getLocation();
        }
        return null;
    }

    /**
     * 清理所有浮空字
     * 修复：增强版本兼容性和安全清理
     */
    public void cleanup() {
        plugin.getLogger().info("开始清理所有浮空字...");

        // 停止区块检查任务
        if (chunkCheckTask != null) {
            try {
                chunkCheckTask.cancel();
                chunkCheckTask = null;
            } catch (Exception e) {
                plugin.getLogger().warning("停止区块检查任务时出错: " + e.getMessage());
            }
        }

        // 停止所有更新任务
        int taskCount = 0;
        for (BukkitTask task : updateTasks.values()) {
            if (task != null) {
                try {
                    task.cancel();
                    taskCount++;
                } catch (Exception e) {
                    // 忽略单个任务取消错误
                }
            }
        }
        updateTasks.clear();

        if (taskCount > 0) {
            plugin.getLogger().info("已停止 " + taskCount + " 个浮空字更新任务");
        }

        // 修复：安全移除所有浮空字实体（版本兼容）
        int removedCount = 0;
        for (ArmorStand hologram : holograms.values()) {
            if (hologram != null) {
                try {
                    if (!hologram.isDead()) {
                        // 1.16.x版本特殊处理
                        if (com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 16) &&
                            !com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 17)) {
                            try {
                                hologram.setCustomNameVisible(false);
                                hologram.setVisible(false);
                            } catch (Exception e) {
                                // 忽略设置错误
                            }
                        }
                        hologram.remove();
                        removedCount++;
                    }
                } catch (Exception e) {
                    // 修复：单个实体移除失败不影响其他实体
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().warning("移除浮空字实体时出错: " + e.getMessage());
                    }
                }
            }
        }
        holograms.clear();

        if (removedCount > 0) {
            plugin.getLogger().info("已移除 " + removedCount + " 个浮空字实体");
        }

        // 清理备份数据
        int backupCount = hologramBackups.size();
        hologramBackups.clear();

        if (backupCount > 0) {
            plugin.getLogger().info("已清理 " + backupCount + " 个浮空字备份数据");
        }

        plugin.getLogger().info("浮空字清理完成");
    }

    /**
     * 创建或更新浮空字（兼容旧API）
     */
    public void createOrUpdateHologram(Location location, String text) {
        // 修复：检查浮空字是否启用
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            // 如果浮空字被禁用，移除现有的浮空字
            removeHologram(location);
            return;
        }

        String id = locationToId(location);

        // 修复：检查是否已存在浮空字，如果存在则更新，否则创建
        ArmorStand existingHologram = holograms.get(id);
        if (existingHologram != null && !existingHologram.isDead()) {
            // 浮空字已存在，只更新文本
            updateHologram(id, text);
        } else {
            // 浮空字不存在或已死亡，创建新的
            createHologram(id, location, text);
        }
    }

    /**
     * 移除指定位置的浮空字（兼容旧API）
     */
    public void removeHologram(Location location) {
        String id = locationToId(location);
        removeHologram(id);
    }

    /**
     * 将位置转换为ID
     */
    private String locationToId(Location location) {
        return location.getWorld().getName() + "_" +
               location.getBlockX() + "_" +
               location.getBlockY() + "_" +
               location.getBlockZ();
    }

    /**
     * 移除所有浮空字
     */
    public void removeAllHolograms() {
        for (ArmorStand armorStand : holograms.values()) {
            if (armorStand != null && !armorStand.isDead()) {
                armorStand.remove();
            }
        }
        holograms.clear();
    }

    /**
     * 更新所有浮空字的样式（使用固定的最佳实践设置）
     */
    public void updateAllHologramsStyle() {
        for (ArmorStand armorStand : holograms.values()) {
            if (armorStand != null && !armorStand.isDead()) {
                try {
                    armorStand.setVisible(false);        // 隐藏盔甲架本体
                    armorStand.setGravity(false);        // 禁用重力
                    armorStand.setCanPickupItems(false); // 禁止拾取物品
                    armorStand.setMarker(true);          // 标记模式（无碰撞箱）
                    armorStand.setSmall(true);           // 小型盔甲架
                } catch (Exception e) {
                    plugin.getLogger().warning("更新浮空字样式时出错: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 启动区块检查任务，防止浮空字因区块卸载而丢失
     */
    private void startChunkCheckTask() {
        // 修复：如果浮空字功能禁用，完全跳过区块检查任务
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            plugin.getLogger().info("浮空字功能已禁用，跳过区块检查任务");
            return;
        }

        // 检查是否启用区块保护
        if (!plugin.getConfig().getBoolean("treasure-chest.hologram.chunk_protection.enabled", true)) {
            plugin.getLogger().info("区块卸载保护已禁用");
            return;
        }

        // 从配置读取检查间隔
        long checkInterval = plugin.getConfig().getLong("treasure-chest.hologram.chunk_protection.check_interval", 600L);

        chunkCheckTask = new BukkitRunnable() {
            @Override
            public void run() {
                // 修复：在任务执行时再次检查浮空字是否启用
                if (!plugin.getTreasureItemManager().isHologramEnabled()) {
                    // 浮空字被禁用，停止任务
                    this.cancel();
                    chunkCheckTask = null;
                    return;
                }
                checkAndRecreateHolograms();
            }
        }.runTaskTimer(plugin, checkInterval, checkInterval);

        // 只在调试模式下显示区块卸载保护启动信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("区块卸载保护已启动，检查间隔: " + (checkInterval / 20.0) + "秒");
        }
    }

    /**
     * 检查并重建丢失的浮空字
     */
    private void checkAndRecreateHolograms() {
        // 修复：如果浮空字功能禁用，完全跳过检查和重建
        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
            return;
        }

        int recreatedCount = 0;

        for (Map.Entry<String, HologramData> entry : hologramBackups.entrySet()) {
            String id = entry.getKey();
            HologramData backup = entry.getValue();

            ArmorStand hologram = holograms.get(id);

            // 检查浮空字是否丢失或死亡
            if (hologram == null || hologram.isDead()) {
                // 检查区块是否加载
                if (backup.getLocation().getChunk().isLoaded()) {
                    // 从配置读取玩家检测范围
                    double detectionRange = plugin.getConfig().getDouble("treasure-chest.hologram.chunk_protection.player_detection_range", 64.0);

                    // 检查是否有玩家在附近（避免在无人区域重建）
                    if (hasPlayersNearby(backup.getLocation(), detectionRange)) {
                        recreateHologramFromBackup(id, backup.getText());
                        recreatedCount++;
                    }
                }
            }
        }

        if (recreatedCount > 0) {
            // 调试模式下显示定期检查重建日志
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("重建了 " + recreatedCount + " 个丢失的浮空字");
            }
        }
    }

    /**
     * 从备份重建浮空字
     */
    private void recreateHologramFromBackup(String id, String text) {
        HologramData backup = hologramBackups.get(id);
        if (backup == null) {
            return;
        }

        try {
            Location location = backup.getLocation();

            // 确保区块已加载
            if (!location.getChunk().isLoaded()) {
                location.getChunk().load();
            }

            // 创建新的浮空字实体
            ArmorStand armorStand = (ArmorStand) location.getWorld().spawnEntity(
                location, EntityType.ARMOR_STAND);

            // 应用浮空字最佳实践设置（固定值）
            armorStand.setVisible(false);        // 隐藏盔甲架本体
            armorStand.setGravity(false);        // 禁用重力
            armorStand.setCanPickupItems(false); // 禁止拾取物品
            armorStand.setCustomName(text);
            armorStand.setCustomNameVisible(true);
            armorStand.setMarker(true);          // 标记模式（无碰撞箱）
            armorStand.setSmall(true);           // 小型盔甲架

            // 更新映射
            holograms.put(id, armorStand);

            // 更新备份文本
            backup.setText(text);

        } catch (Exception e) {
            plugin.getLogger().warning("重建浮空字失败 (" + id + "): " + e.getMessage());
        }
    }

    /**
     * 检查指定位置附近是否有玩家
     */
    private boolean hasPlayersNearby(Location location, double radius) {
        // 修复：如果没有在线玩家，直接返回false，避免不必要的计算
        java.util.Collection<? extends org.bukkit.entity.Player> onlinePlayers = plugin.getServer().getOnlinePlayers();
        if (onlinePlayers.isEmpty()) {
            return false;
        }

        for (org.bukkit.entity.Player player : onlinePlayers) {
            if (player.getWorld().equals(location.getWorld()) &&
                player.getLocation().distance(location) <= radius) {
                return true;
            }
        }
        return false;
    }

    /**
     * 强制重建所有浮空字（用于调试）
     */
    public void forceRecreateAllHolograms() {
        plugin.getLogger().info("强制重建所有浮空字...");

        for (String id : hologramBackups.keySet()) {
            HologramData backup = hologramBackups.get(id);
            if (backup != null) {
                recreateHologramFromBackup(id, backup.getText());
            }
        }

        plugin.getLogger().info("强制重建完成，共处理 " + hologramBackups.size() + " 个浮空字");
    }

    /**
     * 按世界清理浮空字
     */
    public void cleanupByWorld(String worldName) {
        plugin.getLogger().info("清理世界 '" + worldName + "' 的浮空字...");

        int removedCount = 0;
        java.util.Iterator<java.util.Map.Entry<String, ArmorStand>> iterator = holograms.entrySet().iterator();

        while (iterator.hasNext()) {
            java.util.Map.Entry<String, ArmorStand> entry = iterator.next();
            String id = entry.getKey();
            ArmorStand hologram = entry.getValue();

            if (hologram != null) {
                try {
                    // 检查浮空字是否在指定世界
                    if (hologram.getWorld().getName().equals(worldName)) {
                        if (!hologram.isDead()) {
                            // 1.16.x版本特殊处理
                            if (com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 16) &&
                                !com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 17)) {
                                try {
                                    hologram.setCustomNameVisible(false);
                                    hologram.setVisible(false);
                                } catch (Exception e) {
                                    // 忽略设置错误
                                }
                            }
                            hologram.remove();
                            removedCount++;
                        }
                        iterator.remove();
                    }
                } catch (Exception e) {
                    // 如果获取世界失败，也移除这个浮空字
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().warning("检查浮空字世界时出错: " + e.getMessage());
                    }
                    iterator.remove();
                }
            } else {
                iterator.remove();
            }
        }

        plugin.getLogger().info("世界 '" + worldName + "' 清理完成，移除了 " + removedCount + " 个浮空字");
    }

    /**
     * 按世界强制重建浮空字
     */
    public void forceRecreateHologramsByWorld(String worldName) {
        plugin.getLogger().info("强制重建世界 '" + worldName + "' 的浮空字...");

        int recreatedCount = 0;
        for (String id : hologramBackups.keySet()) {
            HologramData backup = hologramBackups.get(id);
            if (backup != null && backup.getLocation().getWorld().getName().equals(worldName)) {
                recreateHologramFromBackup(id, backup.getText());
                recreatedCount++;
            }
        }

        plugin.getLogger().info("世界 '" + worldName + "' 重建完成，处理了 " + recreatedCount + " 个浮空字");
    }

    /**
     * 检查是否存在指定位置的浮空字
     */
    public boolean hasHologram(Location location) {
        String id = locationToId(location);
        ArmorStand hologram = holograms.get(id);
        return hologram != null && !hologram.isDead();
    }

    /**
     * 强制备份浮空字
     */
    public void backupHologram(Location location, String text) {
        String id = locationToId(location);
        ArmorStand hologram = holograms.get(id);

        if (hologram != null && !hologram.isDead()) {
            // 从现有浮空字获取样式设置
            boolean invisible = !hologram.isVisible();
            boolean noGravity = !hologram.hasGravity();
            boolean noPickup = !hologram.getCanPickupItems();
            boolean marker = hologram.isMarker();
            boolean small = hologram.isSmall();

            // 创建或更新备份
            hologramBackups.put(id, new HologramData(location, text,
                invisible, noGravity, noPickup, marker, small));
        }
    }

    /**
     * 获取浮空字统计信息
     */
    public String getHologramStats() {
        int activeCount = 0;
        int deadCount = 0;
        int missingCount = 0;

        for (ArmorStand hologram : holograms.values()) {
            if (hologram == null) {
                missingCount++;
            } else if (hologram.isDead()) {
                deadCount++;
            } else {
                activeCount++;
            }
        }

        return String.format("浮空字统计: 活跃=%d, 死亡=%d, 丢失=%d, 备份=%d",
            activeCount, deadCount, missingCount, hologramBackups.size());
    }

    /**
     * 基于距离清理浮空字
     * @param maxDistance 最大距离，超过此距离的浮空字将被移除
     * @return 被清理的浮空字数量
     */
    public int cleanupHologramsByDistance(double maxDistance) {
        int cleanedCount = 0;

        // 获取所有在线玩家
        java.util.List<org.bukkit.entity.Player> onlinePlayers = new java.util.ArrayList<>(plugin.getServer().getOnlinePlayers());

        // 修复：如果没有在线玩家，清理所有浮空字
        if (onlinePlayers.isEmpty()) {
            cleanedCount = holograms.size();
            cleanup();
            return cleanedCount;
        }

        // 遍历所有浮空字
        java.util.Iterator<java.util.Map.Entry<String, ArmorStand>> iterator = holograms.entrySet().iterator();
        while (iterator.hasNext()) {
            java.util.Map.Entry<String, ArmorStand> entry = iterator.next();
            String id = entry.getKey();
            ArmorStand hologram = entry.getValue();

            if (hologram == null || hologram.isDead()) {
                continue;
            }

            org.bukkit.Location hologramLocation = hologram.getLocation();
            boolean hasNearbyPlayer = false;

            // 检查是否有玩家在指定距离内
            for (org.bukkit.entity.Player player : onlinePlayers) {
                if (player.getWorld().equals(hologramLocation.getWorld()) &&
                    player.getLocation().distance(hologramLocation) <= maxDistance) {
                    hasNearbyPlayer = true;
                    break;
                }
            }

            // 如果没有玩家在附近，移除浮空字
            if (!hasNearbyPlayer) {
                removeHologram(id);
                cleanedCount++;

                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("距离清理：移除了远离玩家的浮空字 " + id);
                }
            }
        }

        return cleanedCount;
    }

    /**
     * 浮空字更新器接口
     */
    public interface HologramUpdater {
        /**
         * 获取更新后的文本
         * @return 新文本，如果返回null则移除浮空字
         */
        String getUpdatedText();
    }

    /**
     * 浮空字数据备份类
     */
    private static class HologramData {
        private final Location location;
        private String text;
        private final boolean invisible;
        private final boolean noGravity;
        private final boolean noPickup;
        private final boolean marker;
        private final boolean small;

        public HologramData(Location location, String text, boolean invisible,
                           boolean noGravity, boolean noPickup, boolean marker, boolean small) {
            this.location = location.clone();
            this.text = text;
            this.invisible = invisible;
            this.noGravity = noGravity;
            this.noPickup = noPickup;
            this.marker = marker;
            this.small = small;
        }

        public Location getLocation() { return location.clone(); }
        public String getText() { return text; }
        public void setText(String text) { this.text = text; }
        public boolean isInvisible() { return invisible; }
        public boolean isNoGravity() { return noGravity; }
        public boolean isNoPickup() { return noPickup; }
        public boolean isMarker() { return marker; }
        public boolean isSmall() { return small; }
    }
}
