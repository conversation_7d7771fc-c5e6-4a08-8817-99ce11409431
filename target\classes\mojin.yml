# ===============================================
#           HangEvacuation 摸金箱种类配置
# ===============================================
# 
# 🎯 配置说明：
# - 每个摸金箱种类都有独立的配置
# - 可以设置不同的外观、名称、描述等
# - 支持自定义材料和模型数据
#
# 📦 槽位数量配置 (slots)：
# - 固定数量：slots: 5 (固定5个槽位)
# - 范围随机：slots: "2-5" (随机2到5个槽位)
# - 范围随机：slots: "3-8" (随机3到8个槽位)
# - 注意：范围随机需要用引号包围，格式为 "最小值-最大值"
#
# ===============================================

# 摸金箱种类配置
chest_types:
  # 通用摸金箱（默认）
  common:
    name: "§6摸金箱"
    display_name: "§6普通摸金箱"
    description:
      - "§7一个普通的摸金箱"
      - "§7可能包含各种物品"
      - "§e右键打开搜索宝藏"
    material: "CHEST"
    custom_model_data: 0
    rarity: "§f普通"
    rarity_color: "§f"
    enabled: true
    # 摸金箱专属配置
    slots: 5                    # 物品槽位数量 (支持范围随机: "2-5" 表示2到5个随机槽位)
    refresh_time: 5             # 刷新时间（分钟）
    
  # 武器箱
  weapon:
    name: "§c武器箱"
    display_name: "§c军用武器箱"
    description:
      - "§7军用级别的武器箱"
      - "§7包含各种强力武器"
      - "§c危险物品，小心处理"
      - "§e右键打开搜索武器"
    material: "TRAPPED_CHEST"
    custom_model_data: 1001
    rarity: "§c稀有"
    rarity_color: "§c"
    enabled: true
    # 摸金箱专属配置
    slots: "6-10"               # 武器箱物品更多 (范围随机: 6到10个随机槽位)
    refresh_time: 15            # 稀有箱子刷新时间更长
    
  # 弹药箱
  ammo:
    name: "§e弹药箱"
    display_name: "§e军用弹药箱"
    description:
      - "§7军用级别的弹药箱"
      - "§7包含各种弹药和爆炸物"
      - "§6小心！内含爆炸物"
      - "§e右键打开搜索弹药"
    material: "BARREL"
    custom_model_data: 1002
    rarity: "§e罕见"
    rarity_color: "§e"
    enabled: true
    # 摸金箱专属配置
    slots: 6                    # 弹药箱中等物品数量 (支持范围随机: "4-8" 表示4到8个随机槽位)
    refresh_time: 10            # 中等刷新时间
    
  # 医疗箱
  medical:
    name: "§a医疗箱"
    display_name: "§a野战医疗箱"
    description:
      - "§7野战医疗用品箱"
      - "§7包含各种医疗用品"
      - "§a可以救命的好东西"
      - "§e右键打开搜索医疗用品"
    material: "WHITE_SHULKER_BOX"
    custom_model_data: 1003
    rarity: "§a常见"
    rarity_color: "§a"
    enabled: true
    # 摸金箱专属配置
    slots: 4                    # 医疗箱物品较少 (支持范围随机: "2-6" 表示2到6个随机槽位)
    refresh_time: 3             # 常见箱子刷新快
    
  # 补给箱
  supply:
    name: "§b补给箱"
    display_name: "§b军用补给箱"
    description:
      - "§7军用补给物资箱"
      - "§7包含食物和基础物资"
      - "§b维持生存的必需品"
      - "§e右键打开搜索补给"
    material: "BLUE_SHULKER_BOX"
    custom_model_data: 1004
    rarity: "§b普通"
    rarity_color: "§b"
    enabled: true
    # 摸金箱专属配置
    slots: 7                    # 补给箱物品较多 (支持范围随机: "5-9" 表示5到9个随机槽位)
    refresh_time: 8             # 中等偏长刷新时间
    
  # 装备箱
  equipment:
    name: "§d装备箱"
    display_name: "§d特种装备箱"
    description:
      - "§7特种部队装备箱"
      - "§7包含高级装备和工具"
      - "§d专业级别的装备"
      - "§e右键打开搜索装备"
    material: "ENDER_CHEST"
    custom_model_data: 1005
    rarity: "§d史诗"
    rarity_color: "§d"
    enabled: true
    # 摸金箱专属配置
    slots: "7-12"               # 装备箱物品最多（史诗级） (范围随机: 7到12个随机槽位)
    refresh_time: 20            # 史诗箱子刷新时间最长

# 摸金箱全局设置
global_settings:
  # 默认摸金箱种类
  default_type: "common"
  
  # 是否显示种类信息
  show_type_info: true
  
  # 摸金箱放置音效
  place_sound: "BLOCK_CHEST_OPEN"
  place_sound_volume: 0.8
  place_sound_pitch: 1.0
  
  # 摸金箱打开音效
  open_sound: "BLOCK_CHEST_OPEN"
  open_sound_volume: 1.0
  open_sound_pitch: 1.2

# 种类权限配置
permissions:
  # 是否启用权限检查
  enable_permission_check: false
  
  # 权限前缀
  permission_prefix: "evacuation.chest."
  
  # 种类权限映射
  type_permissions:
    common: "evacuation.chest.common"
    weapon: "evacuation.chest.weapon"
    ammo: "evacuation.chest.ammo"
    medical: "evacuation.chest.medical"
    supply: "evacuation.chest.supply"
    equipment: "evacuation.chest.equipment"

# GUI设置
gui_settings:
  # 种类选择GUI标题
  selection_title: "§6选择摸金箱种类"
  
  # 管理GUI标题格式
  management_title_format: "§6{type_name} §7- 战利品管理"
  
  # 每页显示的种类数量
  types_per_page: 21
  
  # 是否显示禁用的种类
  show_disabled_types: false

# 调试设置
debug:
  # 是否启用调试模式
  enabled: false
  
  # 调试日志级别
  log_level: "INFO"
  
  # 是否在控制台显示种类加载信息
  show_loading_info: true
