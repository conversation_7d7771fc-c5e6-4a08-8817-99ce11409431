# 🎯 撤离点倒计时提示增强报告

## 📋 **用户需求**

用户要求为撤离点的倒计时提示添加：
1. **开关控制**：为倒计时提示添加开关
2. **Title显示**：添加title和subtitle显示
3. **ActionBar显示**：添加动作栏显示
4. **完全自定义**：三种提示都可以自定义开关和内容

## ✅ **实现内容**

### **1. 配置文件新增**

**文件**: `config.yml`
**新增配置段**:

```yaml
# 撤离点设置
evacuation:
  # 撤离倒计时提示设置
  countdown-notifications:
    # 聊天消息提示开关
    chat-message:
      enabled: true
    # Title标题提示开关
    title:
      enabled: true
    # ActionBar动作栏提示开关
    actionbar:
      enabled: true

# 消息设置
messages:
  # 撤离Title消息 (主标题和副标题)
  evacuation-title-enter: "§6撤离区域"
  evacuation-subtitle-enter: "§e开始倒计时..."
  evacuation-title-countdown: "§c撤离倒计时"
  evacuation-subtitle-countdown: "§e{time} 秒"
  evacuation-title-success: "§a撤离成功！"
  evacuation-subtitle-success: "§7已传送到安全区域"
  evacuation-title-cancelled: "§c撤离取消"
  evacuation-subtitle-cancelled: "§7已离开撤离区域"
  
  # 撤离ActionBar消息
  evacuation-actionbar-enter: "§e▶ 进入撤离区域，开始倒计时..."
  evacuation-actionbar-countdown: "§c⏰ 撤离倒计时: {time} 秒"
  evacuation-actionbar-success: "§a✓ 撤离成功！正在传送..."
  evacuation-actionbar-cancelled: "§c✗ 撤离已取消"
```

### **2. 代码实现**

**文件**: `CountdownManager.java`
**新增方法**:

```java
/**
 * 🔧 新增：发送撤离通知（支持聊天、Title、ActionBar三种方式）
 */
private void sendEvacuationNotification(Player player, String type, String timeValue) {
    // 检查聊天消息开关
    if (plugin.getConfig().getBoolean("evacuation.countdown-notifications.chat-message.enabled", true)) {
        // 发送聊天消息
    }

    // 检查Title开关
    if (plugin.getConfig().getBoolean("evacuation.countdown-notifications.title.enabled", true)) {
        // 发送Title和Subtitle
    }

    // 检查ActionBar开关
    if (plugin.getConfig().getBoolean("evacuation.countdown-notifications.actionbar.enabled", true)) {
        // 发送ActionBar消息
    }
}
```

## 🎮 **功能特性**

### **三种提示方式**

1. **聊天消息 (Chat Message)**
   - 传统的聊天栏消息
   - 可以保存在聊天记录中
   - 适合详细信息显示

2. **标题显示 (Title/Subtitle)**
   - 屏幕中央大字显示
   - 视觉冲击力强
   - 适合重要提示

3. **动作栏 (ActionBar)**
   - 屏幕下方状态栏显示
   - 不干扰聊天记录
   - 适合实时状态更新

### **完全可配置**

每种提示方式都有独立的开关：
```yaml
countdown-notifications:
  chat-message:
    enabled: true    # 可设置为false禁用聊天消息
  title:
    enabled: true    # 可设置为false禁用Title显示
  actionbar:
    enabled: true    # 可设置为false禁用ActionBar显示
```

### **自定义消息内容**

所有消息都可以在 `messages:` 配置段中自定义：
- **Title消息**：分别配置主标题和副标题
- **ActionBar消息**：配置动作栏显示内容
- **变量支持**：支持 `{time}` 变量显示倒计时时间

## 📊 **使用场景对比**

| 提示方式 | 优势 | 适用场景 | 示例效果 |
|----------|------|----------|----------|
| **聊天消息** | 详细信息、可保存 | 详细说明、日志记录 | `§c撤离倒计时: 5 秒` |
| **Title显示** | 视觉突出、醒目 | 重要提示、状态变化 | 大字显示 `撤离倒计时` |
| **ActionBar** | 不干扰聊天、实时 | 状态监控、倒计时 | `⏰ 撤离倒计时: 5 秒` |

## 🎯 **配置示例**

### **全部启用（默认）**
```yaml
countdown-notifications:
  chat-message:
    enabled: true
  title:
    enabled: true
  actionbar:
    enabled: true
```
**效果**：玩家会同时收到聊天消息、Title显示和ActionBar提示

### **只启用Title和ActionBar**
```yaml
countdown-notifications:
  chat-message:
    enabled: false
  title:
    enabled: true
  actionbar:
    enabled: true
```
**效果**：不显示聊天消息，只显示Title和ActionBar

### **只启用聊天消息**
```yaml
countdown-notifications:
  chat-message:
    enabled: true
  title:
    enabled: false
  actionbar:
    enabled: false
```
**效果**：只显示传统的聊天消息

### **自定义消息内容**
```yaml
messages:
  # 自定义Title消息
  evacuation-title-countdown: "§c⚠ 紧急撤离 ⚠"
  evacuation-subtitle-countdown: "§e还有 {time} 秒"
  
  # 自定义ActionBar消息
  evacuation-actionbar-countdown: "§c🚨 紧急撤离倒计时: {time} 秒 🚨"
```

## 🔧 **技术实现**

### **NMS适配器支持**
- 使用插件的NMS管理器发送Title和ActionBar
- 自动适配不同Minecraft版本
- 包含异常处理，确保稳定性

### **消息变量替换**
```java
// 替换时间变量
if (timeValue != null) {
    subtitle = subtitle.replace("{time}", timeValue);
    actionbarMessage = actionbarMessage.replace("{time}", timeValue);
}
```

### **开关检查机制**
```java
// 每种提示方式都有独立的开关检查
if (plugin.getConfig().getBoolean("evacuation.countdown-notifications.chat-message.enabled", true)) {
    // 发送聊天消息
}
```

## 🎮 **用户体验提升**

### **视觉层次**
- **Title**：最重要的状态变化（进入撤离区域、撤离成功）
- **ActionBar**：实时倒计时状态
- **Chat**：详细信息和日志记录

### **个性化配置**
- 服务器管理员可以根据服务器风格选择提示方式
- 可以完全自定义消息内容和颜色
- 支持表情符号和特殊字符

### **性能优化**
- 只有启用的提示方式才会执行
- NMS调用包含异常处理
- 避免不必要的消息发送

## 💡 **使用建议**

### **推荐配置**
```yaml
# 平衡配置：重要信息用Title，状态用ActionBar
countdown-notifications:
  chat-message:
    enabled: false    # 减少聊天干扰
  title:
    enabled: true     # 重要状态变化
  actionbar:
    enabled: true     # 实时倒计时
```

### **消息设计建议**
- **Title**：简洁明了，突出重点
- **ActionBar**：包含图标，增强视觉效果
- **Chat**：详细信息，便于查看历史

## 🎉 **总结**

现在撤离点倒计时提示功能已经全面增强：
- ✅ **三种提示方式**：聊天、Title、ActionBar
- ✅ **独立开关控制**：每种方式都可以单独启用/禁用
- ✅ **完全自定义**：所有消息内容都可以配置
- ✅ **变量支持**：支持时间变量替换
- ✅ **版本兼容**：使用NMS适配器支持不同版本
- ✅ **异常处理**：包含完善的错误处理机制

管理员可以根据服务器需求灵活配置撤离提示方式，为玩家提供最佳的撤离体验！

---

**实现完成时间**: 2025-06-15  
**影响范围**: 撤离点倒计时提示系统  
**兼容性**: 完全向下兼容  
**用户体验**: 显著提升，支持多种提示方式
