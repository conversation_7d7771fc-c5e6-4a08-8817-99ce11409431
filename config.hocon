exempt: [
]

libs: []

stringEncryption {
  type: STANDARD
  enabled: true
  exempt: []
}

numberEncryption {
  enabled: true
  exempt: []
}

intAnnotationEncryption {
  enabled: true
  exempt: []
}

stringAnnotationEncryption {
  enabled: true
  exempt: []
}

exceptionReturn {
  enabled: true
  exempt: []
}

flowCondition {
  enabled: true
  exempt: []
}

flowException {
  enabled: true
  strength: AGGRESSIVE
  exempt: []
}

flowRange {
  enabled: true
  exempt: []
}

flowFactoryMaker {
  enabled: true
  exempt: []
}

flowSwitch {
  enabled: true
  exempt: []
}

outliner {
  enabled: true
  exempt: []
}

ahegao {
  enabled: true
  exempt: []
}

native: {
  enabled: false
  exempt: []
}

driver: {
  enabled: false
}

reference {
  enabled: false
}

fileCrasher: {
  enabled: false
}

classRenamer {
  enabled: false
  type: CUSTOM
  prefix: "skido/"
  chars: [
    "K"
    "oO",
    "o0"
  ]
  depth: 3
}

methodRenamer {
  enabled: false
  type: CUSTOM
  chars: [
    "K"
    "oO",
    "o0"
  ]
  depth: 3
}

fieldRenamer {
  enabled: false
  type: ALPHABETICAL
}
