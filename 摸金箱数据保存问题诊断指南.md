# 🔧 摸金箱数据保存问题诊断指南

## 🚨 **问题描述**
服务器重启后，摸金箱GUI中的物品消失，摸金箱变成空白或普通箱子。

## 🔍 **诊断步骤**

### **1. 启用调试模式**
在 `config.yml` 中设置：
```yaml
debug:
  enabled: true
```

### **2. 使用调试命令**
```bash
# 基本调试信息
/evacuation debug

# 详细调试信息
/evacuation debug verbose
```

### **3. 检查关键指标**

#### **数据同步状态**
- ✅ **正常**: 内存中摸金箱数量 = 文件中摸金箱数量
- ❌ **异常**: 数量不一致，说明数据未正确保存

#### **文件状态检查**
- 数据文件路径: `plugins/HangEvacuation/chests.yml`
- 文件大小: 应该 > 0 字节
- 文件权限: 可读可写
- 备份文件: 应该存在 `chests.yml.backup`

#### **配置检查**
- 自动保存间隔: 默认5分钟
- 调试模式: 建议启用以查看详细日志

## 🛠️ **修复方案**

### **方案1: 手动强制保存**
```bash
# 立即保存所有数据
/evacuation save
```

### **方案2: 重载插件配置**
```bash
# 重载配置文件
/evacuation reload
```

### **方案3: 检查文件权限**
确保服务器对插件数据目录有读写权限：
```bash
# Linux/Unix 系统
chmod 755 plugins/HangEvacuation/
chmod 644 plugins/HangEvacuation/*.yml
```

### **方案4: 从备份恢复**
如果主文件损坏，插件会自动尝试从备份恢复。
手动恢复方法：
1. 停止服务器
2. 将 `chests.yml.backup` 重命名为 `chests.yml`
3. 启动服务器

## 📊 **增强功能说明**

### **自动保存机制**
- **频率**: 每5分钟自动保存（可配置）
- **验证**: 保存后验证数据完整性
- **日志**: 详细的保存过程日志

### **数据备份机制**
- **自动备份**: 每次保存前创建备份
- **紧急备份**: 保存失败时创建时间戳备份
- **恢复机制**: 启动时自动检查并恢复损坏文件

### **原子性保存**
- **临时文件**: 先保存到临时文件
- **原子替换**: 成功后原子性替换主文件
- **完整性验证**: 保存后验证文件可读性

## 🔧 **新增的修复功能**

### **1. 增强的错误处理**
- 详细的错误日志
- 多重备份机制
- 自动恢复功能

### **2. 数据验证**
- 保存前验证数据完整性
- 保存后验证文件可读性
- 内存与文件数据一致性检查

### **3. 调试工具**
- 实时数据统计
- 文件状态检查
- 系统资源监控

## 📝 **常见问题解答**

### **Q: 为什么数据会丢失？**
A: 可能原因：
1. 服务器异常关闭（强制停止、崩溃）
2. 磁盘空间不足
3. 文件权限问题
4. 序列化失败

### **Q: 如何预防数据丢失？**
A: 预防措施：
1. 启用自动保存（默认已启用）
2. 定期手动保存 `/evacuation save`
3. 确保足够的磁盘空间
4. 正常关闭服务器

### **Q: 数据恢复后物品还是消失怎么办？**
A: 检查步骤：
1. 确认摸金箱类型配置正确
2. 检查物品序列化是否完整
3. 验证模组物品兼容性
4. 查看详细错误日志

## 🚀 **性能优化建议**

### **1. 调整自动保存频率**
```yaml
# config.yml
auto_save_interval: 10  # 改为10分钟（减少I/O）
```

### **2. 禁用详细调试（生产环境）**
```yaml
debug:
  enabled: false  # 生产环境建议关闭
```

### **3. 监控磁盘空间**
确保数据目录有足够空间用于备份文件。

## 📞 **技术支持**

如果问题仍然存在，请提供以下信息：
1. `/evacuation debug verbose` 的完整输出
2. 服务器日志中的相关错误信息
3. 服务器版本和插件版本
4. 问题复现步骤

**联系方式**: 
- QQ群: 361919269
- 微信: hang060217
