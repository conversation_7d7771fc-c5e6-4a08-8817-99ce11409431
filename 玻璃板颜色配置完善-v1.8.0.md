# 🎨 玻璃板颜色配置完善 - v1.8.0

## 📅 更新日期: 2025-01-XX

### 🎯 更新类型: 配置系统重构 + 用户体验改进

---

## 🆕 主要更新

### 🎨 **完全可配置的玻璃板颜色系统**
- **新功能**: 搜索进度的所有颜色变换都可以在配置文件中自定义
- **阶段化配置**: 支持多个进度阶段，每个阶段独立配置颜色、名称、描述
- **占位符支持**: 支持 `{progress}` 占位符显示实时进度百分比
- **1.12.2兼容**: 完美支持1.12.2的data值颜色系统

---

## 🎮 新配置结构

### 🎨 **完整配置示例**

```yaml
# 物品设置
items:
  # 未搜索的物品 (灰色玻璃板)
  unsearched-item:
    material: STAINED_GLASS_PANE
    data: 7  # 灰色
    name: "§7未搜索"
    lore:
      - "§7等待自动搜索"

  # 搜索进度玻璃板颜色配置
  searching-progress:
    # 基础材质
    material: STAINED_GLASS_PANE
    
    # CustomModelData 设置 (仅在1.14+版本生效)
    custom_model_data: 0
    
    # 搜索进度阶段配置 (按进度百分比划分)
    stages:
      # 0-25% 进度阶段
      stage_1:
        progress_range: [0, 25]
        data: 14  # 红色
        name: "§c正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §c{progress}%"
          - "§7状态: §c开始搜索"
      
      # 26-50% 进度阶段  
      stage_2:
        progress_range: [26, 50]
        data: 1   # 橙色
        name: "§6正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §6{progress}%"
          - "§7状态: §6搜索中"
      
      # 51-75% 进度阶段
      stage_3:
        progress_range: [51, 75]
        data: 4   # 黄色
        name: "§e正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §e{progress}%"
          - "§7状态: §e即将完成"
      
      # 76-100% 进度阶段
      stage_4:
        progress_range: [76, 100]
        data: 5   # 浅绿色
        name: "§a正在搜索... {progress}%"
        lore:
          - "§7搜索进度: §a{progress}%"
          - "§7状态: §a马上完成"
```

---

## 🔧 技术特性

### 🎯 **智能阶段检测**
- **动态匹配**: 根据进度百分比自动匹配对应阶段
- **范围配置**: 每个阶段可以自定义进度范围
- **灵活扩展**: 可以添加更多阶段或修改现有阶段

### 🎨 **占位符系统**
- **`{progress}`**: 显示当前搜索进度百分比
- **实时更新**: 占位符会随着搜索进度实时更新
- **多处应用**: 可以在名称和Lore中使用

### 🔄 **版本兼容性**
- **1.12.2及以下**: 使用 `STAINED_GLASS_PANE` + data值
- **1.13+**: 自动转换为新的彩色玻璃板材质
- **CustomModelData**: 1.14+版本支持自定义模型

---

## 🎨 自定义配置示例

### 🌈 **彩虹进度条**
```yaml
searching-progress:
  stages:
    rainbow_1:
      progress_range: [0, 16]
      data: 14  # 红色
      name: "§c🔍 搜索中 {progress}%"
    rainbow_2:
      progress_range: [17, 33]
      data: 1   # 橙色  
      name: "§6🔍 搜索中 {progress}%"
    rainbow_3:
      progress_range: [34, 50]
      data: 4   # 黄色
      name: "§e🔍 搜索中 {progress}%"
    rainbow_4:
      progress_range: [51, 66]
      data: 5   # 浅绿色
      name: "§a🔍 搜索中 {progress}%"
    rainbow_5:
      progress_range: [67, 83]
      data: 11  # 蓝色
      name: "§9🔍 搜索中 {progress}%"
    rainbow_6:
      progress_range: [84, 100]
      data: 10  # 紫色
      name: "§d🔍 搜索中 {progress}%"
```

### 🎯 **简化双色模式**
```yaml
searching-progress:
  stages:
    searching:
      progress_range: [0, 99]
      data: 14  # 红色
      name: "§c正在搜索... {progress}%"
      lore:
        - "§7请耐心等待..."
    completing:
      progress_range: [100, 100]
      data: 5   # 绿色
      name: "§a即将完成! {progress}%"
      lore:
        - "§a马上就好了！"
```

---

## 🎮 1.12.2 Data值参考

### 🎨 **STAINED_GLASS_PANE 颜色对照表**
```
data: 0  - 白色 (WHITE)
data: 1  - 橙色 (ORANGE)  
data: 2  - 品红色 (MAGENTA)
data: 3  - 淡蓝色 (LIGHT_BLUE)
data: 4  - 黄色 (YELLOW)
data: 5  - 浅绿色 (LIME)
data: 6  - 粉色 (PINK)
data: 7  - 灰色 (GRAY)
data: 8  - 淡灰色 (LIGHT_GRAY)
data: 9  - 青色 (CYAN)
data: 10 - 紫色 (PURPLE)
data: 11 - 蓝色 (BLUE)
data: 12 - 棕色 (BROWN)
data: 13 - 绿色 (GREEN)
data: 14 - 红色 (RED)
data: 15 - 黑色 (BLACK)
```

---

## 🚀 使用指南

### 📝 **配置步骤**
1. **编辑config.yml**: 修改 `items.searching-progress.stages` 部分
2. **设置进度范围**: 确保所有范围覆盖0-100%，无重叠
3. **选择颜色**: 参考上面的data值对照表
4. **自定义文本**: 使用 `{progress}` 占位符显示进度
5. **重载插件**: 使用 `/evac reload` 应用新配置

### ⚠️ **注意事项**
- 进度范围不能重叠
- 必须覆盖0-100%的完整范围
- data值必须在0-15之间（1.12.2）
- 占位符区分大小写

---

## 🎉 版本信息
- **版本**: v1.8.0
- **更新日期**: 2025年
- **主要改进**: 完全可配置的玻璃板颜色系统
- **兼容性**: Minecraft 1.8.8-1.21.4
