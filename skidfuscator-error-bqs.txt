handler=Block #EK, types=[Ljava/lang/RuntimeException;], range=[Block #AJ, Block #AI]
handler=Block #EO, types=[Ljava/lang/IllegalAccessException;], range=[Block #AM, Block #AL]
handler=Block #ES, types=[Ljava/lang/IllegalAccessException;], range=[Block #AP, Block #AO]
handler=Block #EW, types=[Ljava/lang/IllegalAccessException;], range=[Block #AS, Block #AR]
handler=Block #FA, types=[Ljava/lang/RuntimeException;], range=[Block #AV, Block #AU]
handler=Block #FE, types=[Ljava/lang/IllegalAccessException;], range=[Block #AY, Block #AX]
handler=Block #FI, types=[Ljava/lang/IllegalAccessException;], range=[Block #BB, Block #BA]
handler=Block #FM, types=[Ljava/lang/IllegalAccessException;], range=[Block #BE, Block #BD]
handler=Block #FQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #BH, Block #BG]
===#Block A(size=7, flags=1)===
   0. lvar70 = {********** ^ {886233178 ^ **********}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. synth(lvar2 = lvar2);
   4. synth(lvar3 = lvar3);
   5. synth(lvar4 = lvar4);
   6. lvar70 = {********** ^ lvar70};
      -> Immediate #A -> #B
===#Block B(size=5, flags=0)===
   0. lvar5 = lvar1;
   1. lvar6 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.tvljcviqcfwmgah(), lvar70);
   2. lvar11 = lvar5.hasPermission(lvar6);
   3. if (lvar11 != {********** ^ lvar70})
      goto CZ
   4. lvar70 = {********** ^ lvar70};
      -> Immediate #B -> #AG
      -> ConditionalJump[IF_ICMPNE] #B -> #CZ
      <- Immediate #A -> #B
===#Block CZ(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 113647595:
      	 goto	#DA
      case **********:
      	 goto	#CZ
      case **********:
      	 goto	#BK
      case **********:
      	 goto	#BN
      default:
      	 goto	#BK
   }
      -> Immediate #CZ -> #DA
      -> Switch[**********] #CZ -> #BN
      -> Switch[113647595] #CZ -> #DA
      -> Switch[**********] #CZ -> #BK
      -> Switch[**********] #CZ -> #CZ
      -> DefaultSwitch #CZ -> #BK
      <- ConditionalJump[IF_ICMPNE] #B -> #CZ
      <- Switch[**********] #CZ -> #CZ
===#Block DA(size=2, flags=100)===
   0. lvar70 = {201196886 ^ lvar70};
   1. goto BN
      -> UnconditionalJump[GOTO] #DA -> #BN
      <- Immediate #CZ -> #DA
      <- Switch[113647595] #CZ -> #DA
===#Block BN(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto DH
   1. goto BU
      -> ConditionalJump[IF_ICMPEQ] #BN -> #DH
      -> UnconditionalJump[GOTO] #BN -> #BU
      <- Switch[**********] #CZ -> #BN
      <- UnconditionalJump[GOTO] #DA -> #BN
===#Block BU(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, 140033312);
   1. goto BK
      -> UnconditionalJump[GOTO] #BU -> #BK
      <- UnconditionalJump[GOTO] #BN -> #BU
===#Block DH(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 100434652:
      	 goto	#DI
      case **********:
      	 goto	#BK
      case **********:
      	 goto	#DH
      case **********:
      	 goto	#C
      default:
      	 goto	#BK
   }
      -> Switch[**********] #DH -> #DH
      -> Immediate #DH -> #DI
      -> Switch[100434652] #DH -> #DI
      -> Switch[**********] #DH -> #C
      -> Switch[**********] #DH -> #BK
      -> DefaultSwitch #DH -> #BK
      <- Switch[**********] #DH -> #DH
      <- ConditionalJump[IF_ICMPEQ] #BN -> #DH
===#Block DI(size=2, flags=100)===
   0. lvar70 = {31893905 ^ lvar70};
   1. goto C
      -> UnconditionalJump[GOTO] #DI -> #C
      <- Immediate #DH -> #DI
      <- Switch[100434652] #DH -> #DI
===#Block C(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar4;
   2. lvar13 = lvar12.length;
   3. if (lvar13 != {1414357917 ^ lvar70})
      goto DK
   4. lvar70 = {834719921 ^ lvar70};
      -> ConditionalJump[IF_ICMPNE] #C -> #DK
      -> Immediate #C -> #D
      <- UnconditionalJump[GOTO] #DI -> #C
      <- Switch[**********] #DH -> #C
===#Block D(size=4, flags=0)===
   0. lvar14 = lvar0;
   1. lvar51 = lvar1;
   2. _consume(lvar14.showHelp(lvar51, 743956132));
   3. lvar70 = {********** ^ lvar70};
      -> Immediate #D -> #E
      <- Immediate #C -> #D
===#Block E(size=2, flags=0)===
   0. lvar15 = {181611034 ^ lvar70};
   1. return lvar15;
      <- Immediate #D -> #E
===#Block DK(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto BO
      -> UnconditionalJump[GOTO] #DK -> #BO
      <- ConditionalJump[IF_ICMPNE] #C -> #DK
===#Block BO(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -793725703)
      goto CY
   1. goto BX
      -> ConditionalJump[IF_ICMPEQ] #BO -> #CY
      -> UnconditionalJump[GOTO] #BO -> #BX
      <- UnconditionalJump[GOTO] #DK -> #BO
===#Block BX(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto BK
      -> UnconditionalJump[GOTO] #BX -> #BK
      <- UnconditionalJump[GOTO] #BO -> #BX
===#Block CY(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto F
      -> UnconditionalJump[GOTO] #CY -> #F
      <- ConditionalJump[IF_ICMPEQ] #BO -> #CY
===#Block F(size=12, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar4;
   2. lvar52 = {********** ^ lvar70};
   3. lvar17 = lvar16[lvar52];
   4. lvar18 = lvar17.toLowerCase();
   5. lvar9 = lvar18;
   6. lvar19 = {-********** ^ lvar70};
   7. lvar10 = lvar19;
   8. lvar20 = lvar9;
   9. lvar21 = lvar20.hashCode();
   10. svar72 = {lvar21 ^ lvar70};
   11. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(svar72)) {
      case 65293635:
      	 goto	#ED
      case 109000323:
      	 goto	#EE
      case 160778521:
      	 goto	#EF
      case 166769197:
      	 goto	#EG
      case 190848428:
      	 goto	#EI
      default:
      	 goto	#EJ
   }
      -> Switch[65293635] #F -> #ED
      -> Switch[160778521] #F -> #EF
      -> Switch[166769197] #F -> #EG
      -> DefaultSwitch #F -> #EJ
      -> Switch[109000323] #F -> #EE
      -> Switch[190848428] #F -> #EI
      <- UnconditionalJump[GOTO] #CY -> #F
===#Block EI(size=2, flags=10100)===
   0. lvar70 = {926770008 ^ lvar70};
   1. goto O
      -> UnconditionalJump[GOTO] #EI -> #O
      <- Switch[190848428] #F -> #EI
===#Block O(size=6, flags=0)===
   0. // Frame: locals[2] [java/lang/String, 1] stack[0] []
   1. lvar46 = lvar9;
   2. lvar65 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.vgbbbwuywmlwxps(), lvar70);
   3. lvar47 = lvar46.equals(lvar65);
   4. if (lvar47 == {********** ^ lvar70})
      goto DJ
   5. lvar70 = {********** ^ lvar70};
      -> Immediate #O -> #P
      -> ConditionalJump[IF_ICMPEQ] #O -> #DJ
      <- UnconditionalJump[GOTO] #EI -> #O
===#Block DJ(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto BM
      -> UnconditionalJump[GOTO] #DJ -> #BM
      <- ConditionalJump[IF_ICMPEQ] #O -> #DJ
===#Block BM(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == **********)
      goto DB
   1. goto CB
      -> ConditionalJump[IF_ICMPEQ] #BM -> #DB
      -> UnconditionalJump[GOTO] #BM -> #CB
      <- UnconditionalJump[GOTO] #DJ -> #BM
===#Block CB(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 99462205:
      	 goto	#BK
      case 168753987:
      	 goto	#CC
      case 810462954:
      	 goto	#CB
      case 1909098235:
      	 goto	#BK
      default:
      	 goto	#BK
   }
      -> Switch[810462954] #CB -> #CB
      -> Switch[168753987] #CB -> #CC
      -> DefaultSwitch #CB -> #BK
      -> Switch[99462205] #CB -> #BK
      -> Immediate #CB -> #CC
      <- UnconditionalJump[GOTO] #BM -> #CB
      <- Switch[810462954] #CB -> #CB
===#Block CC(size=2, flags=100)===
   0. lvar70 = {1350591642 ^ lvar70};
   1. goto BK
      -> UnconditionalJump[GOTO] #CC -> #BK
      <- Switch[168753987] #CB -> #CC
      <- Immediate #CB -> #CC
===#Block DB(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 168753987:
      	 goto	#DC
      case 185178216:
      	 goto	#Q
      case 367796292:
      	 goto	#DB
      case 1268958967:
      	 goto	#BK
      default:
      	 goto	#BK
   }
      -> Immediate #DB -> #DC
      -> Switch[168753987] #DB -> #DC
      -> Switch[1268958967] #DB -> #BK
      -> DefaultSwitch #DB -> #BK
      -> Switch[367796292] #DB -> #DB
      -> Switch[185178216] #DB -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BM -> #DB
      <- Switch[367796292] #DB -> #DB
===#Block DC(size=2, flags=100)===
   0. lvar70 = {261924838 ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #DC -> #Q
      <- Immediate #DB -> #DC
      <- Switch[168753987] #DB -> #DC
===#Block P(size=3, flags=0)===
   0. lvar48 = {1033449779 ^ lvar70};
   1. lvar10 = lvar48;
   2. goto BV
      -> UnconditionalJump[GOTO] #P -> #BV
      <- Immediate #O -> #P
===#Block BV(size=2, flags=10100)===
   0. lvar70 = {742767827 ^ lvar70};
   1. goto AM
      -> UnconditionalJump[GOTO] #BV -> #AM
      <- UnconditionalJump[GOTO] #P -> #BV
===#Block AM(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 43729726)
      goto AL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AM -> #AL
      -> TryCatch range: [AM...AL] -> EO ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #BV -> #AM
===#Block AL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AM...AL] -> EO ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AM -> #AL
===#Block EO(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case -**********:
      	 goto	#EP
      case **********:
      	 goto	#EQ
      default:
      	 goto	#ER
   }
      -> DefaultSwitch #EO -> #ER
      -> Switch[-**********] #EO -> #EP
      -> Switch[**********] #EO -> #EQ
      <- TryCatch range: [AM...AL] -> EO ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AM...AL] -> EO ([Ljava/lang/IllegalAccessException;])
===#Block EQ(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto AN
      -> UnconditionalJump[GOTO] #EQ -> #AN
      <- Switch[**********] #EO -> #EQ
===#Block EP(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto AN
      -> UnconditionalJump[GOTO] #EP -> #AN
      <- Switch[-**********] #EO -> #EP
===#Block AN(size=2, flags=0)===
   0. _consume(catch());
   1. goto CK
      -> UnconditionalJump[GOTO] #AN -> #CK
      <- UnconditionalJump[GOTO] #EQ -> #AN
      <- UnconditionalJump[GOTO] #EP -> #AN
===#Block CK(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto Q
      -> UnconditionalJump[GOTO] #CK -> #Q
      <- UnconditionalJump[GOTO] #AN -> #CK
===#Block ER(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #EO -> #ER
===#Block EE(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto K
      -> UnconditionalJump[GOTO] #EE -> #K
      <- Switch[109000323] #F -> #EE
===#Block K(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar40 = lvar9;
   2. lvar63 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.xdslacyytnbpwyb(), lvar70);
   3. lvar41 = lvar40.equals(lvar63);
   4. if (lvar41 == {981226482 ^ lvar70})
      goto DS
   5. lvar70 = {********** ^ lvar70};
      -> ConditionalJump[IF_ICMPEQ] #K -> #DS
      -> Immediate #K -> #L
      <- UnconditionalJump[GOTO] #EE -> #K
===#Block L(size=3, flags=0)===
   0. lvar42 = {********** ^ lvar70};
   1. lvar10 = lvar42;
   2. lvar70 = {********** ^ lvar70};
      -> Immediate #L -> #Q
      <- Immediate #K -> #L
===#Block DS(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 22075608:
      	 goto	#DT
      case 462123520:
      	 goto	#BK
      case 772856400:
      	 goto	#BR
      case **********:
      	 goto	#DS
      default:
      	 goto	#BK
   }
      -> Switch[**********] #DS -> #DS
      -> Switch[462123520] #DS -> #BK
      -> DefaultSwitch #DS -> #BK
      -> Switch[22075608] #DS -> #DT
      -> Switch[772856400] #DS -> #BR
      -> Immediate #DS -> #DT
      <- Switch[**********] #DS -> #DS
      <- ConditionalJump[IF_ICMPEQ] #K -> #DS
===#Block DT(size=2, flags=100)===
   0. lvar70 = {40371672 ^ lvar70};
   1. goto BR
      -> UnconditionalJump[GOTO] #DT -> #BR
      <- Switch[22075608] #DS -> #DT
      <- Immediate #DS -> #DT
===#Block BR(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -1063079599)
      goto DL
   1. goto CH
      -> UnconditionalJump[GOTO] #BR -> #CH
      -> ConditionalJump[IF_ICMPEQ] #BR -> #DL
      <- UnconditionalJump[GOTO] #DT -> #BR
      <- Switch[772856400] #DS -> #BR
===#Block DL(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 199971224:
      	 goto	#BK
      case 212295397:
      	 goto	#DM
      case 265863395:
      	 goto	#Q
      case 556832155:
      	 goto	#DL
      default:
      	 goto	#BK
   }
      -> Switch[212295397] #DL -> #DM
      -> Switch[199971224] #DL -> #BK
      -> DefaultSwitch #DL -> #BK
      -> Immediate #DL -> #DM
      -> Switch[556832155] #DL -> #DL
      -> Switch[265863395] #DL -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BR -> #DL
      <- Switch[556832155] #DL -> #DL
===#Block DM(size=2, flags=100)===
   0. lvar70 = {989586042 ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #DM -> #Q
      <- Switch[212295397] #DL -> #DM
      <- Immediate #DL -> #DM
===#Block CH(size=2, flags=10100)===
   0. lvar70 = {1709646086 ^ lvar70};
   1. goto BK
      -> UnconditionalJump[GOTO] #CH -> #BK
      <- UnconditionalJump[GOTO] #BR -> #CH
===#Block EJ(size=2, flags=10100)===
   0. lvar70 = {1793388538 ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #EJ -> #Q
      <- DefaultSwitch #F -> #EJ
===#Block EG(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 160852370:
      	 goto	#EH
      case 899299154:
      	 goto	#G
      case **********:
      	 goto	#EG
      case **********:
      	 goto	#BK
      default:
      	 goto	#BK
   }
      -> Switch[160852370] #EG -> #EH
      -> Switch[899299154] #EG -> #G
      -> Immediate #EG -> #EH
      -> DefaultSwitch #EG -> #BK
      -> Switch[**********] #EG -> #EG
      -> Switch[**********] #EG -> #BK
      <- Switch[166769197] #F -> #EG
      <- Switch[**********] #EG -> #EG
===#Block EH(size=2, flags=100)===
   0. lvar70 = {963268228 ^ lvar70};
   1. goto G
      -> UnconditionalJump[GOTO] #EH -> #G
      <- Switch[160852370] #EG -> #EH
      <- Immediate #EG -> #EH
===#Block G(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar9;
   2. lvar53 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.nsbhiwmudczlsab(), lvar70);
   3. lvar23 = lvar22.equals(lvar53);
   4. if (lvar23 == {********** ^ lvar70})
      goto DP
   5. lvar70 = {********** ^ lvar70};
      -> Immediate #G -> #H
      -> ConditionalJump[IF_ICMPEQ] #G -> #DP
      <- Switch[899299154] #EG -> #G
      <- UnconditionalJump[GOTO] #EH -> #G
===#Block DP(size=2, flags=10100)===
   0. lvar70 = {89709412 ^ lvar70};
   1. goto BP
      -> UnconditionalJump[GOTO] #DP -> #BP
      <- ConditionalJump[IF_ICMPEQ] #G -> #DP
===#Block BP(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto DD
   1. goto CD
      -> ConditionalJump[IF_ICMPEQ] #BP -> #DD
      -> UnconditionalJump[GOTO] #BP -> #CD
      <- UnconditionalJump[GOTO] #DP -> #BP
===#Block CD(size=2, flags=10100)===
   0. lvar70 = {164280678 ^ lvar70};
   1. goto BK
      -> UnconditionalJump[GOTO] #CD -> #BK
      <- UnconditionalJump[GOTO] #BP -> #CD
===#Block DD(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto Q
      -> UnconditionalJump[GOTO] #DD -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BP -> #DD
===#Block H(size=3, flags=0)===
   0. lvar24 = {839212678 ^ lvar70};
   1. lvar10 = lvar24;
   2. goto CU
      -> UnconditionalJump[GOTO] #H -> #CU
      <- Immediate #G -> #H
===#Block CU(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto BE
      -> UnconditionalJump[GOTO] #CU -> #BE
      <- UnconditionalJump[GOTO] #H -> #CU
===#Block BE(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 78353148)
      goto BD
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BE -> #BD
      -> TryCatch range: [BE...BD] -> FM ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CU -> #BE
===#Block BD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BE...BD] -> FM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BE -> #BD
===#Block FM(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case 348221033:
      	 goto	#FO
      case 600688730:
      	 goto	#FN
      default:
      	 goto	#FP
   }
      -> Switch[600688730] #FM -> #FN
      -> Switch[348221033] #FM -> #FO
      -> DefaultSwitch #FM -> #FP
      <- TryCatch range: [BE...BD] -> FM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BE...BD] -> FM ([Ljava/lang/IllegalAccessException;])
===#Block FP(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #FM -> #FP
===#Block FO(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto BF
      -> UnconditionalJump[GOTO] #FO -> #BF
      <- Switch[348221033] #FM -> #FO
===#Block FN(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, 311032214);
   1. goto BF
      -> UnconditionalJump[GOTO] #FN -> #BF
      <- Switch[600688730] #FM -> #FN
===#Block BF(size=2, flags=0)===
   0. _consume(catch());
   1. goto BW
      -> UnconditionalJump[GOTO] #BF -> #BW
      <- UnconditionalJump[GOTO] #FN -> #BF
      <- UnconditionalJump[GOTO] #FO -> #BF
===#Block BW(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #BW -> #Q
      <- UnconditionalJump[GOTO] #BF -> #BW
===#Block EF(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, 674227855);
   1. goto I
      -> UnconditionalJump[GOTO] #EF -> #I
      <- Switch[160778521] #F -> #EF
===#Block I(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar37 = lvar9;
   2. lvar62 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.eupimtfqgdskenf(), lvar70);
   3. lvar38 = lvar37.equals(lvar62);
   4. if (lvar38 == {********** ^ lvar70})
      goto DR
   5. lvar70 = {********** ^ lvar70};
      -> ConditionalJump[IF_ICMPEQ] #I -> #DR
      -> Immediate #I -> #J
      <- UnconditionalJump[GOTO] #EF -> #I
===#Block J(size=3, flags=0)===
   0. lvar39 = {652502746 ^ lvar70};
   1. lvar10 = lvar39;
   2. goto CT
      -> UnconditionalJump[GOTO] #J -> #CT
      <- Immediate #I -> #J
===#Block CT(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto AV
      -> UnconditionalJump[GOTO] #CT -> #AV
      <- UnconditionalJump[GOTO] #J -> #CT
===#Block AV(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 33535211)
      goto AU
   1. throw nullconst;
      -> TryCatch range: [AV...AU] -> FA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AV -> #AU
      <- UnconditionalJump[GOTO] #CT -> #AV
===#Block AU(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AV...AU] -> FA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AV -> #AU
===#Block FA(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case -1662593918:
      	 goto	#FB
      case 1713457290:
      	 goto	#FC
      default:
      	 goto	#FD
   }
      -> DefaultSwitch #FA -> #FD
      -> Switch[-1662593918] #FA -> #FB
      -> Switch[1713457290] #FA -> #FC
      <- TryCatch range: [AV...AU] -> FA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AV...AU] -> FA ([Ljava/lang/RuntimeException;])
===#Block FC(size=2, flags=10100)===
   0. lvar70 = {680859452 ^ lvar70};
   1. goto AW
      -> UnconditionalJump[GOTO] #FC -> #AW
      <- Switch[1713457290] #FA -> #FC
===#Block FB(size=2, flags=10100)===
   0. lvar70 = {936240317 ^ lvar70};
   1. goto AW
      -> UnconditionalJump[GOTO] #FB -> #AW
      <- Switch[-1662593918] #FA -> #FB
===#Block AW(size=2, flags=0)===
   0. _consume(catch());
   1. goto CL
      -> UnconditionalJump[GOTO] #AW -> #CL
      <- UnconditionalJump[GOTO] #FB -> #AW
      <- UnconditionalJump[GOTO] #FC -> #AW
===#Block CL(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 39652390:
      	 goto	#CM
      case 506211408:
      	 goto	#BK
      case **********:
      	 goto	#CL
      case 1979172361:
      	 goto	#Q
      default:
      	 goto	#BK
   }
      -> Immediate #CL -> #CM
      -> Switch[506211408] #CL -> #BK
      -> Switch[1979172361] #CL -> #Q
      -> DefaultSwitch #CL -> #BK
      -> Switch[**********] #CL -> #CL
      -> Switch[39652390] #CL -> #CM
      <- Switch[**********] #CL -> #CL
      <- UnconditionalJump[GOTO] #AW -> #CL
===#Block CM(size=2, flags=100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #CM -> #Q
      <- Immediate #CL -> #CM
      <- Switch[39652390] #CL -> #CM
===#Block FD(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #FA -> #FD
===#Block DR(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, 846012950);
   1. goto BQ
      -> UnconditionalJump[GOTO] #DR -> #BQ
      <- ConditionalJump[IF_ICMPEQ] #I -> #DR
===#Block BQ(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto DQ
   1. goto CQ
      -> UnconditionalJump[GOTO] #BQ -> #CQ
      -> ConditionalJump[IF_ICMPEQ] #BQ -> #DQ
      <- UnconditionalJump[GOTO] #DR -> #BQ
===#Block DQ(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #DQ -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BQ -> #DQ
===#Block CQ(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 31436769:
      	 goto	#BK
      case 225367611:
      	 goto	#CR
      case **********:
      	 goto	#BK
      case **********:
      	 goto	#CQ
      default:
      	 goto	#BK
   }
      -> DefaultSwitch #CQ -> #BK
      -> Switch[**********] #CQ -> #BK
      -> Immediate #CQ -> #CR
      -> Switch[**********] #CQ -> #CQ
      -> Switch[225367611] #CQ -> #CR
      <- UnconditionalJump[GOTO] #BQ -> #CQ
      <- Switch[**********] #CQ -> #CQ
===#Block CR(size=2, flags=100)===
   0. lvar70 = {800778271 ^ lvar70};
   1. goto BK
      -> UnconditionalJump[GOTO] #CR -> #BK
      <- Immediate #CQ -> #CR
      <- Switch[225367611] #CQ -> #CR
===#Block ED(size=2, flags=10100)===
   0. lvar70 = {765395923 ^ lvar70};
   1. goto M
      -> UnconditionalJump[GOTO] #ED -> #M
      <- Switch[65293635] #F -> #ED
===#Block M(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar43 = lvar9;
   2. lvar64 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.bkkhbfvokomcdki(), lvar70);
   3. lvar44 = lvar43.equals(lvar64);
   4. if (lvar44 == {********** ^ lvar70})
      goto CX
   5. lvar70 = {416973538 ^ lvar70};
      -> Immediate #M -> #N
      -> ConditionalJump[IF_ICMPEQ] #M -> #CX
      <- UnconditionalJump[GOTO] #ED -> #M
===#Block CX(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto BL
      -> UnconditionalJump[GOTO] #CX -> #BL
      <- ConditionalJump[IF_ICMPEQ] #M -> #CX
===#Block BL(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == 630468552)
      goto DE
   1. goto CJ
      -> UnconditionalJump[GOTO] #BL -> #CJ
      -> ConditionalJump[IF_ICMPEQ] #BL -> #DE
      <- UnconditionalJump[GOTO] #CX -> #BL
===#Block DE(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 45917412:
      	 goto	#DE
      case 73881899:
      	 goto	#DF
      case 770419408:
      	 goto	#Q
      case 878247230:
      	 goto	#BK
      default:
      	 goto	#BK
   }
      -> Switch[73881899] #DE -> #DF
      -> Switch[45917412] #DE -> #DE
      -> Immediate #DE -> #DF
      -> Switch[770419408] #DE -> #Q
      -> DefaultSwitch #DE -> #BK
      -> Switch[878247230] #DE -> #BK
      <- Switch[45917412] #DE -> #DE
      <- ConditionalJump[IF_ICMPEQ] #BL -> #DE
===#Block DF(size=2, flags=100)===
   0. lvar70 = {106770857 ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #DF -> #Q
      <- Switch[73881899] #DE -> #DF
      <- Immediate #DE -> #DF
===#Block CJ(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto BK
      -> UnconditionalJump[GOTO] #CJ -> #BK
      <- UnconditionalJump[GOTO] #BL -> #CJ
===#Block N(size=3, flags=0)===
   0. lvar45 = {********** ^ lvar70};
   1. lvar10 = lvar45;
   2. goto CG
      -> UnconditionalJump[GOTO] #N -> #CG
      <- Immediate #M -> #N
===#Block CG(size=2, flags=10100)===
   0. lvar70 = {700756486 ^ lvar70};
   1. goto BB
      -> UnconditionalJump[GOTO] #CG -> #BB
      <- UnconditionalJump[GOTO] #N -> #CG
===#Block BB(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 30170682)
      goto BA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BB -> #BA
      -> TryCatch range: [BB...BA] -> FI ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CG -> #BB
===#Block BA(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BB...BA] -> FI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BB -> #BA
===#Block FI(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case -**********:
      	 goto	#FK
      case -**********:
      	 goto	#FJ
      default:
      	 goto	#FL
   }
      -> Switch[-**********] #FI -> #FK
      -> Switch[-**********] #FI -> #FJ
      -> DefaultSwitch #FI -> #FL
      <- TryCatch range: [BB...BA] -> FI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BB...BA] -> FI ([Ljava/lang/IllegalAccessException;])
===#Block FL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #FI -> #FL
===#Block FJ(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto BC
      -> UnconditionalJump[GOTO] #FJ -> #BC
      <- Switch[-**********] #FI -> #FJ
===#Block FK(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, 695927369);
   1. goto BC
      -> UnconditionalJump[GOTO] #FK -> #BC
      <- Switch[-**********] #FI -> #FK
===#Block BC(size=2, flags=0)===
   0. _consume(catch());
   1. goto CI
      -> UnconditionalJump[GOTO] #BC -> #CI
      <- UnconditionalJump[GOTO] #FJ -> #BC
      <- UnconditionalJump[GOTO] #FK -> #BC
===#Block CI(size=2, flags=10100)===
   0. lvar70 = {468603208 ^ lvar70};
   1. goto Q
      -> UnconditionalJump[GOTO] #CI -> #Q
      <- UnconditionalJump[GOTO] #BC -> #CI
===#Block Q(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar10;
   2. svar72 = {lvar25 ^ lvar70};
   3. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(svar72)) {
      case 46185441:
      	 goto	#DU
      case 46185442:
      	 goto	#DV
      case 46185443:
      	 goto	#DX
      case 46185444:
      	 goto	#DZ
      default:
      	 goto	#EB
   }
      -> Switch[46185443] #Q -> #DX
      -> Switch[46185444] #Q -> #DZ
      -> Switch[46185441] #Q -> #DU
      -> Switch[46185442] #Q -> #DV
      -> DefaultSwitch #Q -> #EB
      <- UnconditionalJump[GOTO] #DM -> #Q
      <- UnconditionalJump[GOTO] #CM -> #Q
      <- UnconditionalJump[GOTO] #DQ -> #Q
      <- Immediate #L -> #Q
      <- UnconditionalJump[GOTO] #DC -> #Q
      <- UnconditionalJump[GOTO] #CK -> #Q
      <- UnconditionalJump[GOTO] #DF -> #Q
      <- UnconditionalJump[GOTO] #CI -> #Q
      <- UnconditionalJump[GOTO] #BW -> #Q
      <- UnconditionalJump[GOTO] #EJ -> #Q
      <- Switch[770419408] #DE -> #Q
      <- Switch[1979172361] #CL -> #Q
      <- UnconditionalJump[GOTO] #DD -> #Q
      <- Switch[265863395] #DL -> #Q
      <- Switch[185178216] #DB -> #Q
===#Block EB(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 46185444:
      	 goto	#EC
      case 189208514:
      	 goto	#V
      case 852971673:
      	 goto	#EB
      case 1645895665:
      	 goto	#BK
      default:
      	 goto	#BK
   }
      -> Switch[1645895665] #EB -> #BK
      -> DefaultSwitch #EB -> #BK
      -> Immediate #EB -> #EC
      -> Switch[46185444] #EB -> #EC
      -> Switch[852971673] #EB -> #EB
      -> Switch[189208514] #EB -> #V
      <- DefaultSwitch #Q -> #EB
      <- Switch[852971673] #EB -> #EB
===#Block EC(size=2, flags=100)===
   0. lvar70 = {2109742603 ^ lvar70};
   1. goto V
      -> UnconditionalJump[GOTO] #EC -> #V
      <- Immediate #EB -> #EC
      <- Switch[46185444] #EB -> #EC
===#Block V(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar0;
   2. lvar56 = lvar1;
   3. _consume(lvar29.showHelp(lvar56, 743956132));
   4. lvar70 = {2027671576 ^ lvar70};
      -> Immediate #V -> #AF
      <- UnconditionalJump[GOTO] #EC -> #V
      <- Switch[189208514] #EB -> #V
===#Block DV(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 46185444:
      	 goto	#DW
      case 314819328:
      	 goto	#R
      case 1441170716:
      	 goto	#BK
      case **********:
      	 goto	#DV
      default:
      	 goto	#BK
   }
      -> Switch[1441170716] #DV -> #BK
      -> DefaultSwitch #DV -> #BK
      -> Switch[314819328] #DV -> #R
      -> Immediate #DV -> #DW
      -> Switch[46185444] #DV -> #DW
      -> Switch[**********] #DV -> #DV
      <- Switch[46185442] #Q -> #DV
      <- Switch[**********] #DV -> #DV
===#Block DW(size=2, flags=100)===
   0. lvar70 = {70195639 ^ lvar70};
   1. goto R
      -> UnconditionalJump[GOTO] #DW -> #R
      <- Immediate #DV -> #DW
      <- Switch[46185444] #DV -> #DW
===#Block R(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar26 = lvar0;
   2. lvar54 = lvar1;
   3. _consume(lvar26.showLicenseInfo(lvar54, 698594572));
   4. lvar70 = {********** ^ lvar70};
      -> Immediate #R -> #S
      <- UnconditionalJump[GOTO] #DW -> #R
      <- Switch[314819328] #DV -> #R
===#Block S(size=1, flags=0)===
   0. goto BS
      -> UnconditionalJump[GOTO] #S -> #BS
      <- Immediate #R -> #S
===#Block BS(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 159633367:
      	 goto	#BT
      case 447694366:
      	 goto	#AJ
      case **********:
      	 goto	#BS
      case **********:
      	 goto	#BK
      default:
      	 goto	#BK
   }
      -> DefaultSwitch #BS -> #BK
      -> Switch[**********] #BS -> #BS
      -> Switch[447694366] #BS -> #AJ
      -> Switch[159633367] #BS -> #BT
      -> Switch[**********] #BS -> #BK
      -> Immediate #BS -> #BT
      <- Switch[**********] #BS -> #BS
      <- UnconditionalJump[GOTO] #S -> #BS
===#Block BT(size=2, flags=100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto AJ
      -> UnconditionalJump[GOTO] #BT -> #AJ
      <- Switch[159633367] #BS -> #BT
      <- Immediate #BS -> #BT
===#Block AJ(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 129343421)
      goto AI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AJ -> #AI
      -> TryCatch range: [AJ...AI] -> EK ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #BT -> #AJ
      <- Switch[447694366] #BS -> #AJ
===#Block AI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AJ...AI] -> EK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AJ -> #AI
===#Block EK(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case 672639041:
      	 goto	#EL
      case **********:
      	 goto	#EM
      default:
      	 goto	#EN
   }
      -> Switch[672639041] #EK -> #EL
      -> Switch[**********] #EK -> #EM
      -> DefaultSwitch #EK -> #EN
      <- TryCatch range: [AJ...AI] -> EK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AJ...AI] -> EK ([Ljava/lang/RuntimeException;])
===#Block EN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #EK -> #EN
===#Block EM(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto AK
      -> UnconditionalJump[GOTO] #EM -> #AK
      <- Switch[**********] #EK -> #EM
===#Block EL(size=2, flags=10100)===
   0. lvar70 = {475172065 ^ lvar70};
   1. goto AK
      -> UnconditionalJump[GOTO] #EL -> #AK
      <- Switch[672639041] #EK -> #EL
===#Block AK(size=2, flags=0)===
   0. _consume(catch());
   1. goto CP
      -> UnconditionalJump[GOTO] #AK -> #CP
      <- UnconditionalJump[GOTO] #EL -> #AK
      <- UnconditionalJump[GOTO] #EM -> #AK
===#Block CP(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto AF
      -> UnconditionalJump[GOTO] #CP -> #AF
      <- UnconditionalJump[GOTO] #AK -> #CP
===#Block DU(size=2, flags=10100)===
   0. lvar70 = {102818551 ^ lvar70};
   1. goto AB
      -> UnconditionalJump[GOTO] #DU -> #AB
      <- Switch[46185441] #Q -> #DU
===#Block AB(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar35 = lvar0;
   2. lvar60 = lvar1;
   3. _consume(lvar35.reloadLicense(lvar60, **********));
   4. lvar70 = {933386788 ^ lvar70};
      -> Immediate #AB -> #AC
      <- UnconditionalJump[GOTO] #DU -> #AB
===#Block AC(size=1, flags=0)===
   0. goto CW
      -> UnconditionalJump[GOTO] #AC -> #CW
      <- Immediate #AB -> #AC
===#Block CW(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, 759938651);
   1. goto BH
      -> UnconditionalJump[GOTO] #CW -> #BH
      <- UnconditionalJump[GOTO] #AC -> #CW
===#Block BH(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 174777508)
      goto BG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BH -> #BG
      -> TryCatch range: [BH...BG] -> FQ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #CW -> #BH
===#Block BG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BH...BG] -> FQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BH -> #BG
===#Block FQ(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case -**********:
      	 goto	#FS
      case -248146240:
      	 goto	#FR
      default:
      	 goto	#FT
   }
      -> DefaultSwitch #FQ -> #FT
      -> Switch[-**********] #FQ -> #FS
      -> Switch[-248146240] #FQ -> #FR
      <- TryCatch range: [BH...BG] -> FQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BH...BG] -> FQ ([Ljava/lang/IllegalAccessException;])
===#Block FR(size=2, flags=10100)===
   0. lvar70 = {1537230462 ^ lvar70};
   1. goto BI
      -> UnconditionalJump[GOTO] #FR -> #BI
      <- Switch[-248146240] #FQ -> #FR
===#Block FS(size=2, flags=10100)===
   0. lvar70 = {924074288 ^ lvar70};
   1. goto BI
      -> UnconditionalJump[GOTO] #FS -> #BI
      <- Switch[-**********] #FQ -> #FS
===#Block BI(size=2, flags=0)===
   0. _consume(catch());
   1. goto BZ
      -> UnconditionalJump[GOTO] #BI -> #BZ
      <- UnconditionalJump[GOTO] #FR -> #BI
      <- UnconditionalJump[GOTO] #FS -> #BI
===#Block BZ(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 112541575:
      	 goto	#CA
      case 161735273:
      	 goto	#BZ
      case 426267008:
      	 goto	#BK
      case **********:
      	 goto	#AF
      default:
      	 goto	#BK
   }
      -> Switch[**********] #BZ -> #AF
      -> DefaultSwitch #BZ -> #BK
      -> Switch[426267008] #BZ -> #BK
      -> Immediate #BZ -> #CA
      -> Switch[161735273] #BZ -> #BZ
      -> Switch[112541575] #BZ -> #CA
      <- UnconditionalJump[GOTO] #BI -> #BZ
      <- Switch[161735273] #BZ -> #BZ
===#Block CA(size=2, flags=100)===
   0. lvar70 = {1114803941 ^ lvar70};
   1. goto AF
      -> UnconditionalJump[GOTO] #CA -> #AF
      <- Immediate #BZ -> #CA
      <- Switch[112541575] #BZ -> #CA
===#Block FT(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #FQ -> #FT
===#Block DZ(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 46185444:
      	 goto	#EA
      case 473257301:
      	 goto	#BK
      case 570421374:
      	 goto	#W
      case 1313808521:
      	 goto	#DZ
      default:
      	 goto	#BK
   }
      -> Switch[46185444] #DZ -> #EA
      -> Switch[473257301] #DZ -> #BK
      -> DefaultSwitch #DZ -> #BK
      -> Switch[570421374] #DZ -> #W
      -> Immediate #DZ -> #EA
      -> Switch[1313808521] #DZ -> #DZ
      <- Switch[46185444] #Q -> #DZ
      <- Switch[1313808521] #DZ -> #DZ
===#Block EA(size=2, flags=100)===
   0. lvar70 = {1164251718 ^ lvar70};
   1. goto W
      -> UnconditionalJump[GOTO] #EA -> #W
      <- Switch[46185444] #DZ -> #EA
      <- Immediate #DZ -> #EA
===#Block W(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar30 = lvar4;
   2. lvar31 = lvar30.length;
   3. lvar57 = {********** ^ lvar70};
   4. if (lvar31 >= lvar57)
      goto DG
   5. lvar70 = {169994556 ^ lvar70};
      -> Immediate #W -> #X
      -> ConditionalJump[IF_ICMPGE] #W -> #DG
      <- UnconditionalJump[GOTO] #EA -> #W
      <- Switch[570421374] #DZ -> #W
===#Block DG(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto BJ
      -> UnconditionalJump[GOTO] #DG -> #BJ
      <- ConditionalJump[IF_ICMPGE] #W -> #DG
===#Block BJ(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto DN
   1. goto CN
      -> ConditionalJump[IF_ICMPEQ] #BJ -> #DN
      -> UnconditionalJump[GOTO] #BJ -> #CN
      <- UnconditionalJump[GOTO] #DG -> #BJ
===#Block CN(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto BK
      -> UnconditionalJump[GOTO] #CN -> #BK
      <- UnconditionalJump[GOTO] #BJ -> #CN
===#Block DN(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 121988456:
      	 goto	#DO
      case 250235509:
      	 goto	#BK
      case 413037161:
      	 goto	#Z
      case 739709635:
      	 goto	#DN
      default:
      	 goto	#BK
   }
      -> DefaultSwitch #DN -> #BK
      -> Switch[413037161] #DN -> #Z
      -> Switch[250235509] #DN -> #BK
      -> Immediate #DN -> #DO
      -> Switch[121988456] #DN -> #DO
      -> Switch[739709635] #DN -> #DN
      <- ConditionalJump[IF_ICMPEQ] #BJ -> #DN
      <- Switch[739709635] #DN -> #DN
===#Block DO(size=2, flags=100)===
   0. lvar70 = {79582939 ^ lvar70};
   1. goto Z
      -> UnconditionalJump[GOTO] #DO -> #Z
      <- Immediate #DN -> #DO
      <- Switch[121988456] #DN -> #DO
===#Block Z(size=8, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar34 = lvar0;
   2. lvar59 = lvar1;
   3. lvar7 = lvar4;
   4. lvar8 = {284854601 ^ lvar70};
   5. lvar67 = lvar7[lvar8];
   6. _consume(lvar34.setLicenseKey(lvar59, lvar67, **********));
   7. lvar70 = {64202808 ^ lvar70};
      -> Immediate #Z -> #AA
      <- UnconditionalJump[GOTO] #DO -> #Z
      <- Switch[413037161] #DN -> #Z
===#Block AA(size=1, flags=0)===
   0. goto CS
      -> UnconditionalJump[GOTO] #AA -> #CS
      <- Immediate #Z -> #AA
===#Block CS(size=2, flags=10100)===
   0. lvar70 = {510769825 ^ lvar70};
   1. goto AS
      -> UnconditionalJump[GOTO] #CS -> #AS
      <- UnconditionalJump[GOTO] #AA -> #CS
===#Block AS(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 165388476)
      goto AR
   1. throw nullconst;
      -> TryCatch range: [AS...AR] -> EW ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AS -> #AR
      <- UnconditionalJump[GOTO] #CS -> #AS
===#Block AR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AS...AR] -> EW ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AS -> #AR
===#Block EW(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case 620175825:
      	 goto	#EY
      case **********:
      	 goto	#EX
      default:
      	 goto	#EZ
   }
      -> Switch[620175825] #EW -> #EY
      -> Switch[**********] #EW -> #EX
      -> DefaultSwitch #EW -> #EZ
      <- TryCatch range: [AS...AR] -> EW ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AS...AR] -> EW ([Ljava/lang/IllegalAccessException;])
===#Block EZ(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #EW -> #EZ
===#Block EX(size=2, flags=10100)===
   0. lvar70 = {********** ^ lvar70};
   1. goto AT
      -> UnconditionalJump[GOTO] #EX -> #AT
      <- Switch[**********] #EW -> #EX
===#Block EY(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto AT
      -> UnconditionalJump[GOTO] #EY -> #AT
      <- Switch[620175825] #EW -> #EY
===#Block AT(size=2, flags=0)===
   0. _consume(catch());
   1. goto CV
      -> UnconditionalJump[GOTO] #AT -> #CV
      <- UnconditionalJump[GOTO] #EX -> #AT
      <- UnconditionalJump[GOTO] #EY -> #AT
===#Block CV(size=2, flags=10100)===
   0. lvar70 = com.hang.plugin.commands.LicenseCommand.emfobofyeqcnaqqn(lvar70, **********);
   1. goto AF
      -> UnconditionalJump[GOTO] #CV -> #AF
      <- UnconditionalJump[GOTO] #AT -> #CV
===#Block X(size=4, flags=0)===
   0. lvar32 = lvar1;
   1. lvar58 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.ncjwiedqjrrprii(), lvar70);
   2. _consume(lvar32.sendMessage(lvar58));
   3. lvar70 = {949421258 ^ lvar70};
      -> Immediate #X -> #Y
      <- Immediate #W -> #X
===#Block Y(size=2, flags=0)===
   0. lvar33 = {********** ^ lvar70};
   1. return lvar33;
      <- Immediate #X -> #Y
===#Block DX(size=1, flags=10100)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70)) {
      case 46185444:
      	 goto	#DY
      case **********:
      	 goto	#BK
      case **********:
      	 goto	#T
      case **********:
      	 goto	#DX
      default:
      	 goto	#BK
   }
      -> Switch[46185444] #DX -> #DY
      -> Switch[**********] #DX -> #BK
      -> Switch[**********] #DX -> #DX
      -> Switch[**********] #DX -> #T
      -> Immediate #DX -> #DY
      -> DefaultSwitch #DX -> #BK
      <- Switch[46185443] #Q -> #DX
      <- Switch[**********] #DX -> #DX
===#Block BK(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      <- DefaultSwitch #CQ -> #BK
      <- Switch[1645895665] #EB -> #BK
      <- DefaultSwitch #EB -> #BK
      <- Switch[**********] #CQ -> #BK
      <- DefaultSwitch #BS -> #BK
      <- UnconditionalJump[GOTO] #CH -> #BK
      <- UnconditionalJump[GOTO] #BU -> #BK
      <- Switch[1268958967] #DB -> #BK
      <- DefaultSwitch #DB -> #BK
      <- UnconditionalJump[GOTO] #CC -> #BK
      <- DefaultSwitch #DE -> #BK
      <- Switch[462123520] #DS -> #BK
      <- Switch[**********] #BS -> #BK
      <- Switch[878247230] #DE -> #BK
      <- DefaultSwitch #DS -> #BK
      <- Switch[473257301] #DZ -> #BK
      <- DefaultSwitch #DZ -> #BK
      <- DefaultSwitch #DN -> #BK
      <- DefaultSwitch #BZ -> #BK
      <- UnconditionalJump[GOTO] #CD -> #BK
      <- Switch[426267008] #BZ -> #BK
      <- Switch[250235509] #DN -> #BK
      <- UnconditionalJump[GOTO] #BX -> #BK
      <- UnconditionalJump[GOTO] #CN -> #BK
      <- Switch[**********] #DX -> #BK
      <- Switch[199971224] #DL -> #BK
      <- DefaultSwitch #DL -> #BK
      <- DefaultSwitch #CL -> #BK
      <- UnconditionalJump[GOTO] #CJ -> #BK
      <- Switch[**********] #CZ -> #BK
      <- DefaultSwitch #CB -> #BK
      <- Switch[99462205] #CB -> #BK
      <- DefaultSwitch #DX -> #BK
      <- UnconditionalJump[GOTO] #CR -> #BK
      <- DefaultSwitch #CZ -> #BK
      <- Switch[1441170716] #DV -> #BK
      <- DefaultSwitch #DV -> #BK
      <- Switch[506211408] #CL -> #BK
      <- DefaultSwitch #EG -> #BK
      <- Switch[**********] #EG -> #BK
      <- Switch[**********] #DH -> #BK
      <- DefaultSwitch #DH -> #BK
===#Block DY(size=2, flags=100)===
   0. lvar70 = {11919326 ^ lvar70};
   1. goto T
      -> UnconditionalJump[GOTO] #DY -> #T
      <- Switch[46185444] #DX -> #DY
      <- Immediate #DX -> #DY
===#Block T(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar28 = lvar0;
   2. lvar55 = lvar1;
   3. _consume(lvar28.verifyLicense(lvar55, 544591340));
   4. lvar70 = {127541631 ^ lvar70};
      -> Immediate #T -> #U
      <- UnconditionalJump[GOTO] #DY -> #T
      <- Switch[**********] #DX -> #T
===#Block U(size=1, flags=0)===
   0. goto BY
      -> UnconditionalJump[GOTO] #U -> #BY
      <- Immediate #T -> #U
===#Block BY(size=2, flags=10100)===
   0. lvar70 = {755026748 ^ lvar70};
   1. goto AP
      -> UnconditionalJump[GOTO] #BY -> #AP
      <- UnconditionalJump[GOTO] #U -> #BY
===#Block AP(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 250926013)
      goto AO
   1. throw nullconst;
      -> TryCatch range: [AP...AO] -> ES ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AP -> #AO
      <- UnconditionalJump[GOTO] #BY -> #AP
===#Block AO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AP...AO] -> ES ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AP -> #AO
===#Block ES(size=1, flags=0)===
   0. switch (jyuggekqztwpctck.fqppxskiymtfbbek.sqxebenapriaykub(lvar70)) {
      case 24691378:
      	 goto	#EU
      case 1174670953:
      	 goto	#ET
      default:
      	 goto	#EV
   }
      -> Switch[1174670953] #ES -> #ET
      -> Switch[24691378] #ES -> #EU
      -> DefaultSwitch #ES -> #EV
      <- TryCatch range: [AP...AO] -> ES ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AP...AO] -> ES ([Ljava/lang/IllegalAccessException;])
===#Block EV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ES -> #EV
===#Block EU(size=2, flags=10100)===
   0. lvar70 = {849850297 ^ lvar70};
   1. goto AQ
      -> UnconditionalJump[GOTO] #EU -> #AQ
      <- Switch[24691378] #ES -> #EU
===#Block ET(size=2, flags=10100)===
   0. lvar70 = {1514681890 ^ lvar70};
   1. goto AQ
      -> UnconditionalJump[GOTO] #ET -> #AQ
      <- Switch[1174670953] #ES -> #ET
===#Block AQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto CO
      -> UnconditionalJump[GOTO] #AQ -> #CO
      <- UnconditionalJump[GOTO] #EU -> #AQ
      <- UnconditionalJump[GOTO] #ET -> #AQ
===#Block CO(size=2, flags=10100)===
   0. lvar70 = {1971113388 ^ lvar70};
   1. goto AF
      -> UnconditionalJump[GOTO] #CO -> #AF
      <- UnconditionalJump[GOTO] #AQ -> #CO
===#Block AF(size=3, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar27 = {133462594 ^ lvar70};
   2. return lvar27;
      <- Switch[**********] #BZ -> #AF
      <- UnconditionalJump[GOTO] #CE -> #AF
      <- Immediate #V -> #AF
      <- UnconditionalJump[GOTO] #CP -> #AF
      <- UnconditionalJump[GOTO] #CO -> #AF
      <- UnconditionalJump[GOTO] #CA -> #AF
      <- UnconditionalJump[GOTO] #CV -> #AF
===#Block AG(size=4, flags=0)===
   0. lvar49 = lvar1;
   1. lvar66 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.ixxdivzehgcrwag(), lvar70);
   2. _consume(lvar49.sendMessage(lvar66));
   3. lvar70 = {648850775 ^ lvar70};
      -> Immediate #AG -> #AH
      <- Immediate #B -> #AG
===#Block AH(size=2, flags=0)===
   0. lvar50 = {********** ^ lvar70};
   1. return lvar50;
      <- Immediate #AG -> #AH
