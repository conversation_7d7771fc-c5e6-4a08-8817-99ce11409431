package com.hang.plugin.api;

import com.hang.plugin.HangPlugin;
import org.bukkit.entity.Player;
import org.bukkit.configuration.file.FileConfiguration;

/**
 * HangEvacuation核心插件API接口
 * 供扩展插件使用
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface HangEvacuationAPI {

    /**
     * 获取插件实例
     * @return 核心插件实例
     */
    HangPlugin getPlugin();

    /**
     * 获取插件版本
     * @return 插件版本字符串
     */
    String getVersion();

    /**
     * 检查玩家权限
     * @param player 玩家
     * @param permission 权限节点
     * @return 是否有权限
     */
    boolean hasPermission(Player player, String permission);

    /**
     * 发送消息给玩家
     * @param player 玩家
     * @param message 消息内容
     */
    void sendMessage(Player player, String message);

    /**
     * 发送带变量替换的消息
     * @param player 玩家
     * @param messageKey 消息键
     * @param replacements 替换变量 (key1, value1, key2, value2, ...)
     */
    void sendMessage(Player player, String messageKey, String... replacements);

    /**
     * 获取配置文件
     * @return 主配置文件
     */
    FileConfiguration getConfig();

    /**
     * 保存配置文件
     */
    void saveConfig();

    /**
     * 重载配置文件
     */
    void reloadConfig();

    // 扩展插件系统已移除，现在使用独立插件架构

    /**
     * 记录信息日志
     * @param message 日志消息
     */
    void logInfo(String message);

    /**
     * 记录警告日志
     * @param message 日志消息
     */
    void logWarning(String message);

    /**
     * 记录错误日志
     * @param message 日志消息
     */
    void logError(String message);

    /**
     * 记录错误日志（带异常）
     * @param message 日志消息
     * @param throwable 异常对象
     */
    void logError(String message, Throwable throwable);
}
