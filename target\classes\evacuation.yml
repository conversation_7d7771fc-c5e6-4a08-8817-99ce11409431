# HangEvacuation - 撤离系统配置文件
# 撤离系统专用配置，通用设置请参考核心插件配置

# 撤离点设置
evacuation:
  # 撤离倒计时时间 (秒)
  countdown-time: 10
  # 检测范围 (方块)
  detection-range: 5
  # 检测间隔 (tick, 20tick = 1秒)
  check-interval: 20

  # 撤离倒计时提示设置
  countdown-notifications:
    # 聊天消息提示开关
    chat-message:
      enabled: true
    # Title标题提示开关
    title:
      enabled: true
    # ActionBar动作栏提示开关
    actionbar:
      enabled: true

# 消息设置
messages:
  prefix: "§6[撤离] §r"

  # 撤离相关消息
  evacuation-enter: "§e进入撤离区域，开始倒计时..."
  evacuation-countdown: "§c撤离倒计时: {time} 秒"
  evacuation-success: "§a成功撤离！"
  evacuation-cancelled: "§c撤离已取消"

  # 撤离Title消息 (主标题和副标题)
  evacuation-title-enter: "§6撤离区域"
  evacuation-subtitle-enter: "§e开始倒计时..."
  evacuation-title-countdown: "§c撤离倒计时"
  evacuation-subtitle-countdown: "§e{time} 秒"
  evacuation-title-success: "§a撤离成功！"
  evacuation-subtitle-success: "§7已传送到安全区域"
  evacuation-title-cancelled: "§c撤离取消"
  evacuation-subtitle-cancelled: "§7已离开撤离区域"

  # 撤离ActionBar消息
  evacuation-actionbar-enter: "§e进入撤离区域"
  evacuation-actionbar-countdown: "§c撤离倒计时: {time}"
  evacuation-actionbar-success: "§a撤离成功！"
  evacuation-actionbar-cancelled: "§c撤离取消"

  # 命令相关消息
  no-permission: "§c您没有权限执行此命令！"
  player-only: "§c此命令只能由玩家执行！"
  invalid-usage: "§c用法错误！请查看帮助信息。"

  # 撤离区域管理消息
  zone-created: "§a撤离区域 §6{name} §a创建成功！"
  zone-removed: "§a撤离区域 §6{name} §a已移除！"
  zone-not-found: "§c撤离区域 {name} 不存在！"
  zone-already-exists: "§c撤离区域 {name} 已存在！"

  # 工具相关消息
  tool-given: "§a已给予您撤离点选择工具！"
  tool-usage: "§e左键点击选择第一个坐标，右键点击选择第二个坐标"
  position-selected: "§a已选择{position}位置: §e{x}, {y}, {z}"
  both-positions-selected: "§a已选择完成！区域大小: §e{sizeX}x{sizeY}x{sizeZ} §a(共 §e{volume} §a个方块)"

  # 撤离目标相关消息
  destination-set: "§a最终撤离目标已设置为当前位置！"
  destination-not-set: "§c未设置最终撤离目标位置！"
  teleported-to-safety: "§a您已被传送到安全区域！"
