package com.hang.plugin.listeners;

import com.hang.plugin.HangPlugin;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

/**
 * 聊天监听器 - 处理等级前缀显示
 * 
 * <AUTHOR>
 * @version 1.4.0
 */
public class ChatListener implements Listener {
    
    private final HangPlugin plugin;
    
    public ChatListener(HangPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 处理玩家聊天事件，添加等级前缀
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        // 检查是否启用聊天等级显示
        if (!plugin.getLevelManager().isShowLevelInChat()) {
            return;
        }
        
        Player player = event.getPlayer();
        String levelDisplay = plugin.getLevelManager().getPlayerLevelDisplay(player);
        
        // 获取原始聊天格式
        String format = event.getFormat();
        
        // 在玩家名前添加等级前缀
        // 原格式通常是: <%1$s> %2$s
        // 修改为: [等级]<%1$s> %2$s
        if (format.contains("%1$s")) {
            format = format.replace("%1$s", levelDisplay + "%1$s");
        } else {
            // 如果格式不标准，直接在前面添加
            format = levelDisplay + format;
        }
        
        event.setFormat(format);
    }
}
