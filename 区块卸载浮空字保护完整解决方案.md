# 区块卸载浮空字保护完整解决方案 (增强版)

## 🚨 **问题描述**

在Minecraft服务器中，当包含浮空字的区块被卸载时，会出现以下问题：
- **浮空字实体丢失**：区块卸载时，浮空字实体被移除
- **数据不同步**：浮空字消失但插件仍认为它存在
- **无法自动恢复**：区块重新加载时浮空字不会自动重建
- **影响用户体验**：玩家看不到摸金箱的状态信息
- **跨世界问题**：玩家切换世界时浮空字状态异常
- **服务器重启**：重启后浮空字可能不会正确恢复

## ✅ **完整解决方案**

### **🛡️ 六层保护机制 (增强版)**

#### **1. 数据备份系统**
```java
// 浮空字数据备份类
private static class HologramData {
    private final Location location;      // 位置信息
    private String text;                  // 显示文本
    private final boolean invisible;      // 样式设置
    private final boolean noGravity;
    private final boolean noPickup;
    private final boolean marker;
    private final boolean small;
}
```

#### **2. 区块事件监听**
```java
// 区块加载时自动重建浮空字
@EventHandler
public void onChunkLoad(ChunkLoadEvent event) {
    // 延迟5tick确保区块完全加载
    plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
        // 检查该区块中的摸金箱并重建浮空字
        recreateHologramsInChunk(event.getChunk());
    }, 5L);
}

// 区块卸载时备份浮空字信息
@EventHandler
public void onChunkUnload(ChunkUnloadEvent event) {
    // 备份该区块中所有浮空字的状态
    backupHologramsInChunk(event.getChunk());
}
```

#### **3. 定期检查任务**
```java
// 每30秒自动检查浮空字状态
private void startChunkCheckTask() {
    chunkCheckTask = new BukkitRunnable() {
        @Override
        public void run() {
            checkAndRecreateHolograms();
        }
    }.runTaskTimer(plugin, 600L, 600L);
}
```

#### **4. 更新任务保护**
```java
// 在浮空字更新任务中检查并重建丢失的浮空字
private void startHologramUpdateTask() {
    new BukkitRunnable() {
        @Override
        public void run() {
            for (Map.Entry<String, TreasureChestData> entry : treasureChestData.entrySet()) {
                Location location = parseLocationFromString(entry.getKey());

                // 检查区块是否加载
                if (!location.getChunk().isLoaded()) {
                    continue;
                }

                // 检查浮空字是否存在，不存在则重建
                if (!plugin.getHologramManager().hasHologram(location)) {
                    if (hasPlayersNearby(location, detectionRange)) {
                        recreateHologram(location, entry.getValue());
                    }
                }

                // 正常更新逻辑...
            }
        }
    }.runTaskTimer(plugin, updateInterval, updateInterval);
}
```

#### **5. 智能重建机制**
```java
// 检查并重建丢失的浮空字
private void checkAndRecreateHolograms() {
    for (Map.Entry<String, HologramData> entry : hologramBackups.entrySet()) {
        String id = entry.getKey();
        HologramData backup = entry.getValue();
        ArmorStand hologram = holograms.get(id);

        // 检查浮空字是否丢失或死亡
        if (hologram == null || hologram.isDead()) {
            // 检查区块是否加载且有玩家在附近
            if (backup.getLocation().getChunk().isLoaded() &&
                hasPlayersNearby(backup.getLocation(), detectionRange)) {
                recreateHologramFromBackup(id, backup.getText());
            }
        }
    }
}
```

#### **6. 跨世界保护**
```java
// 玩家切换世界时清理状态
@EventHandler
public void onPlayerChangedWorld(PlayerChangedWorldEvent event) {
    Player player = event.getPlayer();

    // 清理跨世界的GUI状态
    cleanupPlayerGUIStates(player);

    // 清理撤离区域状态
    cleanupEvacuationStates(player);
}
```

### **⚙️ 完整配置选项**

```yaml
treasure-chest:
  hologram:
    # 区块卸载保护设置
    chunk_protection:
      # 是否启用区块卸载保护
      enabled: true

      # 区块检查间隔 (tick, 20tick = 1秒)
      check_interval: 600  # 30秒

      # 玩家检测范围 (方块) - 只在此范围内有玩家时才重建浮空字
      player_detection_range: 64

      # 是否强制加载浮空字所在的区块
      force_load_chunks: true
```

### **🎮 管理命令**

```bash
# 查看浮空字统计信息
/evac hologram stats

# 强制重建所有浮空字
/evac hologram rebuild

# 手动检查并修复浮空字
/evac hologram check

# 清理所有浮空字
/evac hologram clear confirm
```

## 🔧 **技术实现细节**

### **1. 创建时备份**
```java
public void createHologram(String id, Location location, String text) {
    // 检查区块是否已加载
    if (!location.getChunk().isLoaded()) {
        if (plugin.getConfig().getBoolean("...force_load_chunks", true)) {
            location.getChunk().load();
        } else {
            return; // 跳过创建
        }
    }

    // 创建浮空字实体
    ArmorStand armorStand = (ArmorStand) location.getWorld().spawnEntity(...);

    // 备份浮空字数据
    hologramBackups.put(id, new HologramData(location, text, ...));
}
```

### **2. 更新时同步**
```java
public void updateHologram(String id, String text) {
    ArmorStand hologram = holograms.get(id);
    if (hologram != null && !hologram.isDead()) {
        hologram.setCustomName(text);

        // 同时更新备份数据
        HologramData backup = hologramBackups.get(id);
        if (backup != null) {
            backup.setText(text);
        }
    } else {
        // 如果浮空字实体丢失，尝试从备份重建
        HologramData backup = hologramBackups.get(id);
        if (backup != null) {
            recreateHologramFromBackup(id, text);
        }
    }
}
```

### **3. 智能检测**
```java
private boolean hasPlayersNearby(Location location, double radius) {
    for (Player player : plugin.getServer().getOnlinePlayers()) {
        if (player.getWorld().equals(location.getWorld()) &&
            player.getLocation().distance(location) <= radius) {
            return true;
        }
    }
    return false;
}
```

## 📊 **性能优化**

### **1. 检查频率控制**
- **默认间隔**：30秒检查一次
- **可配置**：管理员可调整检查频率
- **智能跳过**：无玩家区域不进行重建

### **2. 内存管理**
- **ConcurrentHashMap**：线程安全的数据存储
- **及时清理**：移除浮空字时同步清理备份
- **异常处理**：完善的错误恢复机制

### **3. 网络优化**
- **批量处理**：一次检查处理多个浮空字
- **条件重建**：只在必要时重建浮空字
- **日志记录**：详细的操作日志便于调试

## 🎯 **使用场景**

### **场景1：玩家离开区域**
1. 玩家离开摸金箱区域
2. 区块在一段时间后被卸载
3. 浮空字实体被移除
4. 备份数据保留在内存中
5. 玩家返回时区块重新加载
6. 检查任务发现浮空字丢失
7. 自动从备份重建浮空字

### **场景2：服务器重启**
1. 服务器关闭时清理所有浮空字
2. 重启后插件重新加载
3. 摸金箱数据从文件恢复
4. 浮空字根据摸金箱状态重新创建
5. 备份数据同步建立

### **场景3：跨世界传送**
1. 玩家传送到其他世界
2. 原世界区块可能被卸载
3. 浮空字实体消失
4. 玩家返回原世界时
5. 区块重新加载
6. 浮空字自动重建

## 🔍 **故障排除**

### **问题1：浮空字不重建**
**可能原因**：
- 区块保护被禁用
- 检查间隔设置过长
- 玩家检测范围过小

**解决方案**：
```bash
/evac hologram stats    # 查看状态
/evac hologram rebuild  # 强制重建
```

### **问题2：性能影响**
**可能原因**：
- 检查间隔过短
- 浮空字数量过多
- 玩家检测范围过大

**解决方案**：
```yaml
chunk_protection:
  check_interval: 1200  # 增加到60秒
  player_detection_range: 32  # 减少检测范围
```

### **问题3：内存占用**
**可能原因**：
- 备份数据未及时清理
- 大量无效的浮空字备份

**解决方案**：
```bash
/evac hologram clear confirm  # 清理所有浮空字
/evac reload                  # 重载配置
```

## 📈 **监控和维护**

### **统计信息**
```bash
/evac hologram stats
# 输出示例：
# 浮空字统计: 活跃=15, 死亡=2, 丢失=1, 备份=18
# 区块检查任务: 运行中 (每30秒检查一次)
```

### **日志监控**
```
[INFO] 区块卸载保护已启动，检查间隔: 30.0秒
[INFO] 重建了 3 个丢失的浮空字
[INFO] 为浮空字强制加载区块: world_100_64_200
```

### **定期维护**
- **每周检查**：使用 `/evac hologram stats` 查看状态
- **性能监控**：观察服务器TPS是否受影响
- **配置调优**：根据服务器负载调整检查间隔

## 🎉 **总结**

这个完整的解决方案提供了：

✅ **自动检测**：定期检查浮空字状态
✅ **智能重建**：只在需要时重建浮空字
✅ **数据备份**：完整保存浮空字信息
✅ **性能优化**：最小化对服务器性能的影响
✅ **管理工具**：丰富的命令用于监控和维护
✅ **配置灵活**：所有参数都可以自定义
✅ **日志完善**：详细的操作记录便于调试

现在您再也不用担心区块卸载导致的浮空字问题了！
