package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.utils.VersionUtils;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;


import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 倒计时管理器
 *
 * <AUTHOR>
 */
public class CountdownManager {

    private final HangPlugin plugin;
    private final Map<UUID, CountdownTask> activeCountdowns;

    public CountdownManager(HangPlugin plugin) {
        this.plugin = plugin;
        this.activeCountdowns = new HashMap<>();
    }

    /**
     * 开始撤离倒计时
     *
     * @param player 玩家
     * @param evacuationPointName 撤离点名称
     */
    public void startEvacuationCountdown(Player player, String evacuationPointName) {
        UUID playerId = player.getUniqueId();

        // 如果玩家已经有倒计时，先取消
        cancelCountdown(playerId);

        // 从evacuation.yml读取配置
        org.bukkit.configuration.file.FileConfiguration evacuationConfig = getEvacuationConfig();
        int countdownTime = evacuationConfig.getInt("evacuation.countdown-time", 10);

        // 🔧 新增：发送进入撤离区域的多种提示
        sendEvacuationNotification(player, "enter", null);

        // 创建新的倒计时任务
        CountdownTask task = new CountdownTask(player, evacuationPointName, countdownTime, this, plugin);
        activeCountdowns.put(playerId, task);

        // 启动倒计时
        task.runTaskTimer(plugin, 0L, 20L); // 每秒执行一次
    }

    /**
     * 取消玩家的倒计时
     *
     * @param playerId 玩家UUID
     */
    public void cancelCountdown(UUID playerId) {
        CountdownTask task = activeCountdowns.remove(playerId);
        if (task != null) {
            task.cancel();

            Player player = plugin.getServer().getPlayer(playerId);
            if (player != null && player.isOnline()) {
                // 🔧 新增：发送撤离取消的多种提示
                sendEvacuationNotification(player, "cancelled", null);
            }
        }
    }

    /**
     * 检查玩家是否有活跃的倒计时
     *
     * @param playerId 玩家UUID
     * @return 是否有活跃的倒计时
     */
    public boolean hasActiveCountdown(UUID playerId) {
        return activeCountdowns.containsKey(playerId);
    }

    /**
     * 获取玩家的剩余倒计时时间
     *
     * @param playerId 玩家UUID
     * @return 剩余时间（秒），如果没有倒计时则返回-1
     */
    public int getRemainingTime(UUID playerId) {
        CountdownTask task = activeCountdowns.get(playerId);
        return task != null ? task.getRemainingTime() : -1;
    }

    /**
     * 关闭管理器，取消所有倒计时
     */
    public void shutdown() {
        for (CountdownTask task : activeCountdowns.values()) {
            task.cancel();
        }
        activeCountdowns.clear();
    }

    /**
     * 🔧 新增：发送撤离通知（支持聊天、Title、ActionBar三种方式）
     *
     * @param player 玩家
     * @param type 通知类型 (enter, countdown, success, cancelled)
     * @param timeValue 时间值（仅在countdown类型时使用）
     */
    private void sendEvacuationNotification(Player player, String type, String timeValue) {
        // 从evacuation.yml读取配置
        org.bukkit.configuration.file.FileConfiguration evacuationConfig = getEvacuationConfig();

        // 检查聊天消息开关
        if (evacuationConfig.getBoolean("evacuation.countdown-notifications.chat-message.enabled", true)) {
            String messageKey = "evacuation-" + type;
            if (timeValue != null) {
                player.sendMessage(plugin.getEvacuationMessage(messageKey, "time", timeValue));
            } else {
                player.sendMessage(plugin.getEvacuationMessage(messageKey));
            }
        }

        // 检查Title开关
        if (evacuationConfig.getBoolean("evacuation.countdown-notifications.title.enabled", true)) {
            String titleKey = "evacuation-title-" + type;
            String subtitleKey = "evacuation-subtitle-" + type;

            // 🔧 修复：使用getRawEvacuationMessage获取不带前缀的Title消息
            String title = plugin.getRawEvacuationMessage(titleKey);
            String subtitle = plugin.getRawEvacuationMessage(subtitleKey);

            // 替换时间变量
            if (timeValue != null) {
                subtitle = subtitle.replace("{time}", timeValue);
            }

            // 发送Title (显示2秒)
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                try {
                    plugin.getNMSManager().getAdapter().sendTitle(player, title, subtitle, 10, 40, 10);
                } catch (Exception e) {
                    plugin.getLogger().warning("发送Title失败: " + e.getMessage());
                }
            }
        }

        // 检查ActionBar开关
        if (evacuationConfig.getBoolean("evacuation.countdown-notifications.actionbar.enabled", true)) {
            String actionbarKey = "evacuation-actionbar-" + type;

            // 🔧 修复：使用getRawEvacuationMessage获取不带前缀的ActionBar消息
            String actionbarMessage = plugin.getRawEvacuationMessage(actionbarKey);

            // 替换时间变量
            if (timeValue != null) {
                actionbarMessage = actionbarMessage.replace("{time}", timeValue);
            }

            // 发送ActionBar
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                try {
                    plugin.getNMSManager().getAdapter().sendActionBar(player, actionbarMessage);
                } catch (Exception e) {
                    plugin.getLogger().warning("发送ActionBar失败: " + e.getMessage());
                }
            }
        }
    }

    /**
     * 倒计时任务类
     */
    private static class CountdownTask extends BukkitRunnable {

        private final Player player;
        private final String evacuationPointName;
        private int remainingTime;
        private final CountdownManager manager;
        private final HangPlugin plugin;

        public CountdownTask(Player player, String evacuationPointName, int initialTime, CountdownManager manager, HangPlugin plugin) {
            this.player = player;
            this.evacuationPointName = evacuationPointName;
            this.remainingTime = initialTime;
            this.manager = manager;
            this.plugin = plugin;
        }

        @Override
        public void run() {
            // 检查玩家是否还在线
            if (!player.isOnline()) {
                cancel();
                manager.activeCountdowns.remove(player.getUniqueId());
                return;
            }

            // 检查玩家是否还在撤离区域内
            String currentZone = plugin.getEvacuationSystem().checkPlayerInEvacuationZone(player);
            if (currentZone == null || !currentZone.equals(evacuationPointName)) {
                // 玩家离开了撤离区域，取消倒计时
                cancel();
                manager.activeCountdowns.remove(player.getUniqueId());
                // 🔧 新增：发送撤离取消的多种提示
                manager.sendEvacuationNotification(player, "cancelled", null);
                return;
            }

            if (remainingTime <= 0) {
                // 倒计时结束，撤离成功
                cancel();
                manager.activeCountdowns.remove(player.getUniqueId());
                handleEvacuationSuccess();
                return;
            }

            // 🔧 新增：发送倒计时的多种提示
            manager.sendEvacuationNotification(player, "countdown", String.valueOf(remainingTime));

            // 可以添加音效或粒子效果
            if (remainingTime <= 5) {
                // 最后5秒播放音效
                VersionUtils.playCompatibleSound(player, "NOTE_PLING", 1.0f, 1.0f);
            }

            remainingTime--;
        }

        /**
         * 处理撤离成功
         */
        private void handleEvacuationSuccess() {
            // 🔧 新增：发送撤离成功的多种提示
            manager.sendEvacuationNotification(player, "success", null);

            // 播放成功音效
            VersionUtils.playCompatibleSound(player, "LEVEL_UP", 1.0f, 1.0f);

            // 传送玩家到最终撤离目标
            org.bukkit.Location finalDestination = plugin.getEvacuationSystem().getFinalDestination();
            if (finalDestination != null) {
                player.teleport(finalDestination);
                player.sendMessage("§a您已被传送到安全区域！");
                plugin.getLogger().info("玩家 " + player.getName() + " 撤离成功，已传送到最终目标");
            } else {
                player.sendMessage("§c撤离成功，但未设置最终撤离目标位置！");
                plugin.getLogger().warning("撤离成功但未设置最终撤离目标，玩家: " + player.getName());
            }

            // 这里可以添加撤离成功的奖励逻辑
            // 例如：给予经验、物品等
        }

        /**
         * 获取剩余时间
         *
         * @return 剩余时间（秒）
         */
        public int getRemainingTime() {
            return remainingTime;
        }

        @Override
        public synchronized void cancel() throws IllegalStateException {
            super.cancel();
        }
    }

    /**
     * 获取撤离配置文件
     */
    private org.bukkit.configuration.file.FileConfiguration getEvacuationConfig() {
        java.io.File evacuationFile = new java.io.File(plugin.getDataFolder(), "evacuation.yml");
        if (!evacuationFile.exists()) {
            plugin.saveResource("evacuation.yml", false);
        }
        return org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(evacuationFile);
    }
}
