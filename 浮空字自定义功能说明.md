# 浮空字自定义功能说明

## 🎨 **功能概述**

为摸金箱浮空字添加了完整的自定义功能，包括文本内容、位置、样式等多方面的个性化设置。

## ⚙️ **配置选项**

### **1. 基础开关**
```yaml
treasure-chest:
  # 是否启用浮空字提示 (全局设置)
  hologram_enabled: true
```

### **2. 位置偏移设置**
```yaml
treasure-chest:
  hologram:
    # 浮空字位置偏移
    position:
      x: 0.5      # X轴偏移 (默认箱子中心)
      y: 1.5      # Y轴偏移 (箱子上方高度)
      z: 0.5      # Z轴偏移 (默认箱子中心)
```

**说明**：
- `x: 0.5, z: 0.5` 表示浮空字显示在方块的中心位置
- `y: 1.5` 表示浮空字显示在方块上方1.5格的高度
- 可以使用负数来调整位置（如 `y: -0.5` 显示在方块下方）

### **3. 文本内容自定义**
```yaml
treasure-chest:
  hologram:
    messages:
      # 未搜索状态 - 支持变量: {count} = 剩余物品数量, {total} = 总物品数量
      unsearched: "§6还有 §e{count} §6个物品未搜索"

      # 已搜索完毕状态
      fully_searched: "§a已搜索完毕"

      # 可以刷新状态
      can_refresh: "§a可以刷新！右键重新生成物品"

      # 刷新倒计时 - 支持变量: {minutes} = 分钟, {seconds} = 秒数
      refresh_countdown: "§e刷新倒计时: §c{minutes}:{seconds}"

      # 搜索中状态 - 支持变量: {player} = 搜索者名称
      searching: "§b{player} §7正在搜索中..."
```

**支持的变量**：
- `{count}` - 剩余未搜索的物品数量
- `{total}` - 摸金箱中的总物品数量
- `{minutes}` - 刷新倒计时的分钟数
- `{seconds}` - 刷新倒计时的秒数（自动补零，如 "05"）
- `{player}` - 当前正在搜索的玩家名称

### **4. 样式设置**
```yaml
treasure-chest:
  hologram:
    style:
      # 是否显示为小型盔甲架
      small: true

      # 是否设置为标记模式 (无碰撞箱)
      marker: true

      # 是否隐藏盔甲架本体
      invisible: true

      # 是否禁用重力
      no_gravity: true

      # 是否禁止拾取物品
      no_pickup: true
```

**样式说明**：
- `small: true` - 浮空字使用小型盔甲架，占用空间更小
- `marker: true` - 设置为标记模式，玩家无法与浮空字碰撞
- `invisible: true` - 隐藏盔甲架本体，只显示文字
- `no_gravity: true` - 禁用重力，浮空字不会掉落
- `no_pickup: true` - 禁止拾取物品，避免意外交互

### **5. 更新频率设置**
```yaml
treasure-chest:
  hologram:
    # 浮空字更新频率 (tick, 20tick = 1秒)
    update_interval: 20
```

## 🎯 **使用示例**

### **示例1：简约风格**
```yaml
treasure-chest:
  hologram:
    messages:
      unsearched: "§7剩余: §f{count}"
      fully_searched: "§a✓ 完成"
      can_refresh: "§e⟲ 可刷新"
      refresh_countdown: "§c{minutes}:{seconds}"
      searching: "§b{player}搜索中"
    position:
      y: 1.2  # 降低高度
```

### **示例2：详细信息风格**
```yaml
treasure-chest:
  hologram:
    messages:
      unsearched: "§6[摸金箱] §e还有{count}个物品等待发现 §7(共{total}个)"
      fully_searched: "§a[摸金箱] 所有宝藏已被发现！"
      can_refresh: "§a[摸金箱] 宝藏已刷新，右键重新开始探索！"
      refresh_countdown: "§e[摸金箱] 下次刷新: §c{minutes}分{seconds}秒"
      searching: "§b[摸金箱] §f{player} §7正在探索宝藏..."
```

### **示例3：游戏化风格**
```yaml
treasure-chest:
  hologram:
    messages:
      unsearched: "§6💰 §e{count}个宝藏待发现"
      fully_searched: "§a✨ 宝藏已清空"
      can_refresh: "§a🔄 新宝藏已到达！"
      refresh_countdown: "§e⏰ 补给倒计时 §c{minutes}:{seconds}"
      searching: "§b🔍 §f{player} §7正在寻宝..."
    position:
      y: 2.0  # 更高的位置
```

## 🔧 **管理命令**

### **重载配置**
```
/evac reload
```
**功能**：
- 重新加载所有配置文件
- 自动更新现有浮空字的样式和文本
- 如果浮空字被禁用，会自动移除所有现有浮空字

**权限**：`evacuation.reload`

## 🎨 **颜色代码参考**

### **常用颜色**
- `§0` - 黑色
- `§1` - 深蓝色
- `§2` - 深绿色
- `§3` - 深青色
- `§4` - 深红色
- `§5` - 深紫色
- `§6` - 金色
- `§7` - 灰色
- `§8` - 深灰色
- `§9` - 蓝色
- `§a` - 绿色
- `§b` - 青色
- `§c` - 红色
- `§d` - 粉色
- `§e` - 黄色
- `§f` - 白色

### **格式代码**
- `§l` - 粗体
- `§m` - 删除线
- `§n` - 下划线
- `§o` - 斜体
- `§r` - 重置格式

## 📊 **状态说明**

### **浮空字显示的状态**

1. **未搜索状态**
   - 显示剩余物品数量
   - 使用 `unsearched` 配置

2. **搜索中状态**
   - 显示当前搜索者名称
   - 使用 `searching` 配置
   - 只在有玩家正在搜索时显示

3. **已搜索完毕状态**
   - 显示搜索完成信息
   - 使用 `fully_searched` 配置

4. **可以刷新状态**
   - 显示可以刷新的提示
   - 使用 `can_refresh` 配置
   - 在刷新时间到达时显示

5. **刷新倒计时状态**
   - 显示距离下次刷新的时间
   - 使用 `refresh_countdown` 配置
   - 实时更新倒计时

## 🚀 **高级技巧**

### **1. 动态内容**
使用变量创建动态内容：
```yaml
unsearched: "§6宝藏进度: §e{count}§7/§e{total} §6剩余"
```

### **2. 多行显示**
虽然单个浮空字不支持多行，但可以通过多个浮空字实现：
```yaml
# 可以通过调整y偏移创建多个浮空字
position:
  y: 1.8  # 主浮空字
# 如需多行，可以创建额外的浮空字管理器
```

### **3. 条件显示**
通过 `hologram_enabled` 配置可以完全禁用浮空字：
```yaml
hologram_enabled: false  # 完全禁用浮空字功能
```

## 🎉 **总结**

浮空字自定义功能提供了：

✅ **完全自定义的文本内容**
✅ **灵活的位置调整**
✅ **丰富的样式选项**
✅ **动态变量支持**
✅ **实时配置重载**
✅ **多种显示状态**

通过这些配置选项，您可以创建符合服务器风格的个性化浮空字显示效果！
