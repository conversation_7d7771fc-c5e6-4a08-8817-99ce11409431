# 1.12.2音效兼容性修复

## 🐛 **问题描述**

在Minecraft 1.12.2版本中，以下两个音效无法正常播放：
- `entity.experience_orb.pickup` (搜索进行中音效)
- `entity.item.pickup` (物品拾取音效)

这是因为1.12.2版本的音效命名格式与1.13+版本不同。

## 🔧 **修复方案**

### **1. 版本检测与兼容性处理**

在 `VersionUtils.java` 中添加了专门的1.12.2版本音效处理：

```java
// 🔧 1.12.2版本特殊处理
if (isVersionAtLeast(1, 12) && !isVersionAtLeast(1, 13)) {
    Sound sound = get1_12_2CompatibleSound(soundName);
    if (sound != null) {
        player.playSound(player.getLocation(), sound, volume, pitch);
        return;
    }
}
```

### **2. 1.12.2音效映射表**

| 配置中的音效名称 | 1.12.2版本实际音效 | 1.13+版本音效 |
|---|---|---|
| `entity.experience_orb.pickup` | `ENTITY_EXPERIENCE_ORB_PICKUP` | `entity.experience_orb.pickup` |
| `entity.item.pickup` | `ENTITY_ITEM_PICKUP` | `entity.item.pickup` |
| `entity.player.levelup` | `ENTITY_PLAYER_LEVELUP` | `entity.player.levelup` |
| `block.chest.open` | `BLOCK_CHEST_OPEN` | `block.chest.open` |
| `block.chest.close` | `BLOCK_CHEST_CLOSE` | `block.chest.close` |
| `block.note_block.pling` | `BLOCK_NOTE_PLING` | `block.note_block.pling` |

### **3. 自动兼容性处理**

系统会自动检测服务器版本并使用正确的音效格式：

```java
private static Sound get1_12_2CompatibleSound(String soundName) {
    switch (soundName.toLowerCase()) {
        case "entity.experience_orb.pickup":
            try {
                return Sound.valueOf("ENTITY_EXPERIENCE_ORB_PICKUP");
            } catch (IllegalArgumentException e) {
                return Sound.valueOf("CLICK"); // 备用音效
            }
        case "entity.item.pickup":
            try {
                return Sound.valueOf("ENTITY_ITEM_PICKUP");
            } catch (IllegalArgumentException e) {
                return Sound.valueOf("CLICK"); // 备用音效
            }
        // ... 其他音效映射
    }
}
```

## ⚙️ **配置选项**

### **自动兼容模式（推荐）**

保持配置文件中的默认设置，系统会自动处理版本兼容性：

```yaml
treasure-chest:
  animation:
    sounds:
      search-start:
        enabled: true
        sound: "entity.experience_orb.pickup"  # 自动兼容
        volume: 0.5
        pitch: 1.0
      
      item-pickup:
        enabled: true
        sound: "entity.item.pickup"  # 自动兼容
        volume: 0.6
        pitch: 1.3
```

### **手动指定1.12.2音效**

如果需要手动指定1.12.2版本的音效，可以直接使用枚举名称：

```yaml
treasure-chest:
  animation:
    sounds:
      search-start:
        enabled: true
        sound: "ENTITY_EXPERIENCE_ORB_PICKUP"  # 1.12.2专用
        volume: 0.5
        pitch: 1.0
      
      item-pickup:
        enabled: true
        sound: "ENTITY_ITEM_PICKUP"  # 1.12.2专用
        volume: 0.6
        pitch: 1.3
```

## 🎯 **测试验证**

### **测试步骤**

1. **搜索进行中音效测试**：
   - 打开摸金箱
   - 开始搜索物品
   - 应该听到经验球拾取音效

2. **物品拾取音效测试**：
   - 搜索完成后点击物品
   - 将物品拖拽到背包
   - 应该听到物品拾取音效

### **故障排除**

如果音效仍然无法播放：

1. **检查音效是否启用**：
   ```yaml
   search-start:
     enabled: true  # 确保为true
   ```

2. **检查音量设置**：
   ```yaml
   volume: 0.5  # 确保不为0
   ```

3. **尝试备用音效**：
   ```yaml
   sound: "CLICK"  # 使用通用点击音效
   ```

4. **启用调试模式**：
   ```yaml
   debug:
     enabled: true
   ```

## 🔄 **版本兼容性**

### **支持的版本**
- ✅ Minecraft 1.12.2 - 使用枚举格式音效
- ✅ Minecraft 1.13+ - 使用字符串格式音效
- ✅ 自动检测版本并使用正确格式

### **备用机制**
如果指定的音效无法播放，系统会按以下顺序尝试：
1. 原始音效名称
2. 版本兼容的音效名称
3. 通用的CLICK音效
4. 静默处理（不播放音效）

## 📋 **更新内容**

### **修改的文件**
1. `VersionUtils.java` - 添加1.12.2音效兼容性处理
2. `config.yml` - 更新音效配置注释
3. 所有音效播放方法 - 使用兼容性播放方法

### **新增功能**
- 自动版本检测
- 1.12.2专用音效映射
- 多级备用音效机制
- 详细的配置注释

## 🚀 **使用建议**

1. **保持默认配置**：让系统自动处理版本兼容性
2. **测试音效**：在1.12.2服务器上测试所有音效功能
3. **调整音量**：根据服务器需要调整音效音量
4. **备用方案**：如有问题可临时禁用音效或使用CLICK音效

现在1.12.2版本的音效应该可以正常播放了！
