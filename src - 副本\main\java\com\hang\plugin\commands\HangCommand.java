package com.hang.plugin.commands;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.utils.VersionUtils;

import org.bukkit.Location;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 插件命令处理器
 *
 * <AUTHOR>
 */
public class HangCommand implements CommandExecutor, TabCompleter {

    private final HangPlugin plugin;

    public HangCommand(HangPlugin plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        String commandName = command.getName().toLowerCase();

        switch (commandName) {
            case "evacuation":
                return handleMainCommand(sender, args);
            case "evac":
                return handleEvacuationCommand(sender, args);
            default:
                return false;
        }
    }

    /**
     * 处理主命令 /evacuation
     */
    private boolean handleMainCommand(CommandSender sender, String[] args) {
        if (args.length == 0) {
            sender.sendMessage("§6=== 摸金插件 ===");
            sender.sendMessage("§e/evac give - 给予摸金箱");
            sender.sendMessage("§e/evac - 撤离点管理");
            sender.sendMessage("§e/evacuation reload - 重载配置文件");
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "reload":
                if (!sender.hasPermission("evacuation.reload")) {
                    sender.sendMessage(plugin.getMessage("no-permission"));
                    return true;
                }

                plugin.reloadPluginConfig();
                sender.sendMessage("§a配置文件已重载！");
                return true;

            case "help":
                sender.sendMessage("§6=== 摸金插件帮助 ===");
                sender.sendMessage("§e/evac give [玩家] [数量] - 给予摸金箱");
                sender.sendMessage("§e/evac gui - 打开战利品管理界面");
                sender.sendMessage("§e/evacuation reload - 重载配置文件");
                sender.sendMessage("§e/evacuation debug [verbose] - 显示调试信息");
                sender.sendMessage("§e/evacuation save [cleanup] - 强制保存所有数据");
                sender.sendMessage("§7  添加 cleanup 参数可同时清理残留浮空字");
                sender.sendMessage("§e/evacuation refresh [类型] [世界名] - 刷新浮空字和摸金箱");
                sender.sendMessage("§7  类型: all, holograms, chests, items");
                sender.sendMessage("§7  世界名: 可选，指定要刷新的世界");
                return true;

            case "debug":
                if (!sender.hasPermission("evacuation.debug")) {
                    sender.sendMessage(plugin.getMessage("no-permission"));
                    return true;
                }
                return handleDebugCommand(sender, args);

            case "save":
                if (!sender.hasPermission("evacuation.admin")) {
                    sender.sendMessage(plugin.getMessage("no-permission"));
                    return true;
                }
                return handleSaveCommand(sender, args);

            case "refresh":
                if (!sender.hasPermission("evacuation.admin")) {
                    sender.sendMessage(plugin.getMessage("no-permission"));
                    return true;
                }
                return handleRefreshCommand(sender, args);

            default:
                sender.sendMessage("§c未知的子命令。使用 /evacuation help 查看帮助。");
                return true;
        }
    }

    /**
     * 处理给予摸金箱命令 /evac give [种类] [玩家] [数量]
     */
    private boolean handleGiveChest(CommandSender sender, String[] args) {
        // OP权限已在调用前检查过了

        String chestType = "common"; // 默认种类
        Player targetPlayer = null;
        int amount = 1;

        // 解析参数 (args[0] 是 "give")
        if (args.length == 1) {
            // /evac give - 给自己默认摸金箱
            if (sender instanceof Player) {
                targetPlayer = (Player) sender;
            } else {
                sender.sendMessage("§c控制台必须指定玩家名称！");
                return true;
            }
        } else if (args.length == 2) {
            // /evac give <种类或玩家>
            // 先检查是否为摸金箱种类
            if (plugin.getChestTypeManager().getChestType(args[1]) != null) {
                // 是种类，给自己
                chestType = args[1];
                if (sender instanceof Player) {
                    targetPlayer = (Player) sender;
                } else {
                    sender.sendMessage("§c控制台必须指定玩家名称！");
                    return true;
                }
            } else {
                // 是玩家名，给指定玩家默认摸金箱
                targetPlayer = plugin.getServer().getPlayer(args[1]);
                if (targetPlayer == null) {
                    sender.sendMessage("§c玩家 " + args[1] + " 不在线！");
                    return true;
                }
            }
        } else if (args.length == 3) {
            // /evac give <种类> <玩家> 或 /evac give <玩家> <数量>
            if (plugin.getChestTypeManager().getChestType(args[1]) != null) {
                // 第一个参数是种类
                chestType = args[1];
                targetPlayer = plugin.getServer().getPlayer(args[2]);
                if (targetPlayer == null) {
                    sender.sendMessage("§c玩家 " + args[2] + " 不在线！");
                    return true;
                }
            } else {
                // 第一个参数是玩家名，第二个是数量
                targetPlayer = plugin.getServer().getPlayer(args[1]);
                if (targetPlayer == null) {
                    sender.sendMessage("§c玩家 " + args[1] + " 不在线！");
                    return true;
                }

                try {
                    amount = Integer.parseInt(args[2]);
                    if (amount <= 0 || amount > 64) {
                        sender.sendMessage("§c数量必须在1-64之间！");
                        return true;
                    }
                } catch (NumberFormatException e) {
                    sender.sendMessage("§c无效的数量！");
                    return true;
                }
            }
        } else if (args.length == 4) {
            // /evac give <种类> <玩家> <数量>
            chestType = args[1];
            if (plugin.getChestTypeManager().getChestType(chestType) == null) {
                sender.sendMessage("§c未知的摸金箱种类: " + chestType);
                sender.sendMessage("§e可用种类: " + String.join(", ", plugin.getChestTypeManager().getAllChestTypes()
                    .stream().map(type -> type.getTypeId()).toArray(String[]::new)));
                return true;
            }

            targetPlayer = plugin.getServer().getPlayer(args[2]);
            if (targetPlayer == null) {
                sender.sendMessage("§c玩家 " + args[2] + " 不在线！");
                return true;
            }

            try {
                amount = Integer.parseInt(args[3]);
                if (amount <= 0 || amount > 64) {
                    sender.sendMessage("§c数量必须在1-64之间！");
                    return true;
                }
            } catch (NumberFormatException e) {
                sender.sendMessage("§c无效的数量！");
                return true;
            }
        } else {
            sender.sendMessage("§c用法: /evac give [种类] [玩家] [数量]");
            sender.sendMessage("§e示例: /evac give weapon player1 5");
            return true;
        }

        // 验证摸金箱种类
        if (plugin.getChestTypeManager().getChestType(chestType) == null) {
            sender.sendMessage("§c未知的摸金箱种类: " + chestType);
            return true;
        }

        // 给予摸金箱物品
        org.bukkit.inventory.ItemStack treasureChest = plugin.getChestTypeManager().createChestItem(chestType, amount);
        targetPlayer.getInventory().addItem(treasureChest);

        // 获取种类显示名称
        String typeName = plugin.getChestTypeManager().getChestType(chestType).getDisplayName();

        // 发送消息
        if (sender.equals(targetPlayer)) {
            sender.sendMessage("§a已给予您 " + amount + " 个 " + typeName + "§a！");
        } else {
            sender.sendMessage("§a已给予 " + targetPlayer.getName() + " " + amount + " 个 " + typeName + "§a！");
            targetPlayer.sendMessage("§a您收到了 " + amount + " 个 " + typeName + "§a！");
        }

        return true;
    }



    /**
     * 显示evac命令帮助信息
     */
    private void showEvacHelp(Player player) {
        if (!player.isOp()) {
            player.sendMessage("§c此命令只能由服务器管理员使用！");
            return;
        }

        player.sendMessage("§6§l=== EVAC 摸金系统命令帮助 ===");
        player.sendMessage("");
        player.sendMessage("§e§l摸金箱管理:");
        player.sendMessage("  §a/evac give [种类] [玩家] [数量] §7- 给予摸金箱物品");
        player.sendMessage("  §a/evac gui §7- 打开摸金箱种类选择界面 §d(支持模组物品)");
        player.sendMessage("  §a/evac addtype <种类ID> <名称> §7- 添加新的摸金箱种类 §c(管理员)");
        player.sendMessage("  §a/evac removetype <种类ID> §7- 移除摸金箱种类 §c(管理员)");
        player.sendMessage("  §a/evac listtypes §7- 列出所有摸金箱种类");
        player.sendMessage("");
        player.sendMessage("§e§l系统功能:");
        player.sendMessage("  §a/evac save §7- 手动保存所有数据");
        player.sendMessage("  §a/evac refresh [类型] [世界名] §7- 刷新浮空字和摸金箱 §c(重要)");
        player.sendMessage("  §a/evac hologram [操作] §7- 浮空字管理命令");
        player.sendMessage("  §a/evac reload §7- 重载配置文件");
        player.sendMessage("  §a/evac nms §7- 查看NMS适配器信息");
        player.sendMessage("");
        player.sendMessage("§e§l撤离系统:");
        player.sendMessage("  §a/evac tool §7- 获取撤离点选择工具");
        player.sendMessage("  §a/evac create <名称> §7- 创建撤离区域 §c(管理员)");
        player.sendMessage("  §a/evac remove <名称> §7- 移除撤离区域 §c(管理员)");
        player.sendMessage("  §a/evac list §7- 列出所有撤离区域");
        player.sendMessage("  §a/evac setdest §7- 设置最终撤离目标位置 §c(管理员)");
        player.sendMessage("  §a/evac info §7- 查看撤离系统信息");
        player.sendMessage("");
        player.sendMessage("§e§l等级系统:");
        player.sendMessage("  §a/evac level §7- 查看自己的摸金等级信息");
        player.sendMessage("");
        player.sendMessage("§c注意: 标记为管理员的命令需要管理员权限！");
        player.sendMessage("§c刷新操作会清空摸金箱并重置玩家搜索进度！");
    }

    /**
     * 处理撤离点命令 /evac
     */
    private boolean handleEvacuationCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // 显示evac命令帮助信息
            showEvacHelp(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();



        // 检查OP权限
        if (!player.isOp()) {
            player.sendMessage("§c此命令只能由服务器管理员使用！");
            return true;
        }

        switch (subCommand) {
            case "give":
                return handleGiveChest(sender, args);



            case "gui":
                if (!player.hasPermission("evacuation.admin")) {
                    player.sendMessage("§c你没有权限使用此命令！");
                    return true;
                }

                // 打开摸金箱种类选择界面
                com.hang.plugin.gui.ChestTypeSelectionGUI selectionGUI =
                    new com.hang.plugin.gui.ChestTypeSelectionGUI(plugin, player);

                // 注册GUI到监听器
                plugin.getPlayerListener().registerChestTypeSelectionGUI(player, selectionGUI);

                selectionGUI.open();
                return true;





            case "nms":
            case "version":
                // 显示NMS适配器信息
                if (plugin.getNMSManager() != null) {
                    String info = plugin.getNMSManager().getVersionInfo();
                    for (String line : info.split("\n")) {
                        player.sendMessage("§6[摸金] §r" + line);
                    }
                } else {
                    player.sendMessage("§6[摸金] §cNMS管理器未初始化");
                }
                return true;



            case "addtype":
                return handleAddChestType(sender, args);

            case "removetype":
                return handleRemoveChestType(sender, args);

            case "listtypes":
                return handleListChestTypes(sender);

            case "reload":
                if (!sender.hasPermission("evacuation.reload")) {
                    sender.sendMessage("§c您没有权限使用此命令！");
                    return true;
                }

                plugin.reloadPluginConfig();

                // 重新加载浮空字配置后，更新所有现有的浮空字
                plugin.getServer().getScheduler().runTaskLater(plugin, new Runnable() {
                    @Override
                    public void run() {
                        // 检查浮空字是否被禁用
                        if (!plugin.getTreasureItemManager().isHologramEnabled()) {
                            // 如果被禁用，移除所有浮空字
                            plugin.getHologramManager().removeAllHolograms();
                            sender.sendMessage("§e浮空字已禁用，所有现有浮空字已移除");
                        } else {
                            // 如果启用，更新所有浮空字的样式和文本
                            plugin.getHologramManager().updateAllHologramsStyle();
                            sender.sendMessage("§a浮空字配置已更新，所有浮空字已刷新");
                        }
                    }
                }, 1L);

                sender.sendMessage("§a配置文件已重载！");
                return true;



            case "hologram":
            case "holo":
                // 浮空字管理命令
                return handleHologramCommand(player, args);

            case "save":
                return handleSaveCommand(sender, args);

            case "refresh":
                if (!player.hasPermission("evacuation.admin")) {
                    player.sendMessage("§c您没有权限使用此命令！");
                    return true;
                }
                return handleRefreshCommand(sender, args);

            // 撤离系统命令
            case "tool":
                return handleEvacuationTool(player);
            case "create":
                return handleCreateEvacuationZone(player, args);
            case "remove":
                return handleRemoveEvacuationZone(player, args);
            case "list":
                return handleListEvacuationZones(player);
            case "setdest":
                return handleSetDestination(player);
            case "info":
                return handleEvacuationInfo(player);

            // 等级系统命令
            case "level":
                return handleLevelCommand(player, args);

            default:
                player.sendMessage("§c未知的子命令。用法: /evac <give|gui|save|refresh|hologram|tool|create|remove|list|setdest|level>");
                return true;
        }
    }

    /**
     * 处理撤离点选择工具命令
     */
    private boolean handleEvacuationTool(Player player) {
        if (!player.hasPermission("evacuation.evacuation")) {
            player.sendMessage(plugin.getEvacuationMessage("no-permission"));
            return true;
        }

        // 创建撤离点选择工具
        org.bukkit.inventory.ItemStack tool = new org.bukkit.inventory.ItemStack(org.bukkit.Material.BLAZE_ROD);
        org.bukkit.inventory.meta.ItemMeta meta = tool.getItemMeta();
        meta.setDisplayName("§6撤离点选择工具");
        List<String> lore = new ArrayList<>();
        lore.add("§e左键点击: §7选择第一个坐标");
        lore.add("§e右键点击: §7选择第二个坐标");
        meta.setLore(lore);
        tool.setItemMeta(meta);

        // 给予玩家工具
        player.getInventory().addItem(tool);
        player.sendMessage(plugin.getEvacuationMessage("tool-given"));
        player.sendMessage(plugin.getEvacuationMessage("tool-usage"));

        return true;
    }

    /**
     * 处理创建撤离区域命令
     */
    private boolean handleCreateEvacuationZone(Player player, String[] args) {
        if (!player.hasPermission("evacuation.admin.evacuation")) {
            player.sendMessage(plugin.getEvacuationMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            player.sendMessage("§c用法: /evac create <名称>");
            return true;
        }

        String name = args[1];
        boolean success = plugin.getEvacuationSystem().createEvacuationZone(player, name);

        return true;
    }

    /**
     * 处理移除撤离区域命令
     */
    private boolean handleRemoveEvacuationZone(Player player, String[] args) {
        if (!player.hasPermission("evacuation.admin.evacuation")) {
            player.sendMessage(plugin.getEvacuationMessage("no-permission"));
            return true;
        }

        if (args.length < 2) {
            player.sendMessage("§c用法: /evac remove <名称>");
            return true;
        }

        String name = args[1];
        boolean removed = plugin.getEvacuationSystem().removeEvacuationZone(name);

        if (removed) {
            player.sendMessage(plugin.getEvacuationMessage("zone-removed", "name", name));
        } else {
            player.sendMessage(plugin.getEvacuationMessage("zone-not-found", "name", name));
        }

        return true;
    }

    /**
     * 处理列出撤离区域命令
     */
    private boolean handleListEvacuationZones(Player player) {
        if (!player.hasPermission("evacuation.evacuation")) {
            player.sendMessage(plugin.getEvacuationMessage("no-permission"));
            return true;
        }

        Set<String> zones = plugin.getEvacuationSystem().getEvacuationZoneNames();

        if (zones.isEmpty()) {
            player.sendMessage("§e当前没有设置任何撤离区域");
        } else {
            player.sendMessage("§6撤离区域列表 §7(共 " + zones.size() + " 个):");
            for (String zone : zones) {
                player.sendMessage("§e- " + zone);
            }
        }

        return true;
    }

    /**
     * 处理设置撤离目标命令
     */
    private boolean handleSetDestination(Player player) {
        if (!player.hasPermission("evacuation.admin.evacuation")) {
            player.sendMessage(plugin.getEvacuationMessage("no-permission"));
            return true;
        }

        plugin.getEvacuationSystem().setFinalDestination(player.getLocation());
        player.sendMessage(plugin.getEvacuationMessage("destination-set"));

        return true;
    }

    /**
     * 处理撤离信息命令
     */
    private boolean handleEvacuationInfo(Player player) {
        if (!player.hasPermission("evacuation.evacuation")) {
            player.sendMessage(plugin.getEvacuationMessage("no-permission"));
            return true;
        }

        int zoneCount = plugin.getEvacuationSystem().getEvacuationZoneCount();
        Location destination = plugin.getEvacuationSystem().getFinalDestination();

        player.sendMessage("§6撤离系统信息:");
        player.sendMessage("§e- 撤离区域数量: §f" + zoneCount);

        if (destination != null) {
            player.sendMessage("§e- 最终撤离目标: §f" +
                destination.getWorld().getName() + " (" +
                destination.getBlockX() + ", " +
                destination.getBlockY() + ", " +
                destination.getBlockZ() + ")");
        } else {
            player.sendMessage("§e- 最终撤离目标: §c未设置");
        }

        return true;
    }

    /**
     * 处理等级命令
     */
    private boolean handleLevelCommand(Player player, String[] args) {
        if (!player.hasPermission("evacuation.level")) {
            player.sendMessage("§c您没有权限使用此命令！");
            return true;
        }

        if (plugin.getLevelManager() == null) {
            player.sendMessage("§c等级系统未启用！");
            return true;
        }

        // 获取玩家等级数据
        com.hang.plugin.manager.LevelManager.PlayerLevelData playerData =
            plugin.getLevelManager().getPlayerData(player.getUniqueId());

        // 获取等级信息
        com.hang.plugin.manager.LevelManager.LevelInfo levelInfo =
            plugin.getLevelManager().getAllLevels().get(playerData.getLevel());

        if (levelInfo == null) {
            player.sendMessage("§c无法获取等级信息！");
            return true;
        }

        // 显示等级信息
        player.sendMessage("§6您的摸金等级信息:");
        player.sendMessage("§e- 等级: §f" + playerData.getLevel() + " §7(" +
            plugin.getLevelManager().getColoredLevelName(levelInfo) + "§7)");
        player.sendMessage("§e- 搜索次数: §f" + playerData.getSearches());

        // 计算下一级所需搜索次数
        int nextLevel = playerData.getLevel() + 1;
        com.hang.plugin.manager.LevelManager.LevelInfo nextLevelInfo =
            plugin.getLevelManager().getAllLevels().get(nextLevel);

        if (nextLevelInfo != null) {
            int needed = nextLevelInfo.getRequiredSearches() - playerData.getSearches();
            player.sendMessage("§e- 距离下一级 §f" + nextLevel + " §e还需: §f" +
                Math.max(0, needed) + " §e次搜索");
        } else {
            player.sendMessage("§e- 您已达到最高等级！");
        }

        return true;
    }



    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        String commandName = command.getName().toLowerCase();

        switch (commandName) {
            case "evacuation":
                if (args.length == 1) {
                    completions.addAll(Arrays.asList("reload", "help", "debug", "save", "refresh"));
                } else if (args.length == 2 && args[0].equalsIgnoreCase("refresh")) {
                    // refresh 子命令补全
                    completions.addAll(Arrays.asList("all", "holograms", "chests", "items"));
                } else if (args.length == 3 && args[0].equalsIgnoreCase("refresh")) {
                    // 世界名补全
                    addWorldCompletions(completions);
                } else if (args.length == 4 && args[0].equalsIgnoreCase("refresh") && args[1].equalsIgnoreCase("items")) {
                    // items 命令的 confirm 参数
                    completions.add("confirm");
                }
                break;

            case "evac":
                if (args.length == 1) {
                    completions.addAll(Arrays.asList("give", "gui", "nms", "version", "addtype", "removetype", "listtypes", "reload", "save", "hologram", "holo", "refresh", "tool", "create", "remove", "list", "setdest", "info", "level"));
                } else if (args.length == 2 && args[0].equalsIgnoreCase("give")) {
                    // 添加摸金箱种类补全
                    if (plugin.getChestTypeManager() != null) {
                        for (com.hang.plugin.manager.ChestTypeManager.ChestType chestType : plugin.getChestTypeManager().getEnabledChestTypes()) {
                            completions.add(chestType.getTypeId());
                        }
                    }
                } else if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
                    // 添加在线玩家名称补全
                    for (org.bukkit.entity.Player player : plugin.getServer().getOnlinePlayers()) {
                        completions.add(player.getName());
                    }
                } else if (args.length == 2 && (args[0].equalsIgnoreCase("hologram") || args[0].equalsIgnoreCase("holo"))) {
                    completions.addAll(Arrays.asList("stats", "rebuild", "check", "clear", "cleanup", "distance"));
                } else if (args.length == 3 && (args[0].equalsIgnoreCase("hologram") || args[0].equalsIgnoreCase("holo")) && args[1].equalsIgnoreCase("distance")) {
                    completions.addAll(Arrays.asList("16", "32", "48", "64"));
                } else if (args.length == 2 && args[0].equalsIgnoreCase("refresh")) {
                    // refresh 子命令补全
                    completions.addAll(Arrays.asList("all", "holograms", "chests", "items"));
                } else if (args.length == 3 && args[0].equalsIgnoreCase("refresh")) {
                    // 世界名补全
                    addWorldCompletions(completions);
                } else if (args.length == 4 && args[0].equalsIgnoreCase("refresh") && args[1].equalsIgnoreCase("items")) {
                    // items 命令的 confirm 参数
                    completions.add("confirm");
                } else if (args.length == 2 && args[0].equalsIgnoreCase("remove")) {
                    // 撤离区域名称补全
                    if (plugin.getEvacuationSystem() != null) {
                        completions.addAll(plugin.getEvacuationSystem().getEvacuationZoneNames());
                    }
                }
                break;
        }

        // 过滤匹配的补全选项
        String input = args.length > 0 ? args[args.length - 1].toLowerCase() : "";
        completions.removeIf(completion -> !completion.toLowerCase().startsWith(input));

        return completions;
    }

    /**
     * 添加世界名补全
     */
    private void addWorldCompletions(List<String> completions) {
        for (org.bukkit.World world : plugin.getServer().getWorlds()) {
            completions.add(world.getName());
        }
    }

    /**
     * 处理添加摸金箱种类命令 /evac addtype
     */
    private boolean handleAddChestType(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;

        if (!player.hasPermission("evacuation.admin")) {
            player.sendMessage("§c您没有权限使用此命令！");
            return true;
        }

        if (args.length < 3) {
            player.sendMessage("§c用法: /evac addtype <种类ID> <名称> [材料] [槽位数] [刷新时间]");
            player.sendMessage("§7示例: /evac addtype custom \"§d自定义箱\" CHEST 5 10");
            return true;
        }

        String typeId = args[1];
        String name = args[2];
        String material = args.length > 3 ? args[3] : "CHEST";
        int slots = args.length > 4 ? Integer.parseInt(args[4]) : 5;
        int refreshTime = args.length > 5 ? Integer.parseInt(args[5]) : 5;

        // 检查种类ID是否包含中文字符并给出警告
        if (containsChinese(typeId)) {
            player.sendMessage("§e警告: 种类ID包含中文字符，可能导致兼容性问题！");
            player.sendMessage("§e建议使用英文字符，如: weapon, medical, custom 等");
            player.sendMessage("§7继续创建中...");
        }

        // 创建描述
        List<String> description = Arrays.asList(
            "§7自定义摸金箱",
            "§7通过命令添加",
            "§e右键打开搜索宝藏"
        );

        boolean success = plugin.getChestTypeManager().addChestType(
            typeId, name, name, description, material, 0,
            "§f普通", "§f", true, slots, refreshTime
        );

        if (success) {
            player.sendMessage("§a成功添加摸金箱种类: " + typeId + " (" + name + "§a)");
        } else {
            player.sendMessage("§c添加摸金箱种类失败！种类ID可能已存在。");
        }

        return true;
    }

    /**
     * 处理移除摸金箱种类命令 /evac removetype
     */
    private boolean handleRemoveChestType(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;

        if (!player.hasPermission("evacuation.admin")) {
            player.sendMessage("§c您没有权限使用此命令！");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage("§c用法: /evac removetype <种类ID>");
            return true;
        }

        String typeId = args[1];

        // 防止删除默认种类
        if ("common".equals(typeId)) {
            player.sendMessage("§c不能删除默认摸金箱种类！");
            return true;
        }

        boolean success = plugin.getChestTypeManager().removeChestType(typeId);

        if (success) {
            player.sendMessage("§a成功移除摸金箱种类: " + typeId);
        } else {
            player.sendMessage("§c移除摸金箱种类失败！种类ID不存在。");
        }

        return true;
    }

    /**
     * 处理列出摸金箱种类命令 /evac listtypes
     */
    private boolean handleListChestTypes(CommandSender sender) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§c此命令只能由玩家执行！");
            return true;
        }

        Player player = (Player) sender;

        Collection<com.hang.plugin.manager.ChestTypeManager.ChestType> allTypes =
            plugin.getChestTypeManager().getAllChestTypes();

        if (allTypes.isEmpty()) {
            player.sendMessage("§e当前没有配置任何摸金箱种类。");
            return true;
        }

        player.sendMessage("§6=== 摸金箱种类列表 ===");
        for (com.hang.plugin.manager.ChestTypeManager.ChestType chestType : allTypes) {
            String status = chestType.isEnabled() ? "§a启用" : "§c禁用";
            String slotsInfo = chestType.isRandomSlots() ?
                "§b" + chestType.getSlotsDisplayText() :
                "§7" + chestType.getSlotsDisplayText();
            player.sendMessage(String.format("§e%s §7- %s §7[%s§7] §7槽位:%s §7刷新:%d分钟",
                chestType.getTypeId(),
                chestType.getName(),
                status,
                slotsInfo,
                chestType.getRefreshTime()
            ));
        }

        return true;
    }



    /**
     * 检查字符串是否包含中文字符
     */
    private boolean containsChinese(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }

        for (char c : str.toCharArray()) {
            // 检查是否为中文字符（Unicode范围）
            if (c >= 0x4E00 && c <= 0x9FFF) {
                return true;
            }
        }
        return false;
    }





    /**
     * 处理浮空字管理命令
     */
    private boolean handleHologramCommand(Player player, String[] args) {
        if (!player.hasPermission("evacuation.admin")) {
            player.sendMessage("§c您没有权限使用此命令！");
            return true;
        }

        if (args.length < 2) {
            player.sendMessage("§6=== 浮空字管理命令 ===");
            player.sendMessage("§e/evac hologram stats - 查看浮空字统计信息");
            player.sendMessage("§e/evac hologram rebuild - 强制重建所有浮空字");
            player.sendMessage("§e/evac hologram check - 手动检查并修复浮空字");
            player.sendMessage("§e/evac hologram clear - 清理所有浮空字");
            player.sendMessage("§e/evac hologram cleanup - 清理孤立的浮空字 §c(推荐)");
            player.sendMessage("§e/evac hologram distance [距离] - 清理远离玩家的浮空字 §a(智能)");
            return true;
        }

        String subCmd = args[1].toLowerCase();

        switch (subCmd) {
            case "stats":
                // 显示浮空字统计信息
                String stats = plugin.getHologramManager().getHologramStats();
                player.sendMessage("§6=== 浮空字统计信息 ===");
                player.sendMessage("§e" + stats);

                // 显示区块检查任务状态
                player.sendMessage("§e区块检查任务: §a运行中 §7(每30秒检查一次)");
                return true;

            case "rebuild":
            case "recreate":
                // 强制重建所有浮空字
                player.sendMessage("§6正在强制重建所有浮空字...");
                plugin.getHologramManager().forceRecreateAllHolograms();
                player.sendMessage("§a浮空字重建完成！");
                return true;

            case "check":
            case "fix":
                // 手动检查并修复浮空字
                player.sendMessage("§6正在检查浮空字状态...");

                // 这里可以添加手动检查逻辑
                String statsAfter = plugin.getHologramManager().getHologramStats();
                player.sendMessage("§a检查完成！");
                player.sendMessage("§e" + statsAfter);
                return true;

            case "clear":
            case "clean":
                // 清理所有浮空字
                player.sendMessage("§c警告：这将清理所有浮空字！");
                player.sendMessage("§e如果确认，请使用: /evac hologram clear confirm");

                if (args.length > 2 && args[2].equalsIgnoreCase("confirm")) {
                    plugin.getHologramManager().cleanup();
                    player.sendMessage("§a所有浮空字已清理！");
                }
                return true;

            case "cleanup":
            case "cleanorphan":
                // 清理孤立的浮空字
                player.sendMessage("§6正在清理孤立的浮空字...");
                int cleanedCount = cleanupOrphanedHolograms(player);
                player.sendMessage("§a清理了 " + cleanedCount + " 个孤立的浮空字");
                return true;

            case "distance":
                // 基于距离清理浮空字
                double distance = 32.0; // 默认距离
                if (args.length > 2) {
                    try {
                        distance = Double.parseDouble(args[2]);
                        if (distance <= 0 || distance > 200) {
                            player.sendMessage("§c距离必须在1-200之间！");
                            return true;
                        }
                    } catch (NumberFormatException e) {
                        player.sendMessage("§c无效的距离值！");
                        return true;
                    }
                }

                player.sendMessage("§6正在清理距离玩家超过 " + distance + " 格的浮空字...");
                int distanceCleanedCount = plugin.getHologramManager().cleanupHologramsByDistance(distance);
                player.sendMessage("§a清理了 " + distanceCleanedCount + " 个远离玩家的浮空字");

                // 显示当前配置的自动清理距离
                double autoDistance = plugin.getConfig().getDouble("treasure-chest.hologram.auto_remove_distance", 32.0);
                player.sendMessage("§7提示：自动清理距离设置为 " + autoDistance + " 格");
                return true;

            default:
                player.sendMessage("§c未知的子命令: " + subCmd);
                return true;
        }
    }

    /**
     * 处理调试命令 /evacuation debug
     */
    private boolean handleDebugCommand(CommandSender sender, String[] args) {
        sender.sendMessage("§6=== 摸金插件调试信息 ===");

        try {
            // 基本信息
            sender.sendMessage("§e插件版本: §f" + plugin.getDescription().getVersion());
            sender.sendMessage("§e服务器版本: §f" + plugin.getServer().getVersion());

            // 数据统计
            int memoryChestCount = plugin.getPlayerListener() != null ?
                plugin.getPlayerListener().getTreasureChestDataSize() : 0;
            int fileChestCount = plugin.getChestManager() != null ?
                plugin.getChestManager().getChestCount() : 0;

            sender.sendMessage("§e内存中摸金箱数量: §f" + memoryChestCount);
            sender.sendMessage("§e文件中摸金箱数量: §f" + fileChestCount);

            if (memoryChestCount != fileChestCount) {
                sender.sendMessage("§c警告：数据不同步! 内存和文件中的数量不一致");
            } else {
                sender.sendMessage("§a完成：数据同步正常");
            }

            // 文件状态
            java.io.File chestsFile = new java.io.File(plugin.getDataFolder(), "chests.yml");
            if (chestsFile.exists()) {
                sender.sendMessage("§e数据文件大小: §f" + chestsFile.length() + " 字节");
                sender.sendMessage("§e数据文件路径: §f" + chestsFile.getAbsolutePath());
                sender.sendMessage("§e文件可读: §f" + chestsFile.canRead());
                sender.sendMessage("§e文件可写: §f" + chestsFile.canWrite());

                // 检查备份文件
                java.io.File backupFile = new java.io.File(chestsFile.getAbsolutePath() + ".backup");
                if (backupFile.exists()) {
                    sender.sendMessage("§e备份文件大小: §f" + backupFile.length() + " 字节");
                } else {
                    sender.sendMessage("§e备份文件: §c不存在");
                }
            } else {
                sender.sendMessage("§c错误：数据文件不存在!");
            }

            // 配置信息
            boolean debugEnabled = plugin.getConfig().getBoolean("debug.enabled", false);
            int autoSaveInterval = plugin.getConfig().getInt("performance.auto_save.interval", 5);

            sender.sendMessage("§e调试模式: §f" + (debugEnabled ? "§a启用" : "§c禁用"));
            sender.sendMessage("§e自动保存间隔: §f" + autoSaveInterval + " 分钟");

            // 系统状态
            sender.sendMessage("§e物品管理器: §f" + (plugin.getTreasureItemManager() != null ? "§a正常" : "§c未初始化"));
            sender.sendMessage("§e摸金箱管理器: §f" + (plugin.getChestManager() != null ? "§a正常" : "§c未初始化"));
            sender.sendMessage("§e玩家监听器: §f" + (plugin.getPlayerListener() != null ? "§a正常" : "§c未初始化"));

            // 如果有额外参数，显示更详细的信息
            if (args.length > 1 && args[1].equalsIgnoreCase("verbose")) {
                sender.sendMessage("§6=== 详细调试信息 ===");

                // JVM信息
                Runtime runtime = Runtime.getRuntime();
                long totalMemory = runtime.totalMemory() / 1024 / 1024;
                long freeMemory = runtime.freeMemory() / 1024 / 1024;
                long usedMemory = totalMemory - freeMemory;

                sender.sendMessage("§e内存使用: §f" + usedMemory + "MB / " + totalMemory + "MB");

                // 磁盘空间
                long freeSpace = plugin.getDataFolder().getFreeSpace() / 1024 / 1024;
                sender.sendMessage("§e可用磁盘空间: §f" + freeSpace + "MB");

                // 线程信息
                sender.sendMessage("§e活跃线程数: §f" + Thread.activeCount());
            }

            sender.sendMessage("§7提示: 使用 §e/evacuation debug verbose §7查看详细信息");

        } catch (Exception e) {
            sender.sendMessage("§c获取调试信息时出错: " + e.getMessage());
            plugin.getLogger().severe("调试命令执行失败: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 处理强制保存命令 /evacuation save
     */
    private boolean handleSaveCommand(CommandSender sender, String[] args) {
        sender.sendMessage("§6正在强制保存所有摸金箱数据...");

        try {
            long startTime = System.currentTimeMillis();

            // 获取保存前的数据统计
            int memoryChestCount = plugin.getPlayerListener() != null ?
                plugin.getPlayerListener().getTreasureChestDataSize() : 0;

            // 保存所有数据
            if (plugin.getPlayerListener() != null) {
                plugin.getPlayerListener().saveAllChestDataToFile();
            }

            if (plugin.getChestManager() != null) {
                plugin.getChestManager().saveConfigFile();
            }

            long duration = System.currentTimeMillis() - startTime;
            int savedChestCount = plugin.getChestManager() != null ?
                plugin.getChestManager().getChestCount() : 0;

            sender.sendMessage("§a完成：强制保存完成!");
            sender.sendMessage("§e耗时: §f" + duration + "ms");
            sender.sendMessage("§e内存中摸金箱: §f" + memoryChestCount + " 个");
            sender.sendMessage("§e文件中摸金箱: §f" + savedChestCount + " 个");

            if (savedChestCount != memoryChestCount) {
                sender.sendMessage("§c警告：数据不同步! 建议检查错误日志");
            }

            // 如果有额外参数，执行清理操作
            if (args.length > 1 && args[1].equalsIgnoreCase("cleanup")) {
                sender.sendMessage("§6正在清理残留的浮空字...");
                int cleanedCount = cleanupOrphanedHolograms(sender);
                sender.sendMessage("§a清理了 " + cleanedCount + " 个残留的浮空字");
            }

        } catch (Exception e) {
            sender.sendMessage("§c强制保存时出错: " + e.getMessage());
            plugin.getLogger().severe("强制保存命令执行失败: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 新增：清理孤立的浮空字实体
     */
    private int cleanupOrphanedHolograms(CommandSender sender) {
        int cleanedCount = 0;

        try {
            // 遍历所有已加载的世界
            for (org.bukkit.World world : plugin.getServer().getWorlds()) {
                // 查找所有ArmorStand实体
                for (org.bukkit.entity.Entity entity : world.getEntities()) {
                    if (entity instanceof org.bukkit.entity.ArmorStand) {
                        org.bukkit.entity.ArmorStand armorStand = (org.bukkit.entity.ArmorStand) entity;

                        // 检查是否是浮空字（有自定义名称且不可见）
                        if (armorStand.isCustomNameVisible() && armorStand.getCustomName() != null) {
                            String customName = armorStand.getCustomName();

                            // 修复：更准确地识别摸金箱相关的浮空字
                            if (customName.contains("物品未搜索") || customName.contains("已搜索完毕") ||
                                customName.contains("搜索中") || customName.contains("刷新倒计时") ||
                                customName.contains("可以刷新")) {

                                // 检查该位置附近是否还有对应的摸金箱数据
                                org.bukkit.Location location = armorStand.getLocation();

                                // 修复：检查多个可能的摸金箱位置（浮空字可能有偏移）
                                boolean hasChestData = false;
                                for (int dx = -1; dx <= 1; dx++) {
                                    for (int dy = -2; dy <= 0; dy++) { // 浮空字通常在箱子上方1-2格
                                        for (int dz = -1; dz <= 1; dz++) {
                                            org.bukkit.Location chestLocation = new org.bukkit.Location(
                                                location.getWorld(),
                                                location.getBlockX() + dx,
                                                location.getBlockY() + dy,
                                                location.getBlockZ() + dz
                                            );

                                            if (plugin.getPlayerListener() != null &&
                                                plugin.getPlayerListener().getTreasureChestData(chestLocation) != null) {
                                                hasChestData = true;
                                                break;
                                            }
                                        }
                                        if (hasChestData) break;
                                    }
                                    if (hasChestData) break;
                                }

                                if (!hasChestData) {
                                    // 没有对应的摸金箱数据，移除这个浮空字
                                    armorStand.remove();
                                    cleanedCount++;

                                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                        plugin.getLogger().info("🧹 清理了孤立的浮空字: " + customName +
                                            " 位置: " + location.getBlockX() + "," + location.getBlockY() + "," + location.getBlockZ());
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            sender.sendMessage("§c清理过程中出错: " + e.getMessage());
            plugin.getLogger().warning("清理孤立浮空字时出错: " + e.getMessage());
        }

        return cleanedCount;
    }

    /**
     * 处理刷新命令
     */
    private boolean handleRefreshCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sender.sendMessage("§6=== 刷新命令帮助 ===");
            sender.sendMessage("§e/evacuation refresh all [世界名] - 刷新所有浮空字和摸金箱");
            sender.sendMessage("§e/evacuation refresh holograms [世界名] - 仅刷新所有浮空字");
            sender.sendMessage("§e/evacuation refresh chests [世界名] - 仅刷新所有摸金箱物品");
            sender.sendMessage("§e/evacuation refresh items [世界名] [confirm] - 清空并重新生成所有摸金箱物品");
            sender.sendMessage("§7参数说明:");
            sender.sendMessage("§7  [世界名] - 可选，指定要刷新的世界，不填则刷新所有世界");
            sender.sendMessage("§7  confirm - items 命令必需的确认参数");
            sender.sendMessage("§c警告: 所有刷新操作都会清空摸金箱并重置玩家搜索进度！");
            sender.sendMessage("§c玩家需要重新打开摸金箱才会生成新物品！");
            sender.sendMessage("§6=== 使用示例 ===");
            sender.sendMessage("§e/evacuation refresh all world - 仅刷新 world 世界");
            sender.sendMessage("§e/evacuation refresh holograms world_nether - 仅刷新地狱世界的浮空字");
            return true;
        }

        String subCmd = args[1].toLowerCase();

        switch (subCmd) {
            case "all":
                return handleRefreshAll(sender, args);

            case "holograms":
            case "hologram":
                return handleRefreshHolograms(sender, args);

            case "chests":
            case "chest":
                return handleRefreshChests(sender, args);

            case "items":
            case "item":
                return handleRefreshItems(sender, args);

            default:
                sender.sendMessage("§c未知的刷新类型。使用 /evacuation refresh 查看帮助。");
                return true;
        }
    }

    /**
     * 刷新所有内容
     */
    private boolean handleRefreshAll(CommandSender sender, String[] args) {
        // 解析世界参数
        String targetWorld = null;
        if (args.length >= 3) {
            targetWorld = args[2];
            // 验证世界是否存在
            if (plugin.getServer().getWorld(targetWorld) == null) {
                sender.sendMessage("§c错误: 世界 '" + targetWorld + "' 不存在！");
                return true;
            }
        }

        if (targetWorld != null) {
            sender.sendMessage("§6=== 开始刷新世界 '" + targetWorld + "' 的所有内容 ===");
        } else {
            sender.sendMessage("§6=== 开始刷新所有世界的内容 ===");
        }

        long startTime = System.currentTimeMillis();

        try {
            // 1. 刷新浮空字
            sender.sendMessage("§e[1/3] 正在刷新浮空字...");

            // 修复：检查浮空字是否启用
            if (plugin.getTreasureItemManager().isHologramEnabled()) {
                if (targetWorld != null) {
                    plugin.getHologramManager().cleanupByWorld(targetWorld);
                    plugin.getHologramManager().forceRecreateHologramsByWorld(targetWorld);
                } else {
                    plugin.getHologramManager().cleanup();
                    plugin.getHologramManager().forceRecreateAllHolograms();
                }
                sender.sendMessage("§a✓ 浮空字刷新完成");
            } else {
                sender.sendMessage("§7✓ 浮空字功能已禁用，跳过刷新");
            }

            // 2. 刷新摸金箱物品（清空现有数据，重置搜索进度）
            sender.sendMessage("§e[2/3] 正在刷新摸金箱...");
            int refreshedChests = refreshAllChests(true, targetWorld);
            sender.sendMessage("§a✓ 摸金箱刷新完成 (" + refreshedChests + " 个)");

            // 3. 强制保存数据
            sender.sendMessage("§e[3/3] 正在保存数据...");
            plugin.getChestManager().forceSaveAll();
            sender.sendMessage("§a✓ 数据保存完成");

            long duration = System.currentTimeMillis() - startTime;
            sender.sendMessage("§6=== 刷新完成 ===");
            sender.sendMessage("§e总耗时: §f" + duration + "ms");
            sender.sendMessage("§e刷新的摸金箱: §f" + refreshedChests + " 个");
            sender.sendMessage("§c重要: 所有摸金箱已清空，玩家需要重新打开才会生成新物品！");
            if (targetWorld != null) {
                sender.sendMessage("§e目标世界: §f" + targetWorld);
            }

        } catch (Exception e) {
            sender.sendMessage("§c刷新过程中出错: " + e.getMessage());
            plugin.getLogger().severe("刷新所有内容时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 仅刷新浮空字
     */
    private boolean handleRefreshHolograms(CommandSender sender, String[] args) {
        // 解析世界参数
        String targetWorld = null;
        if (args.length >= 3) {
            targetWorld = args[2];
            // 验证世界是否存在
            if (plugin.getServer().getWorld(targetWorld) == null) {
                sender.sendMessage("§c错误: 世界 '" + targetWorld + "' 不存在！");
                return true;
            }
        }

        if (targetWorld != null) {
            sender.sendMessage("§6正在刷新世界 '" + targetWorld + "' 的浮空字...");
        } else {
            sender.sendMessage("§6正在刷新所有世界的浮空字...");
        }

        try {
            // 修复：检查浮空字是否启用
            if (!plugin.getTreasureItemManager().isHologramEnabled()) {
                sender.sendMessage("§c浮空字功能已禁用！");
                sender.sendMessage("§e请在配置文件中设置 hologram_enabled: true 来启用浮空字功能");
                return true;
            }

            long startTime = System.currentTimeMillis();

            // 清理现有浮空字
            if (targetWorld != null) {
                plugin.getHologramManager().cleanupByWorld(targetWorld);
            } else {
                plugin.getHologramManager().cleanup();
            }

            // 强制重建浮空字
            if (targetWorld != null) {
                plugin.getHologramManager().forceRecreateHologramsByWorld(targetWorld);
            } else {
                plugin.getHologramManager().forceRecreateAllHolograms();
            }

            long duration = System.currentTimeMillis() - startTime;
            String stats = plugin.getHologramManager().getHologramStats();

            sender.sendMessage("§a浮空字刷新完成！");
            sender.sendMessage("§e耗时: §f" + duration + "ms");
            sender.sendMessage("§e" + stats);
            if (targetWorld != null) {
                sender.sendMessage("§e目标世界: §f" + targetWorld);
            }

        } catch (Exception e) {
            sender.sendMessage("§c刷新浮空字时出错: " + e.getMessage());
            plugin.getLogger().severe("刷新浮空字时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 仅刷新摸金箱（不删除现有物品）
     */
    private boolean handleRefreshChests(CommandSender sender, String[] args) {
        // 解析世界参数
        String targetWorld = null;
        if (args.length >= 3) {
            targetWorld = args[2];
            // 验证世界是否存在
            if (plugin.getServer().getWorld(targetWorld) == null) {
                sender.sendMessage("§c错误: 世界 '" + targetWorld + "' 不存在！");
                return true;
            }
        }

        if (targetWorld != null) {
            sender.sendMessage("§6正在刷新世界 '" + targetWorld + "' 的摸金箱...");
        } else {
            sender.sendMessage("§6正在刷新所有世界的摸金箱...");
        }

        try {
            long startTime = System.currentTimeMillis();
            int refreshedChests = refreshAllChests(true, targetWorld);
            long duration = System.currentTimeMillis() - startTime;

            sender.sendMessage("§a摸金箱刷新完成！");
            sender.sendMessage("§e耗时: §f" + duration + "ms");
            sender.sendMessage("§e刷新的摸金箱: §f" + refreshedChests + " 个");
            sender.sendMessage("§c注意: 所有摸金箱已清空，玩家搜索进度已重置！");
            if (targetWorld != null) {
                sender.sendMessage("§e目标世界: §f" + targetWorld);
            }

        } catch (Exception e) {
            sender.sendMessage("§c刷新摸金箱时出错: " + e.getMessage());
            plugin.getLogger().severe("刷新摸金箱时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 重新生成所有摸金箱物品（删除现有物品数据）
     */
    private boolean handleRefreshItems(CommandSender sender, String[] args) {
        // 解析参数：世界名和确认参数
        String targetWorld = null;
        boolean hasConfirm = false;

        // 检查参数：可能的格式
        // /evacuation refresh items confirm
        // /evacuation refresh items world confirm
        // /evacuation refresh items world
        if (args.length >= 3) {
            if (args[2].equalsIgnoreCase("confirm")) {
                hasConfirm = true;
            } else {
                // 第三个参数是世界名
                targetWorld = args[2];
                // 验证世界是否存在
                if (plugin.getServer().getWorld(targetWorld) == null) {
                    sender.sendMessage("§c错误: 世界 '" + targetWorld + "' 不存在！");
                    return true;
                }

                // 检查第四个参数是否是confirm
                if (args.length >= 4 && args[3].equalsIgnoreCase("confirm")) {
                    hasConfirm = true;
                }
            }
        }

        // 需要确认操作
        if (!hasConfirm) {
            if (targetWorld != null) {
                sender.sendMessage("§c警告：此操作将删除世界 '" + targetWorld + "' 中所有摸金箱的现有物品数据！");
                sender.sendMessage("§c该世界所有玩家的搜索进度将被重置！");
                sender.sendMessage("§e如果确认执行，请使用: §f/evacuation refresh items " + targetWorld + " confirm");
            } else {
                sender.sendMessage("§c警告：此操作将删除所有摸金箱中的现有物品数据！");
                sender.sendMessage("§c所有玩家的搜索进度将被重置！");
                sender.sendMessage("§e如果确认执行，请使用: §f/evacuation refresh items confirm");
                sender.sendMessage("§e或指定世界: §f/evacuation refresh items [世界名] confirm");
            }
            return true;
        }

        if (targetWorld != null) {
            sender.sendMessage("§6正在重新生成世界 '" + targetWorld + "' 的摸金箱物品...");
        } else {
            sender.sendMessage("§6正在重新生成所有世界的摸金箱物品...");
        }

        try {
            long startTime = System.currentTimeMillis();
            int refreshedChests = refreshAllChests(true, targetWorld);
            long duration = System.currentTimeMillis() - startTime;

            sender.sendMessage("§a摸金箱物品重新生成完成！");
            sender.sendMessage("§e耗时: §f" + duration + "ms");
            sender.sendMessage("§e重新生成的摸金箱: §f" + refreshedChests + " 个");
            if (targetWorld != null) {
                sender.sendMessage("§e目标世界: §f" + targetWorld);
                sender.sendMessage("§c注意：世界 '" + targetWorld + "' 中所有玩家的搜索进度已重置！");
            } else {
                sender.sendMessage("§c注意：所有玩家的搜索进度已重置！");
            }

        } catch (Exception e) {
            sender.sendMessage("§c重新生成摸金箱物品时出错: " + e.getMessage());
            plugin.getLogger().severe("重新生成摸金箱物品时出错: " + e.getMessage());
            e.printStackTrace();
        }

        return true;
    }

    /**
     * 刷新所有摸金箱的核心方法
     * @param clearItems 是否清空现有物品数据
     * @return 刷新的摸金箱数量
     */
    private int refreshAllChests(boolean clearItems) {
        return refreshAllChests(clearItems, null);
    }

    /**
     * 刷新所有摸金箱的核心方法（支持世界过滤）
     * @param clearItems 是否清空现有物品数据
     * @param targetWorld 目标世界名，null表示所有世界
     * @return 刷新的摸金箱数量
     */
    private int refreshAllChests(boolean clearItems, String targetWorld) {
        int refreshedCount = 0;

        try {
            // 获取所有摸金箱数据
            if (plugin.getPlayerListener() != null) {
                java.util.Map<String, com.hang.plugin.listeners.PlayerListener.TreasureChestData> allChestData =
                    plugin.getPlayerListener().getAllTreasureChestData();

                // 调试：显示获取到的摸金箱数量
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("调试：获取到 " + allChestData.size() + " 个摸金箱数据");
                    if (targetWorld != null) {
                        plugin.getLogger().info("调试：目标世界过滤 = " + targetWorld);
                    }
                }

                for (java.util.Map.Entry<String, com.hang.plugin.listeners.PlayerListener.TreasureChestData> entry : allChestData.entrySet()) {
                    try {
                        String locationKey = entry.getKey();
                        com.hang.plugin.listeners.PlayerListener.TreasureChestData data = entry.getValue();

                        // 解析位置
                        org.bukkit.Location location = parseLocationFromString(locationKey);
                        if (location == null) {
                            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                plugin.getLogger().warning("调试：位置解析失败 = " + locationKey);
                            }
                            continue;
                        }

                        // 世界过滤：如果指定了目标世界，只处理该世界的摸金箱
                        if (targetWorld != null && !location.getWorld().getName().equals(targetWorld)) {
                            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                plugin.getLogger().info("调试：世界过滤跳过 = " + location.getWorld().getName() + " (目标: " + targetWorld + ")");
                            }
                            continue;
                        }

                        if (clearItems) {
                            // 正确逻辑：清空摸金箱内容但保留数据结构，等玩家重新打开时生成新物品
                            // 移除浮空字
                            if (plugin.getTreasureItemManager().isHologramEnabled()) {
                                plugin.getHologramManager().removeHologram(location);
                            }

                            // 正确逻辑：重置摸金箱数据，而不是删除
                            data.reset(); // 清空物品、重置搜索进度、重置时间
                            plugin.getPlayerListener().saveTreasureChestData(location, data); // 保存重置后的数据
                        } else {
                            // 只是刷新，不清空
                            // 强制刷新摸金箱
                            data.setNextRefreshTime(System.currentTimeMillis() - 1000); // 设置为过期

                            // 重新生成物品
                            plugin.getPlayerListener().refreshTreasureChest(location, data);
                        }

                        refreshedCount++;

                    } catch (Exception e) {
                        plugin.getLogger().warning("刷新摸金箱时出错: " + entry.getKey() + " - " + e.getMessage());
                    }
                }

                // 保存所有数据
                plugin.getPlayerListener().saveAllChestDataToFile();
            }

        } catch (Exception e) {
            plugin.getLogger().severe("刷新摸金箱过程中出错: " + e.getMessage());
            e.printStackTrace();
        }

        return refreshedCount;
    }

    /**
     * 从字符串解析位置（辅助方法）
     */
    private org.bukkit.Location parseLocationFromString(String locationString) {
        try {
            // 修复：支持PlayerListener使用的下划线格式 (world_x_y_z)
            String[] parts = locationString.split("_");
            if (parts.length >= 4) {
                String worldName = parts[0];
                int x = Integer.parseInt(parts[1]);
                int y = Integer.parseInt(parts[2]);
                int z = Integer.parseInt(parts[3]);

                org.bukkit.World world = plugin.getServer().getWorld(worldName);
                if (world != null) {
                    return new org.bukkit.Location(world, x, y, z);
                } else {
                    plugin.getLogger().warning("世界不存在: " + worldName + " (位置字符串: " + locationString + ")");
                }
            } else {
                plugin.getLogger().warning("位置字符串格式错误: " + locationString + " (期望格式: world_x_y_z)");
            }
        } catch (NumberFormatException e) {
            plugin.getLogger().warning("位置坐标解析失败: " + locationString + " - " + e.getMessage());
        } catch (Exception e) {
            plugin.getLogger().warning("解析位置字符串失败: " + locationString + " - " + e.getMessage());
        }
        return null;
    }

}
