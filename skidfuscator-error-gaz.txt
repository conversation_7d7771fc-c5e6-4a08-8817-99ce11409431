handler=Block #Y, types=[Ljava/io/IOException;], range=[Block #X, Block #W]
handler=Block #AB, types=[Ljava/lang/IllegalAccessException;], range=[Block #AA, Block #Z]
handler=Block #AE, types=[Ljava/lang/RuntimeException;], range=[Block #AD, Block #AC]
handler=Block #AH, types=[Ljava/io/IOException;], range=[Block #AG, Block #AF]
===#Block A(size=3, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -629149990)
      goto AI
      -> Immediate #A -> #B
      -> ConditionalJump[IF_ICMPNE] #A -> #AI
===#Block B(size=3, flags=0)===
   0. lvar3 = lvar1;
   1. if (lvar3 == nullconst)
      goto D
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 207700258)
      goto AM
      -> Immediate #B -> #C
      -> ConditionalJump[IFNULL] #B -> #D
      -> ConditionalJump[IF_ICMPNE] #B -> #AM
      <- Immediate #A -> #B
===#Block C(size=4, flags=0)===
   0. lvar9 = lvar1;
   1. lvar10 = lvar9.isEmpty();
   2. if (lvar10 == {1649500220 ^ lvar54})
      goto E
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 311099875)
      goto AI
      -> ConditionalJump[IF_ICMPEQ] #C -> #E
      -> ConditionalJump[IF_ICMPNE] #C -> #AI
      -> Immediate #C -> #D
      <- Immediate #B -> #C
===#Block D(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar11 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.polpxguvvmdvbrx(), lvar54);
   2. return lvar11;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1644122280)
      goto AX
      -> ConditionalJump[IF_ICMPNE] #D -> #AX
      <- ConditionalJump[IFNULL] #B -> #D
      <- Immediate #C -> #D
===#Block AX(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1644122280)
      goto AX
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {2110505833 ^ lvar54})
      goto AX
   2. _consume({1454429794 ^ lvar54});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AX -> #AX
      <- ConditionalJump[IF_ICMPNE] #AX -> #AX
      <- ConditionalJump[IF_ICMPNE] #D -> #AX
===#Block E(size=11, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar1;
   2. lvar13 = lvar12.toLowerCase();
   3. lvar7 = lvar13;
   4. lvar14 = {-1854152206 ^ lvar54};
   5. lvar8 = lvar14;
   6. lvar15 = lvar7;
   7. lvar16 = lvar15.hashCode();
   8. svar56 = {lvar16 ^ lvar54};
   9. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(svar56)) {
      case 30481073:
      	 goto	#N
      case 107757332:
      	 goto	#H
      case 108527234:
      	 goto	#J
      case 123370998:
      	 goto	#L
      case 201067313:
      	 goto	#F
      default:
      	 goto	#P
   }
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1948315755)
      goto AP
      -> Switch[30481073] #E -> #N
      -> Switch[123370998] #E -> #L
      -> Switch[108527234] #E -> #J
      -> Switch[107757332] #E -> #H
      -> Switch[201067313] #E -> #F
      -> ConditionalJump[IF_ICMPNE] #E -> #AP
      -> DefaultSwitch #E -> #P
      <- ConditionalJump[IF_ICMPEQ] #C -> #E
===#Block F(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar7;
   2. lvar4 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.mippdmwegkvnhta(), lvar54);
   3. lvar18 = lvar17.equals(lvar4);
   4. if (lvar18 == {1110647729 ^ lvar54})
      goto P
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 295247242)
      goto AQ
      -> ConditionalJump[IF_ICMPNE] #F -> #AQ
      -> Immediate #F -> #G
      -> ConditionalJump[IF_ICMPEQ] #F -> #P
      <- Switch[201067313] #E -> #F
===#Block G(size=4, flags=0)===
   0. lvar19 = {1506416921 ^ lvar54};
   1. lvar8 = lvar19;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -833566510)
      goto AJ
   3. goto X
      -> UnconditionalJump[GOTO] #G -> #X
      -> ConditionalJump[IF_ICMPNE] #G -> #AJ
      <- Immediate #F -> #G
===#Block X(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 244252409)
      goto W
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -931194719)
      goto AT
      -> TryCatch range: [X...W] -> Y ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #X -> #AT
      -> ConditionalJump[IF_ICMPEQ] #X -> #W
      <- UnconditionalJump[GOTO] #G -> #X
===#Block W(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 2017287979)
      goto AY
      -> TryCatch range: [X...W] -> Y ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #W -> #AY
      <- ConditionalJump[IF_ICMPEQ] #X -> #W
===#Block AY(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 2017287979)
      goto AY
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {304444821 ^ lvar54})
      goto AY
   2. _consume({1220066806 ^ lvar54});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AY -> #AY
      <- ConditionalJump[IF_ICMPNE] #W -> #AY
      <- ConditionalJump[IF_ICMPNE] #AY -> #AY
===#Block AT(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -931194719)
      goto AT
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1405948618 ^ lvar54})
      goto AT
   2. _consume({102528083 ^ lvar54});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AT -> #AT
      <- ConditionalJump[IF_ICMPNE] #AT -> #AT
      <- ConditionalJump[IF_ICMPNE] #X -> #AT
===#Block Y(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -2088494694)
      goto AQ
   2. goto P
      -> UnconditionalJump[GOTO] #Y -> #P
      -> ConditionalJump[IF_ICMPNE] #Y -> #AQ
      <- TryCatch range: [X...W] -> Y ([Ljava/io/IOException;])
      <- TryCatch range: [X...W] -> Y ([Ljava/io/IOException;])
===#Block AQ(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 295247242)
      goto AQ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1702449186 ^ lvar54})
      goto AQ
   2. _consume({734811980 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -2088494694)
      goto AQ
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1514242944 ^ lvar54})
      goto AQ
   5. _consume({681580118 ^ lvar54});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AQ -> #AQ
      <- ConditionalJump[IF_ICMPNE] #Y -> #AQ
      <- ConditionalJump[IF_ICMPNE] #F -> #AQ
      <- ConditionalJump[IF_ICMPNE] #AQ -> #AQ
===#Block H(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar30 = lvar7;
   2. lvar48 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.kwdgcomxgiwnjru(), lvar54);
   3. lvar31 = lvar30.equals(lvar48);
   4. if (lvar31 == {1229120848 ^ lvar54})
      goto P
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1243032194)
      goto AS
      -> ConditionalJump[IF_ICMPNE] #H -> #AS
      -> ConditionalJump[IF_ICMPEQ] #H -> #P
      -> Immediate #H -> #I
      <- Switch[107757332] #E -> #H
===#Block I(size=4, flags=0)===
   0. lvar32 = {403216638 ^ lvar54};
   1. lvar8 = lvar32;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1069234208)
      goto AP
   3. goto AG
      -> ConditionalJump[IF_ICMPNE] #I -> #AP
      -> UnconditionalJump[GOTO] #I -> #AG
      <- Immediate #H -> #I
===#Block AG(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 70298222)
      goto AF
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -402391638)
      goto AI
      -> ConditionalJump[IF_ICMPNE] #AG -> #AI
      -> TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #AG -> #AF
      <- UnconditionalJump[GOTO] #I -> #AG
===#Block AF(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1004129421)
      goto AS
      -> TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AF -> #AS
      <- ConditionalJump[IF_ICMPEQ] #AG -> #AF
===#Block AH(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1275371592)
      goto AZ
   2. goto P
      -> UnconditionalJump[GOTO] #AH -> #P
      -> ConditionalJump[IF_ICMPNE] #AH -> #AZ
      <- TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
      <- TryCatch range: [AG...AF] -> AH ([Ljava/io/IOException;])
===#Block AZ(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1275371592)
      goto AZ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {62250713 ^ lvar54})
      goto AZ
   2. _consume({1696837492 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AH -> #AZ
===#Block AP(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1069234208)
      goto AP
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1486431564 ^ lvar54})
      goto AP
   2. _consume({728105964 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1948315755)
      goto AP
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {593992557 ^ lvar54})
      goto AP
   5. _consume({204218832 ^ lvar54});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AP -> #AP
      <- ConditionalJump[IF_ICMPNE] #AP -> #AP
      <- ConditionalJump[IF_ICMPNE] #I -> #AP
      <- ConditionalJump[IF_ICMPNE] #E -> #AP
===#Block AS(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1004129421)
      goto AS
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1946147858 ^ lvar54})
      goto AS
   2. _consume({93965127 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1243032194)
      goto AS
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {2033696230 ^ lvar54})
      goto AS
   5. _consume({621011858 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AS -> #AS
      <- ConditionalJump[IF_ICMPNE] #H -> #AS
      <- ConditionalJump[IF_ICMPNE] #AS -> #AS
      <- ConditionalJump[IF_ICMPNE] #AF -> #AS
===#Block J(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar33 = lvar7;
   2. lvar49 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.itqeehicawlbndz(), lvar54);
   3. lvar34 = lvar33.equals(lvar49);
   4. if (lvar34 == {1584033354 ^ lvar54})
      goto P
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -212635054)
      goto AW
      -> ConditionalJump[IF_ICMPEQ] #J -> #P
      -> Immediate #J -> #K
      -> ConditionalJump[IF_ICMPNE] #J -> #AW
      <- Switch[108527234] #E -> #J
===#Block AW(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -212635054)
      goto AW
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {294219725 ^ lvar54})
      goto AW
   2. _consume({1176256468 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- ConditionalJump[IF_ICMPNE] #J -> #AW
===#Block K(size=4, flags=0)===
   0. lvar35 = {1226553339 ^ lvar54};
   1. lvar8 = lvar35;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1222492114)
      goto AI
   3. goto AA
      -> ConditionalJump[IF_ICMPNE] #K -> #AI
      -> UnconditionalJump[GOTO] #K -> #AA
      <- Immediate #J -> #K
===#Block AA(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 2535842)
      goto Z
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1790592518)
      goto AK
      -> ConditionalJump[IF_ICMPNE] #AA -> #AK
      -> ConditionalJump[IF_ICMPEQ] #AA -> #Z
      -> TryCatch range: [AA...Z] -> AB ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #K -> #AA
===#Block Z(size=2, flags=0)===
   0. throw new java/lang/IllegalAccessException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1293986981)
      goto BB
      -> ConditionalJump[IF_ICMPNE] #Z -> #BB
      -> TryCatch range: [AA...Z] -> AB ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AA -> #Z
===#Block AB(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 598619816)
      goto AO
   2. goto P
      -> ConditionalJump[IF_ICMPNE] #AB -> #AO
      -> UnconditionalJump[GOTO] #AB -> #P
      <- TryCatch range: [AA...Z] -> AB ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AA...Z] -> AB ([Ljava/lang/IllegalAccessException;])
===#Block AO(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 598619816)
      goto AO
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {132179724 ^ lvar54})
      goto AO
   2. _consume({1607729378 ^ lvar54});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AO -> #AO
      <- ConditionalJump[IF_ICMPNE] #AB -> #AO
      <- ConditionalJump[IF_ICMPNE] #AO -> #AO
===#Block BB(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1293986981)
      goto BB
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1596426717 ^ lvar54})
      goto BB
   2. _consume({1347062056 ^ lvar54});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #Z -> #BB
      <- ConditionalJump[IF_ICMPNE] #BB -> #BB
===#Block L(size=6, flags=0)===
   0. // Frame: locals[2] [java/lang/String, 1] stack[0] []
   1. lvar36 = lvar7;
   2. lvar50 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.vhhyotrsydpzysb(), lvar54);
   3. lvar37 = lvar36.equals(lvar50);
   4. if (lvar37 == {7147902 ^ lvar54})
      goto P
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 57183216)
      goto AM
      -> ConditionalJump[IF_ICMPEQ] #L -> #P
      -> ConditionalJump[IF_ICMPNE] #L -> #AM
      -> Immediate #L -> #M
      <- Switch[123370998] #E -> #L
===#Block M(size=4, flags=0)===
   0. lvar38 = {624776419 ^ lvar54};
   1. lvar8 = lvar38;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 703244057)
      goto AR
   3. goto AD
      -> ConditionalJump[IF_ICMPNE] #M -> #AR
      -> UnconditionalJump[GOTO] #M -> #AD
      <- Immediate #L -> #M
===#Block AD(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 24769779)
      goto AC
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1487692466)
      goto AK
      -> ConditionalJump[IF_ICMPNE] #AD -> #AK
      -> TryCatch range: [AD...AC] -> AE ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AD -> #AC
      <- UnconditionalJump[GOTO] #M -> #AD
===#Block AC(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1291854065)
      goto BA
      -> ConditionalJump[IF_ICMPNE] #AC -> #BA
      -> TryCatch range: [AD...AC] -> AE ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AD -> #AC
===#Block BA(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1291854065)
      goto BA
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1265160461 ^ lvar54})
      goto BA
   2. _consume({1286839069 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #AC -> #BA
      <- ConditionalJump[IF_ICMPNE] #BA -> #BA
===#Block AE(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -29827382)
      goto AV
   2. goto P
      -> UnconditionalJump[GOTO] #AE -> #P
      -> ConditionalJump[IF_ICMPNE] #AE -> #AV
      <- TryCatch range: [AD...AC] -> AE ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AD...AC] -> AE ([Ljava/lang/RuntimeException;])
===#Block AV(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -29827382)
      goto AV
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1129929122 ^ lvar54})
      goto AV
   2. _consume({233764664 ^ lvar54});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AV -> #AV
      <- ConditionalJump[IF_ICMPNE] #AE -> #AV
      <- ConditionalJump[IF_ICMPNE] #AV -> #AV
===#Block AK(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1487692466)
      goto AK
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {231633889 ^ lvar54})
      goto AK
   2. _consume({192963341 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1790592518)
      goto AK
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1814578148 ^ lvar54})
      goto AK
   5. _consume({999093720 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AK -> #AK
      <- ConditionalJump[IF_ICMPNE] #AA -> #AK
      <- ConditionalJump[IF_ICMPNE] #AD -> #AK
      <- ConditionalJump[IF_ICMPNE] #AK -> #AK
===#Block N(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar39 = lvar7;
   2. lvar51 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.hbearebjtdscrey(), lvar54);
   3. lvar40 = lvar39.equals(lvar51);
   4. if (lvar40 == {958048738 ^ lvar54})
      goto P
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -925544687)
      goto AN
      -> Immediate #N -> #O
      -> ConditionalJump[IF_ICMPNE] #N -> #AN
      -> ConditionalJump[IF_ICMPEQ] #N -> #P
      <- Switch[30481073] #E -> #N
===#Block AN(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -925544687)
      goto AN
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {684588506 ^ lvar54})
      goto AN
   2. _consume({684350259 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AN -> #AN
      <- ConditionalJump[IF_ICMPNE] #N -> #AN
      <- ConditionalJump[IF_ICMPNE] #AN -> #AN
===#Block O(size=3, flags=0)===
   0. lvar41 = {1773647046 ^ lvar54};
   1. lvar8 = lvar41;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1304274451)
      goto AJ
      -> Immediate #O -> #P
      -> ConditionalJump[IF_ICMPNE] #O -> #AJ
      <- Immediate #N -> #O
===#Block AJ(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -833566510)
      goto AJ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1751138007 ^ lvar54})
      goto AJ
   2. _consume({498662923 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1304274451)
      goto AJ
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {92506847 ^ lvar54})
      goto AJ
   5. _consume({183309875 ^ lvar54});
   6. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AJ -> #AJ
      <- ConditionalJump[IF_ICMPNE] #AJ -> #AJ
      <- ConditionalJump[IF_ICMPNE] #O -> #AJ
      <- ConditionalJump[IF_ICMPNE] #G -> #AJ
===#Block P(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar20 = lvar8;
   2. svar56 = {lvar20 ^ lvar54};
   3. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(svar56)) {
      case 34534626:
      	 goto	#T
      case 34534649:
      	 goto	#S
      case 34534654:
      	 goto	#U
      case 34534655:
      	 goto	#V
      default:
      	 goto	#Q
   }
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 294699664)
      goto AL
      -> Switch[34534655] #P -> #V
      -> Switch[34534654] #P -> #U
      -> Switch[34534626] #P -> #T
      -> ConditionalJump[IF_ICMPNE] #P -> #AL
      -> Switch[34534649] #P -> #S
      -> DefaultSwitch #P -> #Q
      <- ConditionalJump[IF_ICMPEQ] #J -> #P
      <- ConditionalJump[IF_ICMPEQ] #L -> #P
      <- UnconditionalJump[GOTO] #Y -> #P
      <- ConditionalJump[IF_ICMPEQ] #H -> #P
      <- UnconditionalJump[GOTO] #AH -> #P
      <- UnconditionalJump[GOTO] #AE -> #P
      <- ConditionalJump[IF_ICMPEQ] #N -> #P
      <- Immediate #O -> #P
      <- UnconditionalJump[GOTO] #AB -> #P
      <- ConditionalJump[IF_ICMPEQ] #F -> #P
      <- DefaultSwitch #E -> #P
===#Block Q(size=17, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = new java.lang.StringBuilder;
   2. _consume(lvar21.<init>());
   3. lvar42 = lvar1;
   4. lvar5 = {2004965334 ^ lvar54};
   5. lvar6 = {2004965335 ^ lvar54};
   6. lvar43 = lvar42.substring(lvar5, lvar6);
   7. lvar44 = lvar43.toUpperCase();
   8. lvar22 = lvar21.append(lvar44);
   9. lvar45 = lvar1;
   10. lvar52 = {2004965335 ^ lvar54};
   11. lvar46 = lvar45.substring(lvar52);
   12. lvar47 = lvar46.toLowerCase();
   13. lvar23 = lvar22.append(lvar47);
   14. lvar24 = lvar23.toString();
   15. return lvar24;
   16. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1140146509)
      goto AM
      -> ConditionalJump[IF_ICMPNE] #Q -> #AM
      <- DefaultSwitch #P -> #Q
===#Block S(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar26 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.mlakyhzubpftzns(), lvar54);
   2. return lvar26;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1348386512)
      goto AR
      -> ConditionalJump[IF_ICMPNE] #S -> #AR
      <- Switch[34534649] #P -> #S
===#Block AR(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 703244057)
      goto AR
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {926670022 ^ lvar54})
      goto AR
   2. _consume({177052130 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1348386512)
      goto AR
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {959011896 ^ lvar54})
      goto AR
   5. _consume({449223622 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AR -> #AR
      <- ConditionalJump[IF_ICMPNE] #M -> #AR
      <- ConditionalJump[IF_ICMPNE] #S -> #AR
      <- ConditionalJump[IF_ICMPNE] #AR -> #AR
===#Block T(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.idjgxxbzutklhap(), lvar54);
   2. return lvar27;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1834566646)
      goto AM
      -> ConditionalJump[IF_ICMPNE] #T -> #AM
      <- Switch[34534626] #P -> #T
===#Block AM(size=13, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 57183216)
      goto AM
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1240333862 ^ lvar54})
      goto AM
   2. _consume({73452356 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1834566646)
      goto AM
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1851372764 ^ lvar54})
      goto AM
   5. _consume({281631232 ^ lvar54});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1140146509)
      goto AM
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {691746464 ^ lvar54})
      goto AM
   8. _consume({1156515898 ^ lvar54});
   9. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 207700258)
      goto AM
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {707321056 ^ lvar54})
      goto AM
   11. _consume({998746357 ^ lvar54});
   12. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AM -> #AM
      <- ConditionalJump[IF_ICMPNE] #L -> #AM
      <- ConditionalJump[IF_ICMPNE] #AM -> #AM
      <- ConditionalJump[IF_ICMPNE] #B -> #AM
      <- ConditionalJump[IF_ICMPNE] #Q -> #AM
      <- ConditionalJump[IF_ICMPNE] #T -> #AM
===#Block U(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar28 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.rtldtzemvtoevhv(), lvar54);
   2. return lvar28;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1415689713)
      goto AL
      -> ConditionalJump[IF_ICMPNE] #U -> #AL
      <- Switch[34534654] #P -> #U
===#Block AL(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 294699664)
      goto AL
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1175972879 ^ lvar54})
      goto AL
   2. _consume({559046425 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1415689713)
      goto AL
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1579391327 ^ lvar54})
      goto AL
   5. _consume({1571403370 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AL -> #AL
      <- ConditionalJump[IF_ICMPNE] #U -> #AL
      <- ConditionalJump[IF_ICMPNE] #AL -> #AL
      <- ConditionalJump[IF_ICMPNE] #P -> #AL
===#Block V(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.pyfhbvseqedplks(), lvar54);
   2. return lvar29;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1100641502)
      goto AI
      -> ConditionalJump[IF_ICMPNE] #V -> #AI
      <- Switch[34534655] #P -> #V
===#Block AI(size=16, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1222492114)
      goto AI
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1474023538 ^ lvar54})
      goto AI
   2. _consume({292904095 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -402391638)
      goto AI
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1687734821 ^ lvar54})
      goto AI
   5. _consume({1707933019 ^ lvar54});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1100641502)
      goto AI
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1417008392 ^ lvar54})
      goto AI
   8. _consume({322408011 ^ lvar54});
   9. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 311099875)
      goto AI
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {2088634403 ^ lvar54})
      goto AI
   11. _consume({763194949 ^ lvar54});
   12. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -629149990)
      goto AI
   13. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1358494954 ^ lvar54})
      goto AI
   14. _consume({1806266894 ^ lvar54});
   15. throw new java/lang/RuntimeException();
      -> ConditionalJump[IF_ICMPNE] #AI -> #AI
      <- ConditionalJump[IF_ICMPNE] #AG -> #AI
      <- ConditionalJump[IF_ICMPNE] #V -> #AI
      <- ConditionalJump[IF_ICMPNE] #A -> #AI
      <- ConditionalJump[IF_ICMPNE] #AI -> #AI
      <- ConditionalJump[IF_ICMPNE] #C -> #AI
      <- ConditionalJump[IF_ICMPNE] #K -> #AI
