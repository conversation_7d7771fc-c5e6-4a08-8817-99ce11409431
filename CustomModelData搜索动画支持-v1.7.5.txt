# 🎨 HangEvacuation v1.7.5 - CustomModelData 搜索动画支持

## 📅 更新日期: 2025-01-XX

### 🎯 更新类型: 功能增强 + 高版本兼容性改进

---

## 🆕 主要更新

### 🎨 **CustomModelData 搜索动画支持**
- **新增功能**: 为搜索中的玻璃板添加 CustomModelData 支持
- **版本要求**: Minecraft 1.14+ (向下兼容，低版本自动忽略)
- **应用场景**: 配合 ItemsAdder 或材质包显示自定义搜索动画

### 🔧 **技术特性**
- **智能版本检测**: 自动检测服务器版本，仅在支持的版本中启用
- **动画流畅性**: CustomModelData 值在整个搜索过程中保持不变
- **颜色变化保持**: 红→橙→黄→绿的颜色变化逻辑完全保留
- **向后兼容**: 低版本服务器完全不受影响

---

## 🎮 功能详解

### 🎨 **CustomModelData 工作原理**

#### 1. **配置文件设置**
```yaml
# config.yml
items:
  searching-item:
    material: STAINED_GLASS_PANE
    data: 14
    name: "§c正在搜索..."
    lore:
      - "§7搜索进行中..."
    # 🆕 CustomModelData 设置 (仅在1.14+版本生效)
    custom_model_data: 0  # 设置为0表示不使用，正数值指定自定义模型
```

#### 2. **版本兼容性**
- **Minecraft 1.14+**: 完全支持 CustomModelData
- **Minecraft 1.8-1.13**: 自动忽略 CustomModelData，使用原版外观
- **自动检测**: 插件自动检测版本并选择合适的处理方式

#### 3. **动画流畅性保证**
- **固定 CustomModelData**: 整个搜索过程中 CustomModelData 值不变
- **颜色变化**: 仍然保持红→橙→黄→绿的进度颜色变化
- **材质包兼容**: 可以通过材质包为不同颜色的玻璃板设置相同的动画模型

---

## 🔧 技术实现

### 📝 **新增方法 (VersionUtils.java)**

#### 1. **版本检测方法**
```java
/**
 * 检查当前版本是否支持 CustomModelData
 * CustomModelData 在 1.14+ 版本中可用
 */
public static boolean supportsCustomModelData() {
    return isVersionAtLeast(1, 14);
}
```

#### 2. **CustomModelData 设置方法**
```java
/**
 * 为物品设置 CustomModelData (仅在支持的版本中生效)
 * @param item 要设置的物品
 * @param customModelData CustomModelData 值 (0表示不设置)
 * @return 设置后的物品
 */
public static ItemStack setCustomModelData(ItemStack item, int customModelData) {
    if (item == null || customModelData <= 0 || !supportsCustomModelData()) {
        return item;
    }
    
    try {
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            // 使用反射设置 CustomModelData，确保在不支持的版本中不会出错
            Method setCustomModelDataMethod = meta.getClass().getMethod("setCustomModelData", Integer.class);
            setCustomModelDataMethod.invoke(meta, customModelData);
            item.setItemMeta(meta);
        }
    } catch (Exception e) {
        // 静默处理不支持 CustomModelData 的版本
    }
    
    return item;
}
```

### 🎯 **进度显示逻辑更新 (TreasureChestGUI.java)**

#### 修改的方法: `updateProgressDisplay()`
```java
private void updateProgressDisplay(int slot, int progress) {
    // 原有的颜色设置逻辑保持不变
    ItemStack progressItem;
    if (progress <= 25) {
        progressItem = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS_PANE", 14)); // 红色
    } else if (progress <= 50) {
        progressItem = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS_PANE", 1)); // 橙色
    } else if (progress <= 75) {
        progressItem = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS_PANE", 4)); // 黄色
    } else {
        progressItem = new ItemStack(VersionUtils.getCompatibleColoredMaterial("STAINED_GLASS_PANE", 5)); // 浅绿色
    }
    
    // 设置名称和描述
    ItemMeta meta = progressItem.getItemMeta();
    meta.setDisplayName("§e正在搜索中 " + progress + "%");
    meta.setLore(Arrays.asList("§7搜索中..."));
    progressItem.setItemMeta(meta);
    
    // 🆕 为搜索中的玻璃板设置 CustomModelData (仅在高版本生效)
    int customModelData = plugin.getConfig().getInt("items.searching-item.custom_model_data", 0);
    if (customModelData > 0) {
        progressItem = VersionUtils.setCustomModelData(progressItem, customModelData);
    }
    
    inventory.setItem(slot, progressItem);
}
```

---

## 🎨 使用指南

### 🛠️ **配置 CustomModelData**

#### 1. **基础配置**
```yaml
# config.yml
items:
  searching-item:
    custom_model_data: 100001  # 设置您的自定义模型ID
```

#### 2. **ItemsAdder 配置示例**
```yaml
# ItemsAdder 配置
items:
  searching_glass_pane:
    display_name: "搜索中"
    material: RED_STAINED_GLASS_PANE
    custom_model_data: 100001
    # 在这里配置您的动画效果
```

#### 3. **材质包配置**
- 为 `custom_model_data: 100001` 创建动画模型
- 确保所有颜色的玻璃板都使用相同的 CustomModelData
- 动画将在颜色变化时保持流畅

### 🎯 **最佳实践**

#### 1. **CustomModelData 值选择**
- **推荐范围**: 100000-999999 (避免与其他插件冲突)
- **避免使用**: 1-1000 (常用范围，容易冲突)
- **测试建议**: 先用小值测试，确认无冲突后使用

#### 2. **动画设计建议**
- **保持一致性**: 所有进度阶段使用相同的动画
- **性能考虑**: 避免过于复杂的动画影响性能
- **视觉效果**: 动画应该明显但不干扰游戏体验

#### 3. **兼容性测试**
- **多版本测试**: 在不同版本服务器上测试
- **材质包测试**: 确认材质包在所有客户端正常工作
- **性能测试**: 确认动画不会影响服务器性能

---

## 🔍 测试建议

### 🧪 **功能测试**

#### 1. **版本兼容性测试**
```bash
# 测试不同版本
- Minecraft 1.8.8: 应该忽略 CustomModelData，正常显示颜色变化
- Minecraft 1.14+: 应该正确应用 CustomModelData 和颜色变化
```

#### 2. **配置测试**
```yaml
# 测试不同配置值
custom_model_data: 0      # 应该不设置 CustomModelData
custom_model_data: 100001 # 应该设置 CustomModelData 为 100001
```

#### 3. **动画测试**
- 打开摸金箱，观察搜索动画
- 确认颜色变化: 红→橙→黄→绿
- 确认 CustomModelData 在整个过程中保持不变
- 确认动画流畅，无卡顿

### 🎯 **性能测试**
- 多个玩家同时搜索摸金箱
- 观察服务器性能影响
- 检查内存使用情况

---

## 💡 用户体验改进

### 🎨 **视觉效果增强**
- **自定义动画**: 支持完全自定义的搜索动画
- **材质包兼容**: 完美配合各种材质包
- **流畅体验**: 动画在颜色变化时保持连贯

### 🔧 **配置灵活性**
- **可选功能**: CustomModelData 完全可选，不影响基础功能
- **向下兼容**: 低版本服务器无需任何修改
- **简单配置**: 只需修改一个数值即可启用

### 🛡️ **稳定性保证**
- **错误处理**: 完善的异常处理，确保插件稳定
- **版本检测**: 自动检测版本，避免不兼容问题
- **静默降级**: 不支持的版本静默使用原版外观

---

## 🎯 总结

这次更新为 HangEvacuation 插件添加了对高版本 CustomModelData 的支持，使得服务器管理员可以：

1. **🎨 自定义搜索动画**: 通过 ItemsAdder 或材质包创建独特的搜索效果
2. **🔄 保持兼容性**: 低版本服务器完全不受影响
3. **⚡ 流畅体验**: 动画在颜色变化时保持连贯
4. **🛠️ 简单配置**: 只需修改配置文件中的一个数值

这个功能特别适合希望提升玩家体验、使用自定义材质包或 ItemsAdder 的服务器。通过这个更新，摸金箱的搜索过程将变得更加生动和有趣！
