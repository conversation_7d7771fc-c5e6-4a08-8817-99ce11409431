# 启动时浮空字清理功能说明

## 🎯 **功能概述**

为了防止服务器重启后遗留摸金插件的浮空字实体，我们添加了**启动时自动清理功能**。每次服务器启动时，插件会自动扫描所有世界并清除遗留的摸金插件浮空字，确保不会出现浮空字异常或重复显示的问题。

## ✨ **功能特点**

### **1. 智能识别**
- 🔍 **精确识别**：只清除摸金插件相关的浮空字，不影响其他插件的ArmorStand
- 🎯 **多重检测**：通过文本内容、实体属性等多种方式识别
- 🛡️ **安全机制**：检测失败时不会误删其他实体

### **2. 全版本兼容**
- ✅ **1.8.8-1.21.4**：支持所有主流Minecraft版本
- 🔧 **特殊处理**：针对1.16.x版本的特殊兼容性处理
- 🌍 **多世界支持**：自动扫描所有已加载的世界

### **3. 可配置控制**
- ⚙️ **开关控制**：可以通过配置文件启用/禁用此功能
- ⏱️ **延迟设置**：可配置启动后延迟多久开始清理
- 📊 **详细日志**：提供清理过程的详细信息

## 🔧 **配置选项**

在 `config.yml` 中添加了以下配置：

```yaml
treasure-chest:
  hologram:
    # 启动时清理设置
    startup_cleanup:
      enabled: true               # 是否在服务器启动时清除遗留的摸金插件浮空字
      delay_seconds: 1           # 延迟多少秒后开始清理 (确保所有世界都已加载)
```

### **配置说明**

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `enabled` | `true` | 是否启用启动时清理功能 |
| `delay_seconds` | `1` | 服务器启动后延迟多少秒开始清理 |

## 🔍 **识别机制**

### **文本内容检测**
系统会检查ArmorStand的自定义名称是否包含以下内容：

1. **摸金箱类型**：
   - 摸金箱、武器箱、弹药箱
   - 医疗箱、补给箱、装备箱

2. **状态文本**：
   - 搜索中、搜索完成
   - 剩余时间、冷却中

3. **时间格式**：
   - 倒计时格式（如：`05:30`）

### **实体属性检测**
检查ArmorStand是否具有摸金插件浮空字的典型属性：
- ✅ `isSmall()` - 小型盔甲架
- ✅ `isMarker()` - 标记模式
- ✅ `!isVisible()` - 不可见
- ✅ `!hasGravity()` - 无重力
- ✅ `isCustomNameVisible()` - 自定义名称可见

## 🚀 **工作流程**

### **启动序列**
1. **插件启用** → 初始化所有管理器
2. **延迟执行** → 等待配置的延迟时间
3. **检查配置** → 确认清理功能是否启用
4. **扫描世界** → 遍历所有已加载的世界
5. **识别清理** → 识别并清除摸金插件浮空字
6. **加载数据** → 继续正常的数据加载流程

### **清理过程**
```
🧹 开始清除遗留的摸金插件浮空字...
  世界 world: 清除了 3 个浮空字
  世界 world_nether: 清除了 1 个浮空字
✅ 浮空字清除完成: 共检查 3 个世界，清除了 4 个遗留浮空字
```

## 📋 **日志示例**

### **正常清理**
```
[INFO] 🧹 开始清除遗留的摸金插件浮空字...
[INFO]   世界 world: 清除了 2 个浮空字
[INFO]   世界 world_nether: 清除了 1 个浮空字
[INFO] ✅ 浮空字清除完成: 共检查 3 个世界，清除了 3 个遗留浮空字
[INFO] 摸金箱数据加载完成
```

### **无遗留浮空字**
```
[INFO] 🧹 开始清除遗留的摸金插件浮空字...
[INFO] ✅ 浮空字清除完成: 未发现遗留的摸金插件浮空字
[INFO] 摸金箱数据加载完成
```

### **功能禁用**
```
[INFO] 启动时浮空字清理已禁用
[INFO] 摸金箱数据加载完成
```

## ⚙️ **使用建议**

### **推荐设置**
```yaml
startup_cleanup:
  enabled: true      # 建议保持启用
  delay_seconds: 1   # 1秒延迟通常足够
```

### **特殊情况**
1. **大型服务器**：如果世界加载较慢，可以增加 `delay_seconds` 到 3-5 秒
2. **多世界服务器**：建议保持启用，确保所有世界的浮空字都被清理
3. **测试环境**：可以临时禁用以保留浮空字进行调试

## 🛡️ **安全保障**

### **误删防护**
- ✅ **精确识别**：只删除确认是摸金插件的浮空字
- ✅ **异常处理**：单个实体处理失败不影响其他实体
- ✅ **版本兼容**：针对不同版本使用适当的清理方法

### **性能优化**
- ⚡ **延迟执行**：避免在启动时造成性能压力
- ⚡ **批量处理**：高效地处理多个实体
- ⚡ **异常隔离**：错误不会影响插件正常启动

## 🔄 **版本兼容性**

### **全版本支持**
- ✅ **1.8.8-1.12.2**：使用旧版API兼容
- ✅ **1.13-1.15.2**：标准API处理
- ✅ **1.16.x**：特殊兼容性处理
- ✅ **1.17-1.21.4**：最新API支持

### **特殊处理**
```java
// 1.16.x版本特殊处理
if (VersionUtils.isVersionAtLeast(1, 16) && 
    !VersionUtils.isVersionAtLeast(1, 17)) {
    armorStand.setCustomNameVisible(false);
    armorStand.setVisible(false);
}
armorStand.remove();
```

## 🎮 **用户体验**

### **对玩家的影响**
- ✅ **无感知**：玩家不会感受到清理过程
- ✅ **无延迟**：不影响正常游戏体验
- ✅ **自动恢复**：摸金箱浮空字会在需要时自动重新生成

### **对管理员的好处**
- 🧹 **自动维护**：无需手动清理遗留浮空字
- 📊 **详细日志**：清楚了解清理情况
- ⚙️ **灵活配置**：可根据需要调整设置

## 🚨 **故障排除**

### **常见问题**

**Q: 启动时没有看到清理日志？**
A: 检查配置文件中 `startup_cleanup.enabled` 是否为 `true`

**Q: 清理了不应该清理的浮空字？**
A: 检查被清理的浮空字是否符合识别条件，可以调整识别逻辑

**Q: 清理过程中出现错误？**
A: 查看详细错误日志，通常是权限或版本兼容性问题

### **调试模式**
启用调试模式可以看到更详细的信息：
```yaml
debug:
  enabled: true
```

## 📈 **效果验证**

### **验证方法**
1. **重启前**：记录当前浮空字数量
2. **重启服务器**：观察启动日志
3. **检查结果**：确认遗留浮空字被清理
4. **功能测试**：验证新的浮空字正常生成

### **预期结果**
- 🎯 **启动日志**：显示清理过程和结果
- 🎯 **无遗留**：不再有异常的浮空字实体
- 🎯 **正常功能**：摸金箱功能完全正常

## 🎉 **总结**

启动时浮空字清理功能确保了：
- 🧹 **自动清理**：每次启动自动清除遗留浮空字
- 🛡️ **安全可靠**：精确识别，不误删其他实体
- ⚙️ **灵活配置**：可根据需要启用/禁用
- 🌍 **全版本兼容**：支持1.8.8-1.21.4所有版本
- 📊 **详细反馈**：提供清理过程的完整信息

现在您可以放心重启服务器，不用担心浮空字遗留问题了！
