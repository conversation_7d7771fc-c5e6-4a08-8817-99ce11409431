# 🔧 摸金箱类型保存修复报告 - v1.7.2

## 🚨 **问题描述**

用户反馈：**重启服务器后无法正常保存除了摸金箱之外的其他类型的摸金箱**

### **具体症状**
- 武器箱、弹药箱、医疗箱、补给箱、装备箱等重启后失效
- 重启后所有摸金箱都变成普通摸金箱（common类型）
- 特殊类型摸金箱的槽位数量、刷新时间等配置丢失
- 物品生成不再按照特定类型的配置进行

## 🔍 **根本原因分析**

经过深入代码分析，发现了关键问题：

### **核心问题**
在 `ChestManager.java` 中：

1. **`saveChestData` 方法缺陷**：
   ```java
   // ❌ 问题：没有保存 chestType 字段
   chestSection.set("lastRefreshTime", data.getLastRefreshTime());
   chestSection.set("nextRefreshTime", data.getNextRefreshTime());
   chestSection.set("originalItemCount", data.getOriginalItemCount());
   // 缺少：chestSection.set("chestType", data.getChestType());
   ```

2. **`loadChestData` 方法缺陷**：
   ```java
   // ❌ 问题：没有加载 chestType 字段，总是使用默认构造函数
   TreasureChestData data = new TreasureChestData(); // 默认为 "common"
   // 缺少：从配置文件读取 chestType 并使用带参数的构造函数
   ```

### **数据流程问题**
```
放置摸金箱 → 设置类型(weapon/medical等) → 保存到内存 ✅
                                                ↓
重启服务器 → 从文件加载 → chestType丢失 ❌ → 全部变成common类型
```

## ✅ **修复内容**

### **1. 修复保存逻辑**
**文件**: `src/main/java/com/hang/plugin/manager/ChestManager.java:131`

```java
// 保存基本信息
chestSection.set("world", location.getWorld().getName());
chestSection.set("x", location.getBlockX());
chestSection.set("y", location.getBlockY());
chestSection.set("z", location.getBlockZ());
chestSection.set("lastRefreshTime", data.getLastRefreshTime());
chestSection.set("nextRefreshTime", data.getNextRefreshTime());
chestSection.set("originalItemCount", data.getOriginalItemCount());

// 🔧 修复：保存摸金箱类型
chestSection.set("chestType", data.getChestType());
```

### **2. 修复加载逻辑**
**文件**: `src/main/java/com/hang/plugin/manager/ChestManager.java:232-234`

```java
// 🔧 修复：加载摸金箱类型
String chestType = chestSection.getString("chestType", "common");
com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
    new com.hang.plugin.listeners.PlayerListener.TreasureChestData(chestType);
```

## 📊 **修复效果**

### **修复前**
```yaml
# chests.yml 文件结构（缺少 chestType）
chests:
  world_100_64_200:
    world: "world"
    x: 100
    y: 64
    z: 200
    lastRefreshTime: 1703123456789
    nextRefreshTime: 1703123756789
    originalItemCount: 8
    # ❌ 缺少 chestType 字段
```

### **修复后**
```yaml
# chests.yml 文件结构（包含 chestType）
chests:
  world_100_64_200:
    world: "world"
    x: 100
    y: 64
    z: 200
    lastRefreshTime: 1703123456789
    nextRefreshTime: 1703123756789
    originalItemCount: 8
    chestType: "weapon"  # ✅ 正确保存摸金箱类型
```

## 🎯 **测试验证**

### **测试步骤**
1. **放置不同类型摸金箱**：
   ```
   /evac give weapon    # 武器箱
   /evac give medical   # 医疗箱
   /evac give ammo      # 弹药箱
   /evac give supply    # 补给箱
   /evac give equipment # 装备箱
   ```

2. **验证类型正确**：
   - 检查槽位数量是否符合配置
   - 检查刷新时间是否符合配置
   - 检查生成的物品是否符合类型

3. **重启服务器**：
   ```
   /stop
   # 重新启动服务器
   ```

4. **验证修复效果**：
   - 检查摸金箱类型是否保持
   - 检查配置是否正确应用
   - 检查物品生成是否正常

### **预期结果**
- ✅ 武器箱重启后仍然是武器箱（8个槽位，15分钟刷新）
- ✅ 医疗箱重启后仍然是医疗箱（4个槽位，3分钟刷新）
- ✅ 弹药箱重启后仍然是弹药箱（6个槽位，10分钟刷新）
- ✅ 补给箱重启后仍然是补给箱（7个槽位，8分钟刷新）
- ✅ 装备箱重启后仍然是装备箱（9个槽位，20分钟刷新）

## 🔄 **向后兼容性**

### **兼容性保证**
- 现有的 `chests.yml` 文件中没有 `chestType` 字段的摸金箱会自动设为 `"common"` 类型
- 不会影响现有的普通摸金箱功能
- 升级后首次保存会自动添加 `chestType` 字段

### **升级建议**
1. 备份现有的 `chests.yml` 文件
2. 更新插件版本
3. 重启服务器验证功能正常
4. 如有问题可恢复备份文件

## 📝 **版本信息**
- **修复版本**: v1.7.2
- **修复日期**: 2024年
- **主要改进**: 摸金箱类型持久化保存
- **影响文件**: `ChestManager.java`
- **兼容性**: 完全向后兼容

## 🎉 **总结**

这个修复解决了摸金箱系统中一个关键的数据持久化问题，确保了不同类型的摸金箱在服务器重启后能够保持其特定的配置和行为。修复后，玩家可以放心使用各种类型的摸金箱，不用担心重启后功能失效的问题。
