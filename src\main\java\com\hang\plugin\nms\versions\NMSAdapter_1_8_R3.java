package com.hang.plugin.nms.versions;

import com.hang.plugin.nms.interfaces.NMSAdapter;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.List;

/**
 * 1.8-1.12版本的NMS适配器
 *
 * <AUTHOR>
 */
public class NMSAdapter_1_8_R3 implements NMSAdapter {

    private Class<?> craftPlayerClass;
    private Class<?> entityPlayerClass;
    private Class<?> packetClass;
    private Class<?> chatComponentTextClass;
    private Class<?> packetPlayOutChatClass;
    private Class<?> craftItemStackClass;
    private Class<?> nbtTagCompoundClass;
    private Class<?> nmsItemStackClass;

    private Method getHandleMethod;
    private Method sendPacketMethod;
    private Field playerConnectionField;

    // 用于跟踪NMS是否成功初始化
    private boolean nmsInitialized = false;
    private String serverVersion;

    @Override
    public void initialize() {
        try {
            // 获取NMS和CraftBukkit类
            serverVersion = org.bukkit.Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];

            // 尝试加载类，如果失败则使用兼容模式
            try {
                craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + serverVersion + ".entity.CraftPlayer");
                entityPlayerClass = Class.forName("net.minecraft.server." + serverVersion + ".EntityPlayer");
                packetClass = Class.forName("net.minecraft.server." + serverVersion + ".Packet");
                chatComponentTextClass = Class.forName("net.minecraft.server." + serverVersion + ".ChatComponentText");
                packetPlayOutChatClass = Class.forName("net.minecraft.server." + serverVersion + ".PacketPlayOutChat");
                craftItemStackClass = Class.forName("org.bukkit.craftbukkit." + serverVersion + ".inventory.CraftItemStack");
                nbtTagCompoundClass = Class.forName("net.minecraft.server." + serverVersion + ".NBTTagCompound");
                nmsItemStackClass = Class.forName("net.minecraft.server." + serverVersion + ".ItemStack");

                // 获取方法
                getHandleMethod = craftPlayerClass.getMethod("getHandle");
                playerConnectionField = entityPlayerClass.getField("playerConnection");
                sendPacketMethod = playerConnectionField.getType().getMethod("sendPacket", packetClass);

                // 如果所有类和方法都成功加载，标记为已初始化
                nmsInitialized = true;

            } catch (Exception nmsException) {
                // 如果NMS类加载失败，尝试新版本的包结构
                try {
                    // 1.17+ 使用不同的包结构
                    craftPlayerClass = Class.forName("org.bukkit.craftbukkit." + serverVersion + ".entity.CraftPlayer");
                    // 新版本可能没有NMS包，使用兼容模式
                    nmsInitialized = false;
                } catch (Exception compatException) {
                    // 完全兼容模式，只使用Bukkit API
                    nmsInitialized = false;
                }
            }

        } catch (Exception e) {
            // 不抛出异常，允许插件继续运行
            nmsInitialized = false;
        }
    }

    @Override
    public String getVersion() {
        return "通用兼容适配器 (1.8-1.21+)";
    }

    @Override
    public ArmorStand createHologram(Location location, String text) {
        ArmorStand armorStand = (ArmorStand) location.getWorld().spawnEntity(location, EntityType.ARMOR_STAND);
        setupArmorStand(armorStand);
        armorStand.setCustomName(text);
        armorStand.setCustomNameVisible(true);
        return armorStand;
    }

    @Override
    public void setupArmorStand(ArmorStand armorStand) {
        armorStand.setVisible(false);
        armorStand.setGravity(false);
        armorStand.setCanPickupItems(false);
        armorStand.setSmall(true);

        // 使用反射设置可能不存在的方法
        try {
            Method setMarkerMethod = armorStand.getClass().getMethod("setMarker", boolean.class);
            setMarkerMethod.invoke(armorStand, true);
        } catch (Exception ignored) {
            // 1.8可能没有setMarker方法
        }

        try {
            Method setBasePlateMethod = armorStand.getClass().getMethod("setBasePlate", boolean.class);
            setBasePlateMethod.invoke(armorStand, false);
        } catch (Exception ignored) {
            // 某些版本可能没有setBasePlate方法
        }

        try {
            Method setArmsMethod = armorStand.getClass().getMethod("setArms", boolean.class);
            setArmsMethod.invoke(armorStand, false);
        } catch (Exception ignored) {
            // 某些版本可能没有setArms方法
        }
    }

    @Override
    public void sendActionBar(Player player, String message) {
        if (message == null || message.isEmpty()) {
            return;
        }

        // 首先尝试使用现代Bukkit API (1.9+)
        if (tryModernActionBar(player, message)) {
            return;
        }

        // 如果NMS已初始化，尝试使用NMS方法
        if (nmsInitialized && tryNMSActionBar(player, message)) {
            return;
        }

        // 最后尝试使用反射调用Spigot API
        if (trySpigotActionBar(player, message)) {
            return;
        }

        // 所有方法都失败，静默忽略避免控制台污染
    }

    /**
     * 尝试使用现代Bukkit API发送ActionBar
     */
    private boolean tryModernActionBar(Player player, String message) {
        try {
            // 尝试使用1.9+的现代API (通过反射避免编译时依赖)
            Method sendActionBarMethod = player.getClass().getMethod("sendActionBar", String.class);
            sendActionBarMethod.invoke(player, message);
            return true;
        } catch (Exception e) {
            // 1.8或方法不存在
            return false;
        }
    }

    /**
     * 尝试使用NMS方法发送ActionBar
     */
    private boolean tryNMSActionBar(Player player, String message) {
        try {
            Object chatComponent = chatComponentTextClass.getConstructor(String.class).newInstance(message);

            // 1.8-1.11使用不同的构造函数
            Constructor<?> constructor;
            try {
                // 1.12+
                constructor = packetPlayOutChatClass.getConstructor(chatComponentTextClass,
                    Class.forName("net.minecraft.server." + serverVersion + ".ChatMessageType"));
                Object chatMessageType = Class.forName("net.minecraft.server." + serverVersion + ".ChatMessageType")
                    .getField("GAME_INFO").get(null);
                Object packet = constructor.newInstance(chatComponent, chatMessageType);
                sendPacket(player, packet);
                return true;
            } catch (Exception e) {
                // 1.8-1.11
                constructor = packetPlayOutChatClass.getConstructor(chatComponentTextClass, byte.class);
                Object packet = constructor.newInstance(chatComponent, (byte) 2);
                sendPacket(player, packet);
                return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 尝试使用反射调用Spigot API发送ActionBar
     */
    private boolean trySpigotActionBar(Player player, String message) {
        try {
            Object spigot = player.getClass().getMethod("spigot").invoke(player);
            Class<?> chatMessageTypeClass = Class.forName("net.md_5.bungee.api.ChatMessageType");
            Object actionBarType = chatMessageTypeClass.getField("ACTION_BAR").get(null);

            Class<?> textComponentClass = Class.forName("net.md_5.bungee.api.chat.TextComponent");
            Method fromLegacyTextMethod = textComponentClass.getMethod("fromLegacyText", String.class);
            Object[] components = (Object[]) fromLegacyTextMethod.invoke(null, message);

            Method sendMessageMethod = spigot.getClass().getMethod("sendMessage",
                chatMessageTypeClass, Class.forName("[Lnet.md_5.bungee.api.chat.BaseComponent;"));
            sendMessageMethod.invoke(spigot, actionBarType, components);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut) {
        try {
            // 1.8.8 版本的 sendTitle 只有两个参数
            player.sendTitle(title, subtitle);
        } catch (Exception e) {
            // 降级到聊天消息
            if (title != null && !title.isEmpty()) {
                player.sendMessage(title);
            }
            if (subtitle != null && !subtitle.isEmpty()) {
                player.sendMessage(subtitle);
            }
        }
    }

    @Override
    public void playSound(Player player, String sound, float volume, float pitch) {
        try {
            // 转换新版本音效名称到旧版本
            String legacySound = convertSoundName(sound);
            player.playSound(player.getLocation(), legacySound, volume, pitch);
        } catch (Exception e) {
            // 忽略音效播放失败
        }
    }

    private String convertSoundName(String modernSound) {
        // 将现代音效名称转换为1.8-1.12兼容的名称
        switch (modernSound.toLowerCase()) {
            case "entity.experience_orb.pickup":
                return "ORB_PICKUP";
            case "entity.player.levelup":
                return "LEVEL_UP";
            case "ui.button.click":
                return "CLICK";
            case "block.chest.open":
                return "CHEST_OPEN";
            case "block.chest.close":
                return "CHEST_CLOSE";
            default:
                return modernSound.toUpperCase().replace(".", "_");
        }
    }

    @Override
    public String getItemDisplayName(ItemStack item) {
        if (item == null) {
            return "AIR";
        }

        // 1. 优先使用物品的自定义显示名称
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }

        // 2. 尝试通过NMS获取本地化名称
        try {
            // 获取NMS ItemStack
            Method asNMSCopyMethod = craftItemStackClass.getMethod("asNMSCopy", ItemStack.class);
            Object nmsItem = asNMSCopyMethod.invoke(null, item);

            // 尝试获取本地化名称
            try {
                // 1.13+ 版本的方法
                Method getNameMethod = nmsItemStackClass.getMethod("getName");
                Object nameComponent = getNameMethod.invoke(nmsItem);
                if (nameComponent != null) {
                    // 尝试获取文本内容
                    try {
                        Method getTextMethod = nameComponent.getClass().getMethod("getText");
                        String localizedName = (String) getTextMethod.invoke(nameComponent);
                        if (localizedName != null && !localizedName.equals(item.getType().name())) {
                            return localizedName;
                        }
                    } catch (Exception e) {
                        // 尝试toString方法
                        String nameStr = nameComponent.toString();
                        if (nameStr != null && !nameStr.equals(item.getType().name()) &&
                            !nameStr.contains("TranslatableComponent") && !nameStr.contains("LiteralComponent")) {
                            return nameStr;
                        }
                    }
                }
            } catch (Exception e) {
                // 1.8-1.12 版本可能没有getName方法，尝试其他方法
                try {
                    Method getItemMethod = nmsItem.getClass().getMethod("getItem");
                    Object nmsItemType = getItemMethod.invoke(nmsItem);

                    // 尝试获取物品的本地化名称
                    Method getNameMethod = nmsItemType.getClass().getMethod("getName", nmsItemStackClass);
                    String localizedName = (String) getNameMethod.invoke(nmsItemType, nmsItem);
                    if (localizedName != null && !localizedName.equals(item.getType().name())) {
                        return localizedName;
                    }
                } catch (Exception e2) {
                    // 继续尝试其他方法
                }
            }
        } catch (Exception e) {
            // NMS获取失败，继续使用其他方法
        }

        // 3. 尝试从Lore中提取中文名称（模组物品）
        if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
            List<String> lore = item.getItemMeta().getLore();
            for (int i = 0; i < Math.min(lore.size(), 3); i++) {
                String line = lore.get(i);
                if (line != null) {
                    String cleanLine = line.replaceAll("§[0-9a-fk-or]", "").trim();
                    // 检查是否像物品名称
                    if (cleanLine.length() > 1 && cleanLine.length() < 30 &&
                        !cleanLine.contains(":") && !cleanLine.contains("[") &&
                        !cleanLine.contains("(") && !cleanLine.contains("/") &&
                        !cleanLine.matches(".*\\d+.*") && // 不包含数字
                        cleanLine.matches(".*[\\u4e00-\\u9fa5].*")) { // 包含中文字符
                        return cleanLine;
                    }
                }
            }
        }

        // 4. 使用材料名称的中文映射
        return getChineseMaterialName(item.getType().name());
    }

    /**
     * 获取材料的中文名称
     */
    private String getChineseMaterialName(String materialName) {
        // 常见材料的中文映射
        switch (materialName) {
            case "DIAMOND_SWORD": return "钻石剑";
            case "DIAMOND_PICKAXE": return "钻石镐";
            case "DIAMOND_AXE": return "钻石斧";
            case "DIAMOND_SHOVEL": return "钻石锹";
            case "DIAMOND_HOE": return "钻石锄";
            case "DIAMOND_HELMET": return "钻石头盔";
            case "DIAMOND_CHESTPLATE": return "钻石胸甲";
            case "DIAMOND_LEGGINGS": return "钻石护腿";
            case "DIAMOND_BOOTS": return "钻石靴子";
            case "IRON_SWORD": return "铁剑";
            case "IRON_PICKAXE": return "铁镐";
            case "IRON_AXE": return "铁斧";
            case "IRON_SHOVEL": return "铁锹";
            case "IRON_HOE": return "铁锄";
            case "IRON_HELMET": return "铁头盔";
            case "IRON_CHESTPLATE": return "铁胸甲";
            case "IRON_LEGGINGS": return "铁护腿";
            case "IRON_BOOTS": return "铁靴子";
            case "GOLDEN_SWORD": return "金剑";
            case "GOLDEN_PICKAXE": return "金镐";
            case "GOLDEN_AXE": return "金斧";
            case "GOLDEN_SHOVEL": return "金锹";
            case "GOLDEN_HOE": return "金锄";
            case "GOLDEN_HELMET": return "金头盔";
            case "GOLDEN_CHESTPLATE": return "金胸甲";
            case "GOLDEN_LEGGINGS": return "金护腿";
            case "GOLDEN_BOOTS": return "金靴子";
            case "NETHERITE_SWORD": return "下界合金剑";
            case "NETHERITE_PICKAXE": return "下界合金镐";
            case "NETHERITE_AXE": return "下界合金斧";
            case "NETHERITE_SHOVEL": return "下界合金锹";
            case "NETHERITE_HOE": return "下界合金锄";
            case "NETHERITE_HELMET": return "下界合金头盔";
            case "NETHERITE_CHESTPLATE": return "下界合金胸甲";
            case "NETHERITE_LEGGINGS": return "下界合金护腿";
            case "NETHERITE_BOOTS": return "下界合金靴子";
            case "BOW": return "弓";
            case "CROSSBOW": return "弩";
            case "SHIELD": return "盾牌";
            case "APPLE": return "苹果";
            case "GOLDEN_APPLE": return "金苹果";
            case "BREAD": return "面包";
            case "COOKED_BEEF": return "熟牛肉";
            case "COOKED_PORKCHOP": return "熟猪肉";
            case "ENDER_PEARL": return "末影珍珠";
            case "DIAMOND": return "钻石";
            case "EMERALD": return "绿宝石";
            case "GOLD_INGOT": return "金锭";
            case "IRON_INGOT": return "铁锭";
            case "COAL": return "煤炭";
            case "LAPIS_LAZULI": return "青金石";
            case "REDSTONE": return "红石";
            case "BLAZE_ROD": return "烈焰棒";
            case "EXP_BOTTLE": return "经验瓶";
            case "GLASS_BOTTLE": return "玻璃瓶";
            case "POTION": return "药水";
            case "SPLASH_POTION": return "喷溅药水";
            case "LINGERING_POTION": return "滞留药水";
            case "ENCHANTED_BOOK": return "附魔书";
            case "BOOK": return "书";
            case "PAPER": return "纸";
            case "LEATHER": return "皮革";
            case "STRING": return "线";
            case "FEATHER": return "羽毛";
            case "GUNPOWDER": return "火药";
            case "FLINT": return "燧石";
            case "WHEAT": return "小麦";
            case "CARROT": return "胡萝卜";
            case "POTATO": return "马铃薯";
            case "BEETROOT": return "甜菜根";
            case "SUGAR": return "糖";
            case "EGG": return "鸡蛋";
            case "MILK_BUCKET": return "牛奶桶";
            case "WATER_BUCKET": return "水桶";
            case "LAVA_BUCKET": return "岩浆桶";
            case "BUCKET": return "桶";
            case "SADDLE": return "鞍";
            case "NAME_TAG": return "命名牌";
            case "LEAD": return "拴绳";
            case "COMPASS": return "指南针";
            case "CLOCK": return "时钟";
            case "MAP": return "地图";
            case "SHEARS": return "剪刀";
            case "FISHING_ROD": return "钓鱼竿";
            case "FLINT_AND_STEEL": return "打火石";
            case "TORCH": return "火把";
            case "LADDER": return "梯子";
            case "CHEST": return "箱子";
            case "CRAFTING_TABLE": return "工作台";
            case "FURNACE": return "熔炉";
            case "ANVIL": return "铁砧";
            case "ENCHANTING_TABLE": return "附魔台";
            case "ENDER_CHEST": return "末影箱";
            case "BEACON": return "信标";
            case "TOTEM_OF_UNDYING": return "不死图腾";
            case "ELYTRA": return "鞘翅";
            case "TRIDENT": return "三叉戟";
            case "NAUTILUS_SHELL": return "鹦鹉螺壳";
            case "HEART_OF_THE_SEA": return "海洋之心";
            case "CONDUIT": return "潮涌核心";
            case "PRISMARINE_SHARD": return "海晶碎片";
            case "PRISMARINE_CRYSTALS": return "海晶砂粒";
            case "NETHER_STAR": return "下界之星";
            case "WITHER_SKELETON_SKULL": return "凋灵骷髅头颅";
            case "DRAGON_EGG": return "龙蛋";
            case "DRAGON_HEAD": return "龙首";
            case "END_CRYSTAL": return "末地水晶";
            case "CHORUS_FRUIT": return "紫颂果";
            case "POPPED_CHORUS_FRUIT": return "爆裂紫颂果";
            case "SHULKER_SHELL": return "潜影贝壳";
            case "PHANTOM_MEMBRANE": return "幻翼膜";
            default:
                // 如果没有映射，返回格式化的英文名称
                return materialName.toLowerCase().replace("_", " ");
        }
    }


    @Override
    public ItemStack setNBTTag(ItemStack item, String key, String value) {
        try {
            Method asNMSCopyMethod = craftItemStackClass.getMethod("asNMSCopy", ItemStack.class);
            Object nmsItem = asNMSCopyMethod.invoke(null, item);

            Method getTagMethod = nmsItemStackClass.getMethod("getTag");
            Object nbtTag = getTagMethod.invoke(nmsItem);

            if (nbtTag == null) {
                nbtTag = nbtTagCompoundClass.newInstance();
                Method setTagMethod = nmsItemStackClass.getMethod("setTag", nbtTagCompoundClass);
                setTagMethod.invoke(nmsItem, nbtTag);
            }

            Method setStringMethod = nbtTagCompoundClass.getMethod("setString", String.class, String.class);
            setStringMethod.invoke(nbtTag, key, value);

            Method asBukkitCopyMethod = craftItemStackClass.getMethod("asBukkitCopy", nmsItemStackClass);
            return (ItemStack) asBukkitCopyMethod.invoke(null, nmsItem);

        } catch (Exception e) {
            return item;
        }
    }

    @Override
    public String getNBTTag(ItemStack item, String key) {
        try {
            Method asNMSCopyMethod = craftItemStackClass.getMethod("asNMSCopy", ItemStack.class);
            Object nmsItem = asNMSCopyMethod.invoke(null, item);

            Method getTagMethod = nmsItemStackClass.getMethod("getTag");
            Object nbtTag = getTagMethod.invoke(nmsItem);

            if (nbtTag == null) {
                return null;
            }

            Method getStringMethod = nbtTagCompoundClass.getMethod("getString", String.class);
            return (String) getStringMethod.invoke(nbtTag, key);

        } catch (Exception e) {
            return null;
        }
    }

    @Override
    public boolean hasNBTTag(ItemStack item, String key) {
        try {
            Method asNMSCopyMethod = craftItemStackClass.getMethod("asNMSCopy", ItemStack.class);
            Object nmsItem = asNMSCopyMethod.invoke(null, item);

            Method getTagMethod = nmsItemStackClass.getMethod("getTag");
            Object nbtTag = getTagMethod.invoke(nmsItem);

            if (nbtTag == null) {
                return false;
            }

            Method hasKeyMethod = nbtTagCompoundClass.getMethod("hasKey", String.class);
            return (Boolean) hasKeyMethod.invoke(nbtTag, key);

        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void sendPacket(Player player, Object packet) {
        try {
            Object handle = getHandleMethod.invoke(player);
            Object playerConnection = playerConnectionField.get(handle);
            sendPacketMethod.invoke(playerConnection, packet);
        } catch (Exception e) {
            // 忽略数据包发送失败
        }
    }

    @Override
    public int getPing(Player player) {
        try {
            Object handle = getHandleMethod.invoke(player);
            Field pingField = entityPlayerClass.getField("ping");
            return pingField.getInt(handle);
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public void setBlockData(Location location, String blockData) {
        // 1.8-1.12版本的方块数据设置比较简单
        // 这里只是一个基础实现
        try {
            String[] parts = blockData.split(":");
            org.bukkit.Material material = org.bukkit.Material.valueOf(parts[0].toUpperCase());
            byte data = parts.length > 1 ? Byte.parseByte(parts[1]) : 0;

            location.getBlock().setType(material);
            if (data > 0) {
                location.getBlock().setData(data);
            }
        } catch (Exception e) {
            // 忽略方块设置失败
        }
    }

    @Override
    public void spawnParticle(Location location, String particle, int count, double offsetX, double offsetY, double offsetZ) {
        // 1.8版本的粒子效果比较有限，这里提供基础实现
        try {
            // 使用Bukkit API（如果可用）
            org.bukkit.Effect effect = org.bukkit.Effect.valueOf(particle.toUpperCase());
            location.getWorld().playEffect(location, effect, count);
        } catch (Exception e) {
            // 忽略粒子效果失败
        }
    }

    @Override
    public boolean isCustomNameVisible(ArmorStand armorStand) {
        return armorStand.isCustomNameVisible();
    }

    @Override
    public void setCustomNameVisible(ArmorStand armorStand, boolean visible) {
        armorStand.setCustomNameVisible(visible);
    }

    @Override
    public boolean isCompatible() {
        try {
            String version = org.bukkit.Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
            // 扩展兼容性到更多版本，使用兼容模式
            return version.startsWith("v1_8") || version.startsWith("v1_9") ||
                   version.startsWith("v1_10") || version.startsWith("v1_11") ||
                   version.startsWith("v1_12") || version.startsWith("v1_13") ||
                   version.startsWith("v1_14") || version.startsWith("v1_15") ||
                   version.startsWith("v1_16") || version.startsWith("v1_17") ||
                   version.startsWith("v1_18") || version.startsWith("v1_19") ||
                   version.startsWith("v1_20") || version.startsWith("v1_21");
        } catch (Exception e) {
            // 如果无法检测版本，尝试兼容模式
            return true;
        }
    }

    @Override
    public ItemStack createItemFromString(String itemString, int amount) {
        try {
            // 尝试通过Material.matchMaterial创建
            Material material = Material.matchMaterial(itemString);
            if (material != null) {
                return new ItemStack(material, amount);
            }

            // 如果是模组物品格式 (mod:item)，尝试其他方法
            if (itemString.contains(":")) {
                String[] parts = itemString.split(":");
                if (parts.length == 2) {
                    // 尝试不同的格式
                    String[] formats = {
                        parts[1].toUpperCase(),
                        itemString.toUpperCase(),
                        itemString.toLowerCase()
                    };

                    for (String format : formats) {
                        try {
                            material = Material.matchMaterial(format);
                            if (material != null) {
                                return new ItemStack(material, amount);
                            }
                        } catch (Exception e) {
                            // 继续尝试
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 无法创建
        }

        return null;
    }

    @Override
    public boolean isModItem(ItemStack item) {
        if (item == null) return false;

        // 在1.8版本中，检查物品是否有特殊的NBT标签或名称
        try {
            if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
                for (String line : item.getItemMeta().getLore()) {
                    if (line.contains("[模组物品]") || line.contains("mod:")) {
                        return true;
                    }
                }
            }
        } catch (Exception e) {
            // 忽略错误
        }

        return false;
    }

    @Override
    public String getModItemId(ItemStack item) {
        if (item == null || !isModItem(item)) {
            return null;
        }

        // 尝试从NBT或Lore中获取模组物品ID
        try {
            if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
                for (String line : item.getItemMeta().getLore()) {
                    if (line.contains("物品ID: §7")) {
                        String[] parts = line.split("物品ID: §7");
                        if (parts.length > 1) {
                            return parts[1].trim();
                        }
                    }
                }
            }
        } catch (Exception e) {
            // 忽略错误
        }

        return null;
    }

    @Override
    public Material getMaterialByName(String materialName) {
        if (materialName == null || materialName.isEmpty()) {
            return null;
        }

        try {
            // 尝试直接获取材料
            Material material = Material.getMaterial(materialName.toUpperCase());
            if (material != null) {
                return material;
            }

            // 尝试matchMaterial方法
            material = Material.matchMaterial(materialName);
            if (material != null) {
                return material;
            }

            // 如果是模组格式 (mod:item)，尝试只用物品名
            if (materialName.contains(":")) {
                String[] parts = materialName.split(":");
                if (parts.length == 2) {
                    material = Material.matchMaterial(parts[1]);
                    if (material != null) {
                        return material;
                    }
                }
            }
        } catch (Exception e) {
            // 1.8.x版本不支持模组材料，返回null
        }

        return null;
    }

    @Override
    public void cleanup() {
        // 清理资源
    }

    private String getServerVersion() {
        return serverVersion != null ? serverVersion :
            org.bukkit.Bukkit.getServer().getClass().getPackage().getName().split("\\.")[3];
    }
}
