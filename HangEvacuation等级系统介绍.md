# 🏆 HangEvacuation 摸金等级系统完整介绍

## 📖 **系统概述**

HangEvacuation插件的摸金等级系统是一个完整的RPG风格进阶系统，通过搜索摸金箱来获得经验，提升等级，解锁丰厚奖励。系统设计精美，颜色丰富，为玩家提供持续的游戏动力。

## 🎯 **核心机制**

### 📈 **等级提升方式**
- **搜索摸金箱** - 每次成功搜索摸金箱获得1点搜索经验
- **累积经验** - 达到指定搜索次数自动升级
- **即时反馈** - 升级时立即获得奖励和全服广播

### 🎨 **视觉系统**
- **颜色渐进** - 从白色新手到红色加粗王者，颜色逐渐华丽
- **聊天前缀** - 在聊天中显示等级前缀，彰显身份
- **升级特效** - 升级时播放音效和特殊消息

## 🏅 **完整等级表**

| 等级 | 名称 | 搜索要求 | 颜色显示 | 主要奖励 |
|------|------|----------|----------|----------|
| 1 | §f新手摸金者 | 0次 | §f白色 | 基础等级 |
| 2 | §a见习摸金者 | 10次 | §a绿色 | 钻石×1 |
| 3 | §b熟练摸金者 | 25次 | §b蓝色 | 绿宝石×3, 金锭×5 |
| 4 | §d专业摸金者 | 50次 | §d紫色 | 钻石×3, 绿宝石×5, 经验500 |
| 5 | §6大师摸金者 | 100次 | §6金色 | 钻石剑×1, 钻石×5, 经验1000 |
| 6 | §c传奇摸金者 | 200次 | §c红色 | 钻石镐×1, 钻石×10, 绿宝石×10, 经验2000 |
| 7 | §5史诗摸金者 | 350次 | §5深紫色 | 钻石胸甲×1, 钻石×15, 经验3000 |
| 8 | §4神话摸金者 | 500次 | §4深红色 | 全套钻石装备, 钻石×20, 经验5000 |
| 9 | §e§l至尊摸金者 | 750次 | §e§l黄色加粗 | 钻石块×5, 绿宝石块×3, 经验8000 |
| 10 | §c§l摸金王者 | 1000次 | §c§l红色加粗 | 各种块×10, 经验10000 |

## 🎁 **奖励系统详解**

### 💎 **物品奖励**
- **基础材料**: 钻石、绿宝石、金锭等珍贵材料
- **装备奖励**: 钻石工具和装备，助力冒险
- **稀有物品**: 钻石块、绿宝石块等高价值物品
- **完整套装**: 高等级可获得全套钻石装备

### ⭐ **经验奖励**
- **等级2-4**: 500-1000经验，适合新手快速成长
- **等级5-7**: 1000-3000经验，中期稳定提升
- **等级8-10**: 5000-10000经验，后期大幅提升

### 🎉 **特殊奖励**
- **个人消息**: 每次升级都有专属祝贺消息
- **全服广播**: 升级时全服玩家都能看到
- **音效提示**: 升级时播放悦耳的升级音效

## 🎮 **游戏体验**

### 🚀 **升级流程**
1. **放置摸金箱** - 使用 `/evac give` 获得摸金箱
2. **搜索宝藏** - 右键打开摸金箱，搜索物品
3. **累积经验** - 每次搜索获得1点搜索经验
4. **自动升级** - 达到要求时自动升级
5. **获得奖励** - 立即获得等级奖励

### 💬 **社交功能**
- **聊天前缀**: 聊天时显示 `§7[§a见习摸金者§7] 玩家名: 消息内容`
- **等级查询**: 使用 `/evac level` 查看自己的等级信息
- **全服广播**: `§6玩家 §e玩家名 §6达到了摸金等级 §a见习摸金者§6！`

### 📊 **进度追踪**
```
=== 您的摸金等级信息 ===
等级: §7[§a见习摸金者§7]
搜索次数: 15
距离下一等级 §b熟练摸金者 还需要: 10 次搜索
```

## ⚙️ **管理员配置**

### 📝 **配置文件**: `levels.yml`

#### **系统设置**
```yaml
settings:
  enable_level_system: true      # 启用等级系统
  broadcast_levelup: true        # 广播升级消息
  show_level_in_chat: true       # 聊天中显示等级
```

#### **消息自定义**
```yaml
messages:
  levelup_broadcast: "§6玩家 §e{player} §6达到了摸金等级 {level_name}§6！"
  level_info: "§6您的摸金等级: §e{level_name} §6| 搜索次数: §e{searches}"
```

#### **等级配置示例**
```yaml
levels:
  2:
    required_searches: 10          # 升级所需搜索次数
    name: "见习摸金者"             # 等级名称
    color: "§a"                    # 等级颜色
    display_format: "§7[§a{level_name}§7]"  # 聊天显示格式
    rewards:                       # 升级奖励
      - "/give {player} diamond 1"
      - "/tellraw {player} [\"§a恭喜达到见习摸金者等级！\"]"
```

### 🔧 **自定义扩展**

#### **添加新等级**
```yaml
  11:
    required_searches: 1500
    name: "摸金帝王"
    color: "§4§l"
    display_format: "§4§l[§6§l{level_name}§4§l]"
    rewards:
      - "/give {player} nether_star 1"
      - "/tellraw {player} [\"§4§l恭喜达到摸金帝王等级！\"]"
```

#### **修改奖励**
- **物品奖励**: `/give {player} 物品ID 数量`
- **经验奖励**: `/xp add {player} 经验值`
- **消息奖励**: `/tellraw {player} ["消息内容"]`
- **自定义命令**: 任何服务器命令都可以作为奖励

## 🎨 **颜色系统**

### 🌈 **颜色代码表**
| 代码 | 颜色 | 等级示例 |
|------|------|----------|
| `§f` | 白色 | 新手摸金者 |
| `§a` | 绿色 | 见习摸金者 |
| `§b` | 蓝色 | 熟练摸金者 |
| `§d` | 紫色 | 专业摸金者 |
| `§6` | 金色 | 大师摸金者 |
| `§c` | 红色 | 传奇摸金者 |
| `§5` | 深紫色 | 史诗摸金者 |
| `§4` | 深红色 | 神话摸金者 |
| `§e§l` | 黄色加粗 | 至尊摸金者 |
| `§c§l` | 红色加粗 | 摸金王者 |

### ✨ **格式代码**
- `§l` - 加粗
- `§o` - 斜体
- `§n` - 下划线
- `§m` - 删除线
- `§r` - 重置格式

## 📋 **命令系统**

### 👤 **玩家命令**
- `/evac level` - 查看自己的等级信息
- `/evac give` - 获得摸金箱（需要权限）

### 👑 **管理员命令**
- `/evac reload` - 重载插件配置
- `/evac give [玩家]` - 给指定玩家摸金箱

## 🎯 **游戏策略**

### 🚀 **快速升级技巧**
1. **批量放置** - 一次放置多个摸金箱
2. **完全搜索** - 搜索完所有物品再等待刷新
3. **定时回访** - 记住刷新时间，及时回来搜索
4. **团队合作** - 与朋友分享摸金箱位置

### 💡 **高效策略**
- **等级1-2**: 快速升级，专注搜索
- **等级3-5**: 开始获得有价值奖励
- **等级6-8**: 装备奖励期，提升战斗力
- **等级9-10**: 终极目标，获得最高荣誉

## 🏆 **成就系统**

### 🎖️ **里程碑成就**
- **初出茅庐** - 达到见习摸金者
- **小有名气** - 达到熟练摸金者  
- **声名远扬** - 达到大师摸金者
- **传奇人物** - 达到传奇摸金者
- **至高无上** - 达到摸金王者

### 🌟 **特殊荣誉**
- **聊天前缀** - 永久显示最高等级
- **全服认知** - 升级时全服广播
- **专属颜色** - 独特的等级颜色标识

## 🎉 **总结**

HangEvacuation的摸金等级系统为玩家提供了：

✅ **完整的进阶体验** - 从新手到王者的完整成长路径  
✅ **丰富的奖励系统** - 物品、经验、装备全方位奖励  
✅ **精美的视觉效果** - 渐进式颜色系统和特效  
✅ **社交互动功能** - 聊天前缀和全服广播  
✅ **高度可定制性** - 管理员可自由配置等级和奖励  

这是一个设计精良、功能完整的等级系统，为摸金箱游戏增添了持久的游戏动力和社交乐趣！

---

**插件版本**: HangEvacuation v1.5.0  
**支持版本**: Minecraft 1.12.2 & 1.20.1  
**作者**: hangzong  
**技术支持**: V hang060217  
**交流群**: 361919269
