package com.hang.plugin.nms;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.nms.interfaces.NMSAdapter;
import com.hang.plugin.utils.VersionUtils;

/**
 * NMS管理器
 * 负责加载和管理版本适配器
 * 
 * <AUTHOR>
 */
public class NMSManager {
    
    private final HangPlugin plugin;
    private NMSAdapter adapter;
    private boolean initialized = false;
    
    public NMSManager(HangPlugin plugin) {
        this.plugin = plugin;
    }
    
    /**
     * 初始化NMS适配器
     */
    public boolean initialize() {
        if (initialized) {
            return true;
        }
        
        try {
            // 检查版本支持
            if (!VersionUtils.isSupportedVersion()) {
                plugin.getLogger().warning("不支持的Minecraft版本: " + VersionUtils.getServerVersion());
                plugin.getLogger().warning("支持的版本范围: 1.8.x - 1.21.x");
                return false;
            }
            
            // 获取适配器类名
            String adapterClassName = VersionUtils.getAdapterClassName();
            plugin.getLogger().info("正在加载NMS适配器: " + adapterClassName);
            
            // 加载适配器类
            Class<?> adapterClass = Class.forName(adapterClassName);
            adapter = (NMSAdapter) adapterClass.newInstance();
            
            // 检查兼容性
            if (!adapter.isCompatible()) {
                plugin.getLogger().warning("NMS适配器与当前版本不兼容: " + VersionUtils.getVersionInfo());
                return false;
            }
            
            // 初始化适配器
            adapter.initialize();
            initialized = true;
            
            plugin.getLogger().info("NMS适配器加载成功: " + adapter.getVersion());
            plugin.getLogger().info("服务器信息: " + VersionUtils.getVersionInfo());
            plugin.getLogger().info("服务端类型: " + VersionUtils.getServerType());
            
            return true;
            
        } catch (ClassNotFoundException e) {
            plugin.getLogger().warning("找不到适配器类: " + VersionUtils.getAdapterClassName());
            plugin.getLogger().warning("当前版本可能不受支持: " + VersionUtils.getVersionInfo());
            plugin.getLogger().info("插件将以兼容模式运行，某些功能可能无法正常工作");
            return false;

        } catch (Exception e) {
            plugin.getLogger().warning("NMS适配器初始化失败: " + e.getMessage());
            plugin.getLogger().info("插件将以兼容模式运行，某些功能可能无法正常工作");
            return false;
        }
    }
    
    /**
     * 获取NMS适配器
     */
    public NMSAdapter getAdapter() {
        if (!initialized) {
            throw new IllegalStateException("NMS适配器未初始化");
        }
        return adapter;
    }
    
    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * 获取适配器版本
     */
    public String getAdapterVersion() {
        return initialized ? adapter.getVersion() : "未初始化";
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (initialized && adapter != null) {
            try {
                adapter.cleanup();
                plugin.getLogger().info("NMS适配器已清理");
            } catch (Exception e) {
                plugin.getLogger().warning("NMS适配器清理失败: " + e.getMessage());
            }
        }
        
        initialized = false;
        adapter = null;
    }
    
    /**
     * 重新初始化适配器
     */
    public boolean reinitialize() {
        cleanup();
        return initialize();
    }
    
    /**
     * 获取版本信息
     */
    public String getVersionInfo() {
        StringBuilder info = new StringBuilder();
        info.append("=== NMS适配器信息 ===\n");
        info.append("Minecraft版本: ").append(VersionUtils.getServerVersion()).append("\n");
        info.append("NMS版本: ").append(VersionUtils.getNMSVersion()).append("\n");
        info.append("服务端类型: ").append(VersionUtils.getServerType()).append("\n");
        info.append("适配器版本: ").append(getAdapterVersion()).append("\n");
        info.append("初始化状态: ").append(initialized ? "已初始化" : "未初始化").append("\n");
        info.append("版本支持: ").append(VersionUtils.isSupportedVersion() ? "支持" : "不支持");
        
        return info.toString();
    }
    
    /**
     * 检查功能可用性
     */
    public boolean isFeatureAvailable(String feature) {
        if (!initialized) {
            return false;
        }
        
        switch (feature.toLowerCase()) {
            case "hologram":
            case "armorstand":
                return true;
                
            case "actionbar":
                return VersionUtils.isVersionAtLeast(1, 8);
                
            case "title":
                return VersionUtils.isVersionAtLeast(1, 8);
                
            case "particle":
                return VersionUtils.isVersionAtLeast(1, 9);
                
            case "sound":
                return true;
                
            case "nbt":
                return true;
                
            case "blockdata":
                return VersionUtils.isVersionAtLeast(1, 13);
                
            case "persistentdata":
                return VersionUtils.isVersionAtLeast(1, 14);
                
            default:
                return false;
        }
    }
    
    /**
     * 获取可用功能列表
     */
    public String[] getAvailableFeatures() {
        if (!initialized) {
            return new String[0];
        }
        
        java.util.List<String> features = new java.util.ArrayList<>();
        
        if (isFeatureAvailable("hologram")) features.add("全息图");
        if (isFeatureAvailable("actionbar")) features.add("动作栏");
        if (isFeatureAvailable("title")) features.add("标题");
        if (isFeatureAvailable("particle")) features.add("粒子效果");
        if (isFeatureAvailable("sound")) features.add("音效");
        if (isFeatureAvailable("nbt")) features.add("NBT标签");
        if (isFeatureAvailable("blockdata")) features.add("方块数据");
        if (isFeatureAvailable("persistentdata")) features.add("持久化数据");
        
        return features.toArray(new String[0]);
    }

    /**
     * 通过字符串创建物品（支持模组物品）
     */
    public org.bukkit.inventory.ItemStack createItemFromString(String itemString, int amount) {
        if (!initialized || adapter == null) {
            return null;
        }

        try {
            return adapter.createItemFromString(itemString, amount);
        } catch (Exception e) {
            plugin.getLogger().warning("通过NMS创建物品失败: " + itemString + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * 检查物品是否为模组物品
     */
    public boolean isModItem(org.bukkit.inventory.ItemStack item) {
        if (!initialized || adapter == null || item == null) {
            return false;
        }

        try {
            return adapter.isModItem(item);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取模组物品的ID
     */
    public String getModItemId(org.bukkit.inventory.ItemStack item) {
        if (!initialized || adapter == null || item == null) {
            return null;
        }

        try {
            return adapter.getModItemId(item);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 通过名称获取材料（支持模组材料）
     */
    public org.bukkit.Material getMaterialByName(String materialName) {
        if (!initialized || adapter == null || materialName == null) {
            return null;
        }

        try {
            return adapter.getMaterialByName(materialName);
        } catch (Exception e) {
            plugin.getLogger().warning("通过NMS获取材料失败: " + materialName + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * 增强物品NBT数据读取（用于模组物品）
     */
    public org.bukkit.inventory.ItemStack enhanceItemWithNBT(org.bukkit.inventory.ItemStack item) {
        if (item == null) {
            return null;
        }

        try {
            // 尝试使用反射获取更完整的NBT数据
            // 这个方法会尝试从底层NMS获取完整的物品数据

            // 方法1: 尝试通过序列化/反序列化来刷新物品数据
            String serialized = com.hang.plugin.utils.ItemSerializer.serializeItemStack(item);
            if (serialized != null) {
                org.bukkit.inventory.ItemStack enhanced = com.hang.plugin.utils.ItemSerializer.deserializeItemStack(serialized);
                if (enhanced != null && enhanced.hasItemMeta()) {
                    return enhanced;
                }
            }

            // 方法2: 尝试克隆物品来获取完整数据
            org.bukkit.inventory.ItemStack cloned = item.clone();
            if (cloned != null && cloned.hasItemMeta()) {
                return cloned;
            }

            // 如果都失败，返回原始物品
            return item;

        } catch (Exception e) {
            plugin.getLogger().warning("增强物品NBT失败: " + e.getMessage());
            return item;
        }
    }

    /**
     * 获取物品的NBT字符串（用于调试）
     */
    public String getItemNBTString(org.bukkit.inventory.ItemStack item) {
        if (item == null) {
            return null;
        }

        try {
            // 尝试通过序列化获取NBT信息
            String serialized = com.hang.plugin.utils.ItemSerializer.serializeItemStack(item);
            if (serialized != null) {
                // 解码Base64来查看内容
                try {
                    byte[] bytes = java.util.Base64.getDecoder().decode(serialized);
                    return new String(bytes, "UTF-8");
                } catch (Exception e) {
                    // 如果Base64解码失败，返回原始序列化字符串的一部分
                    return serialized.length() > 500 ? serialized.substring(0, 500) + "..." : serialized;
                }
            }

            return null;

        } catch (Exception e) {
            plugin.getLogger().warning("获取物品NBT字符串失败: " + e.getMessage());
            return null;
        }
    }

}
