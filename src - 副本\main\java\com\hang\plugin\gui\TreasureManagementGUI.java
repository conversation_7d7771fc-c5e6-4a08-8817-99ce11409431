package com.hang.plugin.gui;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.manager.TreasureItemManager;
import com.hang.plugin.manager.ModItemManager;
import com.hang.plugin.utils.VersionUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 战利品管理GUI
 */
public class TreasureManagementGUI {

    private final HangPlugin plugin;
    private final Player player;
    private final Inventory inventory;
    private int currentPage = 0;
    private final int itemsPerPage = 45; // 9*5行，留出底部一行做按钮
    private final String chestType; // 摸金箱种类

    public TreasureManagementGUI(HangPlugin plugin, Player player) {
        this(plugin, player, null);
    }

    public TreasureManagementGUI(HangPlugin plugin, Player player, String chestType) {
        this.plugin = plugin;
        this.player = player;
        this.chestType = chestType;

        // 根据摸金箱种类设置标题
        String title;
        if (chestType != null) {
            com.hang.plugin.manager.ChestTypeManager.ChestType type = plugin.getChestTypeManager().getChestType(chestType);
            if (type != null) {
                title = plugin.getChestTypeManager().getConfig()
                    .getString("gui_settings.management_title_format", "§6{type_name} §7- 战利品管理")
                    .replace("{type_name}", type.getDisplayName());
            } else {
                title = "§6战利品管理 - " + chestType;
            }
        } else {
            title = "§6战利品管理";
        }

        this.inventory = Bukkit.createInventory(null, 54, title);
        updateDisplay();
    }

    /**
     * 获取所有物品（包括普通物品和模组物品）
     * 如果指定了摸金箱种类，则只返回该种类的物品
     */
    private List<Object> getAllItems() {
        List<Object> allItems = new ArrayList<>();

        if (chestType != null) {
            // 根据摸金箱种类获取对应的物品
            allItems.addAll(getItemsByChestType(chestType));
        } else {
            // 添加所有普通物品
            allItems.addAll(plugin.getTreasureItemManager().getAllItems());

            // 添加模组物品
            if (plugin.getTreasureItemManager().getModItemManager() != null) {
                allItems.addAll(plugin.getTreasureItemManager().getModItemManager().getAllModItems());
            }
        }

        return allItems;
    }

    /**
     * 根据摸金箱种类获取对应的物品列表（新版本：使用配置中的摸金箱类型）
     */
    private List<Object> getItemsByChestType(String chestType) {
        List<Object> items = new ArrayList<>();
        List<TreasureItemManager.TreasureItem> allTreasureItems = plugin.getTreasureItemManager().getAllItems();

        // 根据物品配置中的摸金箱类型过滤物品
        for (TreasureItemManager.TreasureItem item : allTreasureItems) {
            if (item.getChestTypes().contains(chestType)) {
                items.add(item);
            }
        }

        // ModItemManager已禁用，所有模组物品都应该转换为序列化物品
        // 不再从ModItemManager加载物品

        return items;
    }

    /**
     * 判断是否为武器类物品
     */
    private boolean isWeaponItem(TreasureItemManager.TreasureItem item) {
        String materialName = item.getMaterial().name().toLowerCase();
        return materialName.contains("sword") || materialName.contains("axe") ||
               materialName.contains("bow") || materialName.contains("crossbow") ||
               materialName.contains("trident") || materialName.contains("pickaxe") ||
               materialName.contains("shovel") || materialName.equals("diamond_sword");
    }

    /**
     * 判断是否为弹药类物品
     */
    private boolean isAmmoItem(TreasureItemManager.TreasureItem item) {
        String materialName = item.getMaterial().name().toLowerCase();
        return materialName.contains("arrow") || materialName.contains("firework") ||
               materialName.equals("tnt") || materialName.equals("gunpowder") ||
               materialName.equals("blaze_rod") || materialName.equals("blaze_powder");
    }

    /**
     * 判断是否为医疗类物品
     */
    private boolean isMedicalItem(TreasureItemManager.TreasureItem item) {
        String materialName = item.getMaterial().name().toLowerCase();
        return materialName.contains("potion") || materialName.contains("apple") ||
               materialName.contains("bread") || materialName.contains("stew") ||
               materialName.equals("experience_bottle") || materialName.contains("milk");
    }

    /**
     * 判断是否为补给类物品
     */
    private boolean isSupplyItem(TreasureItemManager.TreasureItem item) {
        String materialName = item.getMaterial().name().toLowerCase();
        return materialName.contains("food") || materialName.contains("bread") ||
               materialName.equals("coal") || materialName.equals("iron_ingot") ||
               materialName.equals("redstone") || materialName.contains("log") ||
               materialName.contains("planks");
    }

    /**
     * 判断是否为装备类物品
     */
    private boolean isEquipmentItem(TreasureItemManager.TreasureItem item) {
        String materialName = item.getMaterial().name().toLowerCase();
        return materialName.contains("diamond") || materialName.contains("emerald") ||
               materialName.contains("gold") || materialName.equals("ender_pearl") ||
               materialName.contains("enchanted") || item.getChance() <= 5.0; // 稀有物品
    }
    
    /**
     * 更新显示
     */
    private void updateDisplay() {
        inventory.clear();

        List<Object> allItems = getAllItems();
        int startIndex = currentPage * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, allItems.size());

        // 显示战利品物品
        for (int i = startIndex; i < endIndex; i++) {
            Object item = allItems.get(i);
            ItemStack displayItem = createManagementDisplayItem(item);

            // 检查displayItem是否为null（可能是AIR材质）
            if (displayItem == null) {
                // 为AIR材质创建一个显示用的物品
                displayItem = new ItemStack(Material.BARRIER);
                ItemMeta meta = displayItem.getItemMeta();
                String itemId = getItemId(item);
                meta.setDisplayName("§c" + itemId + " (空物品)");
                List<String> lore = new ArrayList<>();
                lore.add("§7材质: §fAIR");
                lore.add("§7这是一个空物品");
                addManagementLore(lore, item);
                meta.setLore(lore);
                displayItem.setItemMeta(meta);
            } else {
                // 添加管理信息到Lore
                ItemMeta meta = displayItem.getItemMeta();
                if (meta != null) {
                    List<String> lore = meta.getLore();
                    if (lore == null) {
                        lore = new ArrayList<>();
                    } else {
                        lore = new ArrayList<>(lore); // 创建可修改的副本
                    }

                    addManagementLore(lore, item);
                    meta.setLore(lore);
                    displayItem.setItemMeta(meta);
                }
            }

            inventory.setItem(i - startIndex, displayItem);
        }
        
        // 底部控制按钮
        setupControlButtons();
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 上一页按钮
        if (currentPage > 0) {
            ItemStack prevButton = new ItemStack(Material.ARROW);
            ItemMeta meta = prevButton.getItemMeta();
            meta.setDisplayName("§a上一页");
            meta.setLore(Arrays.asList("§7点击查看上一页"));
            prevButton.setItemMeta(meta);
            inventory.setItem(45, prevButton);
        }
        
        // 下一页按钮（第53格）
        List<Object> allItems = getAllItems();
        if ((currentPage + 1) * itemsPerPage < allItems.size()) {
            ItemStack nextButton = new ItemStack(Material.ARROW);
            ItemMeta meta = nextButton.getItemMeta();
            meta.setDisplayName("§a下一页");
            meta.setLore(Arrays.asList("§7点击查看下一页"));
            nextButton.setItemMeta(meta);
            inventory.setItem(53, nextButton);
        }
        
        // 添加新物品按钮
        ItemStack addButton = new ItemStack(Material.EMERALD);
        ItemMeta addMeta = addButton.getItemMeta();
        addMeta.setDisplayName("§a添加新物品");
        addMeta.setLore(Arrays.asList(
            "§7点击添加新的战利品物品",
            "§7将会复制你手中的物品"
        ));
        addButton.setItemMeta(addMeta);
        inventory.setItem(49, addButton);
        
        // 重载配置按钮
        ItemStack reloadButton = new ItemStack(Material.BOOK);
        ItemMeta reloadMeta = reloadButton.getItemMeta();
        reloadMeta.setDisplayName("§e重载配置");
        reloadMeta.setLore(Arrays.asList("§7重新加载战利品配置文件"));
        reloadButton.setItemMeta(reloadMeta);
        inventory.setItem(47, reloadButton);
        
        // 批量添加背包物品按钮（第50格）
        ItemStack batchAddButton = new ItemStack(Material.HOPPER);
        ItemMeta batchAddMeta = batchAddButton.getItemMeta();
        batchAddMeta.setDisplayName("§6批量添加背包物品");
        List<String> batchAddLore = new ArrayList<>();
        batchAddLore.add("§7一键将背包中的所有物品添加到战利品");
        batchAddLore.add("§7会自动跳过空气和已存在的物品");
        batchAddLore.add("§7使用默认概率和搜索速度");
        batchAddLore.add("");
        batchAddLore.add("§e左键: §f添加背包中的所有物品");
        batchAddLore.add("§c注意: §7此操作会添加大量物品");
        batchAddMeta.setLore(batchAddLore);
        batchAddButton.setItemMeta(batchAddMeta);
        inventory.setItem(50, batchAddButton);

        // 保存配置按钮
        ItemStack saveButton = new ItemStack(VersionUtils.getCompatibleMaterial("BOOK_AND_QUILL"));
        ItemMeta saveMeta = saveButton.getItemMeta();
        saveMeta.setDisplayName("§a保存配置");
        saveMeta.setLore(Arrays.asList("§7保存当前配置到文件"));
        saveButton.setItemMeta(saveMeta);
        inventory.setItem(51, saveButton);

        // 清理无效物品按钮
        ItemStack cleanButton = new ItemStack(Material.BARRIER);
        ItemMeta cleanMeta = cleanButton.getItemMeta();
        cleanMeta.setDisplayName("§c清理无效物品");
        cleanMeta.setLore(Arrays.asList("§7删除所有无效的示例模组物品", "§c警告：此操作不可撤销"));
        cleanButton.setItemMeta(cleanMeta);
        inventory.setItem(46, cleanButton);
        
        // 页面信息
        ItemStack pageInfo = new ItemStack(Material.PAPER);
        ItemMeta pageInfoMeta = pageInfo.getItemMeta();
        int totalPages = (int) Math.ceil((double) allItems.size() / itemsPerPage);
        pageInfoMeta.setDisplayName("§e页面信息");
        pageInfoMeta.setLore(Arrays.asList(
            "§7当前页: §f" + (currentPage + 1),
            "§7总页数: §f" + Math.max(1, totalPages),
            "§7总物品: §f" + allItems.size()
        ));
        pageInfo.setItemMeta(pageInfoMeta);
        inventory.setItem(48, pageInfo);

        // 返回摸金箱种类选择按钮（第52格）- 只在有指定摸金箱种类时显示
        if (chestType != null) {
            ItemStack backButton = new ItemStack(VersionUtils.getCompatibleMaterial("CHEST"));
            ItemMeta backMeta = backButton.getItemMeta();
            backMeta.setDisplayName("§6返回摸金箱种类选择");
            backMeta.setLore(Arrays.asList(
                "§7点击返回到摸金箱种类选择界面",
                "§7方便切换到其他种类进行管理"
            ));
            backButton.setItemMeta(backMeta);
            inventory.setItem(52, backButton);
        }
    }
    
    /**
     * 处理点击事件
     */
    public void handleClick(int slot, boolean isRightClick, boolean isShiftClick) {
        // 添加调试信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("GUI点击事件 - 槽位: " + slot + ", 右键: " + isRightClick + ", Shift: " + isShiftClick);
        }

        // 控制按钮处理
        if (slot >= 45) {
            handleControlButtonClick(slot);
            return;
        }

        // 物品处理
        List<Object> allItems = getAllItems();
        int itemIndex = currentPage * itemsPerPage + slot;

        // 检查索引是否有效
        if (itemIndex < 0 || itemIndex >= allItems.size()) {
            return; // 超出范围或无效索引
        }

        Object item = allItems.get(itemIndex);
        String itemId = getItemId(item);

        if (isRightClick) {
            // 删除物品
            deleteItem(item);
        } else if (isShiftClick) {
            // 复制物品
            copyItem(item);
        } else {
            // 编辑物品
            editItem(item);
        }
    }
    
    /**
     * 处理控制按钮点击
     */
    private void handleControlButtonClick(int slot) {
        switch (slot) {
            case 45: // 上一页
                if (currentPage > 0) {
                    currentPage--;
                    updateDisplay();
                }
                break;
            case 53: // 下一页
                List<Object> allItems = getAllItems();
                if ((currentPage + 1) * itemsPerPage < allItems.size()) {
                    currentPage++;
                    updateDisplay();
                }
                break;
            case 49: // 添加新物品
                addNewItem();
                break;
            case 47: // 重载配置
                reloadConfig();
                break;
            case 50: // 批量添加背包物品
                batchAddInventoryItems();
                break;
            case 51: // 保存配置
                saveConfig();
                break;
            case 52: // 返回摸金箱种类选择
                returnToChestTypeSelection();
                break;
        }
    }
    
    /**
     * 删除物品（支持模组物品）
     */
    private void deleteItem(Object item) {
        String itemId = getItemId(item);

        // 获取友好的物品名称用于显示
        String friendlyName = null;
        if (item instanceof TreasureItemManager.TreasureItem) {
            TreasureItemManager.TreasureItem treasureItem = (TreasureItemManager.TreasureItem) item;
            try {
                ItemStack itemStack = treasureItem.getItemStack();
                if (itemStack != null) {
                    friendlyName = getFriendlyItemName(itemStack);
                }
            } catch (Exception e) {
                // 如果获取失败，使用默认逻辑
            }

            // 如果还是没有友好名称，使用TreasureItemManager的方法
            if (friendlyName == null) {
                friendlyName = plugin.getTreasureItemManager().getItemName(item);
            }

            plugin.getTreasureItemManager().removeItem(itemId);
        } else if (item instanceof ModItemManager.ModItem) {
            // 检测到已废弃的ModItem，提示用户
            player.sendMessage("§c[错误] 检测到已废弃的ModItem类型物品: " + itemId);
            player.sendMessage("§e[提示] 此物品应该已经转换为序列化物品，请联系管理员");
            player.sendMessage("§7[建议] 使用清理按钮删除所有无效物品");
            return;
        } else {
            player.sendMessage("§c[错误] 未知的物品类型: " + item.getClass().getName());
            return;
        }

        // 使用友好名称显示删除消息
        if (friendlyName != null) {
            player.sendMessage("§a已删除物品: §f" + friendlyName);
        } else {
            player.sendMessage("§a已删除物品: §f" + itemId);
        }
        updateDisplay();
    }




    
    /**
     * 复制物品（支持模组物品）
     */
    private void copyItem(Object item) {
        String originalId = getItemId(item);
        String baseNewId = originalId + "_副本";

        // 确保ID唯一性
        String newId = generateUniqueId(baseNewId);

        if (item instanceof TreasureItemManager.TreasureItem) {
            TreasureItemManager.TreasureItem treasureItem = (TreasureItemManager.TreasureItem) item;

            TreasureItemManager.TreasureItem newItem;
            if (treasureItem.isModItem()) {
                // 序列化物品，使用ItemStack构造函数，保留摸金箱种类
                ItemStack originalItem = treasureItem.getItemStack();
                if (originalItem != null) {
                    newItem = new TreasureItemManager.TreasureItem(
                        newId, originalItem, treasureItem.getChance(),
                        treasureItem.getSearchSpeed(), treasureItem.getCommands(),
                        treasureItem.getChestTypes() // 保留原物品的摸金箱种类
                    );
                } else {
                    player.sendMessage("§c复制失败：无法获取原始物品数据");
                    return;
                }
            } else {
                // 普通物品，使用传统构造函数，保留摸金箱种类
                newItem = new TreasureItemManager.TreasureItem(
                    newId, treasureItem.getMaterial(), treasureItem.getAmount(), treasureItem.getData(),
                    treasureItem.getName(), treasureItem.getLore(), treasureItem.getChance(),
                    treasureItem.getSearchSpeed(), treasureItem.getCommands(),
                    treasureItem.getChestTypes() // 保留原物品的摸金箱种类
                );
            }

            plugin.getTreasureItemManager().addItem(newItem);
        } else if (item instanceof ModItemManager.ModItem) {
            // 模组物品暂时不支持复制，因为构造函数比较复杂
            player.sendMessage("§c模组物品暂不支持复制功能");
            return;
        }

        player.sendMessage("§a已复制物品: " + newId);
        updateDisplay();
    }
    
    /**
     * 编辑物品（支持模组物品）
     */
    private void editItem(Object item) {
        if (item instanceof TreasureItemManager.TreasureItem) {
            TreasureItemManager.TreasureItem treasureItem = (TreasureItemManager.TreasureItem) item;

            // 检查是否为序列化的模组物品
            if (treasureItem.isModItem()) {
                player.sendMessage("§e正在打开序列化模组物品编辑界面...");
            }

            // 打开编辑GUI（支持所有类型的TreasureItem）
            TreasureEditGUI editGUI = new TreasureEditGUI(plugin, player, treasureItem, this);
            editGUI.open();
        } else if (item instanceof ModItemManager.ModItem) {
            // 传统模组物品暂时不支持GUI编辑
            player.sendMessage("§c传统模组物品请直接编辑 mod_items.yml 配置文件");
            player.sendMessage("§e编辑完成后使用 /evac reload 重载配置");
            player.sendMessage("§7提示：建议将模组物品转换为序列化物品以支持GUI编辑");
        }
    }
    
    /**
     * 添加新物品
     */
    private void addNewItem() {
        // 修复：使用版本兼容的获取手持物品方法
        ItemStack handItem = getPlayerHandItem();
        if (handItem == null || handItem.getType() == Material.AIR) {
            player.sendMessage("§c请先手持一个物品！");
            return;
        }

        // 添加调试信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("尝试添加物品: " + handItem.getType().name() +
                " (数量: " + handItem.getAmount() + ", 有附魔: " + !handItem.getEnchantments().isEmpty() + ")");
        }

        // 如果当前管理界面有指定的摸金箱种类，直接添加到该种类
        if (chestType != null) {
            addItemToSpecificChestType(handItem, chestType);
        } else {
            // 如果是全局管理界面，才需要选择种类
            openChestTypeSelectionGUI(handItem);
        }
    }

    /**
     * 版本兼容的获取玩家手持物品方法
     */
    private ItemStack getPlayerHandItem() {
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("=== 获取手持物品调试 ===");
        }

        ItemStack handItem = null;

        try {
            // 尝试多种方法获取手持物品
            try {
                // 方法1: 尝试1.9+版本的getItemInMainHand
                java.lang.reflect.Method method = player.getInventory().getClass().getMethod("getItemInMainHand");
                handItem = (ItemStack) method.invoke(player.getInventory());
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("使用getItemInMainHand方法获取: " + (handItem != null ? handItem.getType().name() : "null"));
                }
            } catch (NoSuchMethodException e) {
                // 方法2: 降级到1.8版本的getItemInHand
                handItem = player.getInventory().getItemInHand();
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("使用getItemInHand方法获取: " + (handItem != null ? handItem.getType().name() : "null"));
                }
            }

            // 尝试使用NMS方法获取完整的NBT数据
            if (handItem != null) {
                try {
                    ItemStack nmsEnhancedItem = plugin.getNMSManager().enhanceItemWithNBT(handItem);
                    if (nmsEnhancedItem != null) {
                        handItem = nmsEnhancedItem;
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().info("完成：使用NMS增强物品NBT数据成功");
                        }
                    }
                } catch (Exception nmsEx) {
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().warning("NMS增强失败，使用原始物品: " + nmsEx.getMessage());
                    }
                }
            }

            // 详细检查获取到的物品
            if (handItem != null && plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("手持物品类型: " + handItem.getType().name());
                plugin.getLogger().info("手持物品数量: " + handItem.getAmount());

                if (handItem.hasItemMeta()) {
                    org.bukkit.inventory.meta.ItemMeta meta = handItem.getItemMeta();
                    plugin.getLogger().info("手持物品有ItemMeta: true");

                    if (meta.hasDisplayName()) {
                        plugin.getLogger().info("手持物品名称: " + meta.getDisplayName());
                    } else {
                        plugin.getLogger().info("手持物品没有自定义名称");
                    }

                    if (meta.hasLore()) {
                        plugin.getLogger().info("手持物品Lore行数: " + meta.getLore().size());
                        for (int i = 0; i < meta.getLore().size(); i++) {
                            plugin.getLogger().info("  手持Lore[" + i + "]: " + meta.getLore().get(i));
                        }
                    } else {
                        plugin.getLogger().info("手持物品没有Lore");

                        // 尝试直接从NBT读取Lore并创建增强物品
                        String nbtString = null;
                        try {
                            nbtString = plugin.getNMSManager().getItemNBTString(handItem);
                            if (nbtString != null) {
                                plugin.getLogger().info("NBT数据长度: " + nbtString.length());

                                // 检查是否包含Lore相关的关键词
                                if (nbtString.contains("Lore") || nbtString.contains("lore") ||
                                    nbtString.contains("护盾") || nbtString.contains("护甲") ||
                                    nbtString.contains("display") || nbtString.contains("Display")) {
                                    plugin.getLogger().info("警告：NBT中发现显示相关数据，但Bukkit API无法读取");

                                    // 显示更多NBT内容用于分析
                                    String[] keywords = {"Lore", "lore", "Display", "display", "护盾", "护甲", "Name", "name"};
                                    for (String keyword : keywords) {
                                        int index = nbtString.indexOf(keyword);
                                        if (index >= 0) {
                                            int start = Math.max(0, index - 100);
                                            int end = Math.min(nbtString.length(), index + 300);
                                            plugin.getLogger().info("发现关键词 '" + keyword + "' 的NBT片段:");
                                            plugin.getLogger().info(nbtString.substring(start, end));
                                        }
                                    }
                                }
                            }
                        } catch (Exception nbtEx) {
                            // NBT读取失败，继续使用原始物品
                        }

                        // 重要：不要修改原始物品，直接使用序列化保存
                        // 模组物品的显示信息应该保持在NBT中，不要转换为Lore
                    }
                }
            }

            return handItem;

        } catch (Exception e) {
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().warning("获取手持物品失败: " + e.getMessage());
                e.printStackTrace();
            }

            // 最后的降级方案
            try {
                handItem = player.getInventory().getItemInHand();
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("降级方案获取: " + (handItem != null ? handItem.getType().name() : "null"));
                }
                return handItem;
            } catch (Exception ex) {
                plugin.getLogger().warning("无法获取玩家手持物品: " + ex.getMessage());
                return null;
            }
        }
    }

    /**
     * 生成基于物品名称的友好ID
     */
    private String generateFriendlyItemId(ItemStack itemStack) {
        if (itemStack == null) {
            return "unknown_item_" + System.currentTimeMillis();
        }

        String baseName = null;

        // 优先使用物品的显示名称
        if (itemStack.hasItemMeta() && itemStack.getItemMeta().hasDisplayName()) {
            baseName = itemStack.getItemMeta().getDisplayName();
        } else {
            // 尝试使用NMS获取物品的本地化名称
            try {
                if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                    String nmsName = plugin.getNMSManager().getAdapter().getItemDisplayName(itemStack);
                    if (nmsName != null && !nmsName.equals(itemStack.getType().name())) {
                        baseName = nmsName;
                    }
                }
            } catch (Exception e) {
                // NMS获取失败，继续使用其他方法
            }

            // 如果还是没有名称，尝试从Lore中提取
            if (baseName == null && itemStack.hasItemMeta() && itemStack.getItemMeta().hasLore()) {
                List<String> lore = itemStack.getItemMeta().getLore();
                for (int i = 0; i < Math.min(lore.size(), 3); i++) {
                    String line = lore.get(i);
                    if (line != null) {
                        String cleanLine = line.replaceAll("§[0-9a-fk-or]", "").trim();
                        // 检查是否像物品名称（包含中文字符，不包含数值、冒号等）
                        if (cleanLine.length() > 1 && cleanLine.length() < 20 &&
                            !cleanLine.contains(":") && !cleanLine.contains("[") &&
                            !cleanLine.contains("(") && !cleanLine.contains("/") &&
                            !cleanLine.matches(".*\\d+.*") && // 不包含数字
                            cleanLine.matches(".*[\\u4e00-\\u9fa5].*")) { // 包含中文字符
                            baseName = cleanLine;
                            break;
                        }
                    }
                }
            }

            // 最后降级到材料名称的友好转换
            if (baseName == null) {
                String materialName = itemStack.getType().name();
                if (materialName.startsWith("TACZ_")) {
                    // 特殊处理TACZ物品名称
                    String taczName = materialName.replace("TACZ_", "").replace("_", " ");
                    baseName = "TACZ " + formatTaczName(taczName);
                } else {
                    // 转换常见的材料名称为中文
                    baseName = convertMaterialNameToChinese(materialName);
                }
            }
        }

        // 清理名称，移除颜色代码和特殊字符
        if (baseName != null) {
            baseName = baseName.replaceAll("§[0-9a-fk-or]", "").trim();
            // 移除可能导致配置文件问题的字符
            baseName = baseName.replaceAll("[\\[\\]{}()\"'\\\\/:*?<>|]", "");
            // 限制长度（增加到25字符以支持更长的中文名称）
            if (baseName.length() > 25) {
                baseName = baseName.substring(0, 25);
            }
        }

        // 如果名称为空或太短，使用默认格式
        if (baseName == null || baseName.length() < 2) {
            baseName = itemStack.getType().name().toLowerCase();
        }

        // 确保ID唯一性
        return generateUniqueId(baseName);
    }

    /**
     * 获取友好的物品名称用于显示
     */
    private String getFriendlyItemName(ItemStack itemStack) {
        if (itemStack == null) {
            return "未知物品";
        }

        // 优先使用物品的显示名称
        if (itemStack.hasItemMeta() && itemStack.getItemMeta().hasDisplayName()) {
            String displayName = itemStack.getItemMeta().getDisplayName();
            // 移除颜色代码
            return displayName.replaceAll("§[0-9a-fk-or]", "");
        }

        // 尝试使用NMS获取物品的本地化名称
        try {
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                String nmsName = plugin.getNMSManager().getAdapter().getItemDisplayName(itemStack);
                if (nmsName != null && !nmsName.equals(itemStack.getType().name())) {
                    return nmsName.replaceAll("§[0-9a-fk-or]", "");
                }
            }
        } catch (Exception e) {
            // NMS获取失败，继续使用其他方法
        }

        // 尝试从Lore中提取物品名称
        if (itemStack.hasItemMeta() && itemStack.getItemMeta().hasLore()) {
            List<String> lore = itemStack.getItemMeta().getLore();
            for (int i = 0; i < Math.min(lore.size(), 3); i++) {
                String line = lore.get(i);
                if (line != null) {
                    String cleanLine = line.replaceAll("§[0-9a-fk-or]", "").trim();
                    // 检查是否像物品名称
                    if (cleanLine.length() > 1 && cleanLine.length() < 30 &&
                        !cleanLine.contains(":") && !cleanLine.contains("[") &&
                        !cleanLine.contains("(") && !cleanLine.contains("/") &&
                        !cleanLine.matches(".*\\d+.*") && // 不包含数字
                        cleanLine.matches(".*[\\u4e00-\\u9fa5].*")) { // 包含中文字符
                        return cleanLine;
                    }
                }
            }
        }

        // 最后使用材料名称的中文转换
        return convertMaterialNameToChinese(itemStack.getType().name());
    }

    /**
     * 生成唯一ID
     */
    private String generateUniqueId(String baseName) {
        String baseId = baseName;
        String finalId = baseId;
        int counter = 1;

        // 检查是否已存在相同ID
        List<TreasureItemManager.TreasureItem> allItems = plugin.getTreasureItemManager().getAllItems();
        while (isIdExists(finalId, allItems)) {
            finalId = baseId + "_" + counter;
            counter++;
        }

        return finalId;
    }

    /**
     * 检查ID是否已存在
     */
    private boolean isIdExists(String id, List<TreasureItemManager.TreasureItem> allItems) {
        for (TreasureItemManager.TreasureItem item : allItems) {
            if (item.getId().equals(id)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 格式化TACZ物品名称
     */
    private String formatTaczName(String taczName) {
        // 将常见的TACZ物品名称转换为更友好的格式
        String formatted = taczName.toLowerCase();

        // 替换常见的词汇
        formatted = formatted.replace("modern", "现代");
        formatted = formatted.replace("kinetic", "动能");
        formatted = formatted.replace("gun", "枪");
        formatted = formatted.replace("rifle", "步枪");
        formatted = formatted.replace("pistol", "手枪");
        formatted = formatted.replace("sniper", "狙击");
        formatted = formatted.replace("assault", "突击");
        formatted = formatted.replace("submachine", "冲锋");
        formatted = formatted.replace("machine", "机");
        formatted = formatted.replace("shotgun", "霰弹枪");
        formatted = formatted.replace("grenade", "手榴弹");
        formatted = formatted.replace("launcher", "发射器");
        formatted = formatted.replace("scope", "瞄准镜");
        formatted = formatted.replace("silencer", "消音器");
        formatted = formatted.replace("magazine", "弹匣");
        formatted = formatted.replace("ammo", "弹药");
        formatted = formatted.replace("bullet", "子弹");

        // 首字母大写
        String[] words = formatted.split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(word.substring(0, 1).toUpperCase()).append(word.substring(1));
            }
        }

        return result.toString();
    }

    /**
     * 将材料名称转换为中文
     */
    private String convertMaterialNameToChinese(String materialName) {
        // 常见材料的中文映射
        switch (materialName) {
            case "DIAMOND_SWORD": return "钻石剑";
            case "DIAMOND_PICKAXE": return "钻石镐";
            case "DIAMOND_AXE": return "钻石斧";
            case "DIAMOND_SHOVEL": return "钻石锹";
            case "DIAMOND_HOE": return "钻石锄";
            case "DIAMOND_HELMET": return "钻石头盔";
            case "DIAMOND_CHESTPLATE": return "钻石胸甲";
            case "DIAMOND_LEGGINGS": return "钻石护腿";
            case "DIAMOND_BOOTS": return "钻石靴子";
            case "IRON_SWORD": return "铁剑";
            case "IRON_PICKAXE": return "铁镐";
            case "IRON_AXE": return "铁斧";
            case "IRON_SHOVEL": return "铁锹";
            case "IRON_HOE": return "铁锄";
            case "IRON_HELMET": return "铁头盔";
            case "IRON_CHESTPLATE": return "铁胸甲";
            case "IRON_LEGGINGS": return "铁护腿";
            case "IRON_BOOTS": return "铁靴子";
            case "GOLDEN_SWORD": return "金剑";
            case "GOLDEN_PICKAXE": return "金镐";
            case "GOLDEN_AXE": return "金斧";
            case "GOLDEN_SHOVEL": return "金锹";
            case "GOLDEN_HOE": return "金锄";
            case "GOLDEN_HELMET": return "金头盔";
            case "GOLDEN_CHESTPLATE": return "金胸甲";
            case "GOLDEN_LEGGINGS": return "金护腿";
            case "GOLDEN_BOOTS": return "金靴子";
            case "NETHERITE_SWORD": return "下界合金剑";
            case "NETHERITE_PICKAXE": return "下界合金镐";
            case "NETHERITE_AXE": return "下界合金斧";
            case "NETHERITE_SHOVEL": return "下界合金锹";
            case "NETHERITE_HOE": return "下界合金锄";
            case "NETHERITE_HELMET": return "下界合金头盔";
            case "NETHERITE_CHESTPLATE": return "下界合金胸甲";
            case "NETHERITE_LEGGINGS": return "下界合金护腿";
            case "NETHERITE_BOOTS": return "下界合金靴子";
            case "BOW": return "弓";
            case "CROSSBOW": return "弩";
            case "SHIELD": return "盾牌";
            case "APPLE": return "苹果";
            case "GOLDEN_APPLE": return "金苹果";
            case "BREAD": return "面包";
            case "COOKED_BEEF": return "熟牛肉";
            case "COOKED_PORKCHOP": return "熟猪肉";
            case "ENDER_PEARL": return "末影珍珠";
            case "DIAMOND": return "钻石";
            case "EMERALD": return "绿宝石";
            case "GOLD_INGOT": return "金锭";
            case "IRON_INGOT": return "铁锭";
            case "COAL": return "煤炭";
            case "LAPIS_LAZULI": return "青金石";
            case "REDSTONE": return "红石";
            case "BLAZE_ROD": return "烈焰棒";
            case "EXP_BOTTLE": return "经验瓶";
            case "GLASS_BOTTLE": return "玻璃瓶";
            case "POTION": return "药水";
            case "SPLASH_POTION": return "喷溅药水";
            case "LINGERING_POTION": return "滞留药水";
            case "ENCHANTED_BOOK": return "附魔书";
            case "BOOK": return "书";
            case "PAPER": return "纸";
            case "LEATHER": return "皮革";
            case "STRING": return "线";
            case "FEATHER": return "羽毛";
            case "GUNPOWDER": return "火药";
            case "FLINT": return "燧石";
            case "WHEAT": return "小麦";
            case "CARROT": return "胡萝卜";
            case "POTATO": return "马铃薯";
            case "BEETROOT": return "甜菜根";
            case "SUGAR": return "糖";
            case "EGG": return "鸡蛋";
            case "MILK_BUCKET": return "牛奶桶";
            case "WATER_BUCKET": return "水桶";
            case "LAVA_BUCKET": return "岩浆桶";
            case "BUCKET": return "桶";
            case "SADDLE": return "鞍";
            case "NAME_TAG": return "命名牌";
            case "LEAD": return "拴绳";
            case "COMPASS": return "指南针";
            case "CLOCK": return "时钟";
            case "MAP": return "地图";
            case "SHEARS": return "剪刀";
            case "FISHING_ROD": return "钓鱼竿";
            case "FLINT_AND_STEEL": return "打火石";
            case "TORCH": return "火把";
            case "LADDER": return "梯子";
            case "CHEST": return "箱子";
            case "CRAFTING_TABLE": return "工作台";
            case "FURNACE": return "熔炉";
            case "ANVIL": return "铁砧";
            case "ENCHANTING_TABLE": return "附魔台";
            case "ENDER_CHEST": return "末影箱";
            case "BEACON": return "信标";
            case "TOTEM_OF_UNDYING": return "不死图腾";
            case "ELYTRA": return "鞘翅";
            case "TRIDENT": return "三叉戟";
            case "NAUTILUS_SHELL": return "鹦鹉螺壳";
            case "HEART_OF_THE_SEA": return "海洋之心";
            case "CONDUIT": return "潮涌核心";
            case "PRISMARINE_SHARD": return "海晶碎片";
            case "PRISMARINE_CRYSTALS": return "海晶砂粒";
            case "NETHER_STAR": return "下界之星";
            case "WITHER_SKELETON_SKULL": return "凋灵骷髅头颅";
            case "DRAGON_EGG": return "龙蛋";
            case "DRAGON_HEAD": return "龙首";
            case "END_CRYSTAL": return "末地水晶";
            case "CHORUS_FRUIT": return "紫颂果";
            case "POPPED_CHORUS_FRUIT": return "爆裂紫颂果";
            case "SHULKER_SHELL": return "潜影贝壳";
            case "PHANTOM_MEMBRANE": return "幻翼膜";
            // 添加更多常见物品...
            default:
                // 如果没有映射，返回格式化的英文名称
                return materialName.toLowerCase().replace("_", " ");
        }
    }

    /**
     * 将物品添加到指定的摸金箱种类
     */
    private void addItemToSpecificChestType(ItemStack itemToAdd, String targetChestType) {
        try {
            // 添加详细调试信息
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("开始添加物品到摸金箱种类: " + targetChestType);
                plugin.getLogger().info("物品信息: " + itemToAdd.getType().name() +
                    " x" + itemToAdd.getAmount() +
                    " (有附魔: " + !itemToAdd.getEnchantments().isEmpty() + ")");
                if (!itemToAdd.getEnchantments().isEmpty()) {
                    plugin.getLogger().info("附魔列表: " + itemToAdd.getEnchantments().toString());
                }
            }

            // 创建新的战利品物品，指定摸金箱种类
            String newId = generateFriendlyItemId(itemToAdd);
            TreasureItemManager.TreasureItem newItem = plugin.getTreasureItemManager()
                .createFromItemStackWithChestType(newId, itemToAdd, targetChestType);

            if (newItem == null) {
                player.sendMessage("§c创建战利品物品失败！");
                plugin.getLogger().warning("createFromItemStackWithChestType 返回 null");
                return;
            }

            // 添加调试信息：检查创建的物品类型
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("=== 模组物品检测详细过程 ===");

                // 检测原始物品
                plugin.getLogger().info("原始物品检测结果: " + com.hang.plugin.utils.ItemSerializer.hasModData(itemToAdd));

                // 重要：直接序列化原始物品，不做任何修改
                String serializedData = com.hang.plugin.utils.ItemSerializer.serializeItemStack(itemToAdd);
                plugin.getLogger().info("原始物品序列化长度: " + (serializedData != null ? serializedData.length() : "null"));

                // 验证序列化是否完整保存了模组数据
                if (serializedData != null) {
                    ItemStack testDeserialize = com.hang.plugin.utils.ItemSerializer.deserializeItemStack(serializedData);
                    if (testDeserialize != null) {
                        plugin.getLogger().info("完成：序列化验证成功，物品类型: " + testDeserialize.getType().name());

                        // 检查是否保持了模组特征
                        boolean stillModItem = com.hang.plugin.utils.ItemSerializer.hasModData(testDeserialize);
                        plugin.getLogger().info("反序列化后模组物品状态: " + stillModItem);

                        // 对于模组物品，不要尝试读取Lore，因为信息在NBT中
                        if (stillModItem) {
                            plugin.getLogger().info("警告：模组物品的显示信息存储在NBT中，不在Lore中");
                            plugin.getLogger().info("完成：将保持原始NBT结构，确保模组功能完整");
                        }
                    }
                }



                plugin.getLogger().info("创建的物品类型: " + (newItem.isModItem() ? "序列化物品" : "普通物品"));
                if (newItem.getSerializedData() != null) {
                    plugin.getLogger().info("序列化数据长度: " + newItem.getSerializedData().length());
                    plugin.getLogger().info("序列化数据前100字符: " +
                        newItem.getSerializedData().substring(0, Math.min(100, newItem.getSerializedData().length())));
                }

                // 测试反序列化
                ItemStack testItem = newItem.getItemStack();
                if (testItem != null) {
                    plugin.getLogger().info("反序列化测试成功: " + com.hang.plugin.utils.ItemSerializer.getItemDescription(testItem));
                    if (testItem.hasItemMeta() && testItem.getItemMeta().hasDisplayName()) {
                        plugin.getLogger().info("反序列化物品名称: " + testItem.getItemMeta().getDisplayName());
                    }
                    if (!testItem.getEnchantments().isEmpty()) {
                        plugin.getLogger().info("反序列化附魔数量: " + testItem.getEnchantments().size());
                    }
                    if (testItem.hasItemMeta() && testItem.getItemMeta().hasLore()) {
                        plugin.getLogger().info("反序列化Lore行数: " + testItem.getItemMeta().getLore().size());
                        // 显示所有Lore内容
                        for (int i = 0; i < testItem.getItemMeta().getLore().size(); i++) {
                            String loreLine = testItem.getItemMeta().getLore().get(i);
                            plugin.getLogger().info("  反序列化Lore[" + i + "]: " + loreLine);
                            if (loreLine.contains("护盾") || loreLine.contains("Shield")) {
                                plugin.getLogger().info("  完成：发现护盾信息: " + loreLine);
                            }
                        }
                    } else {
                        plugin.getLogger().warning("错误：反序列化后物品没有Lore！");
                    }

                    // 直接测试序列化数据反序列化
                    plugin.getLogger().info("=== 直接测试序列化数据 ===");
                    String itemSerializedData = newItem.getSerializedData();
                    if (itemSerializedData != null) {
                        ItemStack directTest = com.hang.plugin.utils.ItemSerializer.deserializeItemStack(itemSerializedData);
                        if (directTest != null) {
                            plugin.getLogger().info("直接反序列化成功: " + com.hang.plugin.utils.ItemSerializer.getItemDescription(directTest));
                            if (directTest.hasItemMeta() && directTest.getItemMeta().hasLore()) {
                                plugin.getLogger().info("直接反序列化Lore行数: " + directTest.getItemMeta().getLore().size());
                                for (String line : directTest.getItemMeta().getLore()) {
                                    plugin.getLogger().info("  直接Lore: " + line);
                                }
                            }
                        } else {
                            plugin.getLogger().warning("错误：直接反序列化失败！");
                        }
                    }
                } else {
                    plugin.getLogger().warning("反序列化测试失败！返回null");
                }
            }

            plugin.getTreasureItemManager().addItem(newItem);

            // 获取种类显示名称
            String displayName = targetChestType;
            com.hang.plugin.manager.ChestTypeManager.ChestType type = plugin.getChestTypeManager().getChestType(targetChestType);
            if (type != null) {
                displayName = type.getDisplayName();
            }

            // 获取友好的物品名称用于显示
            String friendlyItemName = getFriendlyItemName(itemToAdd);
            player.sendMessage("§a已添加新物品到 " + displayName + ": §f" + newId);
            player.sendMessage("§7物品: §f" + friendlyItemName);

            // 显示物品类型信息
            if (newItem.isModItem()) {
                player.sendMessage("§7类型: §d✨ 序列化物品 (完整保存NBT数据)");
            } else {
                player.sendMessage("§7类型: §f普通物品");
            }

            // 刷新显示
            updateDisplay();

        } catch (Exception e) {
            player.sendMessage("§c添加物品时发生错误: " + e.getMessage());
            plugin.getLogger().severe("添加物品到摸金箱失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 打开摸金箱种类选择GUI
     */
    private void openChestTypeSelectionGUI(ItemStack itemToAdd) {
        Inventory selectionGUI = Bukkit.createInventory(null, 27, "§6添加物品 - 选择摸金箱种类");

        // 从配置动态获取摸金箱种类
        java.util.Collection<com.hang.plugin.manager.ChestTypeManager.ChestType> availableTypes =
            plugin.getChestTypeManager().getAllChestTypes();

        if (availableTypes.isEmpty()) {
            // 如果配置中没有种类，使用默认种类
            String[] chestTypes = {"common", "weapon", "ammo", "medical", "supply", "equipment"};
            String[] typeNames = {"§f普通摸金箱", "§c武器箱", "§e弹药箱", "§a医疗箱", "§b补给箱", "§d装备箱"};
            Material[] typeMaterials = {Material.CHEST, Material.TRAPPED_CHEST, Material.DISPENSER,
                                       Material.HOPPER, Material.DROPPER, Material.ENDER_CHEST};

            for (int i = 0; i < chestTypes.length && i < 15; i++) {
                ItemStack typeItem = new ItemStack(typeMaterials[i % typeMaterials.length]);
                ItemMeta meta = typeItem.getItemMeta();
                meta.setDisplayName(typeNames[i]);

                List<String> lore = new ArrayList<>();
                lore.add("§7点击将物品添加到此种类摸金箱");
                lore.add("§7种类ID: §e" + chestTypes[i]);
                lore.add("");
                lore.add("§e左键: §f添加到此种类");
                meta.setLore(lore);

                typeItem.setItemMeta(meta);
                selectionGUI.setItem(i + 10, typeItem);
            }
        } else {
            // 使用配置中的摸金箱种类
            int slot = 10;
            for (com.hang.plugin.manager.ChestTypeManager.ChestType chestType : availableTypes) {
                if (slot >= 25) break; // 最多显示15个种类

                ItemStack typeItem = new ItemStack(chestType.getMaterial());
                ItemMeta meta = typeItem.getItemMeta();
                meta.setDisplayName(chestType.getDisplayName());

                List<String> lore = new ArrayList<>();
                lore.add("§7点击将物品添加到此种类摸金箱");
                lore.add("§7种类ID: §e" + chestType.getTypeId());
                if (chestType.getDescription() != null && !chestType.getDescription().isEmpty()) {
                    lore.add("§7描述: §f" + chestType.getDescription());
                }
                lore.add("");
                lore.add("§e左键: §f添加到此种类");
                meta.setLore(lore);

                typeItem.setItemMeta(meta);
                selectionGUI.setItem(slot, typeItem);
                slot++;
            }
        }

        // 添加取消按钮
        ItemStack cancelItem = new ItemStack(Material.BARRIER);
        ItemMeta cancelMeta = cancelItem.getItemMeta();
        cancelMeta.setDisplayName("§c取消");
        cancelMeta.setLore(Arrays.asList("§7返回战利品管理"));
        cancelItem.setItemMeta(cancelMeta);
        selectionGUI.setItem(22, cancelItem);

        // 存储待添加的物品信息
        player.setMetadata("pending_item", new org.bukkit.metadata.FixedMetadataValue(plugin, itemToAdd));

        player.openInventory(selectionGUI);
    }

    /**
     * 处理摸金箱种类选择
     */
    public void handleChestTypeSelection(InventoryClickEvent event) {
        if (!event.getView().getTitle().equals("§6添加物品 - 选择摸金箱种类")) {
            return;
        }

        event.setCancelled(true);

        if (event.getClickedInventory() == null || event.getCurrentItem() == null) {
            return;
        }

        ItemStack clicked = event.getCurrentItem();
        if (!clicked.hasItemMeta() || !clicked.getItemMeta().hasDisplayName()) {
            return;
        }

        String displayName = clicked.getItemMeta().getDisplayName();

        // 处理取消
        if (displayName.equals("§c取消")) {
            player.removeMetadata("pending_item", plugin);
            open(); // 返回主GUI
            return;
        }

        // 获取待添加的物品
        if (!player.hasMetadata("pending_item")) {
            player.sendMessage("§c错误：未找到待添加的物品");
            return;
        }

        ItemStack itemToAdd = (ItemStack) player.getMetadata("pending_item").get(0).value();
        player.removeMetadata("pending_item", plugin);

        // 确定选择的种类
        String selectedType = null;
        int slot = event.getSlot();

        // 从配置获取摸金箱种类
        java.util.Collection<com.hang.plugin.manager.ChestTypeManager.ChestType> availableTypes =
            plugin.getChestTypeManager().getAllChestTypes();

        if (availableTypes.isEmpty()) {
            // 使用默认种类
            String[] chestTypes = {"common", "weapon", "ammo", "medical", "supply", "equipment"};
            if (slot >= 10 && slot <= 15) {
                selectedType = chestTypes[slot - 10];
            }
        } else {
            // 从配置中的种类获取 - 修复槽位计算
            if (slot >= 10 && slot < 25) {
                // 计算在种类列表中的索引
                int index = slot - 10;
                java.util.List<com.hang.plugin.manager.ChestTypeManager.ChestType> typesList =
                    new java.util.ArrayList<>(availableTypes);

                if (index >= 0 && index < typesList.size()) {
                    selectedType = typesList.get(index).getTypeId();
                }
            }
        }

        if (selectedType != null) {
            // 创建新的战利品物品，指定摸金箱种类
            String newId = generateFriendlyItemId(itemToAdd);
            TreasureItemManager.TreasureItem newItem = plugin.getTreasureItemManager()
                .createFromItemStackWithChestType(newId, itemToAdd, selectedType);
            plugin.getTreasureItemManager().addItem(newItem);

            // 获取友好的物品名称用于显示
            String friendlyItemName = getFriendlyItemName(itemToAdd);
            player.sendMessage("§a已添加新物品到 " + displayName + ": §f" + newId);
            player.sendMessage("§7物品: §f" + friendlyItemName);
            open(); // 返回主GUI
        } else {
            player.sendMessage("§c无效的选择");
        }
    }
    
    /**
     * 重载配置
     */
    private void reloadConfig() {
        plugin.getTreasureItemManager().reloadConfig();
        player.sendMessage("§a配置已重载！");
        updateDisplay();
    }
    
    /**
     * 保存配置
     */
    private void saveConfig() {
        // 保存普通物品配置
        plugin.getTreasureItemManager().saveConfig();

        player.sendMessage("§a配置已保存！");
    }
    
    /**
     * 打开GUI
     */
    public void open() {
        // 注册GUI到监听器
        plugin.getPlayerListener().registerManagementGUI(player, this);
        player.openInventory(inventory);
    }
    
    /**
     * 获取库存对象
     */
    public Inventory getInventory() {
        return inventory;
    }

    /**
     * 获取玩家对象
     */
    public Player getPlayer() {
        return player;
    }
    
    /**
     * 刷新显示
     */
    public void refresh() {
        updateDisplay();
    }

    /**
     * 创建管理GUI专用的显示物品（支持序列化物品）
     */
    private ItemStack createManagementDisplayItem(Object item) {
        if (item instanceof TreasureItemManager.TreasureItem) {
            TreasureItemManager.TreasureItem treasureItem = (TreasureItemManager.TreasureItem) item;
            // 使用新的getItemStack方法，支持序列化物品
            return treasureItem.getItemStack();
        } else if (item instanceof ModItemManager.ModItem) {
            // 模组物品使用原有方法
            return plugin.getTreasureItemManager().createItemStack(item);
        }
        return null;
    }

    /**
     * 添加管理信息到Lore（支持模组物品）
     */
    private void addManagementLore(List<String> lore, Object item) {
        // 添加分隔线和管理信息
        lore.add("§7§m------------------------");

        if (item instanceof TreasureItemManager.TreasureItem) {
            TreasureItemManager.TreasureItem treasureItem = (TreasureItemManager.TreasureItem) item;

            // 显示物品类型
            if (treasureItem.isModItem()) {
                lore.add("§e类型: §d✨ 序列化物品 (模组物品)");
            } else {
                lore.add("§e类型: §f普通物品");
            }

            lore.add("§e概率: §f" + treasureItem.getChance() + "%");
            lore.add("§e搜索速度: §f" + treasureItem.getSearchSpeed() + "秒");

            if (treasureItem.getCommands() != null && !treasureItem.getCommands().isEmpty()) {
                lore.add("§e命令数: §f" + treasureItem.getCommands().size());
            }

            // 如果是序列化物品，显示额外信息
            if (treasureItem.isModItem()) {
                lore.add("§7完整保存所有NBT数据");
            }
        } else if (item instanceof ModItemManager.ModItem) {
            ModItemManager.ModItem modItem = (ModItemManager.ModItem) item;
            lore.add("§e类型: §6模组物品");
            lore.add("§e模组ID: §f" + modItem.getModId());
            lore.add("§e概率: §f" + (modItem.getProbability() * 100) + "%");
            lore.add("§e搜索速度: §f" + modItem.getSearchSpeed() + "秒");
            if (modItem.getCommands() != null && !modItem.getCommands().isEmpty()) {
                lore.add("§e命令数: §f" + modItem.getCommands().size());
            }
        }

        lore.add("§7§m------------------------");
        lore.add("§a左键: §f编辑物品");
        lore.add("§c右键: §f删除物品");
        lore.add("§eShift+左键: §f复制物品");
    }

    /**
     * 获取物品ID（支持模组物品）
     */
    private String getItemId(Object item) {
        if (item instanceof TreasureItemManager.TreasureItem) {
            return ((TreasureItemManager.TreasureItem) item).getId();
        } else if (item instanceof ModItemManager.ModItem) {
            return ((ModItemManager.ModItem) item).getId();
        }
        return "unknown";
    }

    /**
     * 返回到摸金箱种类选择GUI
     */
    private void returnToChestTypeSelection() {
        // 关闭当前GUI
        player.closeInventory();

        // 延迟1tick后打开摸金箱种类选择GUI，避免GUI切换冲突
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            ChestTypeSelectionGUI chestTypeGUI = new ChestTypeSelectionGUI(plugin, player);
            // 重要：注册GUI到PlayerListener中，这样点击事件才能正确处理
            plugin.getPlayerListener().registerChestTypeSelectionGUI(player, chestTypeGUI);
            player.openInventory(chestTypeGUI.getInventory());
        }, 1L);
    }

    /**
     * 从背包点击添加物品
     */
    public void addItemFromInventoryClick(ItemStack itemToAdd) {
        // 如果当前管理界面有指定的摸金箱种类，直接添加到该种类
        if (chestType != null) {
            addItemToSpecificChestType(itemToAdd, chestType);
        } else {
            // 如果是全局管理界面，需要选择种类
            openChestTypeSelectionForInventoryItem(itemToAdd);
        }
    }

    /**
     * 为背包点击的物品打开摸金箱种类选择GUI
     */
    private void openChestTypeSelectionForInventoryItem(ItemStack itemToAdd) {
        // 关闭当前GUI
        player.closeInventory();

        // 延迟打开种类选择GUI
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            Inventory selectionGUI = Bukkit.createInventory(null, 27, "§6选择摸金箱种类 - " + getItemDisplayName(itemToAdd));

            // 从配置动态获取摸金箱种类
            java.util.Collection<com.hang.plugin.manager.ChestTypeManager.ChestType> availableTypes =
                plugin.getChestTypeManager().getAllChestTypes();

            if (availableTypes.isEmpty()) {
                player.sendMessage("§c没有可用的摸金箱种类！");
                return;
            }

            int slot = 0;
            for (com.hang.plugin.manager.ChestTypeManager.ChestType type : availableTypes) {
                if (slot >= 27) break;

                ItemStack typeItem = new ItemStack(Material.CHEST);
                ItemMeta meta = typeItem.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(type.getDisplayName());
                    List<String> lore = new ArrayList<>();
                    lore.add("§7点击将物品添加到此种类");
                    lore.add("§7物品: §f" + getItemDisplayName(itemToAdd));
                    meta.setLore(lore);
                    typeItem.setItemMeta(meta);
                }
                selectionGUI.setItem(slot, typeItem);
                slot++;
            }

            player.openInventory(selectionGUI);

            // 注册临时的点击处理器
            registerTemporaryChestTypeSelection(player, selectionGUI, itemToAdd);

        }, 1L);
    }

    /**
     * 注册临时的摸金箱种类选择处理器
     */
    private void registerTemporaryChestTypeSelection(Player player, Inventory selectionGUI, ItemStack itemToAdd) {
        // 这里需要在PlayerListener中添加临时处理逻辑
        // 为了简化，我们直接在这里处理，或者可以扩展PlayerListener
        player.sendMessage("§e请点击要添加到的摸金箱种类");
    }

    /**
     * 获取物品显示名称
     */
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        return item.getType().name();
    }

    /**
     * 批量添加背包中的物品到战利品
     */
    private void batchAddInventoryItems() {
        // 检查权限
        if (!player.hasPermission("evacuation.admin")) {
            player.sendMessage("§c您没有权限批量添加物品！");
            return;
        }

        // 如果没有指定摸金箱种类，提示用户
        if (chestType == null) {
            player.sendMessage("§c请先选择一个摸金箱种类再进行批量添加！");
            return;
        }

        int addedCount = 0;
        int skippedCount = 0;
        List<String> addedItems = new ArrayList<>();

        // 遍历玩家背包中的所有物品
        for (int slot = 0; slot < player.getInventory().getSize(); slot++) {
            ItemStack item = player.getInventory().getItem(slot);

            // 跳过空物品
            if (item == null || item.getType() == Material.AIR) {
                continue;
            }

            try {
                // 创建物品副本
                ItemStack itemToAdd = item.clone();

                // 检查物品是否已存在
                if (isItemAlreadyExists(itemToAdd)) {
                    skippedCount++;
                    continue;
                }

                // 添加物品到指定的摸金箱种类
                addItemToSpecificChestType(itemToAdd, chestType);
                addedCount++;

                // 记录添加的物品名称（最多显示10个）
                if (addedItems.size() < 10) {
                    addedItems.add(getItemDisplayName(itemToAdd));
                }

            } catch (Exception e) {
                // 如果添加失败，跳过这个物品
                skippedCount++;
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().warning("批量添加物品时出错: " + e.getMessage());
                }
            }
        }

        // 刷新显示
        updateDisplay();

        // 给玩家反馈
        if (addedCount > 0) {
            player.sendMessage("§a批量添加完成！");
            player.sendMessage("§e成功添加: §f" + addedCount + " §e个物品");

            if (skippedCount > 0) {
                player.sendMessage("§7跳过: §f" + skippedCount + " §7个物品（空物品或已存在）");
            }

            // 显示部分添加的物品名称
            if (!addedItems.isEmpty()) {
                StringBuilder itemList = new StringBuilder("§7添加的物品: §f");
                for (int i = 0; i < Math.min(addedItems.size(), 5); i++) {
                    if (i > 0) itemList.append("§7, §f");
                    itemList.append(addedItems.get(i));
                }
                if (addedItems.size() > 5) {
                    itemList.append("§7... 等 ").append(addedCount).append(" 个物品");
                }
                player.sendMessage(itemList.toString());
            }

        } else {
            player.sendMessage("§e没有可添加的物品！");
            if (skippedCount > 0) {
                player.sendMessage("§7背包中的 " + skippedCount + " 个物品已存在或为空物品");
            }
        }
    }

    /**
     * 检查物品是否已经存在于战利品中
     */
    private boolean isItemAlreadyExists(ItemStack itemToCheck) {
        // 获取所有物品
        List<Object> allItems = getAllItems();

        for (Object item : allItems) {
            if (item instanceof TreasureItemManager.TreasureItem) {
                TreasureItemManager.TreasureItem treasureItem = (TreasureItemManager.TreasureItem) item;
                ItemStack existingItem = treasureItem.getItemStack();
                if (existingItem != null && isSimilarItem(existingItem, itemToCheck)) {
                    return true;
                }
            } else if (item instanceof ModItemManager.ModItem) {
                ModItemManager.ModItem modItem = (ModItemManager.ModItem) item;
                // 对于ModItem，比较材质和名称
                if (modItem.getMaterial() == itemToCheck.getType()) {
                    String modItemName = modItem.getName();
                    String checkItemName = itemToCheck.hasItemMeta() && itemToCheck.getItemMeta().hasDisplayName() ?
                        itemToCheck.getItemMeta().getDisplayName() : itemToCheck.getType().name();
                    if (modItemName != null && modItemName.equals(checkItemName)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * 检查两个物品是否相似（用于判断重复）
     */
    private boolean isSimilarItem(ItemStack item1, ItemStack item2) {
        if (item1.getType() != item2.getType()) {
            return false;
        }

        // 检查显示名称
        String name1 = item1.hasItemMeta() && item1.getItemMeta().hasDisplayName() ?
            item1.getItemMeta().getDisplayName() : item1.getType().name();
        String name2 = item2.hasItemMeta() && item2.getItemMeta().hasDisplayName() ?
            item2.getItemMeta().getDisplayName() : item2.getType().name();

        return name1.equals(name2);
    }


}
