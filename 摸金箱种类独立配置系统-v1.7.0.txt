===============================================
  HangEvacuation 摸金箱种类独立配置系统 - v1.7.0
===============================================

🎮 插件名称: HangEvacuation (Hang摸金撤离)
📅 更新日期: 2024-12-19
🔧 版本号: 1.7.0
👨‍💻 作者: hangzong(航总)
📞 技术支持: 微信 hang060217

===============================================
            🆕 摸金箱种类独立配置功能
===============================================

🎯 【核心升级】
✅ 每种摸金箱现在都有独立的槽位数量配置
✅ 每种摸金箱现在都有独立的刷新时间配置
✅ 摸金箱种类信息持久化存储
✅ 动态标题显示对应摸金箱种类名称
✅ 完全自定义的摸金箱体验

🔧 【配置系统重构】
✅ mojin.yml 中每种摸金箱都有 slots 和 refresh_time 配置
✅ TreasureItemManager 支持按种类获取配置
✅ TreasureChestData 存储摸金箱种类信息
✅ 摸金箱放置时自动识别和存储种类

===============================================
              📋 摸金箱种类配置详情
===============================================

🎯 【默认配置方案】

📦 **普通摸金箱 (common)**
- 槽位数量: 5 个
- 刷新时间: 5 分钟
- 稀有度: 普通
- 适合: 新手玩家

🔫 **武器箱 (weapon)**
- 槽位数量: 8 个
- 刷新时间: 15 分钟
- 稀有度: 稀有
- 适合: 武器收集

💥 **弹药箱 (ammo)**
- 槽位数量: 6 个
- 刷新时间: 10 分钟
- 稀有度: 罕见
- 适合: 弹药补给

🏥 **医疗箱 (medical)**
- 槽位数量: 4 个
- 刷新时间: 3 分钟
- 稀有度: 常见
- 适合: 快速恢复

📦 **补给箱 (supply)**
- 槽位数量: 7 个
- 刷新时间: 8 分钟
- 稀有度: 普通
- 适合: 生存物资

⚙️ **装备箱 (equipment)**
- 槽位数量: 9 个
- 刷新时间: 20 分钟
- 稀有度: 史诗
- 适合: 高级装备

===============================================
              🔧 技术实现细节
===============================================

📊 【数据存储升级】
```java
public static class TreasureChestData {
    private String chestType; // 新增：摸金箱种类
    
    public TreasureChestData(String chestType) {
        this.chestType = chestType != null ? chestType : "common";
    }
}
```

🎯 【配置读取优化】
```java
// 根据摸金箱种类获取槽位数量
public int getChestSlots(String chestType) {
    ChestType type = plugin.getChestTypeManager().getChestType(chestType);
    return type != null ? type.getSlots() : getChestSlots();
}

// 根据摸金箱种类获取刷新时间
public int getRefreshTime(String chestType) {
    ChestType type = plugin.getChestTypeManager().getChestType(chestType);
    return type != null ? type.getRefreshTime() : getRefreshTime();
}
```

🔄 【放置逻辑升级】
```java
// 摸金箱放置时自动识别种类
String chestType = plugin.getChestTypeManager().getChestTypeFromItem(item);
TreasureChestData data = new TreasureChestData(chestType);
```

===============================================
              📝 配置文件示例
===============================================

🔧 【mojin.yml 配置示例】
```yaml
chest_types:
  weapon:
    name: "§c武器箱"
    display_name: "§c军用武器箱"
    description:
      - "§7军用级别的武器箱"
      - "§7包含各种强力武器"
    material: "TRAPPED_CHEST"
    rarity: "§c稀有"
    enabled: true
    # 摸金箱专属配置
    slots: 8                    # 武器箱物品更多
    refresh_time: 15            # 稀有箱子刷新时间更长
    
  medical:
    name: "§a医疗箱"
    display_name: "§a野战医疗箱"
    description:
      - "§7野战医疗用品箱"
      - "§a可以救命的好东西"
    material: "WHITE_SHULKER_BOX"
    rarity: "§a常见"
    enabled: true
    # 摸金箱专属配置
    slots: 4                    # 医疗箱物品较少
    refresh_time: 3             # 常见箱子刷新快
```

===============================================
              🎮 游戏体验升级
===============================================

🎯 【玩家体验】
- **差异化体验**: 不同种类摸金箱提供不同的游戏体验
- **策略选择**: 玩家可以根据需求选择合适的摸金箱类型
- **时间管理**: 不同刷新时间让玩家合理安排探索计划
- **收集乐趣**: 多样化的摸金箱增加收集和探索乐趣

🔧 【服务器管理】
- **平衡调节**: 管理员可以精确调节每种摸金箱的平衡性
- **内容分层**: 不同稀有度的摸金箱适合不同阶段的玩家
- **经济控制**: 通过刷新时间控制物品产出速度
- **自定义扩展**: 可以轻松添加新的摸金箱种类

===============================================
              📊 配置对比表
===============================================

| 摸金箱种类 | 槽位数 | 刷新时间 | 稀有度 | 适用场景 |
|------------|--------|----------|--------|----------|
| 普通摸金箱 | 5      | 5分钟    | 普通   | 新手入门 |
| 武器箱     | 8      | 15分钟   | 稀有   | 战斗装备 |
| 弹药箱     | 6      | 10分钟   | 罕见   | 弹药补给 |
| 医疗箱     | 4      | 3分钟    | 常见   | 快速恢复 |
| 补给箱     | 7      | 8分钟    | 普通   | 生存物资 |
| 装备箱     | 9      | 20分钟   | 史诗   | 顶级装备 |

===============================================
              🔄 兼容性说明
===============================================

🛡️ **向下兼容**
- 现有的摸金箱会自动识别为 "common" 类型
- 原有的配置文件继续有效
- 不会影响已放置的摸金箱功能

📈 **平滑升级**
- 插件会自动为现有摸金箱分配默认种类
- 配置文件会自动生成默认值
- 无需手动迁移数据

===============================================
              🎯 使用指南
===============================================

📋 【管理员操作】
1. **配置摸金箱种类**: 编辑 mojin.yml 文件
2. **调整平衡性**: 修改 slots 和 refresh_time 参数
3. **重载配置**: 使用 /evac reload 应用更改
4. **给予摸金箱**: /evac give <种类> <玩家> <数量>

🎮 【玩家体验】
1. **获得摸金箱**: 通过命令或其他方式获得不同种类的摸金箱
2. **放置摸金箱**: 右键放置，系统自动识别种类
3. **搜索宝藏**: 不同种类提供不同数量的物品
4. **等待刷新**: 根据种类等待相应的刷新时间

===============================================
              ❓ 常见问题
===============================================

Q: 如何自定义新的摸金箱种类？
A: 在 mojin.yml 的 chest_types 节点下添加新配置，包含 slots 和 refresh_time

Q: 现有的摸金箱会受影响吗？
A: 不会，现有摸金箱会自动识别为 "common" 类型并正常工作

Q: 可以修改已放置摸金箱的种类吗？
A: 摸金箱种类在放置时确定，如需更改需要重新放置

Q: 如何平衡不同种类的摸金箱？
A: 通过调整 slots（物品数量）和 refresh_time（刷新时间）来平衡

===============================================
              📦 文件清单
===============================================

📁 **新增功能**
- ChestTypeManager.ChestType 类新增 slots 和 refreshTime 字段
- TreasureChestData 类新增 chestType 字段和相关方法
- TreasureItemManager 新增按种类获取配置的方法
- TreasureChestGUI 支持动态种类配置

📁 **配置文件**
- mojin.yml 每种摸金箱新增 slots 和 refresh_time 配置
- treasure_items.yml 中的全局配置标记为弃用但保持兼容

===============================================
              🎉 更新总结
===============================================

🎯 本次更新实现了摸金箱种类的完全独立配置，每种摸金箱
   都可以有自己的槽位数量和刷新时间。这让服务器管理员
   能够创建更加丰富和平衡的游戏体验，玩家也能享受到
   更多样化的摸金箱探索乐趣。

🔧 技术支持: 微信 hang060217
🎮 交流Q群: 361919269
👨‍💻 作者: hangzong(航总)
🏷️ 标签: Hang系列插件
