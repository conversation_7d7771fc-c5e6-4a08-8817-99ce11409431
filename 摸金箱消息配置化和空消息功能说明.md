# 🎯 摸金箱消息配置化和空消息功能说明

## 📋 **功能概述**

现在所有摸金箱相关的提示消息都可以在 `config.yml` 中自定义配置，并且支持**空消息不发送**功能。

### **✨ 核心特性**
- 🔧 **完全可配置**：所有消息都可以在配置文件中自定义
- 🚫 **空消息不发送**：如果配置中消息为空字符串 `""`，则不会发送该消息
- 🔄 **变量替换**：支持动态变量替换（如物品名称、时间等）
- 🎨 **颜色代码支持**：支持Minecraft颜色代码和格式化

## 📝 **配置文件示例**

### **完整消息配置**
```yaml
messages:
  # 摸金箱搜索完成消息
  treasure-chest-fully-searched: "§a恭喜！您已搜索完这个摸金箱的所有物品！"
  treasure-chest-refresh-time: "§e箱子将在 {minutes} 分钟后刷新"

  # 摸金箱冷却期间消息
  treasure-chest-cooldown-title: "§6摸金箱已搜索完毕！"
  treasure-chest-cooldown-refresh: "§e物品将在 §c{minutes}:{seconds} §e后刷新"
  treasure-chest-cooldown-hint: "§7您可以拿走剩余的物品，但需要等待刷新时间到达才会有新物品"

  # 手动搜索消息
  manual-search-already-searched: "§c这个物品已经被搜索过了！"
  manual-search-in-progress: "§e这个物品正在搜索中！"
  manual-search-wait-current: "§e请等待当前物品搜索完成后再搜索其他物品！"
  manual-search-start: "§a开始手动搜索物品..."

  # 搜索冷却消息
  search-cooldown-remaining: "§c搜索冷却中，请等待 {seconds} 秒"

  # 物品搜索完成消息
  item-search-complete: "§a搜索完成！发现了：{item_name}"
  item-search-empty: "§7这里什么都没有..."
```

### **空消息示例（不发送消息）**
```yaml
messages:
  # 设置为空字符串，将不发送这些消息
  treasure-chest-fully-searched: ""
  item-search-empty: ""
  manual-search-start: ""
  
  # 保留重要消息
  treasure-chest-refresh-time: "§e箱子将在 {minutes} 分钟后刷新"
  search-cooldown-remaining: "§c搜索冷却中，请等待 {seconds} 秒"
```

## 🔧 **支持的变量**

### **时间相关变量**
- `{minutes}` - 分钟数
- `{seconds}` - 秒数
- `{time}` - 通用时间

### **物品相关变量**
- `{item_name}` - 物品名称
- `{chest_name}` - 摸金箱名称

### **数量相关变量**
- `{count}` - 数量
- `{total}` - 总数

## 🎮 **使用场景**

### **场景1：完全静默模式**
```yaml
messages:
  # 所有消息都设为空，创造完全静默的体验
  treasure-chest-fully-searched: ""
  treasure-chest-refresh-time: ""
  item-search-complete: ""
  item-search-empty: ""
  manual-search-already-searched: ""
  manual-search-in-progress: ""
  manual-search-wait-current: ""
  manual-search-start: ""
  search-cooldown-remaining: ""
```

### **场景2：简化消息模式**
```yaml
messages:
  # 只保留关键消息，去掉冗余提示
  treasure-chest-fully-searched: "§a搜索完成！"
  treasure-chest-refresh-time: "§e{minutes}分钟后刷新"
  item-search-complete: "§a发现：{item_name}"
  item-search-empty: ""  # 空物品不提示
  manual-search-already-searched: ""  # 重复搜索不提示
  manual-search-in-progress: ""  # 搜索中不提示
  manual-search-wait-current: "§e请等待..."
  manual-search-start: ""  # 开始搜索不提示
  search-cooldown-remaining: "§c冷却中：{seconds}秒"
```

### **场景3：详细提示模式**
```yaml
messages:
  # 提供详细的游戏反馈
  treasure-chest-fully-searched: "§l§a✓ 恭喜！您已完全探索了这个摸金箱！"
  treasure-chest-refresh-time: "§e⏰ 摸金箱将在 §c{minutes} §e分钟后重新装填物品"
  item-search-complete: "§a🎁 搜索成功！您发现了：§e{item_name}"
  item-search-empty: "§7💨 搜索完毕，但这里空空如也..."
  manual-search-already-searched: "§c❌ 这个位置已经被搜索过了！"
  manual-search-in-progress: "§e🔍 这个物品正在搜索中，请稍候..."
  manual-search-wait-current: "§e⏳ 请等待当前搜索完成后再继续！"
  manual-search-start: "§a🔍 开始仔细搜索这个位置..."
  search-cooldown-remaining: "§c⏰ 搜索技能冷却中，还需等待 {seconds} 秒"
```

## 🎨 **自定义样式示例**

### **RPG风格**
```yaml
messages:
  treasure-chest-fully-searched: "§6⚔ 宝箱探索完成！经验值+10"
  item-search-complete: "§a⚡ 获得战利品：§e{item_name}"
  search-cooldown-remaining: "§c🛡 技能冷却：{seconds}秒"
```

### **现代风格**
```yaml
messages:
  treasure-chest-fully-searched: "§b[系统] 容器扫描完成"
  item-search-complete: "§a[发现] {item_name}"
  search-cooldown-remaining: "§c[冷却] {seconds}s"
```

### **可爱风格**
```yaml
messages:
  treasure-chest-fully-searched: "§d🌸 哇！箱子里的宝贝都被你找到啦~"
  item-search-complete: "§a✨ 发现小宝贝：{item_name} (｡◕‿◕｡)"
  item-search-empty: "§7🐾 这里什么都没有呢~ (´･ω･`)"
```

## 🔄 **配置重载**

修改配置后，使用以下命令重载配置：
```
/evac reload
```

或者重启服务器使配置生效。

## 📊 **技术实现**

### **空消息检查逻辑**
```java
// 新的消息发送方法
plugin.sendMessageIfNotEmpty(player, "message-key", "默认消息");

// 内部实现
public void sendMessageIfNotEmpty(Player player, String messageKey, String defaultMessage) {
    String message = getConfig().getString("messages." + messageKey, defaultMessage);
    if (message != null && !message.trim().isEmpty()) {
        player.sendMessage(message);
    }
    // 如果消息为空或null，则不发送任何消息
}
```

### **变量替换支持**
```java
// 带变量替换的消息发送
plugin.sendMessageIfNotEmpty(player, "item-search-complete", 
    "§a搜索完成！发现了：{item_name}", 
    "item_name", itemName);
```

## 🎯 **最佳实践建议**

### **1. 保留重要消息**
建议保留以下关键消息，不要设为空：
- `search-cooldown-remaining` - 冷却提示很重要
- `treasure-chest-refresh-time` - 刷新时间信息有用

### **2. 简化冗余消息**
可以考虑设为空的消息：
- `item-search-empty` - 空物品提示可能不必要
- `manual-search-start` - 开始搜索提示可能冗余

### **3. 个性化定制**
根据服务器风格调整消息：
- 生存服务器：使用简洁实用的消息
- RPG服务器：使用沉浸式的角色扮演消息
- 休闲服务器：使用轻松有趣的消息

## 🎊 **总结**

这个功能让服务器管理员可以：
- ✅ 完全控制所有摸金箱消息
- ✅ 创造静默或详细的游戏体验
- ✅ 适配不同服务器的风格需求
- ✅ 减少不必要的消息干扰
- ✅ 提供个性化的玩家体验

现在您可以根据服务器的需求，自由定制摸金箱系统的所有消息提示！
