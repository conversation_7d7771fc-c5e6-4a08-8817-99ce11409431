/*
 * Copyright (C) 2024 HangZong Contributors
 *
 * This Source Code Form is subject to the terms of the Mozilla Public
 * License, v. 2.0. If a copy of the MPL was not distributed with this
 * file, You can obtain one at https://mozilla.org/MPL/2.0/.
 */

package com.hang.plugin.license;

import com.hang.plugin.HangPlugin;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.InetAddress;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.spec.X509EncodedKeySpec;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 许可证管理器
 *
 * 基于 Lukittu 许可证系统的在线验证实现
 */
public class LicenseManager {

    // Lukittu API 常量
    private static final String API_BASE_URL = "https://app.lukittu.com/api/v1/client/teams";
    private static final String VERIFY_ENDPOINT = "/verification/verify";
    private static final String HEARTBEAT_ENDPOINT = "/verification/heartbeat";
    private static final String VERSION = "1.0.0";
    private static final int TIMEOUT_MILLIS = 10000; // 10 seconds

    // 硬编码的许可证信息（生产环境中应该混淆）
    private static final String TEAM_ID = "3dc75429-cdf4-411d-a901-9c7775a5a758";
    private static final String PRODUCT_ID = "7c9cc610-0b9c-4392-b307-2c87d3baa020";
    private static final String RSA_PUBLIC_KEY = "LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUE0ZXZSQ1RxSk5CaUhtbVFGdUFiNgpGQUVWREZHbUdkYWhrS2t3eFdpVjFZT3lhdCtwSjh2emRVNnoyU1p2MkMrYk1FWGREbHFjQXBQVThhUVNvcUxqCmtsVjNwMW42Q3dGUEtDNGt1ck9yQy9tVzIwR0VqdjRqTC93dW9ydWQxS002UUhCMk5tbGVER25qa0I2VXhOL3AKUkdQU1QzYlpsc1dRWm92R0ZBNXM0RWFnSGhOZVcxS3BQbm0vclFTQzBKYnd6Wmo0SU1UK29Hb3lJSTZPbXRmYQo4TEgvV0JhejVPcXBqaC94RjVkbE9NK2VBNklrQ3FBSmdhWFJ1ck1CemhPWHZ6dHdvSEdTQUE1YTlTeE9oSi9sCjEwY21wTStSZkFNcFdHTUxxZGtkeTk2Yk83cWp5cjQrMEZmbWw5QitGSWxGU0NGZEMwWm4wd3JCZWdOM2xBYVgKTVFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==";

    // JSON 处理器
    private static final Gson GSON = new GsonBuilder()
            .disableHtmlEscaping()
            .setPrettyPrinting()
            .create();

    // 错误消息映射
    private static final Map<String, String> ERROR_MESSAGES = new HashMap<String, String>() {{
        put("RELEASE_NOT_FOUND", "配置中指定的版本无效");
        put("LICENSE_NOT_FOUND", "许可证未在配置中指定，或许可证无效");
        put("IP_LIMIT_REACHED", "许可证的IP地址限制已达到上限。如有问题请联系支持");
        put("MAXIMUM_CONCURRENT_SEATS", "同一许可证连接的最大设备数");
        put("RATE_LIMIT", "同一IP地址在短时间内连接过多。请稍等片刻！");
        put("LICENSE_EXPIRED", "许可证已过期");
        put("INTERNAL_SERVER_ERROR", "上游服务有问题。请通知支持！");
        put("BAD_REQUEST", "无效的请求格式或参数。检查您的许可证配置");
    }};

    private final HangPlugin plugin;
    private boolean isLicenseValid = false;
    private LicenseInfo licenseInfo = null;
    private String deviceIdentifier = null;
    private ScheduledExecutorService scheduler = null;

    public LicenseManager(HangPlugin plugin) {
        this.plugin = plugin;
    }

    /**
     * 许可证信息数据类
     */
    public static class LicenseInfo {
        private final String licenseKey;
        private final String serverName;
        private final String expiryDate;
        private final int maxPlayers;
        private final Set<String> features;
        private final String issuedDate;
        private final String version;

        public LicenseInfo(String licenseKey, String serverName, String expiryDate, 
                          int maxPlayers, Set<String> features, String issuedDate, String version) {
            this.licenseKey = licenseKey;
            this.serverName = serverName;
            this.expiryDate = expiryDate;
            this.maxPlayers = maxPlayers;
            this.features = features;
            this.issuedDate = issuedDate;
            this.version = version;
        }

        // Getters
        public String getLicenseKey() { return licenseKey; }
        public String getServerName() { return serverName; }
        public String getExpiryDate() { return expiryDate; }
        public int getMaxPlayers() { return maxPlayers; }
        public Set<String> getFeatures() { return features; }
        public String getIssuedDate() { return issuedDate; }
        public String getVersion() { return version; }
    }

    /**
     * 验证许可证密钥（在线验证）
     */
    public boolean validateLicenseKey(String licenseKey) {
        try {
            if (licenseKey == null || licenseKey.trim().isEmpty()) {
                plugin.getLogger().severe("许可证密钥为空");
                return false;
            }

            // 生成设备标识符
            deviceIdentifier = generateHardwareIdentifier();

            // 生成随机挑战
            String challenge = generateRandomChallenge();

            // 构建 API URL
            String url = API_BASE_URL + "/" + TEAM_ID + VERIFY_ENDPOINT;

            // 构建请求体
            String jsonBody = String.format(
                "{\n" +
                "  \"licenseKey\": \"%s\",\n" +
                "  \"productId\": \"%s\",\n" +
                "  \"challenge\": \"%s\",\n" +
                "  \"version\": \"%s\",\n" +
                "  \"deviceIdentifier\": \"%s\"\n" +
                "}",
                licenseKey, PRODUCT_ID, challenge, VERSION, deviceIdentifier
            );

            // 发送请求并处理响应
            boolean verificationSuccess = fetchAndHandleResponse(url, jsonBody, RSA_PUBLIC_KEY, challenge);

            if (verificationSuccess) {
                isLicenseValid = true;
                plugin.getLogger().info("许可证验证成功");

                // 启动心跳调度器
                setupHeartbeatScheduler(licenseKey);

                return true;
            } else {
                plugin.getLogger().severe("许可证验证失败");
                return false;
            }

        } catch (Exception e) {
            plugin.getLogger().severe("验证许可证时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 生成随机挑战字符串以防止重放攻击
     */
    private String generateRandomChallenge() {
        SecureRandom secureRandom = new SecureRandom();
        byte[] randomBytes = new byte[32];
        secureRandom.nextBytes(randomBytes);
        return bytesToHex(randomBytes);
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder result = new StringBuilder(bytes.length * 2);
        for (byte b : bytes) {
            result.append(String.format("%02x", b));
        }
        return result.toString();
    }

    /**
     * 发送 HTTP 请求到许可证服务器并处理响应
     */
    private boolean fetchAndHandleResponse(String urlString, String jsonBody, 
                                         String publicKeyBase64, String challenge) throws IOException {
        HttpURLConnection connection = null;
        boolean success = false;

        try {
            URI uri = URI.create(urlString);
            connection = (HttpURLConnection) uri.toURL().openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", buildUserAgent());
            connection.setConnectTimeout(TIMEOUT_MILLIS);
            connection.setReadTimeout(TIMEOUT_MILLIS);
            connection.setDoOutput(true);

            // 发送请求体
            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();

            if (responseCode == HttpURLConnection.HTTP_OK) {
                try (InputStream inputStream = connection.getInputStream()) {
                    success = handleJsonResponse(inputStream, publicKeyBase64, challenge);
                }
            } else {
                InputStream errorStream = connection.getErrorStream();
                if (errorStream != null) {
                    try (InputStream es = errorStream) {
                        handleJsonResponse(es, null, null);
                    }
                }

                if (responseCode >= 400) {
                    plugin.getLogger().severe("HTTP 错误代码: " + responseCode);
                }
            }

        } catch (Exception e) {
            plugin.getLogger().severe("连接到 HangZong 许可证服务失败: " + e.getMessage());
            throw new IOException("连接到许可证服务器失败", e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }

        return success;
    }

    /**
     * 处理 JSON 响应
     */
    private boolean handleJsonResponse(InputStream inputStream, String publicKey, String challenge) {
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            JsonObject json = GSON.fromJson(reader, JsonObject.class);

            // 记录响应用于调试
            String respString = GSON.toJson(json);

            // 检查这是否是需要验证的成功响应
            if (publicKey != null && challenge != null) {
                if (validateResponse(json) && validateChallenge(json, challenge, publicKey)) {
                    // 创建许可证信息
                    Set<String> features = new HashSet<>();
                    features.add("ALL");

                    licenseInfo = new LicenseInfo(
                        "验证成功",
                        "HangZong Licensed Server",
                        null,
                        -1,
                        features,
                        "2024-01-01",
                        VERSION
                    );
                    return true;
                }
            }

            // 处理错误响应
            if (json.has("result")) {
                JsonObject result = json.getAsJsonObject("result");

                if (result.has("code")) {
                    String errorCode = result.get("code").getAsString();
                    String errorMessage = ERROR_MESSAGES.getOrDefault(errorCode,
                        "HangZong 许可证检查失败，错误代码: " + errorCode);
                    plugin.getLogger().severe("错误: " + errorMessage);
                    return false;
                }
            }

            return false;
        } catch (Exception e) {
            plugin.getLogger().severe("处理 JSON 响应时发生错误: " + e.getMessage());
            return false;
        }
    }

    /**
     * 验证响应是否表示有效许可证
     */
    private boolean validateResponse(JsonObject json) {
        try {
            JsonObject result = json.getAsJsonObject("result");
            return result != null && result.has("valid") && result.get("valid").getAsBoolean();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 验证挑战签名
     */
    private boolean validateChallenge(JsonObject response, String originalChallenge, String base64PublicKey) {
        try {
            if (!validateResponse(response)) {
                return false;
            }

            String signedChallenge = response.getAsJsonObject("result")
                .get("challengeResponse").getAsString();

            return verifySignature(originalChallenge, signedChallenge, base64PublicKey);
        } catch (Exception e) {
            plugin.getLogger().severe("挑战验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 执行实际的加密签名验证
     */
    private boolean verifySignature(String challenge, String signatureHex, String base64PublicKey) {
        try {
            byte[] signatureBytes = hexStringToByteArray(signatureHex);
            byte[] decodedKeyBytes = Base64.getDecoder().decode(base64PublicKey);

            String decodedKeyString = new String(decodedKeyBytes)
                .replace("-----BEGIN PUBLIC KEY-----", "")
                .replace("-----END PUBLIC KEY-----", "")
                .replaceAll("\\s", "");

            byte[] publicKeyBytes = Base64.getDecoder().decode(decodedKeyString);
            X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PublicKey publicKey = keyFactory.generatePublic(keySpec);

            Signature signature = Signature.getInstance("SHA256withRSA");
            signature.initVerify(publicKey);
            signature.update(challenge.getBytes());

            return signature.verify(signatureBytes);
        } catch (Exception e) {
            plugin.getLogger().severe("签名验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 将十六进制字符串转换为字节数组
     */
    private byte[] hexStringToByteArray(String hex) {
        int len = hex.length();
        byte[] data = new byte[len / 2];
        for (int i = 0; i < len; i += 2) {
            data[i / 2] = (byte) ((Character.digit(hex.charAt(i), 16) << 4)
                                + Character.digit(hex.charAt(i + 1), 16));
        }
        return data;
    }

    /**
     * 设置心跳调度器
     */
    private void setupHeartbeatScheduler(String licenseKey) {
        scheduler = Executors.newSingleThreadScheduledExecutor();

        scheduler.scheduleAtFixedRate(() -> {
            try {
                sendHeartbeat(licenseKey);
            } catch (Exception e) {
                // 静默处理心跳失败
            }
        }, 15, 15, TimeUnit.MINUTES);
    }

    /**
     * 发送心跳到许可证服务器
     */
    private void sendHeartbeat(String licenseKey) {
        String urlString = API_BASE_URL + "/" + TEAM_ID + HEARTBEAT_ENDPOINT;
        HttpURLConnection connection = null;

        try {
            URI uri = URI.create(urlString);
            connection = (HttpURLConnection) uri.toURL().openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setRequestProperty("User-Agent", buildUserAgent());
            connection.setConnectTimeout(TIMEOUT_MILLIS);
            connection.setReadTimeout(TIMEOUT_MILLIS);
            connection.setDoOutput(true);

            String jsonBody = String.format(
                "{\n" +
                "    \"licenseKey\": \"%s\",\n" +
                "    \"productId\": \"%s\",\n" +
                "    \"deviceIdentifier\": \"%s\"\n" +
                "}",
                licenseKey, PRODUCT_ID, deviceIdentifier
            );

            try (java.io.OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonBody.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }

            int responseCode = connection.getResponseCode();
            // 静默处理心跳响应

        } catch (IOException e) {
            // 静默处理心跳异常
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 构建用户代理字符串
     */
    private String buildUserAgent() {
        return String.format(
            "AceDexLoader/%s (%s %s; %s)",
            VERSION,
            System.getProperty("os.name"),
            System.getProperty("os.version"),
            System.getProperty("os.arch")
        );
    }

    /**
     * 生成硬件标识符
     */
    private String generateHardwareIdentifier() {
        try {
            String osName = System.getProperty("os.name");
            String osVersion = System.getProperty("os.version");
            String osArch = System.getProperty("os.arch");
            String hostname = InetAddress.getLocalHost().getHostName();

            String combinedIdentifier = osName + osVersion + osArch + hostname;
            return UUID.nameUUIDFromBytes(combinedIdentifier.getBytes()).toString();
        } catch (Exception e) {
            return UUID.randomUUID().toString();
        }
    }

    /**
     * 检查许可证是否有效
     */
    public boolean isValid() {
        return isLicenseValid;
    }

    /**
     * 获取许可证信息
     */
    public LicenseInfo getLicenseInfo() {
        return licenseInfo;
    }

    /**
     * 检查是否有特定功能的许可
     */
    public boolean hasFeature(String feature) {
        if (!isLicenseValid) return false;
        LicenseInfo info = licenseInfo;
        if (info == null) return false;
        return info.getFeatures().contains("ALL") || info.getFeatures().contains(feature);
    }

    /**
     * 关闭许可证管理器
     */
    public void shutdown() {
        if (scheduler != null) {
            if (!scheduler.isShutdown()) {
                scheduler.shutdown();
                try {
                    if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduler.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    scheduler.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
        }
    }
}
