# 🎲 摸金箱概率重载修复说明

## 🐛 **问题描述**

在战利品管理GUI中，每次点击"重载"按钮后，物品的概率都会发生变化，通常是变得越来越小。

### **问题现象**
- **初始状态**：钻石概率显示为 `5.0%`
- **第1次重载**：钻石概率变为 `0.05%`
- **第2次重载**：钻石概率变为 `0.0005%`
- **第3次重载**：钻石概率变为 `0.000005%`
- **结果**：物品变得几乎不可能获得

## 🔍 **问题根源分析**

### **概率格式混乱**

插件内部使用了**两种不同的概率格式**：

1. **配置文件格式**：小数格式（0.0-1.0）
   - 例如：`0.05` 表示 5%

2. **内部计算格式**：百分比格式（0.0-100.0）
   - 例如：`5.0` 表示 5%

### **错误的转换逻辑**

#### **保存时的问题**
```java
// 🐛 错误：将百分比格式除以100保存
config.set(path + ".probability", item.getChance() / 100.0);
```

#### **加载时的问题**
```java
// 🐛 错误：直接读取小数格式作为百分比使用
double probability = section.getDouble("probability", 0.0);
```

### **问题流程示例**

| 步骤 | 配置文件值 | 内部值 | GUI显示 | 说明 |
|------|------------|--------|---------|------|
| 初始 | `0.05` | `5.0` | `5.0%` | ✅ 正确 |
| 保存 | `5.0/100=0.05` | `5.0` | `5.0%` | ✅ 看似正确 |
| 重载 | `0.05` | `0.05` | `0.05%` | ❌ 错误！ |
| 再保存 | `0.05/100=0.0005` | `0.05` | `0.05%` | ❌ 更错误！ |
| 再重载 | `0.0005` | `0.0005` | `0.0005%` | ❌ 完全错误！ |

## 🔧 **修复方案**

### **1. 修复加载逻辑**

**序列化物品加载修复**：
```java
double probability = section.getDouble("probability", 0.0);
// 🔧 修复：将配置文件中的小数格式转换为百分比格式（内部使用）
probability = probability * 100.0;
```

**普通物品加载修复**：
```java
double probability = section.getDouble("probability", 0.0);
// 🔧 修复：将配置文件中的小数格式转换为百分比格式（内部使用）
probability = probability * 100.0;
```

### **2. 保持保存逻辑**

保存逻辑保持不变，继续将百分比格式转换为小数格式：
```java
// 保存时：将百分比格式转换为小数格式
config.set(path + ".probability", item.getChance() / 100.0);
```

### **3. 统一格式规范**

- **配置文件**：始终使用小数格式（0.0-1.0）
- **内部计算**：始终使用百分比格式（0.0-100.0）
- **GUI显示**：显示百分比格式（如 `5.0%`）

## ✅ **修复验证**

### **修复后的流程**

| 步骤 | 配置文件值 | 内部值 | GUI显示 | 说明 |
|------|------------|--------|---------|------|
| 初始 | `0.05` | `5.0` | `5.0%` | ✅ 正确 |
| 重载 | `0.05` | `0.05*100=5.0` | `5.0%` | ✅ 修复后正确！ |
| 保存 | `5.0/100=0.05` | `5.0` | `5.0%` | ✅ 保持正确 |
| 再重载 | `0.05` | `0.05*100=5.0` | `5.0%` | ✅ 持续正确！ |

### **测试步骤**

1. **查看初始概率**：
   - 打开战利品管理GUI
   - 记录各物品的概率值

2. **执行重载测试**：
   - 点击"重载"按钮
   - 检查概率是否保持不变

3. **多次重载测试**：
   - 连续点击重载按钮3-5次
   - 确认概率始终保持稳定

4. **保存测试**：
   - 修改某个物品的概率
   - 点击"保存"按钮
   - 重载后检查概率是否正确

## 🎯 **概率计算机制**

### **正确的概率使用**

修复后，概率计算使用统一的百分比格式：

```java
// 概率检查（使用百分比格式）
if (random.nextDouble() * 100 < probability) {
    return item; // 选中该物品
}
```

### **概率示例**

| 物品 | 配置文件值 | 内部值 | 实际概率 |
|------|------------|--------|----------|
| 钻石 | `0.05` | `5.0` | 5% |
| 铁锭 | `0.15` | `15.0` | 15% |
| 煤炭 | `0.20` | `20.0` | 20% |
| 空物品 | `0.25` | `25.0` | 25% |

## 🚀 **性能影响**

### **修复的性能优势**

1. **稳定的概率**：不再因重载而改变
2. **正确的随机分布**：物品出现频率符合预期
3. **一致的游戏体验**：玩家不会遇到概率异常

### **对现有数据的影响**

- ✅ **向后兼容**：现有配置文件无需修改
- ✅ **自动修复**：重载时自动修正概率格式
- ✅ **数据安全**：不会丢失任何配置数据

## 🔍 **调试信息**

### **启用调试模式**

```yaml
debug:
  enabled: true
```

### **调试日志示例**

```
[INFO] 加载物品 diamond: 配置值=0.05, 转换后=5.0
[INFO] 加载物品 iron_ingot: 配置值=0.15, 转换后=15.0
[INFO] 概率计算: 随机值=23.5, 物品概率=5.0, 结果=未选中
[INFO] 概率计算: 随机值=3.2, 物品概率=5.0, 结果=选中
```

## 📋 **相关文件**

### **修改的文件**
- `src/main/java/com/hang/plugin/manager/TreasureItemManager.java`
  - `loadTreasureItem()` 方法：修复加载时的概率转换
  - 第148-155行：序列化物品概率修复
  - 第173-180行：普通物品概率修复

### **配置文件格式**
- `treasure_items.yml`：保持小数格式（0.0-1.0）
- GUI显示：百分比格式（0.0%-100.0%）

## 🎉 **修复效果**

### **修复前**
- ❌ 每次重载概率都会变小
- ❌ 物品变得越来越难获得
- ❌ 游戏平衡被破坏

### **修复后**
- ✅ 重载后概率保持稳定
- ✅ 物品出现频率符合配置
- ✅ 游戏平衡得到维护

## 🔄 **升级说明**

### **对于现有服务器**

1. **无需手动操作**：插件会自动处理概率格式
2. **立即生效**：重载插件后问题即可解决
3. **数据安全**：所有现有配置都会被正确处理

### **对于新服务器**

1. **开箱即用**：新安装的插件不会有此问题
2. **标准配置**：使用推荐的概率配置即可

---

**修复版本**: v2.1.0+  
**技术支持**: 微信 hang060217  
**交流Q群**: 361919269  
**插件作者**: hangzong(航总)
