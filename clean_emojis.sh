#!/bin/bash

# 清理Java源代码中的表情符号脚本

echo "开始清理Java源代码中的表情符号..."

# 定义要清理的表情符号和对应的替换文本
declare -A emoji_map=(
    ["🔧"]="修复："
    ["🚀"]="优化："
    ["🆕"]="新增："
    ["💾"]="保存："
    ["📊"]="统计："
    ["🎯"]="目标："
    ["⚡"]="性能："
    ["🔍"]="调试："
    ["🛑"]="停止："
    ["🔇"]="禁用："
    ["🗑️"]="清理："
    ["⚠️"]="警告："
    ["✅"]="完成："
    ["❌"]="错误："
    ["📝"]="记录："
    ["🎮"]="游戏："
    ["🌍"]="世界："
    ["👤"]="玩家："
    ["📦"]="物品："
    ["🎨"]="样式："
    ["🔄"]="刷新："
    ["⏰"]="时间："
    ["🎉"]="成功："
    ["🔥"]="热点："
    ["💡"]="提示："
    ["🛡️"]="保护："
    ["🎪"]="特效："
    ["🌟"]="特殊："
    ["🏆"]="奖励："
    ["🎁"]="礼品："
    ["🔐"]="安全："
    ["📈"]="增长："
    ["📉"]="减少："
    ["🔀"]="切换："
    ["⭐"]="星级："
    ["🎖️"]="徽章："
    ["🏅"]="奖牌："
    ["🎊"]="庆祝："
    ["🎈"]="气球："
    ["🎀"]="装饰："
    ["🎭"]="面具："
    ["🎵"]="音乐："
    ["🎶"]="音符："
    ["🎤"]="麦克风："
    ["🎧"]="耳机："
    ["🎬"]="电影："
    ["🕹️"]="控制器："
    ["🎲"]="骰子："
    ["🃏"]="扑克："
    ["🎰"]="老虎机："
    ["🎳"]="保龄球："
    ["⚽"]="足球："
    ["🏀"]="篮球："
    ["🏈"]="橄榄球："
    ["⚾"]="棒球："
    ["🎾"]="网球："
    ["🏐"]="排球："
    ["🏉"]="橄榄球："
    ["🎱"]="台球："
    ["🏓"]="乒乓球："
    ["🏸"]="羽毛球："
    ["🥅"]="球门："
    ["⛳"]="高尔夫："
    ["🏹"]="弓箭："
    ["🎣"]="钓鱼："
    ["🥊"]="拳击："
    ["🥋"]="武术："
    ["🎿"]="滑雪："
    ["⛷️"]="滑雪："
    ["🏂"]="滑板："
    ["🏋️"]="举重："
    ["🤸"]="体操："
    ["🤾"]="手球："
    ["🏌️"]="高尔夫："
    ["🧘"]="瑜伽："
    ["🏃"]="跑步："
    ["🚶"]="步行："
    ["🧗"]="攀岩："
    ["🚴"]="骑行："
    ["🏊"]="游泳："
    ["🤽"]="水球："
    ["🚣"]="划船："
    ["🧜"]="美人鱼："
    ["🏄"]="冲浪："
    ["🛀"]="洗澡："
)

# 查找所有Java文件
find src/main/java -name "*.java" -type f | while read -r file; do
    echo "处理文件: $file"
    
    # 为每个表情符号创建替换命令
    for emoji in "${!emoji_map[@]}"; do
        replacement="${emoji_map[$emoji]}"
        # 使用sed进行替换，处理可能的空格
        sed -i "s/${emoji}[[:space:]]*/${replacement}/g" "$file"
    done
done

echo "表情符号清理完成！"
