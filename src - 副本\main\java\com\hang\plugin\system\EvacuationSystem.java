package com.hang.plugin.system;

import com.hang.plugin.HangPlugin;
import org.bukkit.Location;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.UUID;

/**
 * 撤离点系统 - 支持区域选择
 *
 * <AUTHOR>
 */
public class EvacuationSystem {

    private final HangPlugin plugin;
    private final Map<String, EvacuationZone> evacuationZones;
    private final Map<UUID, Location> playerSelections1; // 第一个选择点
    private final Map<UUID, Location> playerSelections2; // 第二个选择点
    private final File dataFile;
    private FileConfiguration dataConfig;
    private Location finalDestination; // 最终撤离目标位置

    // 🔧 新增：延迟加载机制
    private final Map<String, String> pendingWorldLoads; // 等待加载的世界数据

    public EvacuationSystem(HangPlugin plugin) {
        this.plugin = plugin;
        this.evacuationZones = new HashMap<>();
        this.playerSelections1 = new HashMap<>();
        this.playerSelections2 = new HashMap<>();
        this.pendingWorldLoads = new HashMap<>();
        this.dataFile = new File(plugin.getDataFolder(), "evacuation_points.yml");

        loadEvacuationData();

        // 🔧 新增：延迟重试加载机制
        scheduleRetryLoad();
    }

    /**
     * 加载撤离数据
     */
    private void loadEvacuationData() {
        if (!dataFile.exists()) {
            plugin.saveResource("evacuation_points.yml", false);
        }

        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
        evacuationZones.clear();

        // 加载最终撤离目标
        if (dataConfig.contains("final-destination")) {
            String worldName = dataConfig.getString("final-destination.world");
            if (worldName != null) {
                org.bukkit.World world = plugin.getServer().getWorld(worldName);
                if (world != null) {
                    double x = dataConfig.getDouble("final-destination.x");
                    double y = dataConfig.getDouble("final-destination.y");
                    double z = dataConfig.getDouble("final-destination.z");
                    float yaw = (float) dataConfig.getDouble("final-destination.yaw", 0.0);
                    float pitch = (float) dataConfig.getDouble("final-destination.pitch", 0.0);
                    finalDestination = new Location(world, x, y, z, yaw, pitch);
                } else {
                    // 🔧 世界未加载，记录待加载
                    plugin.getLogger().warning("撤离目标世界 '" + worldName + "' 未找到，将在世界加载后重试");
                    pendingWorldLoads.put("final-destination", worldName);
                }
            }
        }

        // 加载撤离区域
        if (dataConfig.contains("evacuation-zones")) {
            Set<String> keys = dataConfig.getConfigurationSection("evacuation-zones").getKeys(false);

            for (String key : keys) {
                String path = "evacuation-zones." + key;
                EvacuationZone zone = loadEvacuationZone(path);
                if (zone != null) {
                    evacuationZones.put(key, zone);
                } else {
                    // 🔧 记录加载失败的区域，稍后重试
                    String worldName = dataConfig.getString(path + ".world");
                    if (worldName != null) {
                        plugin.getLogger().warning("撤离区域 '" + key + "' 的世界 '" + worldName + "' 未找到，将在世界加载后重试");
                        pendingWorldLoads.put("zone-" + key, worldName);
                    }
                }
            }
        }
    }

    /**
     * 从配置加载撤离区域
     */
    private EvacuationZone loadEvacuationZone(String path) {
        try {
            String worldName = dataConfig.getString(path + ".world");
            if (worldName == null) return null;

            org.bukkit.World world = plugin.getServer().getWorld(worldName);
            if (world == null) {
                plugin.getLogger().warning("加载撤离区域时找不到世界: " + worldName);
                return null;
            }

            double x1 = dataConfig.getDouble(path + ".pos1.x");
            double y1 = dataConfig.getDouble(path + ".pos1.y");
            double z1 = dataConfig.getDouble(path + ".pos1.z");

            double x2 = dataConfig.getDouble(path + ".pos2.x");
            double y2 = dataConfig.getDouble(path + ".pos2.y");
            double z2 = dataConfig.getDouble(path + ".pos2.z");

            Location pos1 = new Location(world, x1, y1, z1);
            Location pos2 = new Location(world, x2, y2, z2);

            return new EvacuationZone(pos1, pos2);

        } catch (Exception e) {
            plugin.getLogger().warning("加载撤离区域失败: " + path + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * 设置玩家的第一个选择点
     */
    public void setPlayerSelection1(Player player, Location location) {
        playerSelections1.put(player.getUniqueId(), location.clone());
        player.sendMessage("§a第一个坐标已选择: " + formatLocation(location));
    }

    /**
     * 设置玩家的第二个选择点
     */
    public void setPlayerSelection2(Player player, Location location) {
        playerSelections2.put(player.getUniqueId(), location.clone());
        player.sendMessage("§a第二个坐标已选择: " + formatLocation(location));

        // 检查是否两个点都已选择
        if (playerSelections1.containsKey(player.getUniqueId())) {
            player.sendMessage("§e两个坐标都已选择，现在可以使用 §6/evac create <名称> §e创建撤离区域");
        }
    }

    /**
     * 创建撤离区域
     */
    public boolean createEvacuationZone(Player player, String name) {
        // 从PlayerListener获取选择的坐标
        Location pos1 = plugin.getPlayerListener().getFirstPosition(player);
        Location pos2 = plugin.getPlayerListener().getSecondPosition(player);

        if (pos1 == null || pos2 == null) {
            player.sendMessage("§c请先使用撤离点选择工具选择两个坐标！");
            player.sendMessage("§e使用 §6/evac tool §e获取选择工具");
            return false;
        }

        if (evacuationZones.containsKey(name)) {
            player.sendMessage("§c撤离区域 " + name + " 已存在！");
            return false;
        }

        // 检查是否在同一世界
        if (!pos1.getWorld().equals(pos2.getWorld())) {
            player.sendMessage("§c两个坐标必须在同一世界！");
            return false;
        }

        EvacuationZone zone = new EvacuationZone(pos1, pos2);
        evacuationZones.put(name, zone);
        saveEvacuationData();

        // 清除玩家的选择
        plugin.getPlayerListener().clearPlayerSelection(player);

        player.sendMessage("§a撤离区域 §6" + name + " §a创建成功！");
        player.sendMessage(String.format("§7区域范围: (%d,%d,%d) 到 (%d,%d,%d)",
            pos1.getBlockX(), pos1.getBlockY(), pos1.getBlockZ(),
            pos2.getBlockX(), pos2.getBlockY(), pos2.getBlockZ()));
        return true;
    }

    /**
     * 检查玩家是否在撤离区域内
     */
    public String checkPlayerInEvacuationZone(Player player) {
        Location playerLocation = player.getLocation();

        for (Map.Entry<String, EvacuationZone> entry : evacuationZones.entrySet()) {
            if (entry.getValue().contains(playerLocation)) {
                return entry.getKey();
            }
        }

        return null;
    }

    /**
     * 设置最终撤离目标
     */
    public void setFinalDestination(Location location) {
        this.finalDestination = location.clone();
        saveEvacuationData();
    }

    /**
     * 获取最终撤离目标
     */
    public Location getFinalDestination() {
        return finalDestination != null ? finalDestination.clone() : null;
    }

    /**
     * 获取所有撤离区域名称
     */
    public Set<String> getEvacuationZoneNames() {
        return evacuationZones.keySet();
    }

    /**
     * 获取撤离区域数量
     */
    public int getEvacuationZoneCount() {
        return evacuationZones.size();
    }

    /**
     * 移除撤离区域
     */
    public boolean removeEvacuationZone(String name) {
        boolean removed = evacuationZones.remove(name) != null;
        if (removed) {
            saveEvacuationData();
        }
        return removed;
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        evacuationZones.clear();
        playerSelections1.clear();
        playerSelections2.clear();
        loadEvacuationData();
    }

    /**
     * 格式化位置信息
     */
    private String formatLocation(Location loc) {
        return String.format("%s: %d, %d, %d",
            loc.getWorld().getName(),
            loc.getBlockX(),
            loc.getBlockY(),
            loc.getBlockZ());
    }

    /**
     * 保存撤离数据
     */
    private void saveEvacuationData() {
        try {
            // 保存最终撤离目标
            if (finalDestination != null) {
                dataConfig.set("final-destination.world", finalDestination.getWorld().getName());
                dataConfig.set("final-destination.x", finalDestination.getX());
                dataConfig.set("final-destination.y", finalDestination.getY());
                dataConfig.set("final-destination.z", finalDestination.getZ());
                dataConfig.set("final-destination.yaw", finalDestination.getYaw());
                dataConfig.set("final-destination.pitch", finalDestination.getPitch());
            }

            // 清空现有撤离区域数据
            dataConfig.set("evacuation-zones", null);

            // 保存所有撤离区域
            for (Map.Entry<String, EvacuationZone> entry : evacuationZones.entrySet()) {
                String path = "evacuation-zones." + entry.getKey();
                EvacuationZone zone = entry.getValue();

                dataConfig.set(path + ".world", zone.getPos1().getWorld().getName());
                dataConfig.set(path + ".pos1.x", zone.getPos1().getX());
                dataConfig.set(path + ".pos1.y", zone.getPos1().getY());
                dataConfig.set(path + ".pos1.z", zone.getPos1().getZ());
                dataConfig.set(path + ".pos2.x", zone.getPos2().getX());
                dataConfig.set(path + ".pos2.y", zone.getPos2().getY());
                dataConfig.set(path + ".pos2.z", zone.getPos2().getZ());
            }

            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存撤离数据时出错: " + e.getMessage());
        }
    }

    /**
     * 撤离区域类
     */
    public static class EvacuationZone {
        private final Location pos1;
        private final Location pos2;
        private final int minX, maxX, minY, maxY, minZ, maxZ;

        public EvacuationZone(Location pos1, Location pos2) {
            this.pos1 = pos1.clone();
            this.pos2 = pos2.clone();

            // 计算边界
            this.minX = Math.min(pos1.getBlockX(), pos2.getBlockX());
            this.maxX = Math.max(pos1.getBlockX(), pos2.getBlockX());
            this.minY = Math.min(pos1.getBlockY(), pos2.getBlockY());
            this.maxY = Math.max(pos1.getBlockY(), pos2.getBlockY());
            this.minZ = Math.min(pos1.getBlockZ(), pos2.getBlockZ());
            this.maxZ = Math.max(pos1.getBlockZ(), pos2.getBlockZ());
        }

        public boolean contains(Location location) {
            if (!location.getWorld().equals(pos1.getWorld())) {
                return false;
            }

            int x = location.getBlockX();
            int y = location.getBlockY();
            int z = location.getBlockZ();

            return x >= minX && x <= maxX &&
                   y >= minY && y <= maxY &&
                   z >= minZ && z <= maxZ;
        }

        public Location getPos1() { return pos1.clone(); }
        public Location getPos2() { return pos2.clone(); }
    }

    /**
     * 获取撤离区域数量
     * @return 撤离区域数量
     */
    public int getEvacuationPointCount() {
        return evacuationZones.size();
    }

    /**
     * 🔧 新增：延迟重试加载机制
     * 在插件启动后延迟几秒重试加载，给多世界插件时间加载所有世界
     */
    private void scheduleRetryLoad() {
        if (!pendingWorldLoads.isEmpty()) {
            // 延迟5秒后重试加载
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                retryLoadPendingWorlds();
            }, 100L); // 5秒 = 100 ticks
        }
    }

    /**
     * 🔧 新增：重试加载待处理的世界数据
     */
    private void retryLoadPendingWorlds() {
        if (pendingWorldLoads.isEmpty()) {
            return;
        }

        plugin.getLogger().info("正在重试加载撤离点数据...");

        Map<String, String> stillPending = new HashMap<>();
        int successCount = 0;

        for (Map.Entry<String, String> entry : pendingWorldLoads.entrySet()) {
            String key = entry.getKey();
            String worldName = entry.getValue();

            org.bukkit.World world = plugin.getServer().getWorld(worldName);
            if (world != null) {
                // 世界已加载，重新加载对应的数据
                if (key.equals("final-destination")) {
                    loadFinalDestination(worldName);
                    successCount++;
                } else if (key.startsWith("zone-")) {
                    String zoneName = key.substring(5); // 移除 "zone-" 前缀
                    loadEvacuationZoneByName(zoneName);
                    successCount++;
                }
            } else {
                // 世界仍未加载，继续等待
                stillPending.put(key, worldName);
            }
        }

        // 更新待处理列表
        pendingWorldLoads.clear();
        pendingWorldLoads.putAll(stillPending);

        if (successCount > 0) {
            plugin.getLogger().info("成功重新加载了 " + successCount + " 个撤离点数据");
        }

        // 如果还有未加载的，再次延迟重试
        if (!pendingWorldLoads.isEmpty()) {
            plugin.getLogger().warning("仍有 " + pendingWorldLoads.size() + " 个撤离点数据等待世界加载");
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                retryLoadPendingWorlds();
            }, 200L); // 10秒后再次重试
        }
    }

    /**
     * 🔧 新增：重新加载最终撤离目标
     */
    private void loadFinalDestination(String worldName) {
        if (dataConfig.contains("final-destination")) {
            org.bukkit.World world = plugin.getServer().getWorld(worldName);
            if (world != null) {
                double x = dataConfig.getDouble("final-destination.x");
                double y = dataConfig.getDouble("final-destination.y");
                double z = dataConfig.getDouble("final-destination.z");
                float yaw = (float) dataConfig.getDouble("final-destination.yaw", 0.0);
                float pitch = (float) dataConfig.getDouble("final-destination.pitch", 0.0);
                finalDestination = new Location(world, x, y, z, yaw, pitch);
                plugin.getLogger().info("成功重新加载最终撤离目标: " + worldName);
            }
        }
    }

    /**
     * 🔧 新增：按名称重新加载撤离区域
     */
    private void loadEvacuationZoneByName(String zoneName) {
        String path = "evacuation-zones." + zoneName;
        EvacuationZone zone = loadEvacuationZone(path);
        if (zone != null) {
            evacuationZones.put(zoneName, zone);
            plugin.getLogger().info("成功重新加载撤离区域: " + zoneName);
        }
    }
}
