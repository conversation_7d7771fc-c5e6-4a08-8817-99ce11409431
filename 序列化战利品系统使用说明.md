# 🎁 序列化战利品系统使用说明

## 🌟 **系统概述**

全新的序列化战利品系统完美解决了模组物品显示马赛克的问题！通过完整的ItemStack序列化技术，可以100%保留模组物品的所有数据，包括NBT标签、自定义属性等。

## ✨ **核心特性**

### 🔥 **完美模组物品支持**
- ✅ **完整序列化**: 将ItemStack完整序列化为Base64字符串
- ✅ **NBT保留**: 保留所有模组物品的NBT数据和自定义属性
- ✅ **真实显示**: 在GUI中显示真实的模组物品，不再有马赛克
- ✅ **数据完整**: 包括物品名称、Lore、附魔、耐久度等所有信息

### 📁 **配置文件存储**
- 📄 **独立配置**: 使用`serialized_treasures.yml`独立存储
- 🔒 **数据安全**: Base64编码确保数据完整性
- 🔄 **热重载**: 支持配置文件热重载
- 💾 **自动备份**: 自动处理配置文件错误和备份

## 🎮 **使用方法**

### 📋 **基础命令**
```
/evac sgui          - 打开序列化战利品管理界面
/evac serialized    - 同上（别名命令）
```

### 🖱️ **GUI操作指南**

#### 🎯 **添加新战利品**
1. **准备物品**: 将要添加的物品（包括模组物品）拿在手中
2. **点击添加**: 点击界面中的绿色绿宝石按钮"添加新战利品"
3. **自动保存**: 系统会自动生成唯一ID并保存物品的完整数据

#### ⚙️ **管理现有战利品**
- **左键点击**: 编辑战利品属性（开发中）
- **右键点击**: 删除战利品
- **Shift+左键**: 切换启用/禁用状态

#### 🔧 **控制按钮**
- **📄 保存配置**: 手动保存所有更改
- **📖 重载配置**: 重新加载配置文件
- **⬅️➡️ 翻页**: 浏览多页战利品

## 📊 **战利品属性**

### 🎲 **基础属性**
- **ID**: 唯一标识符（自动生成）
- **概率**: 出现概率（0-100%）
- **搜索速度**: 搜索耗时（1-30秒）
- **分类**: 战利品分类
- **状态**: 启用/禁用

### 🏷️ **显示信息**
每个战利品在GUI中会显示：
```
原始物品信息（包括模组物品的完整显示）
§7§m------------------------
§e战利品ID: example_item
§e概率: 10.0%
§e搜索速度: 3秒
§e分类: default
§e状态: §a启用
§d✨ 模组物品 (如果是模组物品)
§7§m------------------------
§a左键: 编辑战利品
§c右键: 删除战利品
§eShift+左键: 切换启用状态
```

## 🔧 **配置文件结构**

### 📁 **serialized_treasures.yml**
```yaml
treasures:
  example_item:
    serialized_item: "rO0ABXNyABpvcmcuYnVra2l0..." # Base64序列化数据
    chance: 10.0
    search_speed: 3
    commands: []
    category: "default"
    enabled: true
    created_time: 1234567890123
    description: "DIAMOND_SWORD x1 (§b传说之剑)"
```

## 🎯 **模组物品支持**

### ✅ **支持的模组物品类型**
- 🗡️ **武器装备**: 模组武器、盔甲、工具
- 🧪 **特殊物品**: 药水、食物、材料
- 🔮 **魔法物品**: 带有特殊NBT的物品
- 🏗️ **建筑方块**: 模组方块和装饰品
- ⚡ **机械物品**: 工业模组的机器和组件

### 🔍 **检测机制**
系统会自动检测模组物品：
- 📏 **序列化长度**: 复杂NBT数据通常序列化后较长
- 🏷️ **NBT标签**: 检查是否包含模组特有的NBT数据
- 🎨 **显示标记**: 在GUI中用"§d✨ 模组物品"标识

## 🚀 **技术优势**

### 💪 **vs 传统方式**
| 特性 | 传统方式 | 序列化方式 |
|------|----------|------------|
| 模组物品支持 | ❌ 马赛克显示 | ✅ 完美显示 |
| NBT数据保留 | ❌ 部分丢失 | ✅ 100%保留 |
| 自定义属性 | ❌ 不支持 | ✅ 完全支持 |
| 配置复杂度 | 🔶 需要手动配置 | ✅ 自动处理 |
| 兼容性 | 🔶 版本敏感 | ✅ 通用兼容 |

### 🛡️ **安全特性**
- **数据完整性**: Base64编码确保数据不被破坏
- **错误恢复**: 自动处理序列化失败的情况
- **备份机制**: 配置文件错误时自动备份
- **版本兼容**: 支持1.8-1.21.x全版本

## 📝 **使用示例**

### 🎮 **添加模组武器**
1. 获取一把模组武器（如：热力膨胀的扳手）
2. 拿在手中，执行`/evac sgui`
3. 点击"添加新战利品"按钮
4. 系统自动保存，武器完整信息被保留

### 🔧 **管理战利品**
1. 在GUI中找到要修改的战利品
2. 右键删除不需要的物品
3. Shift+左键切换启用状态
4. 点击保存按钮确保更改生效

## ⚠️ **注意事项**

### 📋 **使用建议**
- 🎯 **权限要求**: 需要`evacuation.admin`权限
- 💾 **定期保存**: 重要更改后及时点击保存按钮
- 🔄 **重载谨慎**: 重载会覆盖未保存的更改
- 📊 **性能考虑**: 大量战利品可能影响GUI加载速度

### 🐛 **故障排除**
- **序列化失败**: 检查物品是否为有效的ItemStack
- **GUI不显示**: 确认权限和命令拼写
- **配置丢失**: 检查`serialized_treasures.yml`文件权限

## 🎊 **总结**

序列化战利品系统是模组物品管理的完美解决方案！通过先进的序列化技术，彻底解决了模组物品显示马赛克的问题，让你的服务器战利品系统更加完善和专业。

**🌟 立即体验**: `/evac sgui` 开启全新的战利品管理体验！
