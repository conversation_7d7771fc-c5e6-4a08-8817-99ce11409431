# 自定义音效配置指南

## 🎵 **音效系统概述**

HangEvacuation 插件提供了完整的自定义音效系统，让您可以为摸金箱的各种操作配置不同的音效。

## 🔧 **配置位置**

音效配置位于 `config.yml` 文件中的 `treasure-chest.animation.sounds` 部分：

```yaml
treasure-chest:
  animation:
    sounds:
      # 各种音效配置...
```

## 🎯 **支持的音效类型**

### **1. 搜索开始音效 (search-start)**
- **触发时机**：每次开始搜索物品时播放
- **默认音效**：`entity.experience_orb.pickup`
- **用途**：提示玩家搜索已开始

```yaml
search-start:
  enabled: true
  sound: "entity.experience_orb.pickup"
  volume: 0.5
  pitch: 1.0
```

### **2. 搜索成功音效 (search-success)**
- **触发时机**：物品搜索完成时播放
- **默认音效**：`entity.player.levelup`
- **用途**：庆祝找到物品

```yaml
search-success:
  enabled: true
  sound: "entity.player.levelup"
  volume: 0.8
  pitch: 1.2
```

### **3. 搜索进行中音效 (search-progress)** 🆕
- **触发时机**：进度条更新时播放（可选）
- **默认音效**：`block.note_block.pling`
- **用途**：进度反馈音效

```yaml
search-progress:
  enabled: false  # 默认禁用，避免过于频繁
  sound: "block.note_block.pling"
  volume: 0.3
  pitch: 1.5
  interval: 3  # 每隔3次进度更新播放一次
```

### **4. 摸金箱打开音效 (chest-open)** 🆕
- **触发时机**：打开摸金箱GUI时播放
- **默认音效**：`block.chest.open`
- **用途**：模拟箱子打开声音

```yaml
chest-open:
  enabled: true
  sound: "block.chest.open"
  volume: 1.0
  pitch: 1.2
```

### **5. 摸金箱关闭音效 (chest-close)** 🆕
- **触发时机**：关闭摸金箱GUI时播放
- **默认音效**：`block.chest.close`
- **用途**：模拟箱子关闭声音

```yaml
chest-close:
  enabled: true
  sound: "block.chest.close"
  volume: 0.8
  pitch: 1.0
```

### **6. 物品拾取音效 (item-pickup)** 🆕
- **触发时机**：玩家拿取物品时播放
- **默认音效**：`entity.item.pickup`
- **用途**：确认物品已拾取

```yaml
item-pickup:
  enabled: true
  sound: "entity.item.pickup"
  volume: 0.6
  pitch: 1.3
```

### **7. 搜索失败音效 (search-fail)** 🆕
- **触发时机**：搜索失败时播放（可选功能）
- **默认音效**：`entity.villager.no`
- **用途**：表示搜索未找到物品

```yaml
search-fail:
  enabled: false  # 默认禁用
  sound: "entity.villager.no"
  volume: 0.4
  pitch: 0.8
```

## 🎛️ **音效参数说明**

### **enabled (布尔值)**
- `true`：启用此音效
- `false`：禁用此音效

### **sound (字符串)**
- 音效名称，支持原版和模组音效
- 格式：`命名空间:音效名称`
- 例如：`minecraft:entity.player.levelup`

### **volume (浮点数)**
- 音量大小，范围：0.0 - 2.0
- `0.0`：静音
- `1.0`：正常音量
- `2.0`：最大音量

### **pitch (浮点数)**
- 音调高低，范围：0.5 - 2.0
- `0.5`：最低音调
- `1.0`：正常音调
- `2.0`：最高音调

### **interval (整数)** - 仅限 search-progress
- 播放间隔，每隔多少次进度更新播放一次
- `0`：每次都播放
- `3`：每隔3次播放一次

## 🎼 **常用音效推荐**

### **成功类音效**
```yaml
# 升级音效
sound: "entity.player.levelup"

# 经验球音效
sound: "entity.experience_orb.pickup"

# 铃铛音效
sound: "block.note_block.bell"

# 钟声音效
sound: "block.note_block.chime"
```

### **提示类音效**
```yaml
# 点击音效
sound: "ui.button.click"

# 音符盒音效
sound: "block.note_block.pling"

# 弹出音效
sound: "entity.item.pickup"
```

### **容器类音效**
```yaml
# 箱子音效
sound: "block.chest.open"
sound: "block.chest.close"

# 潜影盒音效
sound: "block.shulker_box.open"
sound: "block.shulker_box.close"

# 桶音效
sound: "block.barrel.open"
sound: "block.barrel.close"
```

### **失败类音效**
```yaml
# 村民拒绝音效
sound: "entity.villager.no"

# 失败音效
sound: "block.note_block.bass"

# 错误音效
sound: "entity.enderman.teleport"
```

## 🔧 **模组音效支持**

插件支持模组音效，只需要使用正确的音效名称：

```yaml
# 模组音效示例
sound: "modname:custom_sound"
sound: "industrialcraft:machine_sound"
sound: "thermalexpansion:device_sound"
```

## 🎯 **实用配置示例**

### **静音配置**
```yaml
# 禁用所有音效
search-start:
  enabled: false
search-success:
  enabled: false
chest-open:
  enabled: false
chest-close:
  enabled: false
item-pickup:
  enabled: false
```

### **低音量配置**
```yaml
# 所有音效设为低音量
search-start:
  volume: 0.2
search-success:
  volume: 0.3
chest-open:
  volume: 0.4
```

### **高音调配置**
```yaml
# 所有音效设为高音调
search-start:
  pitch: 1.5
search-success:
  pitch: 1.8
item-pickup:
  pitch: 2.0
```

## ⚠️ **注意事项**

1. **版本兼容性**：某些音效在不同Minecraft版本中可能不存在
2. **模组依赖**：模组音效需要对应模组已安装
3. **性能影响**：过于频繁的音效可能影响性能
4. **玩家体验**：建议测试音效组合的整体效果

## 🔄 **重载配置**

修改音效配置后，使用以下命令重载：

```
/evac reload
```

配置将立即生效，无需重启服务器。
