===============================================
        HangEvacuation 日志输出优化 - v1.7.0
===============================================

🎮 插件名称: HangEvacuation (Hang摸金撤离)
📅 优化日期: 2024-12-19
🔧 版本号: 1.7.0
👨‍💻 作者: hangzong(航总)
📞 技术支持: 微信 hang060217

===============================================
              🐛 优化的问题
===============================================

❌ **问题1：物品列表加载信息刷屏**
- 现象：12个物品的详细信息占用大量日志空间
- 影响：日志难以阅读，重要信息被淹没

❌ **问题2：日志顺序混乱**
- 现象：物品加载信息在插件启用信息之前显示
- 影响：启动信息不够整洁，顺序不合理

❌ **问题3：信息冗余**
- 现象：每个物品的详细信息都显示
- 影响：对普通用户来说信息过多

===============================================
              ✅ 优化方案
===============================================

🎯 **解决方案1：简化日志输出**

📋 **优化前**：
```
[HangEvacuation] 加载的物品列表：
[HangEvacuation]   - iron_ingot: IRON_INGOT (概率: 0.15%)
[HangEvacuation]   - diamond: DIAMOND (概率: 0.05%)
[HangEvacuation]   - blaze_rod: BLAZE_ROD (概率: 0.08%)
[HangEvacuation]   - gold_ingot: GOLD_INGOT (概率: 0.12%)
[HangEvacuation]   - exp_bottle: EXPERIENCE_BOTTLE (概率: 0.1%)
[HangEvacuation]   - ender_pearl: ENDER_PEARL (概率: 0.06%)
[HangEvacuation]   - coal: COAL (概率: 0.2%)
[HangEvacuation]   - lapis: LAPIS_LAZULI (概率: 0.15%)
[HangEvacuation]   - emerald: EMERALD (概率: 0.08%)
[HangEvacuation]   - redstone: REDSTONE (概率: 0.18%)
[HangEvacuation]   - diamond_sword: DIAMOND_SWORD (概率: 0.02%)
[HangEvacuation]   - empty: AIR (概率: 0.25%)
```

📋 **优化后**：
```
[HangEvacuation] === HangEvacuation 1.7.0-1.8.8-1.21.4 已启用! ===
[HangEvacuation] 当前服务端: Paper | 支持版本: 1.8.8-1.21.4
[HangEvacuation] 已加载 0 个撤离区域
[HangEvacuation] 已加载 12 个摸金箱物品
[HangEvacuation] 已加载 2 个模组物品
[HangEvacuation] 已加载 10 个等级配置
[HangEvacuation] 已创建摸金箱数据文件: chests.yml
[HangEvacuation] 摸金箱系统已开启 | 撤离系统已开启 | 等级系统已开启 | 模组物品支持已开启 | NMS适配器已启用
[HangEvacuation] 作者: hangzong(航总) | 如需技术支持请加V: hang060217
[HangEvacuation] 交流Q群: 361919269 | Hang系列插件
```

🎯 **解决方案2：添加调试模式**

📋 **技术实现**：
```java
// 只在调试模式下显示详细物品列表
if (plugin.getConfig().getBoolean("debug.enabled", false)) {
    plugin.getLogger().info("加载的物品列表：");
    for (String itemId : treasureItems.keySet()) {
        TreasureItem item = treasureItems.get(itemId);
        plugin.getLogger().info("  - " + itemId + ": " + 
            item.getMaterial().name() + " (概率: " + item.getChance() + "%)");
    }
}
```

📋 **配置文件**：
```yaml
# config.yml
debug:
  # 是否启用调试模式（显示详细的加载信息）
  enabled: false
```

===============================================
              🔧 技术细节
===============================================

📊 **修改的文件**

1. **TreasureItemManager.java**
   - 移除构造函数中的详细日志输出
   - 添加调试模式控制的详细日志
   - 保留错误警告信息

2. **config.yml**
   - 新增 debug.enabled 配置项
   - 默认关闭调试模式

🛡️ **保留的重要日志**
- 插件启用信息
- 各种数据统计（物品数量、等级数量等）
- 错误和警告信息
- 功能启用状态

🔧 **调试模式功能**
- 当 `debug.enabled: true` 时显示详细物品列表
- 当 `debug.enabled: false` 时只显示统计信息
- 管理员可以根据需要开启调试模式

===============================================
              📋 日志输出对比
===============================================

🔄 **启动日志对比**

📊 **优化前（刷屏）**：
```
[HangEvacuation] 正在加载NMS适配器: com.hang.plugin.nms.versions.NMSAdapter_1_8_R3
[HangEvacuation] NMS适配器加载成功: 通用兼容适配器 (1.8-1.21.4)
[HangEvacuation] 服务器信息: Minecraft 1.21.1 (NMS: craftbukkit)
[HangEvacuation] 服务器类型: Paper
[HangEvacuation] 已加载 12 个摸金箱物品
[HangEvacuation] 加载的物品列表：
[HangEvacuation]   - iron_ingot: IRON_INGOT (概率: 0.15%)
[HangEvacuation]   - diamond: DIAMOND (概率: 0.05%)
[HangEvacuation]   - blaze_rod: BLAZE_ROD (概率: 0.08%)
[HangEvacuation]   - gold_ingot: GOLD_INGOT (概率: 0.12%)
[HangEvacuation]   - exp_bottle: EXPERIENCE_BOTTLE (概率: 0.1%)
[HangEvacuation]   - ender_pearl: ENDER_PEARL (概率: 0.06%)
[HangEvacuation]   - coal: COAL (概率: 0.2%)
[HangEvacuation]   - lapis: LAPIS_LAZULI (概率: 0.15%)
[HangEvacuation]   - emerald: EMERALD (概率: 0.08%)
[HangEvacuation]   - redstone: REDSTONE (概率: 0.18%)
[HangEvacuation]   - diamond_sword: DIAMOND_SWORD (概率: 0.02%)
[HangEvacuation]   - empty: AIR (概率: 0.25%)
[HangEvacuation] 摸金箱物品管理器初始化完成
[HangEvacuation] 已创建摸金箱种类配置文件: mojin.yml
[HangEvacuation] 已加载 6 个摸金箱种类
[HangEvacuation] === HangEvacuation 1.6.0-1.8.8-1.21.4 已启用! ===
[HangEvacuation] 当前服务端: Paper | 支持版本: 1.8.8-1.21.4
[HangEvacuation] 已加载 0 个撤离区域
[HangEvacuation] 已加载 12 个摸金箱物品
[HangEvacuation] 已加载 2 个模组物品
[HangEvacuation] 已加载 10 个等级配置
[HangEvacuation] 已创建摸金箱数据文件: chests.yml
[HangEvacuation] 摸金箱系统已开启 | 撤离系统已开启 | 等级系统已开启 | 模组物品支持已开启 | NMS适配器已启用
[HangEvacuation] 作者: hangzong(航总) | 如需技术支持请加V: hang060217
[HangEvacuation] 交流Q群: 361919269 | Hang系列插件
```

✅ **优化后（简洁）**：
```
[HangEvacuation] 正在加载NMS适配器: com.hang.plugin.nms.versions.NMSAdapter_1_8_R3
[HangEvacuation] NMS适配器加载成功: 通用兼容适配器 (1.8-1.21.4)
[HangEvacuation] 服务器信息: Minecraft 1.21.1 (NMS: craftbukkit)
[HangEvacuation] 服务器类型: Paper
[HangEvacuation] 已创建摸金箱种类配置文件: mojin.yml
[HangEvacuation] 已加载 6 个摸金箱种类
[HangEvacuation] === HangEvacuation 1.7.0-1.8.8-1.21.4 已启用! ===
[HangEvacuation] 当前服务端: Paper | 支持版本: 1.8.8-1.21.4
[HangEvacuation] 已加载 0 个撤离区域
[HangEvacuation] 已加载 12 个摸金箱物品
[HangEvacuation] 已加载 2 个模组物品
[HangEvacuation] 已加载 10 个等级配置
[HangEvacuation] 已创建摸金箱数据文件: chests.yml
[HangEvacuation] 摸金箱系统已开启 | 撤离系统已开启 | 等级系统已开启 | 模组物品支持已开启 | NMS适配器已启用
[HangEvacuation] 作者: hangzong(航总) | 如需技术支持请加V: hang060217
[HangEvacuation] 交流Q群: 361919269 | Hang系列插件
```

===============================================
              🎮 使用说明
===============================================

🔧 **普通使用（默认）**
- 启动时只显示统计信息
- 日志简洁清晰
- 重要信息突出显示

🔍 **调试模式**
- 在 config.yml 中设置 `debug.enabled: true`
- 重启服务器或使用 `/evac reload`
- 显示详细的物品加载信息

📋 **配置示例**
```yaml
# config.yml
debug:
  enabled: true  # 开启调试模式
```

🎯 **适用场景**
- **普通用户**：使用默认设置，日志简洁
- **开发者/管理员**：开启调试模式，查看详细信息
- **问题排查**：临时开启调试模式定位问题

===============================================
              📊 优化效果
===============================================

📈 **日志行数对比**
- **优化前**：启动时约25行日志
- **优化后**：启动时约15行日志
- **减少**：约40%的日志输出

🎯 **可读性提升**
- 重要信息更突出
- 启动信息更整洁
- 调试信息可控制

⚡ **性能影响**
- 减少日志I/O操作
- 降低日志文件大小
- 提升启动速度

===============================================
              ❓ 常见问题
===============================================

Q: 如何查看详细的物品加载信息？
A: 在 config.yml 中设置 debug.enabled: true，然后重启服务器

Q: 调试模式会影响性能吗？
A: 调试模式只影响日志输出，对游戏性能无影响

Q: 可以只开启部分调试信息吗？
A: 目前是全局调试开关，未来可能会添加更细粒度的控制

Q: 错误信息还会显示吗？
A: 是的，错误和警告信息始终显示，不受调试模式影响

===============================================
              🔧 技术支持
===============================================

🎮 如有问题，请联系：
- 微信: hang060217
- QQ群: 361919269
- 作者: hangzong(航总)
- 标签: Hang系列插件

💡 建议：
- 普通使用保持调试模式关闭
- 遇到问题时临时开启调试模式
- 定期检查日志文件大小
