#!/bin/bash

echo "正在编译hangevacuation插件..."
echo

# 检查Maven是否安装
if ! command -v mvn &> /dev/null; then
    echo "错误: 未找到Maven，请确保Maven已安装并添加到PATH环境变量中"
    exit 1
fi

# 清理并编译项目
echo "清理项目..."
mvn clean

echo
echo "编译项目..."
mvn package

if [ $? -eq 0 ]; then
    echo
    echo "================================"
    echo "编译成功！"
    echo "插件文件位置: target/HangEvacuation-Universal-1.7.0.jar"
    echo "================================"
    echo
    echo "将jar文件复制到服务器的plugins文件夹中即可使用"
else
    echo
    echo "================================"
    echo "编译失败！请检查错误信息"
    echo "================================"
    exit 1
fi
