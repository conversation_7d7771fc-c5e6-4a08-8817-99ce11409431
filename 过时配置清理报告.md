# 过时配置清理报告

## 🧹 **清理内容**

成功删除了 `treasure_items.yml` 中的过时配置项，这些配置项现在都没有用了：

### **删除的配置项**

```yaml
# 🗑️ 已删除的过时配置
chest_settings:
  # 默认槽位数量 (已弃用 - 现在在 mojin.yml 中每个摸金箱种类单独配置)
  slots: 5
  # 默认搜索冷却时间 (秒) - 手动点击搜索后的冷却时间，防止频繁点击
  # 注意：在自动搜索模式下，此设置基本不起作用
  search_cooldown: 1
  # 是否启用浮空字提示 (全局设置，仍在使用)
  hologram_enabled: true
```

## 📋 **配置迁移对照表**

| 配置项 | 原位置 | 新位置 | 状态 |
|--------|--------|--------|------|
| 槽位数量 | `treasure_items.yml` → `chest_settings.slots` | `mojin.yml` → 每个摸金箱种类单独配置 | ✅ 已迁移 |
| 搜索冷却时间 | `treasure_items.yml` → `chest_settings.search_cooldown` | `config.yml` → `treasure-chest.search-cooldown` | ✅ 已迁移 |
| 浮空字启用 | `treasure_items.yml` → `chest_settings.hologram_enabled` | `config.yml` → `treasure-chest.hologram_enabled` | ✅ 已迁移 |

## 🎯 **现在的正确配置位置**

### **1. 主配置文件 (config.yml)**
```yaml
treasure-chest:
  # 搜索冷却时间 (秒) - 手动点击搜索的冷却时间
  search-cooldown: 1
  # 是否启用浮空字提示 (全局设置)
  hologram_enabled: true
  # 其他全局设置...
```

### **2. 摸金箱种类配置 (mojin.yml)**
```yaml
# 每个摸金箱种类都有独立的槽位配置
common:
  name: "普通摸金箱"
  slots: 5  # 槽位数量在这里配置

weapon:
  name: "武器箱"
  slots: 6  # 每种箱子可以有不同的槽位数量

# 其他种类...
```

### **3. 物品配置文件 (treasure_items.yml)**
```yaml
# 现在只包含物品配置，不再有全局设置
items:
  diamond:
    material: DIAMOND
    probability: 0.05
    search_speed: 8  # 每个物品的搜索时间
    # 其他物品属性...

# 注意说明
# - 搜索时间: 在上面的 items 配置中每个物品单独设置 search_speed
# - 刷新时间: 在主配置文件 config.yml 中的 treasure-chest.refresh-time 配置
# - 搜索冷却时间: 在主配置文件 config.yml 中的 treasure-chest.search-cooldown 配置
# - 浮空字启用: 在主配置文件 config.yml 中的 treasure-chest.hologram_enabled 配置
# - 槽位数量: 在 mojin.yml 中每个摸金箱种类单独配置
```

## 🔄 **配置文件职责分工**

### **config.yml - 主配置文件**
- ✅ 全局功能开关
- ✅ 系统级设置
- ✅ 消息配置
- ✅ 撤离系统配置
- ✅ 摸金箱全局设置

### **treasure_items.yml - 物品配置文件**
- ✅ 物品定义和属性
- ✅ 物品概率和搜索时间
- ✅ 物品分类和适用箱子
- ❌ ~~全局设置~~ (已移除)

### **mojin.yml - 摸金箱种类配置**
- ✅ 摸金箱种类定义
- ✅ 每种箱子的槽位数量
- ✅ 每种箱子的刷新时间
- ✅ 箱子名称和显示

## 📝 **清理后的优势**

### **1. 配置结构更清晰**
- 每个配置文件职责明确
- 避免配置项重复和冲突
- 更容易维护和理解

### **2. 避免混淆**
- 用户不会在错误的文件中寻找配置
- 减少配置错误的可能性
- 提高配置的一致性

### **3. 更好的扩展性**
- 新功能配置有明确的归属
- 便于添加新的配置项
- 保持向后兼容性

## 🚀 **用户指导**

### **如果您之前在 treasure_items.yml 中配置了这些项**

**旧配置 (已无效)**：
```yaml
# treasure_items.yml
chest_settings:
  slots: 8
  search_cooldown: 3
  hologram_enabled: false
```

**新配置 (正确位置)**：
```yaml
# config.yml
treasure-chest:
  search-cooldown: 3
  hologram_enabled: false

# mojin.yml
common:
  slots: 8
weapon:
  slots: 8
# 其他种类也需要设置...
```

### **配置检查清单**

- ✅ 检查 `config.yml` 中的 `treasure-chest` 配置
- ✅ 检查 `mojin.yml` 中每个摸金箱种类的 `slots` 配置
- ✅ 删除 `treasure_items.yml` 中的 `chest_settings` 部分（如果存在）
- ✅ 重启服务器使配置生效

## 🎉 **总结**

此次清理删除了 `treasure_items.yml` 中的过时配置项，使配置文件结构更加清晰和合理。现在每个配置文件都有明确的职责，用户可以更容易地找到和修改相应的配置项。

**配置清理已完成，文件结构更加整洁！**
