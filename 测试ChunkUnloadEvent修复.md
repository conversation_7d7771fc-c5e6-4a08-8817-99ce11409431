# 🧪 ChunkUnloadEvent StackOverflowError 修复测试指南

## 📋 **测试目标**

验证ChunkUnloadEvent的StackOverflowError修复是否有效，确保：
1. ✅ 不再出现StackOverflowError
2. ✅ 浮空字备份功能正常工作
3. ✅ 服务器性能稳定
4. ✅ 区块加载/卸载正常处理

## 🔧 **测试环境准备**

### **1. 服务器配置**
- **版本**: Paper 1.12.2
- **内存**: 建议至少2GB
- **插件**: HangEvacuation v2.3.0（修复版本）

### **2. 配置文件设置**
```yaml
# config.yml
treasure-chest:
  hologram:
    chunk_protection:
      enabled: true
      player_detection_range: 64.0

debug:
  enabled: true  # 测试期间启用调试
  hologram_rebuild_logs:
    chunk_load: true
    chunk_unload: true
    update_task: false
    periodic_check: false
```

## 🎯 **测试步骤**

### **测试1: 基础功能验证**

1. **启动服务器**
   ```bash
   # 观察启动日志，确保没有错误
   tail -f logs/latest.log | grep -i "error\|exception\|stackoverflow"
   ```

2. **放置摸金箱**
   - 在不同区块中放置多个摸金箱
   - 确认浮空字正常显示

3. **触发区块卸载**
   - 远离摸金箱区域（超过64格）
   - 等待区块自然卸载

4. **返回区域**
   - 重新进入摸金箱区域
   - 确认浮空字重新显示

### **测试2: 压力测试**

1. **大量摸金箱测试**
   ```bash
   # 使用命令快速创建多个摸金箱
   /evac give [玩家] treasure_chest_common 64
   ```

2. **快速移动测试**
   - 使用传送命令快速在不同区域移动
   - 观察是否出现StackOverflowError

3. **多玩家测试**
   - 多个玩家同时在不同区域活动
   - 触发大量区块加载/卸载事件

### **测试3: 异常情况测试**

1. **服务器重载测试**
   ```bash
   /reload confirm
   ```

2. **插件重载测试**
   ```bash
   /plugman reload HangEvacuation
   ```

3. **内存压力测试**
   - 创建大量实体
   - 观察内存使用情况

## 📊 **预期结果**

### **正常情况**
- ✅ 服务器启动无错误
- ✅ 浮空字正常显示和隐藏
- ✅ 区块保护功能正常工作
- ✅ 日志中无StackOverflowError

### **日志示例**
```
[INFO] [HangEvacuation]: 插件启动完成
[INFO] [HangEvacuation]: 已清理 0 个残留浮空字实体
[INFO] [HangEvacuation]: 区块卸载时备份了 3 个浮空字 (区块: -55,-103)
[INFO] [HangEvacuation]: 区块加载时重建了 3 个浮空字 (区块: -55,-103)
```

## 🚨 **问题排查**

### **如果仍然出现StackOverflowError**

1. **检查配置**
   ```yaml
   treasure-chest:
     hologram:
       chunk_protection:
         enabled: false  # 临时禁用
   ```

2. **查看详细日志**
   ```bash
   # 启用详细调试
   debug:
     enabled: true
   ```

3. **检查插件冲突**
   - 禁用其他可能冲突的插件
   - 逐个启用确定冲突源

### **性能问题排查**

1. **TPS监控**
   ```bash
   /tps
   ```

2. **内存使用**
   ```bash
   /gc
   ```

3. **线程分析**
   - 使用JProfiler或类似工具
   - 检查是否有线程死锁

## 🔍 **测试检查点**

### **启动阶段**
- [ ] 插件正常加载
- [ ] 配置文件读取成功
- [ ] 浮空字管理器初始化
- [ ] 区块保护功能启用

### **运行阶段**
- [ ] 摸金箱放置正常
- [ ] 浮空字显示正确
- [ ] 区块卸载备份成功
- [ ] 区块加载重建成功

### **压力测试**
- [ ] 大量摸金箱处理正常
- [ ] 快速移动无异常
- [ ] 多玩家环境稳定
- [ ] 长时间运行无问题

## 📈 **性能基准**

### **修复前**
- ❌ StackOverflowError频繁出现
- ❌ 服务器可能崩溃
- ❌ TPS下降明显

### **修复后**
- ✅ 无StackOverflowError
- ✅ TPS保持稳定（>19.5）
- ✅ 内存使用正常
- ✅ 响应时间快速

## 🎯 **成功标准**

测试通过的标准：
1. **稳定性**: 连续运行24小时无StackOverflowError
2. **功能性**: 所有浮空字功能正常工作
3. **性能**: TPS保持在19.0以上
4. **兼容性**: 与其他插件无冲突

## 📝 **测试报告模板**

```
测试日期: [日期]
测试环境: Paper 1.12.2
插件版本: HangEvacuation v2.3.0

测试结果:
- 基础功能: [通过/失败]
- 压力测试: [通过/失败]
- 异常处理: [通过/失败]
- 性能表现: [TPS值]

发现问题:
[列出发现的问题]

建议:
[改进建议]
```

## 🔄 **持续监控**

修复部署后，建议持续监控：
1. **每日检查**: 服务器日志中的错误信息
2. **每周分析**: 性能数据和TPS趋势
3. **每月评估**: 插件稳定性和功能完整性

通过这些测试，我们可以确保ChunkUnloadEvent的StackOverflowError问题得到彻底解决！
