name: HangEvacuation
version: 2.4.0
description: Hang系列插件 - 付费版本 摸金箱系统插件，支持1.8.8-1.21.4，包含摸金箱搜索、等级系统、撤离点系统、CustomModelData动画等功能
author: hangzong
main: com.hang.plugin.HangPlugin
api-version: 1.13

# 软依赖 - 可选插件，如果存在则启用额外功能
softdepend: [PlaceholderAPI]

commands:
  evacuation:
    description: 摸金插件主命令
    usage: /evacuation <subcommand>
    permission: evacuation.use
  evac:
    description: 摸金插件统一命令
    usage: /evac <give|gui|reload>
    permission: evacuation.use
  license:
    description: 许可证管理命令
    usage: /license <status|set|verify|info|reload>
    permission: hang.admin

permissions:
  evacuation.use:
    description: 使用摸金插件基础功能
    default: true
  evacuation.chest:
    description: 使用摸金箱功能
    default: true
  evacuation.level:
    description: 使用等级系统功能
    default: true
  evacuation.evacuation:
    description: 使用撤离点功能
    default: true

  evacuation.admin:
    description: 管理摸金箱
    default: op
  evacuation.admin.level:
    description: 管理等级系统
    default: op
  evacuation.admin.evacuation:
    description: 管理撤离点系统
    default: op
  evacuation.reload:
    description: 重载插件配置
    default: op

  hang.admin:
    description: 许可证管理权限
    default: op
