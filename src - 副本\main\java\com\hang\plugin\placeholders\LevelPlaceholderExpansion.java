package com.hang.plugin.placeholders;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.manager.LevelManager;
import org.bukkit.entity.Player;

/**
 * 等级系统 PlaceholderAPI 扩展
 * 提供摸金等级相关的占位符
 *
 * <AUTHOR>
 * @version 1.8.5
 */
public class LevelPlaceholderExpansion {

    private final HangPlugin plugin;
    private Object actualExpansion; // 实际的PlaceholderExpansion对象
    
    public LevelPlaceholderExpansion(HangPlugin plugin) {
        this.plugin = plugin;

        // 尝试创建实际的PlaceholderExpansion对象
        try {
            actualExpansion = new ActualPlaceholderExpansion(plugin);
        } catch (NoClassDefFoundError | Exception e) {
            // PlaceholderAPI不存在或版本不兼容
            actualExpansion = null;
            plugin.getLogger().info("PlaceholderAPI不可用，占位符功能已禁用");
        }
    }
    
    public String getIdentifier() {
        return "evacuation";
    }
    
    public String getAuthor() {
        return "hangzong";
    }
    
    public String getVersion() {
        return plugin.getDescription().getVersion();
    }
    
    public boolean persist() {
        return true;
    }
    
    public boolean canRegister() {
        return true;
    }
    
    public boolean register() {
        if (actualExpansion != null) {
            try {
                return (Boolean) actualExpansion.getClass().getMethod("register").invoke(actualExpansion);
            } catch (Exception e) {
                plugin.getLogger().warning("PlaceholderAPI扩展注册失败: " + e.getMessage());
                return false;
            }
        }
        return false;
    }

    public void unregister() {
        if (actualExpansion != null) {
            try {
                actualExpansion.getClass().getMethod("unregister").invoke(actualExpansion);
            } catch (Exception e) {
                plugin.getLogger().warning("PlaceholderAPI扩展注销失败: " + e.getMessage());
            }
        }
    }
    


    /**
     * 处理占位符请求 (内部方法)
     */
    public String onPlaceholderRequest(Player player, String params) {
        if (player == null) {
            return "";
        }
        
        switch (params.toLowerCase()) {
            // ==================== 等级系统占位符 ====================
            case "level":
            case "level_number":
                return getLevelNumber(player);
                
            case "level_name":
                return getLevelName(player);
                
            case "level_color":
                return getLevelColor(player);
                
            case "level_colored":
            case "level_display":
                return getLevelColored(player);
                
            case "level_format":
            case "level_chat_format":
                return getLevelChatFormat(player);
                
            case "next_level":
            case "next_level_number":
                return getNextLevelNumber(player);
                
            case "next_level_name":
                return getNextLevelName(player);
                
            case "next_level_colored":
                return getNextLevelColored(player);
                
            case "next_level_required":
                return getNextLevelRequired(player);
                
            case "level_searches_needed":
            case "level_needed":
                return getLevelSearchesNeeded(player);
                
            case "level_progress":
            case "level_progress_percent":
                return getLevelProgressPercent(player);
                
            case "level_progress_bar":
                return getLevelProgressBar(player);
                
            case "max_level":
                return getMaxLevel();
                
            case "is_max_level":
                return getIsMaxLevel(player);
                
            case "searches":
            case "search_count":
                return getSearchCount(player);
                
            default:
                return null; // 未知占位符
        }
    }
    
    // ==================== 等级系统方法 ====================
    
    private String getLevelNumber(Player player) {
        if (plugin.getLevelManager() == null) return "1";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        return String.valueOf(playerData.getLevel());
    }
    
    private String getLevelName(Player player) {
        if (plugin.getLevelManager() == null) return "新手摸金者";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        LevelManager.LevelInfo levelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
        return levelInfo != null ? levelInfo.getName() : "未知等级";
    }
    
    private String getLevelColor(Player player) {
        if (plugin.getLevelManager() == null) return "§f";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        LevelManager.LevelInfo levelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
        return levelInfo != null ? levelInfo.getColor() : "§f";
    }
    
    private String getLevelColored(Player player) {
        if (plugin.getLevelManager() == null) return "§f新手摸金者";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        LevelManager.LevelInfo levelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
        return levelInfo != null ? (levelInfo.getColor() + levelInfo.getName()) : "§f未知等级";
    }
    
    private String getLevelChatFormat(Player player) {
        if (plugin.getLevelManager() == null) return "§7[§f新手摸金者§7]";
        return plugin.getLevelManager().getPlayerLevelDisplay(player);
    }
    
    private String getNextLevelNumber(Player player) {
        if (plugin.getLevelManager() == null) return "2";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        int nextLevel = playerData.getLevel() + 1;
        LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
        return nextLevelInfo != null ? String.valueOf(nextLevel) : "已达到最高等级";
    }
    
    private String getNextLevelName(Player player) {
        if (plugin.getLevelManager() == null) return "见习摸金者";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        int nextLevel = playerData.getLevel() + 1;
        LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
        return nextLevelInfo != null ? nextLevelInfo.getName() : "已达到最高等级";
    }
    
    private String getNextLevelColored(Player player) {
        if (plugin.getLevelManager() == null) return "§a见习摸金者";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        int nextLevel = playerData.getLevel() + 1;
        LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
        return nextLevelInfo != null ? (nextLevelInfo.getColor() + nextLevelInfo.getName()) : "§6已达到最高等级";
    }
    
    private String getNextLevelRequired(Player player) {
        if (plugin.getLevelManager() == null) return "10";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        int nextLevel = playerData.getLevel() + 1;
        LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
        return nextLevelInfo != null ? String.valueOf(nextLevelInfo.getRequiredSearches()) : "0";
    }
    
    private String getLevelSearchesNeeded(Player player) {
        if (plugin.getLevelManager() == null) return "10";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        int nextLevel = playerData.getLevel() + 1;
        LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
        if (nextLevelInfo != null) {
            int needed = nextLevelInfo.getRequiredSearches() - playerData.getSearches();
            return String.valueOf(Math.max(0, needed));
        }
        return "0";
    }
    
    private String getLevelProgressPercent(Player player) {
        if (plugin.getLevelManager() == null) return "0";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        LevelManager.LevelInfo currentLevelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
        int nextLevel = playerData.getLevel() + 1;
        LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
        
        if (currentLevelInfo == null || nextLevelInfo == null) {
            return "100";
        }
        
        int currentRequired = currentLevelInfo.getRequiredSearches();
        int nextRequired = nextLevelInfo.getRequiredSearches();
        int playerSearches = playerData.getSearches();
        
        if (playerSearches >= nextRequired) {
            return "100";
        }
        
        double progress = (double) (playerSearches - currentRequired) / (nextRequired - currentRequired) * 100;
        return String.valueOf((int) Math.max(0, progress));
    }

    private String getLevelProgressBar(Player player) {
        if (plugin.getLevelManager() == null) return "§7████████████████████";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        LevelManager.LevelInfo currentLevelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
        int nextLevel = playerData.getLevel() + 1;
        LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);

        if (currentLevelInfo == null || nextLevelInfo == null) {
            return "§a████████████████████";
        }

        int currentRequired = currentLevelInfo.getRequiredSearches();
        int nextRequired = nextLevelInfo.getRequiredSearches();
        int playerSearches = playerData.getSearches();

        if (playerSearches >= nextRequired) {
            return "§a████████████████████";
        }

        double progress = (double) (playerSearches - currentRequired) / (nextRequired - currentRequired);
        int filledBars = (int) (progress * 20);

        StringBuilder bar = new StringBuilder();
        bar.append("§b");  // 蓝色表示等级进度
        for (int i = 0; i < filledBars; i++) {
            bar.append("█");
        }
        bar.append("§7");
        for (int i = filledBars; i < 20; i++) {
            bar.append("█");
        }

        return bar.toString();
    }

    private String getMaxLevel() {
        if (plugin.getLevelManager() == null) return "10";
        return String.valueOf(plugin.getLevelManager().getMaxLevel());
    }

    private String getIsMaxLevel(Player player) {
        if (plugin.getLevelManager() == null) return "false";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        int maxLevel = plugin.getLevelManager().getMaxLevel();
        return String.valueOf(playerData.getLevel() >= maxLevel);
    }

    private String getSearchCount(Player player) {
        if (plugin.getLevelManager() == null) return "0";
        LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
        return String.valueOf(playerData.getSearches());
    }

    /**
     * 实际的PlaceholderExpansion实现类
     * 只有在PlaceholderAPI存在时才会被加载
     */
    private static class ActualPlaceholderExpansion extends me.clip.placeholderapi.expansion.PlaceholderExpansion {

        private final HangPlugin plugin;

        public ActualPlaceholderExpansion(HangPlugin plugin) {
            this.plugin = plugin;
        }

        @Override
        public String getIdentifier() {
            return "evacuation";
        }

        @Override
        public String getAuthor() {
            return "hangzong";
        }

        @Override
        public String getVersion() {
            return plugin.getDescription().getVersion();
        }

        @Override
        public boolean persist() {
            return true;
        }

        @Override
        public boolean canRegister() {
            return true;
        }

        @Override
        public String onRequest(org.bukkit.OfflinePlayer player, String params) {
            if (player == null || !player.isOnline()) {
                return "";
            }

            // 直接在这里处理占位符请求
            Player onlinePlayer = (Player) player.getPlayer();
            return handlePlaceholderRequest(onlinePlayer, params);
        }

        /**
         * 处理占位符请求的实际逻辑
         */
        private String handlePlaceholderRequest(Player player, String params) {
            if (player == null) {
                return "";
            }

            switch (params.toLowerCase()) {
                // ==================== 等级系统占位符 ====================
                case "level":
                case "level_number":
                    return getLevelNumber(player);

                case "level_name":
                    return getLevelName(player);

                case "level_color":
                    return getLevelColor(player);

                case "level_colored":
                case "level_display":
                    return getLevelColored(player);

                case "level_format":
                case "level_chat_format":
                    return getLevelChatFormat(player);

                case "next_level":
                case "next_level_number":
                    return getNextLevelNumber(player);

                case "next_level_name":
                    return getNextLevelName(player);

                case "next_level_colored":
                    return getNextLevelColored(player);

                case "next_level_required":
                    return getNextLevelRequired(player);

                case "level_searches_needed":
                case "level_needed":
                    return getLevelSearchesNeeded(player);

                case "level_progress":
                case "level_progress_percent":
                    return getLevelProgressPercent(player);

                case "level_progress_bar":
                    return getLevelProgressBar(player);

                case "max_level":
                    return getMaxLevel();

                case "is_max_level":
                    return getIsMaxLevel(player);

                case "searches":
                case "search_count":
                    return getSearchCount(player);

                default:
                    return null; // 未知占位符
            }
        }

        // ==================== 等级系统方法 ====================

        private String getLevelNumber(Player player) {
            if (plugin.getLevelManager() == null) return "1";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            return String.valueOf(playerData.getLevel());
        }

        private String getLevelName(Player player) {
            if (plugin.getLevelManager() == null) return "新手摸金者";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            LevelManager.LevelInfo levelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
            return levelInfo != null ? levelInfo.getName() : "未知等级";
        }

        private String getLevelColor(Player player) {
            if (plugin.getLevelManager() == null) return "§f";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            LevelManager.LevelInfo levelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
            return levelInfo != null ? levelInfo.getColor() : "§f";
        }

        private String getLevelColored(Player player) {
            if (plugin.getLevelManager() == null) return "§f新手摸金者";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            LevelManager.LevelInfo levelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
            return levelInfo != null ? (levelInfo.getColor() + levelInfo.getName()) : "§f未知等级";
        }

        private String getLevelChatFormat(Player player) {
            if (plugin.getLevelManager() == null) return "§7[§f新手摸金者§7]";
            return plugin.getLevelManager().getPlayerLevelDisplay(player);
        }

        private String getNextLevelNumber(Player player) {
            if (plugin.getLevelManager() == null) return "2";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            int nextLevel = playerData.getLevel() + 1;
            LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
            return nextLevelInfo != null ? String.valueOf(nextLevel) : "已达到最高等级";
        }

        private String getNextLevelName(Player player) {
            if (plugin.getLevelManager() == null) return "见习摸金者";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            int nextLevel = playerData.getLevel() + 1;
            LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
            return nextLevelInfo != null ? nextLevelInfo.getName() : "已达到最高等级";
        }

        private String getNextLevelColored(Player player) {
            if (plugin.getLevelManager() == null) return "§a见习摸金者";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            int nextLevel = playerData.getLevel() + 1;
            LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
            return nextLevelInfo != null ? (nextLevelInfo.getColor() + nextLevelInfo.getName()) : "§6已达到最高等级";
        }

        private String getNextLevelRequired(Player player) {
            if (plugin.getLevelManager() == null) return "10";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            int nextLevel = playerData.getLevel() + 1;
            LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
            return nextLevelInfo != null ? String.valueOf(nextLevelInfo.getRequiredSearches()) : "0";
        }

        private String getLevelSearchesNeeded(Player player) {
            if (plugin.getLevelManager() == null) return "10";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            int nextLevel = playerData.getLevel() + 1;
            LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);
            if (nextLevelInfo != null) {
                int needed = nextLevelInfo.getRequiredSearches() - playerData.getSearches();
                return String.valueOf(Math.max(0, needed));
            }
            return "0";
        }

        private String getLevelProgressPercent(Player player) {
            if (plugin.getLevelManager() == null) return "0";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            LevelManager.LevelInfo currentLevelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
            int nextLevel = playerData.getLevel() + 1;
            LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);

            if (currentLevelInfo == null || nextLevelInfo == null) {
                return "100";
            }

            int currentRequired = currentLevelInfo.getRequiredSearches();
            int nextRequired = nextLevelInfo.getRequiredSearches();
            int playerSearches = playerData.getSearches();

            if (playerSearches >= nextRequired) {
                return "100";
            }

            double progress = (double) (playerSearches - currentRequired) / (nextRequired - currentRequired) * 100;
            return String.valueOf((int) Math.max(0, progress));
        }

        private String getLevelProgressBar(Player player) {
            if (plugin.getLevelManager() == null) return "§7████████████████████";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            LevelManager.LevelInfo currentLevelInfo = plugin.getLevelManager().getAllLevels().get(playerData.getLevel());
            int nextLevel = playerData.getLevel() + 1;
            LevelManager.LevelInfo nextLevelInfo = plugin.getLevelManager().getAllLevels().get(nextLevel);

            if (currentLevelInfo == null || nextLevelInfo == null) {
                return "§a████████████████████";
            }

            int currentRequired = currentLevelInfo.getRequiredSearches();
            int nextRequired = nextLevelInfo.getRequiredSearches();
            int playerSearches = playerData.getSearches();

            if (playerSearches >= nextRequired) {
                return "§a████████████████████";
            }

            double progress = (double) (playerSearches - currentRequired) / (nextRequired - currentRequired);
            int filledBars = (int) (progress * 20);

            StringBuilder bar = new StringBuilder();
            bar.append("§b");  // 蓝色表示等级进度
            for (int i = 0; i < filledBars; i++) {
                bar.append("█");
            }
            bar.append("§7");
            for (int i = filledBars; i < 20; i++) {
                bar.append("█");
            }

            return bar.toString();
        }

        private String getMaxLevel() {
            if (plugin.getLevelManager() == null) return "10";
            return String.valueOf(plugin.getLevelManager().getMaxLevel());
        }

        private String getIsMaxLevel(Player player) {
            if (plugin.getLevelManager() == null) return "false";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            int maxLevel = plugin.getLevelManager().getMaxLevel();
            return String.valueOf(playerData.getLevel() >= maxLevel);
        }

        private String getSearchCount(Player player) {
            if (plugin.getLevelManager() == null) return "0";
            LevelManager.PlayerLevelData playerData = plugin.getLevelManager().getPlayerData(player.getUniqueId());
            return String.valueOf(playerData.getSearches());
        }
    }
}
