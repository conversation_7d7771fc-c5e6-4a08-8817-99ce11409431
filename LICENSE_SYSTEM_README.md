# HangEvacuation 许可证系统

## 概述

HangEvacuation 插件已成功集成了基于 Lukittu 许可证系统的在线验证功能。该系统确保只有拥有有效许可证的用户才能使用插件的完整功能。

## 系统特性

### 🔐 安全验证
- 基于 RSA 加密的挑战-响应验证机制
- 防重放攻击保护
- 设备指纹识别
- 在线实时验证

### 💓 心跳监控
- 每15分钟自动发送心跳包
- 维持许可证活跃状态
- 自动处理网络异常

### 🛡️ 许可证保护
- 设备数量限制
- IP地址限制
- 许可证过期检查
- 防止密钥共享

## 文件结构

```
src/main/java/com/hang/plugin/
├── license/
│   └── LicenseManager.java          # 许可证管理核心类
├── config/
│   └── LicenseConfig.java           # 许可证配置管理
├── commands/
│   └── LicenseCommand.java          # 许可证管理命令
└── HangPlugin.java                  # 主插件类（已集成许可证）

src/main/resources/
├── license.yml                      # 许可证配置文件模板
└── plugin.yml                       # 插件描述文件（已添加许可证命令）
```

## 配置文件

### license.yml
```yaml
# 许可证密钥配置
# 请将下方的空字符串替换为您的真实许可证密钥
# 示例: "857ff611-618f-40db-8fe2-291b388f0cf0"
# 如需购买许可证，请联系 QQ: **********
license-key: ""
```

## 使用方法

### 1. 首次启动
1. 启动服务器，插件会自动生成 `license.yml` 配置文件
2. 在 `license-key` 字段填入您的许可证密钥
3. 重启服务器或使用 `/evac reload` 重载配置

### 2. 许可证管理命令
```
/license status          # 查看许可证状态
/license set <key>       # 设置许可证密钥
/license verify          # 重新验证许可证
/license info            # 查看许可证详细信息
/license reload          # 重新加载许可证配置
```

### 3. 权限配置
- `hang.admin` - 许可证管理权限（默认：OP）

## 启动流程

### 成功启动
```
╔══════════════════════════════════════════════════════════════╗
║                    🔐 HangZong 许可证系统 🔐                 ║
╠══════════════════════════════════════════════════════════════╣
║  🚀 正在启动 HangZong 许可证验证...                          ║
║  🌐 验证服务器: cn.HangZong.com                              ║
║  📞 技术支持 QQ: **********                                   ║
╚══════════════════════════════════════════════════════════════╝

╔══════════════════════════════════════════════════════════════╗
║                    ✅ 许可证验证成功 ✅                      ║
╠══════════════════════════════════════════════════════════════╣
║  🎉 恭喜！您的许可证已通过 HangZong 服务器验证                ║
║  🔒 插件功能已解锁，可以正常使用                              ║
║  💓 心跳监控已启动，每15分钟自动验证                          ║
╚══════════════════════════════════════════════════════════════╝
```

### 验证失败
```
╔══════════════════════════════════════════════════════════════╗
║                    🚫 许可证验证失败 🚫                      ║
╠══════════════════════════════════════════════════════════════╣
║  ❌ 错误：未配置许可证密钥！                                  ║
║                                                              ║
║  📝 解决方案：                                               ║
║     请在 license.yml 中的 license-key 字段填写您的许可证密钥 ║
║                                                              ║
║  📋 示例配置：                                               ║
║     license-key: "857ff611-618f-40db-8fe2-291b388f0cf0"     ║
║                                                              ║
║  ⚠️  插件将被禁用，直到配置有效的许可证密钥                   ║
╚══════════════════════════════════════════════════════════════╝
```

## 技术实现

### 核心组件

1. **LicenseManager** - 许可证验证核心
   - 在线验证许可证密钥
   - RSA 签名验证
   - 心跳包发送
   - 设备指纹生成

2. **LicenseConfig** - 配置管理
   - 自动生成配置文件
   - 配置读取和保存
   - 兼容旧版本 Bukkit

3. **LicenseCommand** - 命令接口
   - 许可证状态查询
   - 密钥设置和验证
   - 详细信息显示

### 验证流程

1. **初始化阶段**
   - 读取许可证配置
   - 生成设备标识符
   - 创建随机挑战

2. **验证阶段**
   - 发送验证请求到许可证服务器
   - 验证服务器响应签名
   - 检查许可证有效性

3. **维护阶段**
   - 定期发送心跳包
   - 监控许可证状态
   - 处理网络异常

## 错误处理

### 常见错误及解决方案

1. **许可证密钥为空**
   - 在 `license.yml` 中填入有效密钥

2. **许可证验证失败**
   - 检查密钥是否正确
   - 确认网络连接正常
   - 联系技术支持

3. **网络连接问题**
   - 检查服务器网络设置
   - 确认可以访问许可证服务器

## 联系支持

- **技术支持 QQ**: **********
- **验证服务器**: cn.HangZong.com
- **开发者**: hangzong (航总)

## 注意事项

⚠️ **重要提醒**
- 请勿与他人分享您的许可证密钥
- 每个许可证密钥都有设备数量限制
- 许可证验证需要服务器能够访问互联网
- 必须输入有效的许可证密钥才能使用插件
- 如遇到验证问题，请及时联系技术支持

## 更新日志

### v1.0.0
- ✅ 集成 Lukittu 许可证系统
- ✅ 实现在线验证功能
- ✅ 添加心跳监控机制
- ✅ 创建许可证管理命令
- ✅ 自动生成配置文件
- ✅ 完善错误处理和用户提示
