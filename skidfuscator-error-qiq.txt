handler=Block #DK, types=[Ljava/lang/RuntimeException;], range=[Block #V, Block #U]
handler=Block #DO, types=[Ljava/lang/RuntimeException;], range=[Block #Y, Block #X]
handler=Block #DS, types=[Ljava/io/IOException;], range=[Block #AB, Block #AA]
handler=Block #DW, types=[Ljava/io/IOException;], range=[Block #AE, Block #AD]
handler=Block #EA, types=[Ljava/io/IOException;], range=[Block #AH, Block #AG]
handler=Block #EE, types=[Ljava/lang/IllegalAccessException;], range=[Block #AK, Block #AJ]
handler=Block #EI, types=[Ljava/io/IOException;], range=[Block #AN, Block #AM]
handler=Block #EM, types=[Ljava/io/IOException;], range=[Block #AQ, Block #AP]
handler=Block #EQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #AT, Block #AS]
===#Block A(size=7, flags=1)===
   0. lvar35 = {2107410273 ^ {726044778 ^ 138251246}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. synth(lvar2 = lvar2);
   4. synth(lvar3 = lvar3);
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -147003606)
      goto BA
   6. lvar35 = {472487845 ^ lvar35};
      -> ConditionalJump[IF_ICMPNE] #A -> #BA
      -> Immediate #A -> #B
===#Block B(size=5, flags=0)===
   0. lvar5 = lvar1;
   1. lvar6 = {1120799573 ^ lvar35};
   2. if (lvar5 >= lvar6)
      goto CX
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 376461826)
      goto BM
   4. lvar35 = {299726531 ^ lvar35};
      -> ConditionalJump[IF_ICMPGE] #B -> #CX
      -> ConditionalJump[IF_ICMPNE] #B -> #BM
      -> Immediate #B -> #C
      <- Immediate #A -> #B
===#Block C(size=9, flags=0)===
   0. lvar8 = lvar0;
   1. lvar9 = lvar8.currentPage;
   2. lvar26 = {1393786262 ^ lvar35};
   3. lvar10 = {lvar9 * lvar26};
   4. lvar27 = lvar1;
   5. lvar11 = {lvar10 + lvar27};
   6. lvar7 = lvar11;
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1734611942)
      goto BH
   8. lvar35 = {1572070028 ^ lvar35};
      -> ConditionalJump[IF_ICMPNE] #C -> #BH
      -> Immediate #C -> #D
      <- Immediate #B -> #C
===#Block D(size=7, flags=0)===
   0. lvar12 = lvar7;
   1. lvar28 = lvar0;
   2. lvar29 = lvar28.commands;
   3. lvar30 = lvar29.size();
   4. if (lvar12 >= lvar30)
      goto CV
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1963260024)
      goto AW
   6. lvar35 = {1187976190 ^ lvar35};
      -> ConditionalJump[IF_ICMPNE] #D -> #AW
      -> ConditionalJump[IF_ICMPGE] #D -> #CV
      -> Immediate #D -> #E
      <- Immediate #C -> #D
===#Block E(size=4, flags=0)===
   0. lvar13 = lvar2;
   1. if (lvar13 == {1215270129 ^ lvar35})
      goto CU
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1132226442)
      goto BA
   3. lvar35 = {1149352701 ^ lvar35};
      -> ConditionalJump[IF_ICMPEQ] #E -> #CU
      -> Immediate #E -> #I
      -> ConditionalJump[IF_ICMPNE] #E -> #BA
      <- Immediate #D -> #E
===#Block I(size=5, flags=0)===
   0. lvar17 = lvar0;
   1. lvar33 = lvar7;
   2. _consume(lvar17.deleteCommand(lvar33, 1912573988));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1735503968)
      goto BK
   4. goto CO
      -> UnconditionalJump[GOTO] #I -> #CO
      -> ConditionalJump[IF_ICMPNE] #I -> #BK
      <- Immediate #E -> #I
===#Block CO(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 151880793:
      	 goto	#CP
      case 644817111:
      	 goto	#AQ
      case 739241490:
      	 goto	#BH
      case 752401819:
      	 goto	#CO
      default:
      	 goto	#BH
   }
      -> Immediate #CO -> #CP
      -> Switch[151880793] #CO -> #CP
      -> Switch[644817111] #CO -> #AQ
      -> Switch[752401819] #CO -> #CO
      -> Switch[739241490] #CO -> #BH
      -> DefaultSwitch #CO -> #BH
      <- UnconditionalJump[GOTO] #I -> #CO
      <- Switch[752401819] #CO -> #CO
===#Block CP(size=2, flags=100)===
   0. lvar35 = {1641364791 ^ lvar35};
   1. goto AQ
      -> UnconditionalJump[GOTO] #CP -> #AQ
      <- Immediate #CO -> #CP
      <- Switch[151880793] #CO -> #CP
===#Block AQ(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 60950777)
      goto AP
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1775786459)
      goto BD
      -> ConditionalJump[IF_ICMPEQ] #AQ -> #AP
      -> ConditionalJump[IF_ICMPNE] #AQ -> #BD
      -> TryCatch range: [AQ...AP] -> EM ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #CP -> #AQ
      <- Switch[644817111] #CO -> #AQ
===#Block AP(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1099700736)
      goto BK
      -> ConditionalJump[IF_ICMPNE] #AP -> #BK
      -> TryCatch range: [AQ...AP] -> EM ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AQ -> #AP
===#Block EM(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case -1099700736:
      	 goto	#EO
      case 1775786459:
      	 goto	#EN
      default:
      	 goto	#EP
   }
      -> Switch[-1099700736] #EM -> #EO
      -> DefaultSwitch #EM -> #EP
      -> Switch[1775786459] #EM -> #EN
      <- TryCatch range: [AQ...AP] -> EM ([Ljava/io/IOException;])
      <- TryCatch range: [AQ...AP] -> EM ([Ljava/io/IOException;])
===#Block EN(size=2, flags=10100)===
   0. lvar35 = {87733680 ^ lvar35};
   1. goto AR
      -> UnconditionalJump[GOTO] #EN -> #AR
      <- Switch[1775786459] #EM -> #EN
===#Block EP(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #EM -> #EP
===#Block EO(size=2, flags=10100)===
   0. lvar35 = {2144322123 ^ lvar35};
   1. goto AR
      -> UnconditionalJump[GOTO] #EO -> #AR
      <- Switch[-1099700736] #EM -> #EO
===#Block AR(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1074656347)
      goto AV
   2. goto BV
      -> UnconditionalJump[GOTO] #AR -> #BV
      -> ConditionalJump[IF_ICMPNE] #AR -> #AV
      <- UnconditionalJump[GOTO] #EN -> #AR
      <- UnconditionalJump[GOTO] #EO -> #AR
===#Block BV(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 159582508:
      	 goto	#BW
      case 638103047:
      	 goto	#BV
      case 1498035525:
      	 goto	#BB
      case 1891240151:
      	 goto	#J
      default:
      	 goto	#BB
   }
      -> Immediate #BV -> #BW
      -> DefaultSwitch #BV -> #BB
      -> Switch[1891240151] #BV -> #J
      -> Switch[1498035525] #BV -> #BB
      -> Switch[638103047] #BV -> #BV
      -> Switch[159582508] #BV -> #BW
      <- UnconditionalJump[GOTO] #AR -> #BV
      <- Switch[638103047] #BV -> #BV
===#Block BW(size=2, flags=100)===
   0. lvar35 = {591783534 ^ lvar35};
   1. goto J
      -> UnconditionalJump[GOTO] #BW -> #J
      <- Immediate #BV -> #BW
      <- Switch[159582508] #BV -> #BW
===#Block BK(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1099700736)
      goto BK
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1724065265 ^ lvar35})
      goto BK
   2. _consume({379247165 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1735503968)
      goto BK
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {206475244 ^ lvar35})
      goto BK
   5. _consume({1041694174 ^ lvar35});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BK -> #BK
      <- ConditionalJump[IF_ICMPNE] #BK -> #BK
      <- ConditionalJump[IF_ICMPNE] #AP -> #BK
      <- ConditionalJump[IF_ICMPNE] #I -> #BK
===#Block CU(size=2, flags=10100)===
   0. lvar35 = {641364975 ^ lvar35};
   1. goto F
      -> UnconditionalJump[GOTO] #CU -> #F
      <- ConditionalJump[IF_ICMPEQ] #E -> #CU
===#Block F(size=5, flags=0)===
   0. // Frame: locals[1] [1] stack[0] []
   1. lvar14 = lvar3;
   2. if (lvar14 == {1851129630 ^ lvar35})
      goto CT
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1924135155)
      goto BC
   4. lvar35 = {1771267328 ^ lvar35};
      -> ConditionalJump[IF_ICMPNE] #F -> #BC
      -> Immediate #F -> #G
      -> ConditionalJump[IF_ICMPEQ] #F -> #CT
      <- UnconditionalJump[GOTO] #CU -> #F
===#Block CT(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 1413059055);
   1. goto H
      -> UnconditionalJump[GOTO] #CT -> #H
      <- ConditionalJump[IF_ICMPEQ] #F -> #CT
===#Block H(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar0;
   2. lvar32 = lvar7;
   3. _consume(lvar16.editCommand(lvar32, 929725430));
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -748456055)
      goto AV
   5. lvar35 = {1898460692 ^ lvar35};
      -> ConditionalJump[IF_ICMPNE] #H -> #AV
      -> Immediate #H -> #J
      <- UnconditionalJump[GOTO] #CT -> #H
===#Block G(size=5, flags=0)===
   0. lvar15 = lvar0;
   1. lvar31 = lvar7;
   2. _consume(lvar15.copyCommand(lvar31, 354346635));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1043648752)
      goto BB
   4. goto CQ
      -> UnconditionalJump[GOTO] #G -> #CQ
      -> ConditionalJump[IF_ICMPNE] #G -> #BB
      <- Immediate #F -> #G
===#Block CQ(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 122304442:
      	 goto	#CR
      case 1757080681:
      	 goto	#BC
      case 2025924066:
      	 goto	#CQ
      case 2087381950:
      	 goto	#AT
      default:
      	 goto	#BC
   }
      -> Immediate #CQ -> #CR
      -> Switch[2087381950] #CQ -> #AT
      -> Switch[2025924066] #CQ -> #CQ
      -> Switch[1757080681] #CQ -> #BC
      -> DefaultSwitch #CQ -> #BC
      -> Switch[122304442] #CQ -> #CR
      <- UnconditionalJump[GOTO] #G -> #CQ
      <- Switch[2025924066] #CQ -> #CQ
===#Block CR(size=2, flags=100)===
   0. lvar35 = {1367404386 ^ lvar35};
   1. goto AT
      -> UnconditionalJump[GOTO] #CR -> #AT
      <- Immediate #CQ -> #CR
      <- Switch[122304442] #CQ -> #CR
===#Block AT(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 120116710)
      goto AS
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1305236510)
      goto BJ
      -> ConditionalJump[IF_ICMPEQ] #AT -> #AS
      -> TryCatch range: [AT...AS] -> EQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPNE] #AT -> #BJ
      <- UnconditionalJump[GOTO] #CR -> #AT
      <- Switch[2087381950] #CQ -> #AT
===#Block BJ(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1305236510)
      goto BJ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1627264799 ^ lvar35})
      goto BJ
   2. _consume({9168962 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BJ -> #BJ
      <- ConditionalJump[IF_ICMPNE] #BJ -> #BJ
      <- ConditionalJump[IF_ICMPNE] #AT -> #BJ
===#Block AS(size=2, flags=0)===
   0. throw new java/lang/IllegalAccessException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -64092432)
      goto AY
      -> TryCatch range: [AT...AS] -> EQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPNE] #AS -> #AY
      <- ConditionalJump[IF_ICMPEQ] #AT -> #AS
===#Block EQ(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case -1305236510:
      	 goto	#ER
      case -64092432:
      	 goto	#ES
      default:
      	 goto	#ET
   }
      -> Switch[-1305236510] #EQ -> #ER
      -> Switch[-64092432] #EQ -> #ES
      -> DefaultSwitch #EQ -> #ET
      <- TryCatch range: [AT...AS] -> EQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AT...AS] -> EQ ([Ljava/lang/IllegalAccessException;])
===#Block ET(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #EQ -> #ET
===#Block ES(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 633869842);
   1. goto AU
      -> UnconditionalJump[GOTO] #ES -> #AU
      <- Switch[-64092432] #EQ -> #ES
===#Block ER(size=2, flags=10100)===
   0. lvar35 = {1812702128 ^ lvar35};
   1. goto AU
      -> UnconditionalJump[GOTO] #ER -> #AU
      <- Switch[-1305236510] #EQ -> #ER
===#Block AU(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -764496287)
      goto BF
   2. goto CF
      -> ConditionalJump[IF_ICMPNE] #AU -> #BF
      -> UnconditionalJump[GOTO] #AU -> #CF
      <- UnconditionalJump[GOTO] #ES -> #AU
      <- UnconditionalJump[GOTO] #ER -> #AU
===#Block CF(size=2, flags=10100)===
   0. lvar35 = {1896451625 ^ lvar35};
   1. goto J
      -> UnconditionalJump[GOTO] #CF -> #J
      <- UnconditionalJump[GOTO] #AU -> #CF
===#Block CV(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 207043213:
      	 goto	#CW
      case 1101915932:
      	 goto	#BA
      case 1768984082:
      	 goto	#J
      case 1896243549:
      	 goto	#CV
      default:
      	 goto	#BA
   }
      -> Switch[1768984082] #CV -> #J
      -> Switch[1101915932] #CV -> #BA
      -> DefaultSwitch #CV -> #BA
      -> Immediate #CV -> #CW
      -> Switch[1896243549] #CV -> #CV
      -> Switch[207043213] #CV -> #CW
      <- ConditionalJump[IF_ICMPGE] #D -> #CV
      <- Switch[1896243549] #CV -> #CV
===#Block CW(size=2, flags=100)===
   0. lvar35 = {1172621290 ^ lvar35};
   1. goto J
      -> UnconditionalJump[GOTO] #CW -> #J
      <- Immediate #CV -> #CW
      <- Switch[207043213] #CV -> #CW
===#Block J(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. return;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1512228650)
      goto BC
      -> ConditionalJump[IF_ICMPNE] #J -> #BC
      <- Immediate #H -> #J
      <- Switch[1768984082] #CV -> #J
      <- UnconditionalJump[GOTO] #CF -> #J
      <- Switch[1891240151] #BV -> #J
      <- UnconditionalJump[GOTO] #CW -> #J
      <- UnconditionalJump[GOTO] #BW -> #J
===#Block CX(size=2, flags=10100)===
   0. lvar35 = {1080304241 ^ lvar35};
   1. goto K
      -> UnconditionalJump[GOTO] #CX -> #K
      <- ConditionalJump[IF_ICMPGE] #B -> #CX
===#Block K(size=5, flags=0)===
   0. // Frame: locals[1] [null] stack[0] []
   1. lvar18 = lvar1;
   2. svar37 = {lvar18 ^ lvar35};
   3. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(svar37)) {
      case 41911328:
      	 goto	#CY
      case 41911334:
      	 goto	#CZ
      case 41911512:
      	 goto	#DB
      case 41911513:
      	 goto	#DC
      case 41911514:
      	 goto	#DD
      case 41911515:
      	 goto	#DF
      case 41911518:
      	 goto	#DG
      default:
      	 goto	#DI
   }
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 357648776)
      goto BA
      -> Switch[41911514] #K -> #DD
      -> DefaultSwitch #K -> #DI
      -> Switch[41911515] #K -> #DF
      -> Switch[41911328] #K -> #CY
      -> Switch[41911518] #K -> #DG
      -> ConditionalJump[IF_ICMPNE] #K -> #BA
      -> Switch[41911334] #K -> #CZ
      -> Switch[41911512] #K -> #DB
      -> Switch[41911513] #K -> #DC
      <- UnconditionalJump[GOTO] #CX -> #K
===#Block DC(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 628095275);
   1. goto M
      -> UnconditionalJump[GOTO] #DC -> #M
      <- Switch[41911513] #K -> #DC
===#Block M(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar20 = lvar0;
   2. _consume(lvar20.cancelAndReturn(474041386));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1043235025)
      goto AX
   4. lvar35 = {533923921 ^ lvar35};
      -> Immediate #M -> #T
      -> ConditionalJump[IF_ICMPNE] #M -> #AX
      <- UnconditionalJump[GOTO] #DC -> #M
===#Block DB(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 1331934798);
   1. goto P
      -> UnconditionalJump[GOTO] #DB -> #P
      <- Switch[41911512] #K -> #DB
===#Block P(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1850539002)
      goto AW
   2. goto BY
      -> UnconditionalJump[GOTO] #P -> #BY
      -> ConditionalJump[IF_ICMPNE] #P -> #AW
      <- UnconditionalJump[GOTO] #DB -> #P
===#Block BY(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 112650447:
      	 goto	#BZ
      case 1068614010:
      	 goto	#BY
      case 1133138044:
      	 goto	#BG
      case 1642484887:
      	 goto	#AB
      default:
      	 goto	#BG
   }
      -> Switch[1642484887] #BY -> #AB
      -> DefaultSwitch #BY -> #BG
      -> Switch[112650447] #BY -> #BZ
      -> Switch[1133138044] #BY -> #BG
      -> Immediate #BY -> #BZ
      -> Switch[1068614010] #BY -> #BY
      <- Switch[1068614010] #BY -> #BY
      <- UnconditionalJump[GOTO] #P -> #BY
===#Block BZ(size=2, flags=100)===
   0. lvar35 = {1235003751 ^ lvar35};
   1. goto AB
      -> UnconditionalJump[GOTO] #BZ -> #AB
      <- Switch[112650447] #BY -> #BZ
      <- Immediate #BY -> #BZ
===#Block AB(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 68151011)
      goto AA
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 581546176)
      goto BF
      -> ConditionalJump[IF_ICMPNE] #AB -> #BF
      -> ConditionalJump[IF_ICMPEQ] #AB -> #AA
      -> TryCatch range: [AB...AA] -> DS ([Ljava/io/IOException;])
      <- Switch[1642484887] #BY -> #AB
      <- UnconditionalJump[GOTO] #BZ -> #AB
===#Block AA(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1269021961)
      goto AZ
      -> ConditionalJump[IF_ICMPNE] #AA -> #AZ
      -> TryCatch range: [AB...AA] -> DS ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AB -> #AA
===#Block DS(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case 581546176:
      	 goto	#DT
      case 1269021961:
      	 goto	#DU
      default:
      	 goto	#DV
   }
      -> Switch[1269021961] #DS -> #DU
      -> Switch[581546176] #DS -> #DT
      -> DefaultSwitch #DS -> #DV
      <- TryCatch range: [AB...AA] -> DS ([Ljava/io/IOException;])
      <- TryCatch range: [AB...AA] -> DS ([Ljava/io/IOException;])
===#Block DV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #DS -> #DV
===#Block DT(size=2, flags=10100)===
   0. lvar35 = {750523490 ^ lvar35};
   1. goto AC
      -> UnconditionalJump[GOTO] #DT -> #AC
      <- Switch[581546176] #DS -> #DT
===#Block DU(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 27088347);
   1. goto AC
      -> UnconditionalJump[GOTO] #DU -> #AC
      <- Switch[1269021961] #DS -> #DU
===#Block AC(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1195971537)
      goto BU
   2. goto CS
      -> UnconditionalJump[GOTO] #AC -> #CS
      -> ConditionalJump[IF_ICMPNE] #AC -> #BU
      <- UnconditionalJump[GOTO] #DT -> #AC
      <- UnconditionalJump[GOTO] #DU -> #AC
===#Block BU(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1195971537)
      goto BU
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1556978550 ^ lvar35})
      goto BU
   2. _consume({1492273046 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BU -> #BU
      <- ConditionalJump[IF_ICMPNE] #AC -> #BU
      <- ConditionalJump[IF_ICMPNE] #BU -> #BU
===#Block CS(size=2, flags=10100)===
   0. lvar35 = {285211185 ^ lvar35};
   1. goto T
      -> UnconditionalJump[GOTO] #CS -> #T
      <- UnconditionalJump[GOTO] #AC -> #CS
===#Block AZ(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1269021961)
      goto AZ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1330072346 ^ lvar35})
      goto AZ
   2. _consume({1567391175 ^ lvar35});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AA -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AZ -> #AZ
===#Block BF(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 581546176)
      goto BF
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {2127358039 ^ lvar35})
      goto BF
   2. _consume({1483574697 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -764496287)
      goto BF
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {735090203 ^ lvar35})
      goto BF
   5. _consume({2060573782 ^ lvar35});
   6. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BF -> #BF
      <- ConditionalJump[IF_ICMPNE] #BF -> #BF
      <- ConditionalJump[IF_ICMPNE] #AB -> #BF
      <- ConditionalJump[IF_ICMPNE] #AU -> #BF
===#Block CZ(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 41911351:
      	 goto	#DA
      case 881324404:
      	 goto	#AW
      case 1653720112:
      	 goto	#Q
      case 1751152720:
      	 goto	#CZ
      default:
      	 goto	#AW
   }
      -> Switch[41911351] #CZ -> #DA
      -> Immediate #CZ -> #DA
      -> DefaultSwitch #CZ -> #AW
      -> Switch[881324404] #CZ -> #AW
      -> Switch[1751152720] #CZ -> #CZ
      -> Switch[1653720112] #CZ -> #Q
      <- Switch[1751152720] #CZ -> #CZ
      <- Switch[41911334] #K -> #CZ
===#Block DA(size=2, flags=100)===
   0. lvar35 = {1366516841 ^ lvar35};
   1. goto Q
      -> UnconditionalJump[GOTO] #DA -> #Q
      <- Switch[41911351] #CZ -> #DA
      <- Immediate #CZ -> #DA
===#Block Q(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar23 = lvar0;
   2. _consume(lvar23.testAllCommands(1804428905));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1630901566)
      goto AY
   4. goto BX
      -> ConditionalJump[IF_ICMPNE] #Q -> #AY
      -> UnconditionalJump[GOTO] #Q -> #BX
      <- UnconditionalJump[GOTO] #DA -> #Q
      <- Switch[1653720112] #CZ -> #Q
===#Block BX(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 788642390);
   1. goto V
      -> UnconditionalJump[GOTO] #BX -> #V
      <- UnconditionalJump[GOTO] #Q -> #BX
===#Block V(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 31728243)
      goto U
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -423118733)
      goto BG
      -> TryCatch range: [V...U] -> DK ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPNE] #V -> #BG
      -> ConditionalJump[IF_ICMPEQ] #V -> #U
      <- UnconditionalJump[GOTO] #BX -> #V
===#Block U(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1295537045)
      goto BP
      -> ConditionalJump[IF_ICMPNE] #U -> #BP
      -> TryCatch range: [V...U] -> DK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #V -> #U
===#Block BP(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1295537045)
      goto BP
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {493463360 ^ lvar35})
      goto BP
   2. _consume({413016159 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BP -> #BP
      <- ConditionalJump[IF_ICMPNE] #U -> #BP
      <- ConditionalJump[IF_ICMPNE] #BP -> #BP
===#Block DK(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case -1295537045:
      	 goto	#DM
      case -423118733:
      	 goto	#DL
      default:
      	 goto	#DN
   }
      -> Switch[-423118733] #DK -> #DL
      -> Switch[-1295537045] #DK -> #DM
      -> DefaultSwitch #DK -> #DN
      <- TryCatch range: [V...U] -> DK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [V...U] -> DK ([Ljava/lang/RuntimeException;])
===#Block DN(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #DK -> #DN
===#Block DM(size=2, flags=10100)===
   0. lvar35 = {1428463263 ^ lvar35};
   1. goto W
      -> UnconditionalJump[GOTO] #DM -> #W
      <- Switch[-1295537045] #DK -> #DM
===#Block DL(size=2, flags=10100)===
   0. lvar35 = {1604623388 ^ lvar35};
   1. goto W
      -> UnconditionalJump[GOTO] #DL -> #W
      <- Switch[-423118733] #DK -> #DL
===#Block W(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 467884177)
      goto AY
   2. goto CG
      -> UnconditionalJump[GOTO] #W -> #CG
      -> ConditionalJump[IF_ICMPNE] #W -> #AY
      <- UnconditionalJump[GOTO] #DM -> #W
      <- UnconditionalJump[GOTO] #DL -> #W
===#Block CG(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 459977561);
   1. goto T
      -> UnconditionalJump[GOTO] #CG -> #T
      <- UnconditionalJump[GOTO] #W -> #CG
===#Block DG(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 41911351:
      	 goto	#DH
      case 279912579:
      	 goto	#BD
      case 453605245:
      	 goto	#L
      case 517453312:
      	 goto	#DG
      default:
      	 goto	#BD
   }
      -> Switch[279912579] #DG -> #BD
      -> DefaultSwitch #DG -> #BD
      -> Switch[517453312] #DG -> #DG
      -> Switch[453605245] #DG -> #L
      -> Immediate #DG -> #DH
      -> Switch[41911351] #DG -> #DH
      <- Switch[517453312] #DG -> #DG
      <- Switch[41911518] #K -> #DG
===#Block DH(size=2, flags=100)===
   0. lvar35 = {444050891 ^ lvar35};
   1. goto L
      -> UnconditionalJump[GOTO] #DH -> #L
      <- Immediate #DG -> #DH
      <- Switch[41911351] #DG -> #DH
===#Block L(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar19 = lvar0;
   2. _consume(lvar19.saveAndReturn(2002579175));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -957601840)
      goto BN
   4. goto CH
      -> UnconditionalJump[GOTO] #L -> #CH
      -> ConditionalJump[IF_ICMPNE] #L -> #BN
      <- UnconditionalJump[GOTO] #DH -> #L
      <- Switch[453605245] #DG -> #L
===#Block CH(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 2959737:
      	 goto	#CI
      case 1399540193:
      	 goto	#BM
      case 1770295929:
      	 goto	#CH
      case 1824664892:
      	 goto	#Y
      default:
      	 goto	#BM
   }
      -> DefaultSwitch #CH -> #BM
      -> Switch[1399540193] #CH -> #BM
      -> Switch[1770295929] #CH -> #CH
      -> Switch[1824664892] #CH -> #Y
      -> Immediate #CH -> #CI
      -> Switch[2959737] #CH -> #CI
      <- Switch[1770295929] #CH -> #CH
      <- UnconditionalJump[GOTO] #L -> #CH
===#Block CI(size=2, flags=100)===
   0. lvar35 = {185691852 ^ lvar35};
   1. goto Y
      -> UnconditionalJump[GOTO] #CI -> #Y
      <- Immediate #CH -> #CI
      <- Switch[2959737] #CH -> #CI
===#Block Y(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 106763780)
      goto X
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1637396048)
      goto BH
      -> ConditionalJump[IF_ICMPNE] #Y -> #BH
      -> TryCatch range: [Y...X] -> DO ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #Y -> #X
      <- Switch[1824664892] #CH -> #Y
      <- UnconditionalJump[GOTO] #CI -> #Y
===#Block X(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -415515805)
      goto BD
      -> ConditionalJump[IF_ICMPNE] #X -> #BD
      -> TryCatch range: [Y...X] -> DO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #Y -> #X
===#Block DO(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case -1637396048:
      	 goto	#DP
      case -415515805:
      	 goto	#DQ
      default:
      	 goto	#DR
   }
      -> Switch[-415515805] #DO -> #DQ
      -> Switch[-1637396048] #DO -> #DP
      -> DefaultSwitch #DO -> #DR
      <- TryCatch range: [Y...X] -> DO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [Y...X] -> DO ([Ljava/lang/RuntimeException;])
===#Block DR(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #DO -> #DR
===#Block DP(size=2, flags=10100)===
   0. lvar35 = {859444568 ^ lvar35};
   1. goto Z
      -> UnconditionalJump[GOTO] #DP -> #Z
      <- Switch[-1637396048] #DO -> #DP
===#Block DQ(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 1544652802);
   1. goto Z
      -> UnconditionalJump[GOTO] #DQ -> #Z
      <- Switch[-415515805] #DO -> #DQ
===#Block Z(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 129497969)
      goto BC
   2. goto CL
      -> ConditionalJump[IF_ICMPNE] #Z -> #BC
      -> UnconditionalJump[GOTO] #Z -> #CL
      <- UnconditionalJump[GOTO] #DQ -> #Z
      <- UnconditionalJump[GOTO] #DP -> #Z
===#Block CL(size=2, flags=10100)===
   0. lvar35 = {417343269 ^ lvar35};
   1. goto T
      -> UnconditionalJump[GOTO] #CL -> #T
      <- UnconditionalJump[GOTO] #Z -> #CL
===#Block BC(size=10, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1512228650)
      goto BC
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1140407854 ^ lvar35})
      goto BC
   2. _consume({2118159869 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1924135155)
      goto BC
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1599323335 ^ lvar35})
      goto BC
   5. _consume({314138673 ^ lvar35});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 129497969)
      goto BC
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {564071447 ^ lvar35})
      goto BC
   8. _consume({1704298456 ^ lvar35});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- ConditionalJump[IF_ICMPNE] #J -> #BC
      <- ConditionalJump[IF_ICMPNE] #F -> #BC
      <- ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- ConditionalJump[IF_ICMPNE] #Z -> #BC
      <- Switch[1757080681] #CQ -> #BC
      <- DefaultSwitch #CQ -> #BC
===#Block BH(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1637396048)
      goto BH
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {494258296 ^ lvar35})
      goto BH
   2. _consume({1794198982 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1734611942)
      goto BH
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {887511178 ^ lvar35})
      goto BH
   5. _consume({627445319 ^ lvar35});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BH -> #BH
      <- ConditionalJump[IF_ICMPNE] #C -> #BH
      <- ConditionalJump[IF_ICMPNE] #Y -> #BH
      <- ConditionalJump[IF_ICMPNE] #BH -> #BH
      <- Switch[739241490] #CO -> #BH
      <- DefaultSwitch #CO -> #BH
===#Block BM(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 376461826)
      goto BM
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {40720400 ^ lvar35})
      goto BM
   2. _consume({1324792365 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BM -> #BM
      <- DefaultSwitch #CH -> #BM
      <- Switch[1399540193] #CH -> #BM
      <- ConditionalJump[IF_ICMPNE] #B -> #BM
      <- ConditionalJump[IF_ICMPNE] #BM -> #BM
===#Block BD(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1775786459)
      goto BD
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {310690137 ^ lvar35})
      goto BD
   2. _consume({2077312704 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -415515805)
      goto BD
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1287551982 ^ lvar35})
      goto BD
   5. _consume({144424242 ^ lvar35});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BD -> #BD
      <- ConditionalJump[IF_ICMPNE] #X -> #BD
      <- Switch[279912579] #DG -> #BD
      <- DefaultSwitch #DG -> #BD
      <- ConditionalJump[IF_ICMPNE] #BD -> #BD
      <- ConditionalJump[IF_ICMPNE] #AQ -> #BD
===#Block CY(size=2, flags=10100)===
   0. lvar35 = {319652638 ^ lvar35};
   1. goto S
      -> UnconditionalJump[GOTO] #CY -> #S
      <- Switch[41911328] #K -> #CY
===#Block S(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar0;
   2. _consume(lvar25.addNewCommand(618518999));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1925361288)
      goto AV
   4. goto CA
      -> ConditionalJump[IF_ICMPNE] #S -> #AV
      -> UnconditionalJump[GOTO] #S -> #CA
      <- UnconditionalJump[GOTO] #CY -> #S
===#Block CA(size=2, flags=10100)===
   0. lvar35 = {1303997793 ^ lvar35};
   1. goto AE
      -> UnconditionalJump[GOTO] #CA -> #AE
      <- UnconditionalJump[GOTO] #S -> #CA
===#Block AE(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 41680441)
      goto AD
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -520734094)
      goto BL
      -> ConditionalJump[IF_ICMPNE] #AE -> #BL
      -> TryCatch range: [AE...AD] -> DW ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #AE -> #AD
      <- UnconditionalJump[GOTO] #CA -> #AE
===#Block AD(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -394735687)
      goto AW
      -> TryCatch range: [AE...AD] -> DW ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AD -> #AW
      <- ConditionalJump[IF_ICMPEQ] #AE -> #AD
===#Block AW(size=10, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -394735687)
      goto AW
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1686900309 ^ lvar35})
      goto AW
   2. _consume({368160464 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1850539002)
      goto AW
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {2075604403 ^ lvar35})
      goto AW
   5. _consume({918489143 ^ lvar35});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1963260024)
      goto AW
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1969827487 ^ lvar35})
      goto AW
   8. _consume({1135524670 ^ lvar35});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- ConditionalJump[IF_ICMPNE] #D -> #AW
      <- DefaultSwitch #CZ -> #AW
      <- Switch[881324404] #CZ -> #AW
      <- ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- ConditionalJump[IF_ICMPNE] #P -> #AW
      <- ConditionalJump[IF_ICMPNE] #AD -> #AW
===#Block DW(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case -520734094:
      	 goto	#DX
      case -394735687:
      	 goto	#DY
      default:
      	 goto	#DZ
   }
      -> Switch[-394735687] #DW -> #DY
      -> DefaultSwitch #DW -> #DZ
      -> Switch[-520734094] #DW -> #DX
      <- TryCatch range: [AE...AD] -> DW ([Ljava/io/IOException;])
      <- TryCatch range: [AE...AD] -> DW ([Ljava/io/IOException;])
===#Block DX(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 834020929);
   1. goto AF
      -> UnconditionalJump[GOTO] #DX -> #AF
      <- Switch[-520734094] #DW -> #DX
===#Block DZ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #DW -> #DZ
===#Block DY(size=2, flags=10100)===
   0. lvar35 = {1353185528 ^ lvar35};
   1. goto AF
      -> UnconditionalJump[GOTO] #DY -> #AF
      <- Switch[-394735687] #DW -> #DY
===#Block AF(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1833371771)
      goto AY
   2. goto CD
      -> ConditionalJump[IF_ICMPNE] #AF -> #AY
      -> UnconditionalJump[GOTO] #AF -> #CD
      <- UnconditionalJump[GOTO] #DX -> #AF
      <- UnconditionalJump[GOTO] #DY -> #AF
===#Block CD(size=2, flags=10100)===
   0. lvar35 = {1438529860 ^ lvar35};
   1. goto T
      -> UnconditionalJump[GOTO] #CD -> #T
      <- UnconditionalJump[GOTO] #AF -> #CD
===#Block AY(size=13, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 467884177)
      goto AY
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {512318595 ^ lvar35})
      goto AY
   2. _consume({314928450 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1833371771)
      goto AY
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {766744590 ^ lvar35})
      goto AY
   5. _consume({1818968982 ^ lvar35});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -64092432)
      goto AY
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1056969778 ^ lvar35})
      goto AY
   8. _consume({1427961015 ^ lvar35});
   9. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1630901566)
      goto AY
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1002627069 ^ lvar35})
      goto AY
   11. _consume({962978481 ^ lvar35});
   12. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AY -> #AY
      <- ConditionalJump[IF_ICMPNE] #AF -> #AY
      <- ConditionalJump[IF_ICMPNE] #W -> #AY
      <- ConditionalJump[IF_ICMPNE] #AS -> #AY
      <- ConditionalJump[IF_ICMPNE] #AY -> #AY
      <- ConditionalJump[IF_ICMPNE] #Q -> #AY
===#Block BL(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -520734094)
      goto BL
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {546464311 ^ lvar35})
      goto BL
   2. _consume({1896408240 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BL -> #BL
      <- ConditionalJump[IF_ICMPNE] #AE -> #BL
      <- ConditionalJump[IF_ICMPNE] #BL -> #BL
===#Block DF(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 749640650);
   1. goto N
      -> UnconditionalJump[GOTO] #DF -> #N
      <- Switch[41911515] #K -> #DF
===#Block N(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = lvar0;
   2. _consume(lvar21.nextPage(169678756));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1881511897)
      goto BE
   4. goto CE
      -> UnconditionalJump[GOTO] #N -> #CE
      -> ConditionalJump[IF_ICMPNE] #N -> #BE
      <- UnconditionalJump[GOTO] #DF -> #N
===#Block CE(size=2, flags=10100)===
   0. lvar35 = {190295210 ^ lvar35};
   1. goto AK
      -> UnconditionalJump[GOTO] #CE -> #AK
      <- UnconditionalJump[GOTO] #N -> #CE
===#Block AK(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 139538415)
      goto AJ
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 714666633)
      goto AV
      -> TryCatch range: [AK...AJ] -> EE ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AK -> #AJ
      -> ConditionalJump[IF_ICMPNE] #AK -> #AV
      <- UnconditionalJump[GOTO] #CE -> #AK
===#Block AJ(size=2, flags=0)===
   0. throw new java/lang/IllegalAccessException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -2056976885)
      goto BS
      -> TryCatch range: [AK...AJ] -> EE ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPNE] #AJ -> #BS
      <- ConditionalJump[IF_ICMPEQ] #AK -> #AJ
===#Block BS(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -2056976885)
      goto BS
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {725011494 ^ lvar35})
      goto BS
   2. _consume({1821419876 ^ lvar35});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BS -> #BS
      <- ConditionalJump[IF_ICMPNE] #AJ -> #BS
      <- ConditionalJump[IF_ICMPNE] #BS -> #BS
===#Block EE(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case -2056976885:
      	 goto	#EG
      case 714666633:
      	 goto	#EF
      default:
      	 goto	#EH
   }
      -> DefaultSwitch #EE -> #EH
      -> Switch[714666633] #EE -> #EF
      -> Switch[-2056976885] #EE -> #EG
      <- TryCatch range: [AK...AJ] -> EE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AK...AJ] -> EE ([Ljava/lang/IllegalAccessException;])
===#Block EG(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 91503501);
   1. goto AL
      -> UnconditionalJump[GOTO] #EG -> #AL
      <- Switch[-2056976885] #EE -> #EG
===#Block EF(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 1351320861);
   1. goto AL
      -> UnconditionalJump[GOTO] #EF -> #AL
      <- Switch[714666633] #EE -> #EF
===#Block AL(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1362830749)
      goto BB
   2. goto CB
      -> ConditionalJump[IF_ICMPNE] #AL -> #BB
      -> UnconditionalJump[GOTO] #AL -> #CB
      <- UnconditionalJump[GOTO] #EG -> #AL
      <- UnconditionalJump[GOTO] #EF -> #AL
===#Block CB(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 72603963:
      	 goto	#CC
      case 202705648:
      	 goto	#AX
      case 302316538:
      	 goto	#CB
      case 1965999067:
      	 goto	#T
      default:
      	 goto	#AX
   }
      -> Immediate #CB -> #CC
      -> DefaultSwitch #CB -> #AX
      -> Switch[72603963] #CB -> #CC
      -> Switch[202705648] #CB -> #AX
      -> Switch[1965999067] #CB -> #T
      -> Switch[302316538] #CB -> #CB
      <- Switch[302316538] #CB -> #CB
      <- UnconditionalJump[GOTO] #AL -> #CB
===#Block AX(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1043235025)
      goto AX
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {157298984 ^ lvar35})
      goto AX
   2. _consume({1470732408 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AX -> #AX
      <- DefaultSwitch #CB -> #AX
      <- Switch[202705648] #CB -> #AX
      <- ConditionalJump[IF_ICMPNE] #AX -> #AX
      <- ConditionalJump[IF_ICMPNE] #M -> #AX
===#Block CC(size=2, flags=100)===
   0. lvar35 = {1305363207 ^ lvar35};
   1. goto T
      -> UnconditionalJump[GOTO] #CC -> #T
      <- Immediate #CB -> #CC
      <- Switch[72603963] #CB -> #CC
===#Block BB(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1043648752)
      goto BB
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {576838593 ^ lvar35})
      goto BB
   2. _consume({1126340082 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1362830749)
      goto BB
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {427522206 ^ lvar35})
      goto BB
   5. _consume({803365710 ^ lvar35});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- DefaultSwitch #BV -> #BB
      <- Switch[1498035525] #BV -> #BB
      <- ConditionalJump[IF_ICMPNE] #AL -> #BB
      <- ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #G -> #BB
===#Block EH(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #EE -> #EH
===#Block DI(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 41911351:
      	 goto	#DJ
      case 344394432:
      	 goto	#DI
      case 1270113687:
      	 goto	#BI
      case 1391895653:
      	 goto	#T
      default:
      	 goto	#BI
   }
      -> Switch[1391895653] #DI -> #T
      -> DefaultSwitch #DI -> #BI
      -> Switch[41911351] #DI -> #DJ
      -> Switch[344394432] #DI -> #DI
      -> Immediate #DI -> #DJ
      -> Switch[1270113687] #DI -> #BI
      <- DefaultSwitch #K -> #DI
      <- Switch[344394432] #DI -> #DI
===#Block DJ(size=2, flags=100)===
   0. lvar35 = {985461114 ^ lvar35};
   1. goto T
      -> UnconditionalJump[GOTO] #DJ -> #T
      <- Switch[41911351] #DI -> #DJ
      <- Immediate #DI -> #DJ
===#Block DD(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35)) {
      case 41911351:
      	 goto	#DE
      case 243706351:
      	 goto	#DD
      case 410430007:
      	 goto	#BN
      case 466183562:
      	 goto	#O
      default:
      	 goto	#BN
   }
      -> DefaultSwitch #DD -> #BN
      -> Immediate #DD -> #DE
      -> Switch[410430007] #DD -> #BN
      -> Switch[466183562] #DD -> #O
      -> Switch[41911351] #DD -> #DE
      -> Switch[243706351] #DD -> #DD
      <- Switch[41911514] #K -> #DD
      <- Switch[243706351] #DD -> #DD
===#Block DE(size=2, flags=100)===
   0. lvar35 = {1450976680 ^ lvar35};
   1. goto O
      -> UnconditionalJump[GOTO] #DE -> #O
      <- Immediate #DD -> #DE
      <- Switch[41911351] #DD -> #DE
===#Block O(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar0;
   2. _consume(lvar22.previousPage(1964958136));
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1498389302)
      goto BG
   4. goto CN
      -> ConditionalJump[IF_ICMPNE] #O -> #BG
      -> UnconditionalJump[GOTO] #O -> #CN
      <- Switch[466183562] #DD -> #O
      <- UnconditionalJump[GOTO] #DE -> #O
===#Block CN(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 106896030);
   1. goto AN
      -> UnconditionalJump[GOTO] #CN -> #AN
      <- UnconditionalJump[GOTO] #O -> #CN
===#Block AN(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar35) == 266979364)
      goto AM
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1807175622)
      goto BI
      -> TryCatch range: [AN...AM] -> EI ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AN -> #BI
      -> ConditionalJump[IF_ICMPEQ] #AN -> #AM
      <- UnconditionalJump[GOTO] #CN -> #AN
===#Block AM(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1420137721)
      goto AV
      -> TryCatch range: [AN...AM] -> EI ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AM -> #AV
      <- ConditionalJump[IF_ICMPEQ] #AN -> #AM
===#Block AV(size=16, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1925361288)
      goto AV
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {59711945 ^ lvar35})
      goto AV
   2. _consume({1167735153 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 714666633)
      goto AV
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1842037091 ^ lvar35})
      goto AV
   5. _consume({877367877 ^ lvar35});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -748456055)
      goto AV
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {2083967743 ^ lvar35})
      goto AV
   8. _consume({400548822 ^ lvar35});
   9. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1420137721)
      goto AV
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {561490543 ^ lvar35})
      goto AV
   11. _consume({1180310801 ^ lvar35});
   12. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1074656347)
      goto AV
   13. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1157492828 ^ lvar35})
      goto AV
   14. _consume({129881461 ^ lvar35});
   15. throw new java/lang/IllegalAccessException();
      -> ConditionalJump[IF_ICMPNE] #AV -> #AV
      <- ConditionalJump[IF_ICMPNE] #S -> #AV
      <- ConditionalJump[IF_ICMPNE] #H -> #AV
      <- ConditionalJump[IF_ICMPNE] #AV -> #AV
      <- ConditionalJump[IF_ICMPNE] #AK -> #AV
      <- ConditionalJump[IF_ICMPNE] #AM -> #AV
      <- ConditionalJump[IF_ICMPNE] #AR -> #AV
===#Block BI(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1807175622)
      goto BI
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1714988439 ^ lvar35})
      goto BI
   2. _consume({1812868043 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- ConditionalJump[IF_ICMPNE] #AN -> #BI
      <- ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- DefaultSwitch #DI -> #BI
      <- Switch[1270113687] #DI -> #BI
===#Block EI(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar35)) {
      case -1807175622:
      	 goto	#EJ
      case 1420137721:
      	 goto	#EK
      default:
      	 goto	#EL
   }
      -> Switch[-1807175622] #EI -> #EJ
      -> Switch[1420137721] #EI -> #EK
      -> DefaultSwitch #EI -> #EL
      <- TryCatch range: [AN...AM] -> EI ([Ljava/io/IOException;])
      <- TryCatch range: [AN...AM] -> EI ([Ljava/io/IOException;])
===#Block EL(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #EI -> #EL
===#Block EK(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 473707586);
   1. goto AO
      -> UnconditionalJump[GOTO] #EK -> #AO
      <- Switch[1420137721] #EI -> #EK
===#Block EJ(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 1679921370);
   1. goto AO
      -> UnconditionalJump[GOTO] #EJ -> #AO
      <- Switch[-1807175622] #EI -> #EJ
===#Block AO(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1253820695)
      goto BR
   2. goto CK
      -> ConditionalJump[IF_ICMPNE] #AO -> #BR
      -> UnconditionalJump[GOTO] #AO -> #CK
      <- UnconditionalJump[GOTO] #EJ -> #AO
      <- UnconditionalJump[GOTO] #EK -> #AO
===#Block CK(size=2, flags=10100)===
   0. lvar35 = com.hang.plugin.gui.CommandEditGUI.zaxovyrccxexnncc(lvar35, 247357078);
   1. goto T
      -> UnconditionalJump[GOTO] #CK -> #T
      <- UnconditionalJump[GOTO] #AO -> #CK
===#Block T(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. return;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1061764519)
      goto BE
      -> ConditionalJump[IF_ICMPNE] #T -> #BE
      <- UnconditionalJump[GOTO] #CJ -> #T
      <- UnconditionalJump[GOTO] #CG -> #T
      <- Switch[1391895653] #DI -> #T
      <- UnconditionalJump[GOTO] #DJ -> #T
      <- UnconditionalJump[GOTO] #CL -> #T
      <- UnconditionalJump[GOTO] #CS -> #T
      <- UnconditionalJump[GOTO] #CD -> #T
      <- UnconditionalJump[GOTO] #CK -> #T
      <- Switch[1965999067] #CB -> #T
      <- Immediate #M -> #T
      <- UnconditionalJump[GOTO] #CC -> #T
===#Block BE(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1061764519)
      goto BE
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {178320723 ^ lvar35})
      goto BE
   2. _consume({1162242296 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1881511897)
      goto BE
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {780376778 ^ lvar35})
      goto BE
   5. _consume({1135532591 ^ lvar35});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BE -> #BE
      <- ConditionalJump[IF_ICMPNE] #T -> #BE
      <- ConditionalJump[IF_ICMPNE] #N -> #BE
      <- ConditionalJump[IF_ICMPNE] #BE -> #BE
===#Block BR(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1253820695)
      goto BR
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1175865407 ^ lvar35})
      goto BR
   2. _consume({415550789 ^ lvar35});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BR -> #BR
      <- ConditionalJump[IF_ICMPNE] #AO -> #BR
      <- ConditionalJump[IF_ICMPNE] #BR -> #BR
===#Block BG(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -423118733)
      goto BG
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1724609493 ^ lvar35})
      goto BG
   2. _consume({1136626193 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1498389302)
      goto BG
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1263675661 ^ lvar35})
      goto BG
   5. _consume({1509309735 ^ lvar35});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- ConditionalJump[IF_ICMPNE] #O -> #BG
      <- ConditionalJump[IF_ICMPNE] #V -> #BG
      <- DefaultSwitch #BY -> #BG
      <- Switch[1133138044] #BY -> #BG
===#Block BN(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -957601840)
      goto BN
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {367965164 ^ lvar35})
      goto BN
   2. _consume({262367813 ^ lvar35});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BN -> #BN
      <- DefaultSwitch #DD -> #BN
      <- Switch[410430007] #DD -> #BN
      <- ConditionalJump[IF_ICMPNE] #BN -> #BN
      <- ConditionalJump[IF_ICMPNE] #L -> #BN
===#Block BA(size=13, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 1132226442)
      goto BA
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {133669565 ^ lvar35})
      goto BA
   2. _consume({54404585 ^ lvar35});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -147003606)
      goto BA
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {2053142038 ^ lvar35})
      goto BA
   5. _consume({1868272993 ^ lvar35});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != -1496523751)
      goto BA
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {325563673 ^ lvar35})
      goto BA
   8. _consume({1320699415 ^ lvar35});
   9. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != 357648776)
      goto BA
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar35) != {1109906187 ^ lvar35})
      goto BA
   11. _consume({68905873 ^ lvar35});
   12. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- Switch[1101915932] #CV -> #BA
      <- DefaultSwitch #CV -> #BA
      <- ConditionalJump[IF_ICMPNE] #A -> #BA
      <- ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #E -> #BA
      <- ConditionalJump[IF_ICMPNE] #R -> #BA
      <- ConditionalJump[IF_ICMPNE] #K -> #BA
