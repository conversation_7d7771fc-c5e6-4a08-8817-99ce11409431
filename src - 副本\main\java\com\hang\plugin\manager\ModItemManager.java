package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.util.*;
import java.util.Arrays;

/**
 * 模组物品管理器
 * 支持通过NBT标签、自定义模型数据等方式创建模组物品
 * 
 * <AUTHOR>
 */
public class ModItemManager {
    
    private final HangPlugin plugin;
    private final File modItemsFile;
    private FileConfiguration modItemsConfig;
    private final Map<String, ModItem> modItems;
    
    public ModItemManager(HangPlugin plugin) {
        this.plugin = plugin;
        this.modItemsFile = new File(plugin.getDataFolder(), "mod_items.yml");
        this.modItems = new HashMap<>();
        
        loadModItems();
    }
    
    /**
     * 加载模组物品配置
     */
    public void loadModItems() {
        if (!modItemsFile.exists()) {
            createDefaultConfig();
        }
        
        modItemsConfig = YamlConfiguration.loadConfiguration(modItemsFile);
        modItems.clear();
        
        ConfigurationSection itemsSection = modItemsConfig.getConfigurationSection("mod_items");
        if (itemsSection != null) {
            for (String key : itemsSection.getKeys(false)) {
                ConfigurationSection itemSection = itemsSection.getConfigurationSection(key);
                if (itemSection != null) {
                    ModItem item = loadModItem(key, itemSection);
                    if (item != null) {
                        modItems.put(key, item);
                    }
                }
            }
        }
    }
    
    /**
     * 从配置加载单个模组物品
     */
    private ModItem loadModItem(String id, ConfigurationSection section) {
        try {
            String materialName = section.getString("material", "STONE");
            Material material = Material.valueOf(materialName.toUpperCase());

            int amount = section.getInt("amount", 1);
            short data = (short) section.getInt("data", 0);
            String name = section.getString("name");
            List<String> lore = section.getStringList("lore");

            // 模组特定属性
            String modId = section.getString("mod_id");
            String itemId = section.getString("item_id");
            int customModelData = section.getInt("custom_model_data", 0);
            String nbtData = section.getString("nbt_data");
            String giveCommand = section.getString("give_command");

            // 摸金箱属性
            double probability = section.getDouble("probability", 0.1);
            int searchSpeed = section.getInt("search_speed", 3);
            List<String> commands = section.getStringList("commands");

            // 添加摸金箱类型支持
            List<String> chestTypes = section.getStringList("chest_types");
            if (chestTypes.isEmpty()) {
                // 如果没有配置，默认只在普通摸金箱中出现
                chestTypes = Arrays.asList("common");
            }

            return new ModItem(id, material, amount, data, name, lore,
                             modId, itemId, customModelData, nbtData, giveCommand,
                             probability, searchSpeed, commands, chestTypes);

        } catch (Exception e) {
            plugin.getLogger().warning("加载模组物品 " + id + " 时出错: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 创建默认配置文件
     */
    private void createDefaultConfig() {
        try {
            modItemsFile.getParentFile().mkdirs();
            modItemsFile.createNewFile();
            
            FileConfiguration config = YamlConfiguration.loadConfiguration(modItemsFile);
            
            // 移除示例模组物品的自动生成
            // 用户可以根据需要手动添加模组物品到 mod_items.yml 配置文件中
            
            config.save(modItemsFile);
            
        } catch (Exception e) {
            plugin.getLogger().severe("创建默认模组物品配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建模组物品的ItemStack
     */
    public ItemStack createModItemStack(ModItem modItem) {
        // 尝试创建真实的模组物品
        ItemStack realModItem = createRealModItem(modItem);
        if (realModItem != null) {
            return realModItem;
        }

        // 如果无法创建真实模组物品，使用代表物品
        return createRepresentativeItem(modItem);
    }

    /**
     * 尝试创建真实的模组物品
     */
    private ItemStack createRealModItem(ModItem modItem) {
        if (modItem.getGiveCommand() == null || modItem.getGiveCommand().isEmpty()) {
            return null;
        }

        try {
            // 尝试通过NBT或其他方式创建真实的模组物品
            String modItemId = modItem.getFullModItemId();
            if (modItemId != null) {
                // 尝试使用Bukkit的方式创建模组物品
                ItemStack item = createItemFromModId(modItemId, modItem.getAmount());
                if (item != null) {
                    return item;
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("无法创建真实模组物品: " + e.getMessage());
        }

        return null;
    }

    /**
     * 通过模组ID创建物品
     */
    private ItemStack createItemFromModId(String modItemId, int amount) {
        try {
            // 方法1: 尝试使用Bukkit的Material.matchMaterial
            try {
                Material material = Material.matchMaterial(modItemId);
                if (material != null) {
                    return new ItemStack(material, amount);
                }
            } catch (Exception e) {
                // 继续尝试其他方法
            }

            // 方法2: 尝试使用NamespacedKey (1.13+)
            try {
                Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
                Class<?> registryClass = Class.forName("org.bukkit.Registry");

                String[] parts = modItemId.split(":");
                if (parts.length == 2) {
                    Object namespacedKey = namespacedKeyClass.getConstructor(String.class, String.class)
                        .newInstance(parts[0], parts[1]);

                    Object registry = registryClass.getField("MATERIAL").get(null);
                    Material material = (Material) registry.getClass().getMethod("get", namespacedKeyClass)
                        .invoke(registry, namespacedKey);

                    if (material != null) {
                        return new ItemStack(material, amount);
                    }
                }
            } catch (Exception e) {
                // 继续尝试其他方法
            }

            // 方法3: 尝试通过NMS创建物品
            return createItemThroughNMS(modItemId, amount);

        } catch (Exception e) {
            plugin.getLogger().warning("创建模组物品失败: " + modItemId + " - " + e.getMessage());
            return null;
        }
    }

    /**
     * 通过NMS创建模组物品
     */
    private ItemStack createItemThroughNMS(String modItemId, int amount) {
        try {
            // 获取NMS管理器
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                // 尝试通过NMS创建物品
                return plugin.getNMSManager().createItemFromString(modItemId, amount);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("NMS创建模组物品失败: " + e.getMessage());
        }
        return null;
    }

    /**
     * 创建代表物品
     */
    private ItemStack createRepresentativeItem(ModItem modItem) {
        // 获取安全的代表材料
        Material safeMaterial = getSafeMaterial(modItem.getMaterial());

        ItemStack item = new ItemStack(safeMaterial, modItem.getAmount());
        ItemMeta meta = item.getItemMeta();

        if (modItem.getName() != null) {
            meta.setDisplayName(modItem.getName());
        } else {
            // 如果没有自定义名称，使用模组物品ID作为显示名称
            meta.setDisplayName("§6" + modItem.getFullModItemId());
        }

        List<String> lore = new ArrayList<>(modItem.getLore());
        lore.add("§8[模组物品代表]");
        lore.add("§8模组: §7" + modItem.getModId());
        lore.add("§8物品ID: §7" + modItem.getItemId());
        lore.add("§8拿取时将转换为真实物品");
        meta.setLore(lore);

        // 设置自定义模型数据（1.14+）
        if (modItem.getCustomModelData() > 0) {
            try {
                meta.getClass().getMethod("setCustomModelData", int.class)
                    .invoke(meta, modItem.getCustomModelData());
            } catch (Exception e) {
                // 忽略，可能是旧版本
            }
        }

        // 添加特殊标记，用于识别这是模组物品代表
        try {
            // 使用PersistentDataContainer标记 (1.14+)
            Class<?> namespacedKeyClass = Class.forName("org.bukkit.NamespacedKey");
            Class<?> persistentDataTypeClass = Class.forName("org.bukkit.persistence.PersistentDataType");

            Object namespacedKey = namespacedKeyClass.getConstructor(org.bukkit.plugin.Plugin.class, String.class)
                .newInstance(plugin, "mod_item_id");
            Object stringType = persistentDataTypeClass.getField("STRING").get(null);

            Object container = meta.getClass().getMethod("getPersistentDataContainer").invoke(meta);
            container.getClass().getMethod("set", namespacedKeyClass, persistentDataTypeClass, Object.class)
                .invoke(container, namespacedKey, stringType, modItem.getFullModItemId());
        } catch (Exception e) {
            // 1.14以下版本，使用NBT标记
            try {
                // 这里可以添加NBT标记的代码
            } catch (Exception e2) {
                // 忽略，使用Lore作为标识
            }
        }

        item.setItemMeta(meta);
        return item;
    }

    /**
     * 获取安全的材料类型（避免马赛克显示）
     */
    private Material getSafeMaterial(Material originalMaterial) {
        try {
            // 尝试使用原始材料
            if (originalMaterial != null) {
                // 检查材料是否存在且不会显示为马赛克
                if (!isInvalidMaterial(originalMaterial)) {
                    return originalMaterial;
                }
            }
        } catch (Exception e) {
            // 材料不存在或无效
            plugin.getLogger().warning("模组物品材料无效: " + (originalMaterial != null ? originalMaterial.name() : "null"));
        }

        // 使用1.20.1兼容的安全替代材料
        return getCompatibleMaterial();
    }

    /**
     * 检查材料是否无效（会显示为马赛克）
     */
    private boolean isInvalidMaterial(Material material) {
        if (material == null) return true;

        // 检查一些已知会在1.20.1中显示为马赛克的材料
        String materialName = material.name();

        // 避免使用可能导致马赛克的材料
        if (materialName.contains("LEGACY") ||
            materialName.contains("STAINED_GLASS_PANE") ||
            materialName.contains("WOOL") && materialName.length() < 10) {
            return true;
        }

        return false;
    }

    /**
     * 获取1.20.1兼容的材料
     */
    private Material getCompatibleMaterial() {
        // 按优先级尝试不同的材料
        String[] safeMaterials = {
            "KNOWLEDGE_BOOK",    // 知识之书，通常用于表示特殊物品
            "ENCHANTED_BOOK",    // 附魔书
            "WRITTEN_BOOK",      // 成书
            "BOOK",              // 书
            "PAPER",             // 纸
            "STICK",             // 木棍
            "STONE"              // 石头（最后备选）
        };

        for (String materialName : safeMaterials) {
            try {
                Material material = Material.valueOf(materialName);
                // 测试材料是否可用
                new ItemStack(material, 1);
                return material;
            } catch (Exception e) {
                // 继续尝试下一个
            }
        }

        // 如果所有预定义材料都失败，使用第一个可用材料
        for (Material material : Material.values()) {
            try {
                // 检查材料是否可以创建物品（兼容1.12.2）
                if (isValidItemMaterial(material)) {
                    new ItemStack(material, 1);
                    plugin.getLogger().warning("使用备选材料: " + material.name());
                    return material;
                }
            } catch (Exception e) {
                // 继续尝试
            }
        }

        // 最后的备选方案
        return Material.values()[0];
    }

    /**
     * 检查材料是否可以用作物品（兼容1.12.2）
     */
    private boolean isValidItemMaterial(Material material) {
        if (material == null) return false;

        String name = material.name();

        // 排除一些明显不能作为物品的材料
        if (name.contains("AIR") ||
            name.contains("VOID") ||
            name.contains("BARRIER") ||
            name.contains("STRUCTURE") ||
            name.contains("COMMAND") ||
            name.equals("FIRE") ||
            name.equals("WATER") ||
            name.equals("LAVA")) {
            return false;
        }

        try {
            // 尝试创建物品来验证
            new ItemStack(material, 1);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 给予玩家模组物品
     */
    public void giveModItem(org.bukkit.entity.Player player, ModItem modItem) {
        // 添加调试信息，追踪意外调用
        plugin.getLogger().info("[调试] giveModItem被调用 - 玩家: " + player.getName() + ", 物品: " + modItem.getId());

        // 打印调用堆栈，找出是谁调用了这个方法
        StackTraceElement[] stackTrace = Thread.currentThread().getStackTrace();
        plugin.getLogger().info("[调试] 调用堆栈:");
        for (int i = 1; i < Math.min(stackTrace.length, 6); i++) {
            plugin.getLogger().info("[调试]   " + i + ": " + stackTrace[i].toString());
        }

        // 如果有give命令，使用命令给予
        if (modItem.getGiveCommand() != null && !modItem.getGiveCommand().isEmpty()) {
            String command = modItem.getGiveCommand().replace("{player}", player.getName());

            // 尝试多种命令格式
            boolean success = false;
            String[] commandVariants = {
                command,  // 原始命令
                command.replace("give ", "/give "),  // 添加斜杠
                "minecraft:" + command,  // 添加minecraft前缀
                command.replace("tacz:", "timeless_and_classics_guns:")  // TACZ的完整命名空间
            };

            for (String cmd : commandVariants) {
                try {
                    plugin.getServer().dispatchCommand(plugin.getServer().getConsoleSender(), cmd);
                    success = true;
                    break;
                } catch (Exception e) {
                    // 继续尝试下一个命令格式
                }
            }

            if (!success) {
                plugin.getLogger().warning("所有命令格式都失败，给予代表物品");
                // 所有命令都失败时，给予代表物品并提示玩家
                ItemStack item = createModItemStack(modItem);
                player.getInventory().addItem(item);
                player.sendMessage("§c模组物品给予失败，已给予代表物品");
                player.sendMessage("§e请确保服务器已安装 " + modItem.getModId() + " 模组");
            }
        } else {
            // 否则直接给予物品
            ItemStack item = createModItemStack(modItem);
            player.getInventory().addItem(item);
            player.sendMessage("§e已给予模组物品的代表物品");
        }

        // 执行额外命令
        if (modItem.getCommands() != null && !modItem.getCommands().isEmpty()) {
            for (String command : modItem.getCommands()) {
                String processedCommand = command.replace("{player}", player.getName());
                try {
                    plugin.getServer().dispatchCommand(plugin.getServer().getConsoleSender(), processedCommand);
                } catch (Exception e) {
                    plugin.getLogger().warning("执行模组物品命令失败: " + processedCommand + " - " + e.getMessage());
                }
            }
        }
    }
    
    /**
     * 获取所有模组物品
     */
    public Collection<ModItem> getAllModItems() {
        return modItems.values();
    }
    
    /**
     * 获取模组物品
     */
    public ModItem getModItem(String id) {
        return modItems.get(id);
    }
    
    /**
     * 添加模组物品
     */
    public void addModItem(ModItem item) {
        modItems.put(item.getId(), item);
        // 同时保存到配置文件
        saveModItemToConfig(item);
    }

    /**
     * 更新模组物品
     */
    public void updateModItem(ModItem item) {
        modItems.put(item.getId(), item);
        // 保存到配置文件
        saveModItemToConfig(item);
    }
    
    /**
     * 移除模组物品
     */
    public void removeModItem(String id) {
        plugin.getLogger().info("[调试] 开始删除模组物品: " + id);

        // 检查物品是否存在
        if (!modItems.containsKey(id)) {
            plugin.getLogger().warning("[调试] 模组物品不存在: " + id);
            return;
        }

        // 从内存中移除
        ModItem removedItem = modItems.remove(id);
        plugin.getLogger().info("[调试] 已从内存中移除模组物品: " + id);

        // 从配置文件中移除
        if (modItemsConfig != null) {
            modItemsConfig.set("mod_items." + id, null);
            saveConfig();
            plugin.getLogger().info("[调试] 已从配置文件中删除模组物品: " + id);
        } else {
            plugin.getLogger().warning("[调试] 模组物品配置为空，无法保存删除操作");
        }

        plugin.getLogger().info("[调试] 模组物品删除完成: " + id);
    }
    
    /**
     * 重载配置
     */
    public void reload() {
        loadModItems();
    }
    
    /**
     * 保存配置
     */
    public void saveConfig() {
        try {
            modItemsConfig.save(modItemsFile);
        } catch (Exception e) {
            plugin.getLogger().severe("保存模组物品配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置对象（用于强制删除）
     */
    public org.bukkit.configuration.file.FileConfiguration getModItemsConfig() {
        return modItemsConfig;
    }

    /**
     * 保存模组物品到配置文件（包括chest_types）
     */
    public void saveModItemToConfig(ModItem modItem) {
        if (modItemsConfig == null) {
            plugin.getLogger().warning("模组物品配置文件未加载，无法保存");
            return;
        }

        String path = "mod_items." + modItem.getId();

        // 保存基本属性
        modItemsConfig.set(path + ".material", modItem.getMaterial().name());
        modItemsConfig.set(path + ".amount", modItem.getAmount());
        modItemsConfig.set(path + ".data", modItem.getData());
        modItemsConfig.set(path + ".name", modItem.getName());
        modItemsConfig.set(path + ".lore", modItem.getLore());

        // 保存模组特定属性
        modItemsConfig.set(path + ".mod_id", modItem.getModId());
        modItemsConfig.set(path + ".item_id", modItem.getItemId());
        modItemsConfig.set(path + ".custom_model_data", modItem.getCustomModelData());
        modItemsConfig.set(path + ".nbt_data", modItem.getNbtData());
        modItemsConfig.set(path + ".give_command", modItem.getGiveCommand());

        // 保存摸金箱属性
        modItemsConfig.set(path + ".probability", modItem.getProbability());
        modItemsConfig.set(path + ".search_speed", modItem.getSearchSpeed());
        modItemsConfig.set(path + ".commands", modItem.getCommands());

        // 保存摸金箱类型
        modItemsConfig.set(path + ".chest_types", modItem.getChestTypes());

        // 保存到文件
        saveConfig();

        plugin.getLogger().info("已保存模组物品到配置文件: " + modItem.getId() +
                               " (适用箱子: " + String.join(", ", modItem.getChestTypes()) + ")");
    }



    /**
     * 模组物品数据类
     */
    public static class ModItem {
        private final String id;
        private final Material material;
        private final int amount;
        private final short data;
        private final String name;
        private final List<String> lore;

        // 模组特定属性
        private final String modId;
        private final String itemId;
        private final int customModelData;
        private final String nbtData;
        private final String giveCommand;

        // 摸金箱属性
        private final double probability;
        private final int searchSpeed;
        private final List<String> commands;
        private final List<String> chestTypes; // 添加摸金箱类型支持

        public ModItem(String id, Material material, int amount, short data,
                      String name, List<String> lore,
                      String modId, String itemId, int customModelData,
                      String nbtData, String giveCommand,
                      double probability, int searchSpeed, List<String> commands, List<String> chestTypes) {
            this.id = id;
            this.material = material;
            this.amount = amount;
            this.data = data;
            this.name = name;
            this.lore = lore != null ? lore : new ArrayList<>();
            this.modId = modId;
            this.itemId = itemId;
            this.customModelData = customModelData;
            this.nbtData = nbtData;
            this.giveCommand = giveCommand;
            this.probability = probability;
            this.searchSpeed = searchSpeed;
            this.commands = commands != null ? commands : new ArrayList<>();
            this.chestTypes = chestTypes != null ? chestTypes : Arrays.asList("common"); // 默认为common
        }

        // Getters
        public String getId() { return id; }
        public Material getMaterial() { return material; }
        public int getAmount() { return amount; }
        public short getData() { return data; }
        public String getName() { return name; }
        public List<String> getLore() { return lore; }
        public String getModId() { return modId; }
        public String getItemId() { return itemId; }
        public int getCustomModelData() { return customModelData; }
        public String getNbtData() { return nbtData; }
        public String getGiveCommand() { return giveCommand; }
        public double getProbability() { return probability; }
        public int getSearchSpeed() { return searchSpeed; }
        public List<String> getCommands() { return commands; }
        public List<String> getChestTypes() { return chestTypes; } // 添加getter方法

        /**
         * 检查是否为模组物品
         */
        public boolean isModItem() {
            return modId != null && !modId.isEmpty();
        }

        /**
         * 获取完整的模组物品ID
         */
        public String getFullModItemId() {
            if (modId != null && itemId != null) {
                return modId + ":" + itemId;
            }
            return null;
        }
    }
}
