===============================================
    HangEvacuation 插件更新记录 - v1.6.0
===============================================

更新时间: 2025年6月5日
更新类型: 重大功能更新 - NMS多版本支持

===============================================
                核心更新内容
===============================================

🎯 【重大更新】NMS多版本兼容系统
   ✅ 新增NMS适配器架构，支持1.8-1.21.4全版本
   ✅ 自动版本检测和适配
   ✅ 完善的降级保护机制
   ✅ 统一的接口设计

🔧 【技术改进】版本兼容性处理
   ✅ 使用反射处理版本差异
   ✅ 兼容1.8.8基础API
   ✅ 智能降级处理不支持的功能
   ✅ 完善的异常处理机制

⚡【功能增强】新增管理命令
   ✅ /evac nms - 显示NMS适配器信息
   ✅ /evac version - 显示版本信息
   ✅ /evac test - 测试NMS功能

🛠️ 【代码优化】兼容性修复
   ✅ 修复音效播放兼容性问题
   ✅ 修复GUI相关API兼容性
   ✅ 优化全息图创建机制
   ✅ 改进错误处理逻辑

===============================================
                技术细节
===============================================

📦 新增文件结构:
   ├── nms/
   │   ├── interfaces/NMSAdapter.java
   │   ├── versions/NMSAdapter_1_8_R3.java
   │   └── NMSManager.java
   └── utils/VersionUtils.java

🔍 支持的服务端:
   • Spigot (1.8.x - 1.21.4)
   • Paper (1.8.x - 1.21.4)
   • PaperSpigot (1.8.x - 1.21.4)
   • Mohist (1.8.x - 1.21.4)

⚙️ 兼容性特性:
   • 自动版本检测
   • 智能功能降级
   • 反射调用处理
   • 异常安全保护

===============================================
                使用说明
===============================================

🎮 管理员命令:
   /evac nms      - 查看NMS适配器状态
   /evac version  - 查看版本兼容信息
   /evac test     - 测试NMS功能

📋 版本检查:
   插件启动时会自动检测服务器版本
   并显示兼容性信息和可用功能

🔧 故障排除:
   如果遇到兼容性问题，插件会自动
   切换到兼容模式，确保基础功能正常

===============================================
                重要说明
===============================================

⚠️  版本兼容性:
   • 本版本支持Minecraft 1.8.x - 1.21.4
   • 推荐使用Paper或Spigot服务端
   • 某些高级功能在低版本中会自动降级

🔄 升级建议:
   • 从旧版本升级前请备份数据
   • 首次使用建议在测试服务器验证
   • 如有问题请联系技术支持

📞 技术支持:
   • 作者: hangzong(航总)
   • 微信: hang060217
   • QQ群: 361919269

===============================================
                文件信息
===============================================

📁 生成文件:
   • HangEvacuation-1.6.0.jar (主文件)
   • HangEvacuation-1.6.0-obfuscated.jar (混淆版)

📍 文件位置:
   E:\插件\摸金\1.12.2\target\

💾 文件大小:
   约 200KB (包含所有功能)

🔐 安全性:
   已通过ProGuard混淆保护

===============================================
                更新历史
===============================================

v1.6.0 (2025-06-05)
- 新增NMS多版本兼容系统
- 支持1.8-1.21.4全版本
- 新增版本管理命令
- 优化兼容性处理

v1.5.0 (之前版本)
- 基础摸金箱系统
- 撤离系统
- 等级系统
- GUI管理界面

===============================================
            感谢使用 Hang系列插件
===============================================

🎉 HangEvacuation现已支持全版本兼容！
   从Minecraft 1.8到1.21.4，一个插件搞定！

🚀 未来计划:
   • 继续优化性能
   • 添加更多NMS功能
   • 支持更多服务端类型

💝 特别感谢:
   感谢所有用户的支持和反馈！

===============================================
