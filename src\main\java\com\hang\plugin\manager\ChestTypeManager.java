package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.logging.Level;
import java.util.regex.Pattern;
import java.util.regex.Matcher;

/**
 * 摸金箱种类管理器
 * 负责管理不同种类的摸金箱配置
 */
public class ChestTypeManager {
    
    private final HangPlugin plugin;
    private FileConfiguration config;
    private File configFile;
    private final Map<String, ChestType> chestTypes = new HashMap<>();
    
    public ChestTypeManager(HangPlugin plugin) {
        this.plugin = plugin;
        loadConfig();
        loadChestTypes();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        configFile = new File(plugin.getDataFolder(), "mojin.yml");
        
        // 如果配置文件不存在，从资源中复制
        if (!configFile.exists()) {
            try {
                plugin.getDataFolder().mkdirs();
                InputStream inputStream = plugin.getResource("mojin.yml");
                if (inputStream != null) {
                    Files.copy(inputStream, configFile.toPath());
                    // 不在这里输出信息，等待统一显示
                }
            } catch (IOException e) {
                plugin.getLogger().log(Level.SEVERE, "无法创建摸金箱种类配置文件", e);
            }
        }
        
        config = YamlConfiguration.loadConfiguration(configFile);
    }
    
    /**
     * 加载摸金箱种类
     */
    private void loadChestTypes() {
        chestTypes.clear();
        
        ConfigurationSection typesSection = config.getConfigurationSection("chest_types");
        if (typesSection == null) {
            plugin.getLogger().warning("摸金箱种类配置节点不存在！");
            return;
        }
        
        for (String typeId : typesSection.getKeys(false)) {
            try {
                ChestType chestType = loadChestType(typeId, typesSection.getConfigurationSection(typeId));
                if (chestType != null) {
                    chestTypes.put(typeId, chestType);
                    // 不在这里输出信息，等待统一显示
                }
            } catch (Exception e) {
                plugin.getLogger().log(Level.WARNING, "加载摸金箱种类失败: " + typeId, e);
            }
        }
    }
    
    /**
     * 加载单个摸金箱种类
     */
    private ChestType loadChestType(String typeId, ConfigurationSection section) {
        if (section == null) {
            return null;
        }
        
        String name = section.getString("name", "§6摸金箱");
        String displayName = section.getString("display_name", name);
        List<String> description = section.getStringList("description");
        String materialName = section.getString("material", "CHEST");
        int customModelData = section.getInt("custom_model_data", 0);
        String rarity = section.getString("rarity", "§f普通");
        String rarityColor = section.getString("rarity_color", "§f");
        boolean enabled = section.getBoolean("enabled", true);

        // 新增：摸金箱专属配置
        Object slotsConfig = section.get("slots", 5);
        int refreshTime = section.getInt("refresh_time", 5);
        
        // 验证材料（支持模组容器）
        Material material;
        try {
            // 首先尝试原版材料
            material = Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 如果不是原版材料，尝试通过NMS或其他方式获取模组材料
            material = getModMaterial(materialName);
            if (material == null) {
                plugin.getLogger().warning("摸金箱种类 " + typeId + " 的材料无效: " + materialName + "，使用默认材料 CHEST");
                material = Material.CHEST;
            } else {
                plugin.getLogger().info("摸金箱种类 " + typeId + " 使用模组材料: " + materialName);
            }
        }
        
        return new ChestType(typeId, name, displayName, description, material,
                           customModelData, rarity, rarityColor, enabled, slotsConfig, refreshTime);
    }
    
    /**
     * 获取摸金箱种类
     */
    public ChestType getChestType(String typeId) {
        return chestTypes.get(typeId);
    }
    
    /**
     * 获取所有摸金箱种类
     */
    public Collection<ChestType> getAllChestTypes() {
        return chestTypes.values();
    }

    /**
     * 获取所有摸金箱种类ID
     */
    public Set<String> getAllChestTypeIds() {
        return chestTypes.keySet();
    }

    /**
     * 获取摸金箱种类数量
     */
    public int getChestTypeCount() {
        return chestTypes.size();
    }
    
    /**
     * 获取启用的摸金箱种类
     */
    public Collection<ChestType> getEnabledChestTypes() {
        return chestTypes.values().stream()
                .filter(ChestType::isEnabled)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    /**
     * 获取默认摸金箱种类
     */
    public ChestType getDefaultChestType() {
        String defaultTypeId = config.getString("global_settings.default_type", "common");
        ChestType defaultType = chestTypes.get(defaultTypeId);
        
        if (defaultType == null && !chestTypes.isEmpty()) {
            // 如果默认种类不存在，返回第一个可用的种类
            defaultType = chestTypes.values().iterator().next();
        }
        
        return defaultType;
    }
    
    /**
     * 创建摸金箱物品
     */
    public ItemStack createChestItem(String typeId) {
        return createChestItem(typeId, 1);
    }
    
    /**
     * 创建摸金箱物品
     */
    public ItemStack createChestItem(String typeId, int amount) {
        ChestType chestType = getChestType(typeId);
        if (chestType == null) {
            chestType = getDefaultChestType();
        }
        
        if (chestType == null) {
            plugin.getLogger().warning("无法创建摸金箱物品：没有可用的摸金箱种类");
            return new ItemStack(Material.CHEST, amount);
        }
        
        return chestType.createItemStack(amount);
    }
    
    /**
     * 从物品获取摸金箱种类
     */
    public String getChestTypeFromItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return null;
        }
        
        ItemMeta meta = item.getItemMeta();
        if (!meta.hasDisplayName()) {
            return null;
        }
        
        String displayName = meta.getDisplayName();
        
        // 遍历所有种类，匹配显示名称
        for (ChestType chestType : chestTypes.values()) {
            if (chestType.getName().equals(displayName)) {
                return chestType.getTypeId();
            }
        }
        
        return null;
    }
    
    /**
     * 重载配置
     */
    public void reloadConfig() {
        loadConfig();
        loadChestTypes();
        plugin.getLogger().info("摸金箱种类配置已重载");
    }
    
    /**
     * 保存配置
     */
    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().log(Level.SEVERE, "无法保存摸金箱种类配置", e);
        }
    }

    /**
     * 添加新的摸金箱种类
     */
    public boolean addChestType(String typeId, String name, String displayName, List<String> description,
                               String materialName, int customModelData, String rarity, String rarityColor,
                               boolean enabled, Object slots, int refreshTime) {
        if (chestTypes.containsKey(typeId)) {
            plugin.getLogger().warning("摸金箱种类 " + typeId + " 已存在，无法添加");
            return false;
        }

        // 验证材料
        Material material;
        try {
            material = Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            material = getModMaterial(materialName);
            if (material == null) {
                plugin.getLogger().warning("无效的材料: " + materialName + "，使用默认材料 CHEST");
                material = Material.CHEST;
            }
        }

        // 创建新的摸金箱种类
        ChestType newChestType = new ChestType(typeId, name, displayName, description, material,
                                              customModelData, rarity, rarityColor, enabled, slots, refreshTime);

        // 添加到内存
        chestTypes.put(typeId, newChestType);

        // 保存到配置文件
        ConfigurationSection typeSection = config.createSection("chest_types." + typeId);
        typeSection.set("name", name);
        typeSection.set("display_name", displayName);
        typeSection.set("description", description);
        typeSection.set("material", materialName);
        typeSection.set("custom_model_data", customModelData);
        typeSection.set("rarity", rarity);
        typeSection.set("rarity_color", rarityColor);
        typeSection.set("enabled", enabled);
        typeSection.set("slots", slots);
        typeSection.set("refresh_time", refreshTime);

        // 保存配置
        saveConfig();

        plugin.getLogger().info("成功添加新的摸金箱种类: " + typeId + " (" + name + ")");
        return true;
    }

    /**
     * 移除摸金箱种类
     */
    public boolean removeChestType(String typeId) {
        if (!chestTypes.containsKey(typeId)) {
            plugin.getLogger().warning("摸金箱种类 " + typeId + " 不存在，无法移除");
            return false;
        }

        // 从内存中移除
        chestTypes.remove(typeId);

        // 从配置文件中移除
        config.set("chest_types." + typeId, null);

        // 保存配置
        saveConfig();

        plugin.getLogger().info("成功移除摸金箱种类: " + typeId);
        return true;
    }

    /**
     * 检查配置是否有效
     */
    public boolean validateConfig() {
        if (config == null) {
            plugin.getLogger().severe("配置文件未加载！");
            return false;
        }

        ConfigurationSection typesSection = config.getConfigurationSection("chest_types");
        if (typesSection == null) {
            plugin.getLogger().severe("配置文件中缺少 chest_types 节点！");
            return false;
        }

        if (typesSection.getKeys(false).isEmpty()) {
            plugin.getLogger().warning("配置文件中没有定义任何摸金箱种类！");
            return false;
        }

        plugin.getLogger().info("配置验证通过，共加载 " + chestTypes.size() + " 个摸金箱种类");
        return true;
    }
    
    /**
     * 获取模组材料
     * 尝试通过多种方式获取模组容器材料
     */
    private Material getModMaterial(String materialName) {
        try {
            // 方法1: 尝试通过NMS获取
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                Material modMaterial = plugin.getNMSManager().getMaterialByName(materialName);
                if (modMaterial != null) {
                    return modMaterial;
                }
            }

            // 方法2: 尝试通过Bukkit.getMaterial获取（支持命名空间）
            Material material = org.bukkit.Material.getMaterial(materialName.toUpperCase());
            if (material != null) {
                return material;
            }

            // 方法3: 尝试常见的模组容器映射
            String lowerName = materialName.toLowerCase();
            if (lowerName.contains("chest") || lowerName.contains("container") || lowerName.contains("box")) {
                // 如果是容器类型，但无法识别，使用相似的原版容器
                if (lowerName.contains("trapped") || lowerName.contains("trap")) {
                    return Material.TRAPPED_CHEST;
                } else if (lowerName.contains("ender")) {
                    return Material.ENDER_CHEST;
                } else if (lowerName.contains("shulker")) {
                    // 尝试获取潜影盒
                    try {
                        return Material.valueOf("SHULKER_BOX");
                    } catch (IllegalArgumentException e) {
                        try {
                            return Material.valueOf("WHITE_SHULKER_BOX");
                        } catch (IllegalArgumentException e2) {
                            return Material.CHEST;
                        }
                    }
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("获取模组材料时出错: " + materialName + " - " + e.getMessage());
        }

        return null; // 无法获取模组材料
    }

    /**
     * 是否启用调试模式
     */
    private boolean isDebugEnabled() {
        return config.getBoolean("debug.enabled", false);
    }
    
    /**
     * 获取配置对象
     */
    public FileConfiguration getConfig() {
        return config;
    }
    
    /**
     * 摸金箱种类数据类
     */
    public static class ChestType {
        private final String typeId;
        private final String name;
        private final String displayName;
        private final List<String> description;
        private final Material material;
        private final int customModelData;
        private final String rarity;
        private final String rarityColor;
        private final boolean enabled;
        private final Object slotsConfig; // 支持数字或字符串范围
        private final int refreshTime;

        // 范围随机的正则表达式
        private static final Pattern RANGE_PATTERN = Pattern.compile("^(\\d+)-(\\d+)$");

        public ChestType(String typeId, String name, String displayName, List<String> description,
                        Material material, int customModelData, String rarity, String rarityColor,
                        boolean enabled, Object slotsConfig, int refreshTime) {
            this.typeId = typeId;
            this.name = name;
            this.displayName = displayName;
            this.description = new ArrayList<>(description);
            this.material = material;
            this.customModelData = customModelData;
            this.rarity = rarity;
            this.rarityColor = rarityColor;
            this.enabled = enabled;
            this.slotsConfig = slotsConfig;
            this.refreshTime = refreshTime;
        }
        
        /**
         * 创建物品堆叠
         */
        public ItemStack createItemStack(int amount) {
            ItemStack item = new ItemStack(material, amount);
            ItemMeta meta = item.getItemMeta();
            
            if (meta != null) {
                meta.setDisplayName(name);
                
                List<String> lore = new ArrayList<>(description);
                lore.add("");
                lore.add("§7种类: " + rarity);
                lore.add("§7右键放置摸金箱");
                
                meta.setLore(lore);
                
                // 设置自定义模型数据（1.14+版本支持）
                if (customModelData > 0) {
                    try {
                        // 使用反射来调用setCustomModelData方法，兼容1.12.2
                        java.lang.reflect.Method setCustomModelDataMethod = meta.getClass().getMethod("setCustomModelData", int.class);
                        setCustomModelDataMethod.invoke(meta, customModelData);
                    } catch (Exception ignored) {
                        // 1.14以下版本不支持，忽略
                    }
                }
                
                item.setItemMeta(meta);
            }
            
            return item;
        }
        
        /**
         * 获取随机槽位数量
         * 支持固定数字和范围随机
         */
        public int getRandomSlots() {
            if (slotsConfig instanceof String) {
                String slotsStr = (String) slotsConfig;
                Matcher matcher = RANGE_PATTERN.matcher(slotsStr);
                if (matcher.matches()) {
                    int min = Integer.parseInt(matcher.group(1));
                    int max = Integer.parseInt(matcher.group(2));
                    if (min <= max) {
                        return min + new Random().nextInt(max - min + 1);
                    }
                }
                // 如果字符串格式错误，尝试解析为数字
                try {
                    return Integer.parseInt(slotsStr);
                } catch (NumberFormatException e) {
                    return 5; // 默认值
                }
            } else if (slotsConfig instanceof Number) {
                return ((Number) slotsConfig).intValue();
            }
            return 5; // 默认值
        }

        /**
         * 获取槽位配置的显示文本
         */
        public String getSlotsDisplayText() {
            if (slotsConfig instanceof String) {
                String slotsStr = (String) slotsConfig;
                Matcher matcher = RANGE_PATTERN.matcher(slotsStr);
                if (matcher.matches()) {
                    return slotsStr + " (随机)";
                }
                return slotsStr;
            } else if (slotsConfig instanceof Number) {
                return String.valueOf(((Number) slotsConfig).intValue());
            }
            return "5";
        }

        /**
         * 检查是否为范围随机配置
         */
        public boolean isRandomSlots() {
            if (slotsConfig instanceof String) {
                return RANGE_PATTERN.matcher((String) slotsConfig).matches();
            }
            return false;
        }

        // Getters
        public String getTypeId() { return typeId; }
        public String getName() { return name; }
        public String getDisplayName() { return displayName; }
        public List<String> getDescription() { return new ArrayList<>(description); }
        public Material getMaterial() { return material; }
        public int getCustomModelData() { return customModelData; }
        public String getRarity() { return rarity; }
        public String getRarityColor() { return rarityColor; }
        public boolean isEnabled() { return enabled; }

        /**
         * 获取固定槽位数量（向后兼容）
         * @deprecated 使用 getRandomSlots() 代替
         */
        @Deprecated
        public int getSlots() {
            return getRandomSlots();
        }

        public Object getSlotsConfig() { return slotsConfig; }
        public int getRefreshTime() { return refreshTime; }
    }
}
