handler=Block #CQ, types=[Ljava/lang/IllegalArgumentException;], range=[#U...#U]
handler=Block #CT, types=[Ljava/lang/IllegalArgumentException;], range=[#Y...#Y]
handler=Block #CW, types=[Ljava/lang/IllegalArgumentException;], range=[#AA...#AA]
handler=Block #CZ, types=[Ljava/lang/IllegalArgumentException;], range=[#AF...#AF]
handler=Block #DC, types=[Ljava/lang/IllegalArgumentException;], range=[#Q...#Q]
handler=Block #DF, types=[Ljava/io/IOException;], range=[Block #AL, Block #AK]
handler=Block #DJ, types=[Ljava/lang/RuntimeException;], range=[Block #AO, Block #AN]
handler=Block #DN, types=[Ljava/lang/IllegalAccessException;], range=[Block #AR, Block #AQ]
===#Block A(size=5, flags=1)===
   0. lvar125 = {858542979 ^ {2022065543 ^ 1394828066}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1001674448)
      goto AX
   4. lvar125 = {1760056771 ^ lvar125};
      -> ConditionalJump[IF_ICMPNE] #A -> #AX
      -> Immediate #A -> #B
===#Block B(size=53, flags=0)===
   0. lvar3 = {1885413621 ^ lvar125};
   1. lvar12 = new java.lang.String[lvar3];
   2. lvar4 = {1885413605 ^ lvar125};
   3. lvar5 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.kjwewfxlkndfxii(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   4. lvar12[lvar4] = lvar5;
   5. lvar91 = {1885413604 ^ lvar125};
   6. lvar106 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.iysmctgaicwpjlf(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   7. lvar12[lvar91] = lvar106;
   8. lvar92 = {1885413607 ^ lvar125};
   9. lvar107 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.yomnhgvxbjiusde(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   10. lvar12[lvar92] = lvar107;
   11. lvar93 = {1885413606 ^ lvar125};
   12. lvar108 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.amaixukykxxfqmk(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   13. lvar12[lvar93] = lvar108;
   14. lvar94 = {1885413601 ^ lvar125};
   15. lvar109 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.szpkjtoquygzsba(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   16. lvar12[lvar94] = lvar109;
   17. lvar95 = {1885413600 ^ lvar125};
   18. lvar110 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.rmtutkobibmrwbg(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   19. lvar12[lvar95] = lvar110;
   20. lvar96 = {1885413603 ^ lvar125};
   21. lvar111 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.zxucqxoyrndhsvq(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   22. lvar12[lvar96] = lvar111;
   23. lvar97 = {1885413602 ^ lvar125};
   24. lvar112 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.xuweaccxjgazflh(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   25. lvar12[lvar97] = lvar112;
   26. lvar98 = {1885413613 ^ lvar125};
   27. lvar113 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.zanudnimkjalnie(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   28. lvar12[lvar98] = lvar113;
   29. lvar99 = {1885413612 ^ lvar125};
   30. lvar114 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.cthicyhtsvblieb(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   31. lvar12[lvar99] = lvar114;
   32. lvar100 = {1885413615 ^ lvar125};
   33. lvar115 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.yhynkhtijngvwgn(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   34. lvar12[lvar100] = lvar115;
   35. lvar101 = {1885413614 ^ lvar125};
   36. lvar116 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.wtiazczkeijjedm(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   37. lvar12[lvar101] = lvar116;
   38. lvar102 = {1885413609 ^ lvar125};
   39. lvar117 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.dfqvjnidsqoibhe(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   40. lvar12[lvar102] = lvar117;
   41. lvar103 = {1885413608 ^ lvar125};
   42. lvar118 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.txfykwfqdirnjyd(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   43. lvar12[lvar103] = lvar118;
   44. lvar104 = {1885413611 ^ lvar125};
   45. lvar119 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.pmebwwedlheytwt(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   46. lvar12[lvar104] = lvar119;
   47. lvar105 = {1885413610 ^ lvar125};
   48. lvar120 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.iccokenakmeduby(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   49. lvar12[lvar105] = lvar120;
   50. lvar6 = lvar12;
   51. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -2096560341)
      goto BK
   52. lvar125 = {1321623884 ^ lvar125};
      -> Immediate #B -> #C
      -> ConditionalJump[IF_ICMPNE] #B -> #BK
      <- Immediate #A -> #B
===#Block BK(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -2096560341)
      goto BK
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1572605387 ^ lvar125})
      goto BK
   2. _consume({1789373614 ^ lvar125});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BK -> #BK
      <- ConditionalJump[IF_ICMPNE] #BK -> #BK
      <- ConditionalJump[IF_ICMPNE] #B -> #BK
===#Block C(size=4, flags=0)===
   0. lvar13 = lvar1;
   1. if (lvar13 < {1051148713 ^ lvar125})
      goto CB
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -180744887)
      goto BM
   3. lvar125 = {1446042356 ^ lvar125};
      -> Immediate #C -> #D
      -> ConditionalJump[IF_ICMPLT] #C -> #CB
      -> ConditionalJump[IF_ICMPNE] #C -> #BM
      <- Immediate #B -> #C
===#Block BM(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -180744887)
      goto BM
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {948487216 ^ lvar125})
      goto BM
   2. _consume({1299151311 ^ lvar125});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BM -> #BM
      <- ConditionalJump[IF_ICMPNE] #C -> #BM
      <- ConditionalJump[IF_ICMPNE] #BM -> #BM
===#Block CB(size=2, flags=10100)===
   0. lvar125 = {564047347 ^ lvar125};
   1. goto E
      -> UnconditionalJump[GOTO] #CB -> #E
      <- ConditionalJump[IF_ICMPLT] #C -> #CB
===#Block D(size=6, flags=0)===
   0. lvar14 = lvar1;
   1. lvar74 = lvar6;
   2. lvar75 = lvar74.length;
   3. if (lvar14 < lvar75)
      goto BX
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1153227499)
      goto AU
   5. lvar125 = {2007922439 ^ lvar125};
      -> ConditionalJump[IF_ICMPLT] #D -> #BX
      -> Immediate #D -> #E
      -> ConditionalJump[IF_ICMPNE] #D -> #AU
      <- Immediate #C -> #D
===#Block E(size=5, flags=0)===
   0. // Frame: locals[1] [[Ljava/lang/String;] stack[0] []
   1. lvar15 = {523889754 ^ lvar125};
   2. lvar1 = lvar15;
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -103849264)
      goto AZ
   4. lvar125 = {138948392 ^ lvar125};
      -> Immediate #E -> #F
      -> ConditionalJump[IF_ICMPNE] #E -> #AZ
      <- Immediate #D -> #E
      <- UnconditionalJump[GOTO] #CB -> #E
===#Block BX(size=2, flags=10100)===
   0. lvar125 = {2145802287 ^ lvar125};
   1. goto F
      -> UnconditionalJump[GOTO] #BX -> #F
      <- ConditionalJump[IF_ICMPLT] #D -> #BX
===#Block F(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar6;
   2. lvar76 = lvar1;
   3. lvar17 = lvar16[lvar76];
   4. lvar7 = lvar17;
   5. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1148314736)
      goto AT
   6. lvar125 = {961333925 ^ lvar125};
      -> ConditionalJump[IF_ICMPNE] #F -> #AT
      -> Immediate #F -> #G
      <- Immediate #E -> #F
      <- UnconditionalJump[GOTO] #BX -> #F
===#Block G(size=9, flags=0)===
   0. lvar18 = lvar0;
   1. lvar8 = lvar18;
   2. lvar19 = {-775752152 ^ lvar125};
   3. lvar9 = lvar19;
   4. lvar20 = lvar8;
   5. lvar21 = lvar20.hashCode();
   6. svar127 = {lvar21 ^ lvar125};
   7. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(svar127)) {
      case 155643472:
      	 goto	#CC
      case 155860312:
      	 goto	#CE
      case 222451894:
      	 goto	#CG
      case 223333632:
      	 goto	#CH
      default:
      	 goto	#CJ
   }
   8. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1911049913)
      goto AV
      -> Switch[155643472] #G -> #CC
      -> DefaultSwitch #G -> #CJ
      -> Switch[223333632] #G -> #CH
      -> ConditionalJump[IF_ICMPNE] #G -> #AV
      -> Switch[155860312] #G -> #CE
      -> Switch[222451894] #G -> #CG
      <- Immediate #F -> #G
===#Block CG(size=2, flags=10100)===
   0. lvar125 = {997574149 ^ lvar125};
   1. goto N
      -> UnconditionalJump[GOTO] #CG -> #N
      <- Switch[222451894] #G -> #CG
===#Block N(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar71 = lvar8;
   2. lvar90 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.mkrrzaohcihlfvm(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   3. lvar72 = lvar71.equals(lvar90);
   4. if (lvar72 == {357093330 ^ lvar125})
      goto BV
   5. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1438220656)
      goto AX
   6. lvar125 = {506920855 ^ lvar125};
      -> ConditionalJump[IF_ICMPEQ] #N -> #BV
      -> ConditionalJump[IF_ICMPNE] #N -> #AX
      -> Immediate #N -> #O
      <- UnconditionalJump[GOTO] #CG -> #N
===#Block O(size=4, flags=0)===
   0. lvar73 = {192819271 ^ lvar125};
   1. lvar9 = lvar73;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1542554152)
      goto BF
   3. goto BP
      -> ConditionalJump[IF_ICMPNE] #O -> #BF
      -> UnconditionalJump[GOTO] #O -> #BP
      <- Immediate #N -> #O
===#Block BP(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 105150203:
      	 goto	#BQ
      case 289313933:
      	 goto	#BP
      case 584824172:
      	 goto	#AW
      case 982112897:
      	 goto	#AO
      default:
      	 goto	#AW
   }
      -> Switch[584824172] #BP -> #AW
      -> DefaultSwitch #BP -> #AW
      -> Immediate #BP -> #BQ
      -> Switch[982112897] #BP -> #AO
      -> Switch[105150203] #BP -> #BQ
      -> Switch[289313933] #BP -> #BP
      <- UnconditionalJump[GOTO] #O -> #BP
      <- Switch[289313933] #BP -> #BP
===#Block BQ(size=2, flags=100)===
   0. lvar125 = {1149982233 ^ lvar125};
   1. goto AO
      -> UnconditionalJump[GOTO] #BQ -> #AO
      <- Immediate #BP -> #BQ
      <- Switch[105150203] #BP -> #BQ
===#Block AO(size=3, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125) == 183187879)
      goto AN
   1. throw nullconst;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 2141926114)
      goto BL
      -> TryCatch range: [AO...AN] -> DJ ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AO -> #AN
      -> ConditionalJump[IF_ICMPNE] #AO -> #BL
      <- Switch[982112897] #BP -> #AO
      <- UnconditionalJump[GOTO] #BQ -> #AO
===#Block BL(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 2141926114)
      goto BL
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {2092964897 ^ lvar125})
      goto BL
   2. _consume({1083298172 ^ lvar125});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BL -> #BL
      <- ConditionalJump[IF_ICMPNE] #BL -> #BL
      <- ConditionalJump[IF_ICMPNE] #AO -> #BL
===#Block AN(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1918185278)
      goto BE
      -> ConditionalJump[IF_ICMPNE] #AN -> #BE
      -> TryCatch range: [AO...AN] -> DJ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AO -> #AN
===#Block BE(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1918185278)
      goto BE
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {2047049623 ^ lvar125})
      goto BE
   2. _consume({1882952147 ^ lvar125});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BE -> #BE
      <- ConditionalJump[IF_ICMPNE] #AN -> #BE
      <- ConditionalJump[IF_ICMPNE] #BE -> #BE
===#Block DJ(size=1, flags=0)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.pchqajqczyolsclb(lvar125)) {
      case -1918185278:
      	 goto	#DL
      case 2141926114:
      	 goto	#DK
      default:
      	 goto	#DM
   }
      -> DefaultSwitch #DJ -> #DM
      -> Switch[2141926114] #DJ -> #DK
      -> Switch[-1918185278] #DJ -> #DL
      <- TryCatch range: [AO...AN] -> DJ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AO...AN] -> DJ ([Ljava/lang/RuntimeException;])
===#Block DL(size=2, flags=10100)===
   0. lvar125 = {875773148 ^ lvar125};
   1. goto AP
      -> UnconditionalJump[GOTO] #DL -> #AP
      <- Switch[-1918185278] #DJ -> #DL
===#Block DK(size=2, flags=10100)===
   0. lvar125 = com.hang.plugin.utils.VersionUtils.wdmwkfmwwmzsshen(lvar125, 712179864);
   1. goto AP
      -> UnconditionalJump[GOTO] #DK -> #AP
      <- Switch[2141926114] #DJ -> #DK
===#Block AP(size=3, flags=0)===
   0. _consume(catch());
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 741553699)
      goto AX
   2. goto BN
      -> ConditionalJump[IF_ICMPNE] #AP -> #AX
      -> UnconditionalJump[GOTO] #AP -> #BN
      <- UnconditionalJump[GOTO] #DK -> #AP
      <- UnconditionalJump[GOTO] #DL -> #AP
===#Block BN(size=2, flags=10100)===
   0. lvar125 = com.hang.plugin.utils.VersionUtils.wdmwkfmwwmzsshen(lvar125, 1684651989);
   1. goto P
      -> UnconditionalJump[GOTO] #BN -> #P
      <- UnconditionalJump[GOTO] #AP -> #BN
===#Block DM(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #DJ -> #DM
===#Block BF(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1542554152)
      goto BF
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1774382598 ^ lvar125})
      goto BF
   2. _consume({1507968515 ^ lvar125});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BF -> #BF
      <- ConditionalJump[IF_ICMPNE] #O -> #BF
      <- ConditionalJump[IF_ICMPNE] #BF -> #BF
===#Block BV(size=2, flags=10100)===
   0. lvar125 = {346494659 ^ lvar125};
   1. goto P
      -> UnconditionalJump[GOTO] #BV -> #P
      <- ConditionalJump[IF_ICMPEQ] #N -> #BV
===#Block CE(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 160853293:
      	 goto	#CF
      case 1004254715:
      	 goto	#CE
      case 1012605479:
      	 goto	#J
      case 1658161089:
      	 goto	#BG
      default:
      	 goto	#BG
   }
      -> Switch[160853293] #CE -> #CF
      -> Switch[1012605479] #CE -> #J
      -> Immediate #CE -> #CF
      -> Switch[1004254715] #CE -> #CE
      -> Switch[1658161089] #CE -> #BG
      -> DefaultSwitch #CE -> #BG
      <- Switch[1004254715] #CE -> #CE
      <- Switch[155860312] #G -> #CE
===#Block CF(size=2, flags=100)===
   0. lvar125 = {304501035 ^ lvar125};
   1. goto J
      -> UnconditionalJump[GOTO] #CF -> #J
      <- Switch[160853293] #CE -> #CF
      <- Immediate #CE -> #CF
===#Block J(size=7, flags=0)===
   0. // Frame: locals[3] [java/lang/String, java/lang/String, 1] stack[0] []
   1. lvar65 = lvar8;
   2. lvar88 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.msrxhoeqtbyvctx(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   3. lvar66 = lvar65.equals(lvar88);
   4. if (lvar66 == {1008425212 ^ lvar125})
      goto BZ
   5. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -522532895)
      goto AT
   6. lvar125 = {39140265 ^ lvar125};
      -> Immediate #J -> #K
      -> ConditionalJump[IF_ICMPNE] #J -> #AT
      -> ConditionalJump[IF_ICMPEQ] #J -> #BZ
      <- Switch[1012605479] #CE -> #J
      <- UnconditionalJump[GOTO] #CF -> #J
===#Block BZ(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 74760307:
      	 goto	#CA
      case 977266565:
      	 goto	#BZ
      case 1532883505:
      	 goto	#AV
      case 1967369005:
      	 goto	#P
      default:
      	 goto	#AV
   }
      -> Immediate #BZ -> #CA
      -> Switch[1967369005] #BZ -> #P
      -> Switch[1532883505] #BZ -> #AV
      -> DefaultSwitch #BZ -> #AV
      -> Switch[74760307] #BZ -> #CA
      -> Switch[977266565] #BZ -> #BZ
      <- Switch[977266565] #BZ -> #BZ
      <- ConditionalJump[IF_ICMPEQ] #J -> #BZ
===#Block CA(size=2, flags=100)===
   0. lvar125 = {1039434221 ^ lvar125};
   1. goto P
      -> UnconditionalJump[GOTO] #CA -> #P
      <- Immediate #BZ -> #CA
      <- Switch[74760307] #BZ -> #CA
===#Block K(size=4, flags=0)===
   0. lvar67 = {1045324629 ^ lvar125};
   1. lvar9 = lvar67;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -227337559)
      goto BD
   3. goto BO
      -> UnconditionalJump[GOTO] #K -> #BO
      -> ConditionalJump[IF_ICMPNE] #K -> #BD
      <- Immediate #J -> #K
===#Block BO(size=2, flags=10100)===
   0. lvar125 = com.hang.plugin.utils.VersionUtils.wdmwkfmwwmzsshen(lvar125, 273764322);
   1. goto AL
      -> UnconditionalJump[GOTO] #BO -> #AL
      <- UnconditionalJump[GOTO] #K -> #BO
===#Block AL(size=3, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125) == 157042877)
      goto AK
   1. throw nullconst;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1895392697)
      goto AU
      -> ConditionalJump[IF_ICMPNE] #AL -> #AU
      -> TryCatch range: [AL...AK] -> DF ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #AL -> #AK
      <- UnconditionalJump[GOTO] #BO -> #AL
===#Block AK(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1891936448)
      goto AZ
      -> TryCatch range: [AL...AK] -> DF ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AK -> #AZ
      <- ConditionalJump[IF_ICMPEQ] #AL -> #AK
===#Block DF(size=1, flags=0)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.pchqajqczyolsclb(lvar125)) {
      case -1891936448:
      	 goto	#DH
      case 1895392697:
      	 goto	#DG
      default:
      	 goto	#DI
   }
      -> DefaultSwitch #DF -> #DI
      -> Switch[1895392697] #DF -> #DG
      -> Switch[-1891936448] #DF -> #DH
      <- TryCatch range: [AL...AK] -> DF ([Ljava/io/IOException;])
      <- TryCatch range: [AL...AK] -> DF ([Ljava/io/IOException;])
===#Block DH(size=2, flags=10100)===
   0. lvar125 = {2101488991 ^ lvar125};
   1. goto AM
      -> UnconditionalJump[GOTO] #DH -> #AM
      <- Switch[-1891936448] #DF -> #DH
===#Block DG(size=2, flags=10100)===
   0. lvar125 = {1119513344 ^ lvar125};
   1. goto AM
      -> UnconditionalJump[GOTO] #DG -> #AM
      <- Switch[1895392697] #DF -> #DG
===#Block AM(size=3, flags=0)===
   0. _consume(catch());
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1697267131)
      goto AW
   2. goto BS
      -> ConditionalJump[IF_ICMPNE] #AM -> #AW
      -> UnconditionalJump[GOTO] #AM -> #BS
      <- UnconditionalJump[GOTO] #DG -> #AM
      <- UnconditionalJump[GOTO] #DH -> #AM
===#Block BS(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 41965143:
      	 goto	#BT
      case 574420356:
      	 goto	#P
      case 1093594082:
      	 goto	#BS
      case 1996748635:
      	 goto	#BC
      default:
      	 goto	#BC
   }
      -> Switch[41965143] #BS -> #BT
      -> Switch[574420356] #BS -> #P
      -> Immediate #BS -> #BT
      -> DefaultSwitch #BS -> #BC
      -> Switch[1996748635] #BS -> #BC
      -> Switch[1093594082] #BS -> #BS
      <- UnconditionalJump[GOTO] #AM -> #BS
      <- Switch[1093594082] #BS -> #BS
===#Block BT(size=2, flags=100)===
   0. lvar125 = {1833606822 ^ lvar125};
   1. goto P
      -> UnconditionalJump[GOTO] #BT -> #P
      <- Switch[41965143] #BS -> #BT
      <- Immediate #BS -> #BT
===#Block DI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #DF -> #DI
===#Block CH(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 160853293:
      	 goto	#CI
      case 603638333:
      	 goto	#CH
      case 1116063398:
      	 goto	#L
      case 1204163028:
      	 goto	#AZ
      default:
      	 goto	#AZ
   }
      -> Immediate #CH -> #CI
      -> Switch[160853293] #CH -> #CI
      -> DefaultSwitch #CH -> #AZ
      -> Switch[1116063398] #CH -> #L
      -> Switch[1204163028] #CH -> #AZ
      -> Switch[603638333] #CH -> #CH
      <- Switch[223333632] #G -> #CH
      <- Switch[603638333] #CH -> #CH
===#Block CI(size=2, flags=100)===
   0. lvar125 = {2082814299 ^ lvar125};
   1. goto L
      -> UnconditionalJump[GOTO] #CI -> #L
      <- Immediate #CH -> #CI
      <- Switch[160853293] #CH -> #CI
===#Block L(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar68 = lvar8;
   2. lvar89 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.gcugttmewpfrjzc(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   3. lvar69 = lvar68.equals(lvar89);
   4. if (lvar69 == {1377317004 ^ lvar125})
      goto BY
   5. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1866365854)
      goto BB
   6. lvar125 = {1283248864 ^ lvar125};
      -> ConditionalJump[IF_ICMPEQ] #L -> #BY
      -> ConditionalJump[IF_ICMPNE] #L -> #BB
      -> Immediate #L -> #M
      <- UnconditionalJump[GOTO] #CI -> #L
      <- Switch[1116063398] #CH -> #L
===#Block M(size=4, flags=0)===
   0. lvar70 = {509928045 ^ lvar125};
   1. lvar9 = lvar70;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -215542944)
      goto BJ
   3. goto BU
      -> ConditionalJump[IF_ICMPNE] #M -> #BJ
      -> UnconditionalJump[GOTO] #M -> #BU
      <- Immediate #L -> #M
===#Block BU(size=2, flags=10100)===
   0. lvar125 = com.hang.plugin.utils.VersionUtils.wdmwkfmwwmzsshen(lvar125, 440127590);
   1. goto AR
      -> UnconditionalJump[GOTO] #BU -> #AR
      <- UnconditionalJump[GOTO] #M -> #BU
===#Block AR(size=3, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125) == 68762294)
      goto AQ
   1. throw nullconst;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 586780752)
      goto AT
      -> TryCatch range: [AR...AQ] -> DN ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AR -> #AQ
      -> ConditionalJump[IF_ICMPNE] #AR -> #AT
      <- UnconditionalJump[GOTO] #BU -> #AR
===#Block AQ(size=2, flags=0)===
   0. throw new java/lang/IllegalAccessException();
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -2085180381)
      goto AZ
      -> ConditionalJump[IF_ICMPNE] #AQ -> #AZ
      -> TryCatch range: [AR...AQ] -> DN ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AR -> #AQ
===#Block AZ(size=10, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -103849264)
      goto AZ
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {924449980 ^ lvar125})
      goto AZ
   2. _consume({989258037 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1891936448)
      goto AZ
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {119516679 ^ lvar125})
      goto AZ
   5. _consume({1929941049 ^ lvar125});
   6. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -2085180381)
      goto AZ
   7. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1803955385 ^ lvar125})
      goto AZ
   8. _consume({940971343 ^ lvar125});
   9. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AQ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #E -> #AZ
      <- DefaultSwitch #CH -> #AZ
      <- Switch[1204163028] #CH -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AK -> #AZ
===#Block DN(size=1, flags=0)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.pchqajqczyolsclb(lvar125)) {
      case -2085180381:
      	 goto	#DP
      case 586780752:
      	 goto	#DO
      default:
      	 goto	#DQ
   }
      -> DefaultSwitch #DN -> #DQ
      -> Switch[586780752] #DN -> #DO
      -> Switch[-2085180381] #DN -> #DP
      <- TryCatch range: [AR...AQ] -> DN ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AR...AQ] -> DN ([Ljava/lang/IllegalAccessException;])
===#Block DP(size=2, flags=10100)===
   0. lvar125 = {890585823 ^ lvar125};
   1. goto AS
      -> UnconditionalJump[GOTO] #DP -> #AS
      <- Switch[-2085180381] #DN -> #DP
===#Block DO(size=2, flags=10100)===
   0. lvar125 = {1094493521 ^ lvar125};
   1. goto AS
      -> UnconditionalJump[GOTO] #DO -> #AS
      <- Switch[586780752] #DN -> #DO
===#Block AS(size=3, flags=0)===
   0. _consume(catch());
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 723303130)
      goto AT
   2. goto BR
      -> UnconditionalJump[GOTO] #AS -> #BR
      -> ConditionalJump[IF_ICMPNE] #AS -> #AT
      <- UnconditionalJump[GOTO] #DO -> #AS
      <- UnconditionalJump[GOTO] #DP -> #AS
===#Block BR(size=2, flags=10100)===
   0. lvar125 = {1150045770 ^ lvar125};
   1. goto P
      -> UnconditionalJump[GOTO] #BR -> #P
      <- UnconditionalJump[GOTO] #AS -> #BR
===#Block DQ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #DN -> #DQ
===#Block BJ(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -215542944)
      goto BJ
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1079248224 ^ lvar125})
      goto BJ
   2. _consume({1446674007 ^ lvar125});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BJ -> #BJ
      <- ConditionalJump[IF_ICMPNE] #M -> #BJ
      <- ConditionalJump[IF_ICMPNE] #BJ -> #BJ
===#Block BB(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1866365854)
      goto BB
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {760878129 ^ lvar125})
      goto BB
   2. _consume({955791368 ^ lvar125});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #L -> #BB
===#Block BY(size=2, flags=10100)===
   0. lvar125 = {1408756125 ^ lvar125};
   1. goto P
      -> UnconditionalJump[GOTO] #BY -> #P
      <- ConditionalJump[IF_ICMPEQ] #L -> #BY
===#Block CJ(size=2, flags=10100)===
   0. lvar125 = {802345158 ^ lvar125};
   1. goto P
      -> UnconditionalJump[GOTO] #CJ -> #P
      <- DefaultSwitch #G -> #CJ
===#Block CC(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 160853293:
      	 goto	#CD
      case 580193361:
      	 goto	#CC
      case 1078704770:
      	 goto	#BD
      case 1275743661:
      	 goto	#H
      default:
      	 goto	#BD
   }
      -> DefaultSwitch #CC -> #BD
      -> Immediate #CC -> #CD
      -> Switch[1078704770] #CC -> #BD
      -> Switch[580193361] #CC -> #CC
      -> Switch[1275743661] #CC -> #H
      -> Switch[160853293] #CC -> #CD
      <- Switch[155643472] #G -> #CC
      <- Switch[580193361] #CC -> #CC
===#Block CD(size=2, flags=100)===
   0. lvar125 = {1714608748 ^ lvar125};
   1. goto H
      -> UnconditionalJump[GOTO] #CD -> #H
      <- Immediate #CC -> #CD
      <- Switch[160853293] #CC -> #CD
===#Block H(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar8;
   2. lvar77 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.knkauhhudsyyxrw(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   3. lvar23 = lvar22.equals(lvar77);
   4. if (lvar23 == {1208996795 ^ lvar125})
      goto BW
   5. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1082039770)
      goto BI
   6. lvar125 = {2008300617 ^ lvar125};
      -> Immediate #H -> #I
      -> ConditionalJump[IF_ICMPNE] #H -> #BI
      -> ConditionalJump[IF_ICMPEQ] #H -> #BW
      <- Switch[1275743661] #CC -> #H
      <- UnconditionalJump[GOTO] #CD -> #H
===#Block BW(size=2, flags=10100)===
   0. lvar125 = {1239419562 ^ lvar125};
   1. goto P
      -> UnconditionalJump[GOTO] #BW -> #P
      <- ConditionalJump[IF_ICMPEQ] #H -> #BW
===#Block BI(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1082039770)
      goto BI
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1404640452 ^ lvar125})
      goto BI
   2. _consume({972655752 ^ lvar125});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- ConditionalJump[IF_ICMPNE] #H -> #BI
===#Block I(size=4, flags=0)===
   0. lvar24 = {1069281265 ^ lvar125};
   1. lvar9 = lvar24;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -35684463)
      goto AV
   3. lvar125 = {1045706467 ^ lvar125};
      -> ConditionalJump[IF_ICMPNE] #I -> #AV
      -> Immediate #I -> #P
      <- Immediate #H -> #I
===#Block P(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar9;
   2. svar127 = {lvar25 ^ lvar125};
   3. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(svar127)) {
      case 30464622:
      	 goto	#CK
      case 30464623:
      	 goto	#CL
      case 30464656:
      	 goto	#CN
      default:
      	 goto	#CP
   }
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 259967112)
      goto BA
      -> Switch[30464622] #P -> #CK
      -> DefaultSwitch #P -> #CP
      -> Switch[30464623] #P -> #CL
      -> ConditionalJump[IF_ICMPNE] #P -> #BA
      -> Switch[30464656] #P -> #CN
      <- Switch[574420356] #BS -> #P
      <- UnconditionalJump[GOTO] #BT -> #P
      <- Switch[1967369005] #BZ -> #P
      <- UnconditionalJump[GOTO] #CJ -> #P
      <- UnconditionalJump[GOTO] #BV -> #P
      <- UnconditionalJump[GOTO] #BW -> #P
      <- UnconditionalJump[GOTO] #BN -> #P
      <- UnconditionalJump[GOTO] #CA -> #P
      <- UnconditionalJump[GOTO] #BY -> #P
      <- UnconditionalJump[GOTO] #BR -> #P
      <- Immediate #I -> #P
===#Block CN(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 30464656:
      	 goto	#CO
      case 228550791:
      	 goto	#Y
      case 463913978:
      	 goto	#CN
      case 1404126654:
      	 goto	#AX
      default:
      	 goto	#AX
   }
      -> DefaultSwitch #CN -> #AX
      -> Switch[228550791] #CN -> #Y
      -> Switch[1404126654] #CN -> #AX
      -> Switch[463913978] #CN -> #CN
      -> Switch[30464656] #CN -> #CO
      -> Immediate #CN -> #CO
      <- Switch[463913978] #CN -> #CN
      <- Switch[30464656] #P -> #CN
===#Block CO(size=2, flags=100)===
   0. lvar125 = {722928418 ^ lvar125};
   1. goto Y
      -> UnconditionalJump[GOTO] #CO -> #Y
      <- Switch[30464656] #CN -> #CO
      <- Immediate #CN -> #CO
===#Block Y(size=11, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar42 = new java.lang.StringBuilder;
   2. _consume(lvar42.<init>());
   3. lvar82 = lvar7;
   4. lvar43 = lvar42.append(lvar82);
   5. lvar83 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.cboluowlwukxhew(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   6. lvar44 = lvar43.append(lvar83);
   7. lvar45 = lvar44.toString();
   8. lvar46 = org.bukkit.Material.valueOf(lvar45);
   9. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1472647577)
      goto BC
   10. lvar125 = {991032036 ^ lvar125};
      -> Immediate #Y -> #AE
      -> ConditionalJump[IF_ICMPNE] #Y -> #BC
      -> TryCatch range: [Y...Y] -> CT ([Ljava/lang/IllegalArgumentException;])
      <- UnconditionalJump[GOTO] #CO -> #Y
      <- Switch[228550791] #CN -> #Y
===#Block CT(size=1, flags=0)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.pchqajqczyolsclb(lvar125)) {
      case 1472647577:
      	 goto	#CU
      default:
      	 goto	#CV
   }
      -> Switch[1472647577] #CT -> #CU
      -> DefaultSwitch #CT -> #CV
      <- TryCatch range: [Y...Y] -> CT ([Ljava/lang/IllegalArgumentException;])
===#Block CV(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #CT -> #CV
===#Block CU(size=2, flags=10100)===
   0. lvar125 = {410574364 ^ lvar125};
   1. goto Z
      -> UnconditionalJump[GOTO] #CU -> #Z
      <- Switch[1472647577] #CT -> #CU
===#Block Z(size=5, flags=0)===
   0. lvar47 = catch();
   1. // Frame: locals[0] [] stack[1] [java/lang/IllegalArgumentException]
   2. lvar122 = lvar47;
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1811930759)
      goto AY
   4. lvar125 = {1276495437 ^ lvar125};
      -> ConditionalJump[IF_ICMPNE] #Z -> #AY
      -> Immediate #Z -> #AA
      <- UnconditionalJump[GOTO] #CU -> #Z
===#Block AA(size=10, flags=0)===
   0. lvar48 = new java.lang.StringBuilder;
   1. _consume(lvar48.<init>());
   2. lvar84 = lvar7;
   3. lvar49 = lvar48.append(lvar84);
   4. lvar85 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.mmenvbqzmosjpba(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   5. lvar50 = lvar49.append(lvar85);
   6. lvar51 = lvar50.toString();
   7. lvar52 = org.bukkit.Material.valueOf(lvar51);
   8. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -189918445)
      goto AT
   9. lvar125 = {35441688 ^ lvar125};
      -> ConditionalJump[IF_ICMPNE] #AA -> #AT
      -> TryCatch range: [AA...AA] -> CW ([Ljava/lang/IllegalArgumentException;])
      -> Immediate #AA -> #AB
      <- Immediate #Z -> #AA
===#Block AB(size=2, flags=0)===
   0. return lvar52;
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -465013805)
      goto AV
      -> ConditionalJump[IF_ICMPNE] #AB -> #AV
      <- Immediate #AA -> #AB
===#Block CW(size=1, flags=0)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.pchqajqczyolsclb(lvar125)) {
      case -189918445:
      	 goto	#CX
      default:
      	 goto	#CY
   }
      -> Switch[-189918445] #CW -> #CX
      -> DefaultSwitch #CW -> #CY
      <- TryCatch range: [AA...AA] -> CW ([Ljava/lang/IllegalArgumentException;])
===#Block CY(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #CW -> #CY
===#Block CX(size=2, flags=10100)===
   0. lvar125 = com.hang.plugin.utils.VersionUtils.wdmwkfmwwmzsshen(lvar125, 792772670);
   1. goto AC
      -> UnconditionalJump[GOTO] #CX -> #AC
      <- Switch[-189918445] #CW -> #CX
===#Block AC(size=5, flags=0)===
   0. lvar53 = catch();
   1. // Frame: locals[7] [java/lang/String, 1, [Ljava/lang/String;, java/lang/String, java/lang/String, 1, java/lang/IllegalArgumentException] stack[1] [java/lang/IllegalArgumentException]
   2. lvar11 = lvar53;
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1901587742)
      goto AT
   4. lvar125 = {1589336129 ^ lvar125};
      -> ConditionalJump[IF_ICMPNE] #AC -> #AT
      -> Immediate #AC -> #AD
      <- UnconditionalJump[GOTO] #CX -> #AC
===#Block AD(size=4, flags=0)===
   0. lvar54 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.poydxnnlqrvqjhp(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   1. lvar55 = org.bukkit.Material.valueOf(lvar54);
   2. return lvar55;
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 2071122152)
      goto AT
      -> ConditionalJump[IF_ICMPNE] #AD -> #AT
      <- Immediate #AC -> #AD
===#Block BC(size=7, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1472647577)
      goto BC
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {671219875 ^ lvar125})
      goto BC
   2. _consume({376542600 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1573440350)
      goto BC
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {790547878 ^ lvar125})
      goto BC
   5. _consume({1000263638 ^ lvar125});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- ConditionalJump[IF_ICMPNE] #Y -> #BC
      <- DefaultSwitch #BS -> #BC
      <- ConditionalJump[IF_ICMPNE] #U -> #BC
      <- ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- Switch[1996748635] #BS -> #BC
===#Block AE(size=2, flags=0)===
   0. return lvar46;
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1891023176)
      goto AT
      -> ConditionalJump[IF_ICMPNE] #AE -> #AT
      <- Immediate #Y -> #AE
===#Block BA(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 259967112)
      goto BA
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {230009812 ^ lvar125})
      goto BA
   2. _consume({1078598914 ^ lvar125});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #P -> #BA
===#Block CL(size=1, flags=10100)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.uhgfjbbbkrbjopxu(lvar125)) {
      case 30464656:
      	 goto	#CM
      case 879778847:
      	 goto	#AT
      case 1465519011:
      	 goto	#Q
      case 1997483681:
      	 goto	#CL
      default:
      	 goto	#AT
   }
      -> Switch[30464656] #CL -> #CM
      -> Switch[1997483681] #CL -> #CL
      -> Immediate #CL -> #CM
      -> Switch[879778847] #CL -> #AT
      -> DefaultSwitch #CL -> #AT
      -> Switch[1465519011] #CL -> #Q
      <- Switch[1997483681] #CL -> #CL
      <- Switch[30464623] #P -> #CL
===#Block CM(size=2, flags=100)===
   0. lvar125 = {1640266031 ^ lvar125};
   1. goto Q
      -> UnconditionalJump[GOTO] #CM -> #Q
      <- Switch[30464656] #CL -> #CM
      <- Immediate #CL -> #CM
===#Block Q(size=11, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar26 = new java.lang.StringBuilder;
   2. _consume(lvar26.<init>());
   3. lvar78 = lvar7;
   4. lvar27 = lvar26.append(lvar78);
   5. lvar79 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.ujsrmvcxavdmglr(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   6. lvar28 = lvar27.append(lvar79);
   7. lvar29 = lvar28.toString();
   8. lvar30 = org.bukkit.Material.valueOf(lvar29);
   9. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 22872563)
      goto AV
   10. lvar125 = {447858856 ^ lvar125};
      -> TryCatch range: [Q...Q] -> DC ([Ljava/lang/IllegalArgumentException;])
      -> ConditionalJump[IF_ICMPNE] #Q -> #AV
      -> Immediate #Q -> #R
      <- UnconditionalJump[GOTO] #CM -> #Q
      <- Switch[1465519011] #CL -> #Q
===#Block R(size=2, flags=0)===
   0. return lvar30;
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -724351821)
      goto BG
      -> ConditionalJump[IF_ICMPNE] #R -> #BG
      <- Immediate #Q -> #R
===#Block BG(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -724351821)
      goto BG
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {496100711 ^ lvar125})
      goto BG
   2. _consume({187504892 ^ lvar125});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- ConditionalJump[IF_ICMPNE] #R -> #BG
      <- ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- Switch[1658161089] #CE -> #BG
      <- DefaultSwitch #CE -> #BG
===#Block DC(size=1, flags=0)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.pchqajqczyolsclb(lvar125)) {
      case 22872563:
      	 goto	#DD
      default:
      	 goto	#DE
   }
      -> Switch[22872563] #DC -> #DD
      -> DefaultSwitch #DC -> #DE
      <- TryCatch range: [Q...Q] -> DC ([Ljava/lang/IllegalArgumentException;])
===#Block DE(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #DC -> #DE
===#Block DD(size=2, flags=10100)===
   0. lvar125 = {64315738 ^ lvar125};
   1. goto S
      -> UnconditionalJump[GOTO] #DD -> #S
      <- Switch[22872563] #DC -> #DD
===#Block S(size=5, flags=0)===
   0. lvar31 = catch();
   1. // Frame: locals[0] [] stack[1] [java/lang/IllegalArgumentException]
   2. lvar10 = lvar31;
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 536218403)
      goto AV
   4. lvar125 = {1912961427 ^ lvar125};
      -> Immediate #S -> #T
      -> ConditionalJump[IF_ICMPNE] #S -> #AV
      <- UnconditionalJump[GOTO] #DD -> #S
===#Block T(size=4, flags=0)===
   0. lvar32 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.kerwxehqiejvcil(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   1. lvar33 = org.bukkit.Material.valueOf(lvar32);
   2. return lvar33;
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1881290824)
      goto AT
      -> ConditionalJump[IF_ICMPNE] #T -> #AT
      <- Immediate #S -> #T
===#Block CP(size=2, flags=10100)===
   0. lvar125 = {1727468403 ^ lvar125};
   1. goto AJ
      -> UnconditionalJump[GOTO] #CP -> #AJ
      <- DefaultSwitch #P -> #CP
===#Block AJ(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar63 = lvar0;
   2. lvar64 = com.hang.plugin.utils.VersionUtils.getCompatibleMaterial(lvar63, 1680768147);
   3. return lvar64;
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 952521491)
      goto AU
      -> ConditionalJump[IF_ICMPNE] #AJ -> #AU
      <- UnconditionalJump[GOTO] #CP -> #AJ
===#Block CK(size=2, flags=10100)===
   0. lvar125 = {681358741 ^ lvar125};
   1. goto AF
      -> UnconditionalJump[GOTO] #CK -> #AF
      <- Switch[30464622] #P -> #CK
===#Block AF(size=11, flags=0)===
   0. // Frame: locals[1] [null] stack[0] []
   1. lvar56 = new java.lang.StringBuilder;
   2. _consume(lvar56.<init>());
   3. lvar86 = lvar7;
   4. lvar57 = lvar56.append(lvar86);
   5. lvar87 = com.hang.plugin.utils.VersionUtils.yaztccxgmb(com.hang.plugin.utils.VersionUtils.mypcdvhpfklcejl(), com.hang.plugin.utils.VersionUtils.wlsmdhsiogkvljf(), lvar125);
   6. lvar58 = lvar57.append(lvar87);
   7. lvar59 = lvar58.toString();
   8. lvar60 = org.bukkit.Material.valueOf(lvar59);
   9. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1268474913)
      goto AU
   10. lvar125 = {233737767 ^ lvar125};
      -> TryCatch range: [AF...AF] -> CZ ([Ljava/lang/IllegalArgumentException;])
      -> Immediate #AF -> #AG
      -> ConditionalJump[IF_ICMPNE] #AF -> #AU
      <- UnconditionalJump[GOTO] #CK -> #AF
===#Block AU(size=13, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 952521491)
      goto AU
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1370331809 ^ lvar125})
      goto AU
   2. _consume({1995617054 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1895392697)
      goto AU
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {206381498 ^ lvar125})
      goto AU
   5. _consume({1421645268 ^ lvar125});
   6. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1268474913)
      goto AU
   7. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {275904404 ^ lvar125})
      goto AU
   8. _consume({326458878 ^ lvar125});
   9. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1153227499)
      goto AU
   10. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1012380592 ^ lvar125})
      goto AU
   11. _consume({519305511 ^ lvar125});
   12. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AU -> #AU
      <- ConditionalJump[IF_ICMPNE] #AL -> #AU
      <- ConditionalJump[IF_ICMPNE] #D -> #AU
      <- ConditionalJump[IF_ICMPNE] #AU -> #AU
      <- ConditionalJump[IF_ICMPNE] #AF -> #AU
      <- ConditionalJump[IF_ICMPNE] #AJ -> #AU
===#Block AG(size=2, flags=0)===
   0. return lvar60;
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 619648281)
      goto AY
      -> ConditionalJump[IF_ICMPNE] #AG -> #AY
      <- Immediate #AF -> #AG
===#Block CZ(size=1, flags=0)===
   0. switch (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.pchqajqczyolsclb(lvar125)) {
      case 1268474913:
      	 goto	#DA
      default:
      	 goto	#DB
   }
      -> Switch[1268474913] #CZ -> #DA
      -> DefaultSwitch #CZ -> #DB
      <- TryCatch range: [AF...AF] -> CZ ([Ljava/lang/IllegalArgumentException;])
===#Block DB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #CZ -> #DB
===#Block DA(size=2, flags=10100)===
   0. lvar125 = com.hang.plugin.utils.VersionUtils.wdmwkfmwwmzsshen(lvar125, 1323741537);
   1. goto AH
      -> UnconditionalJump[GOTO] #DA -> #AH
      <- Switch[1268474913] #CZ -> #DA
===#Block AH(size=5, flags=0)===
   0. lvar61 = catch();
   1. // Frame: locals[0] [] stack[1] [java/lang/IllegalArgumentException]
   2. lvar123 = lvar61;
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1018097451)
      goto AW
   4. lvar125 = {907231651 ^ lvar125};
      -> Immediate #AH -> #AI
      -> ConditionalJump[IF_ICMPNE] #AH -> #AW
      <- UnconditionalJump[GOTO] #DA -> #AH
===#Block AW(size=10, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1255719059)
      goto AW
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {316581367 ^ lvar125})
      goto AW
   2. _consume({655440569 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1697267131)
      goto AW
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {462893933 ^ lvar125})
      goto AW
   5. _consume({1776597964 ^ lvar125});
   6. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1018097451)
      goto AW
   7. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {884445874 ^ lvar125})
      goto AW
   8. _consume({98893166 ^ lvar125});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- ConditionalJump[IF_ICMPNE] #AM -> #AW
      <- Switch[584824172] #BP -> #AW
      <- DefaultSwitch #BP -> #AW
      <- ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- ConditionalJump[IF_ICMPNE] #W -> #AW
      <- ConditionalJump[IF_ICMPNE] #AH -> #AW
===#Block AI(size=3, flags=0)===
   0. lvar62 = org.bukkit.Material.GLASS;
   1. return lvar62;
   2. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1942691278)
      goto AY
      -> ConditionalJump[IF_ICMPNE] #AI -> #AY
      <- Immediate #AH -> #AI
===#Block AY(size=10, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1942691278)
      goto AY
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1889397455 ^ lvar125})
      goto AY
   2. _consume({474099221 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 619648281)
      goto AY
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1727662253 ^ lvar125})
      goto AY
   5. _consume({1260058740 ^ lvar125});
   6. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1811930759)
      goto AY
   7. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1904965491 ^ lvar125})
      goto AY
   8. _consume({1668244211 ^ lvar125});
   9. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AY -> #AY
      <- ConditionalJump[IF_ICMPNE] #Z -> #AY
      <- ConditionalJump[IF_ICMPNE] #AG -> #AY
      <- ConditionalJump[IF_ICMPNE] #AI -> #AY
      <- ConditionalJump[IF_ICMPNE] #AY -> #AY
===#Block AV(size=16, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -35684463)
      goto AV
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1581528474 ^ lvar125})
      goto AV
   2. _consume({466228616 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -465013805)
      goto AV
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {269601436 ^ lvar125})
      goto AV
   5. _consume({1111757721 ^ lvar125});
   6. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 536218403)
      goto AV
   7. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1546811783 ^ lvar125})
      goto AV
   8. _consume({76987919 ^ lvar125});
   9. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 1911049913)
      goto AV
   10. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1765624790 ^ lvar125})
      goto AV
   11. _consume({452894002 ^ lvar125});
   12. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 22872563)
      goto AV
   13. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {690126659 ^ lvar125})
      goto AV
   14. _consume({1830757988 ^ lvar125});
   15. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AV -> #AV
      <- ConditionalJump[IF_ICMPNE] #AB -> #AV
      <- ConditionalJump[IF_ICMPNE] #I -> #AV
      <- ConditionalJump[IF_ICMPNE] #AV -> #AV
      <- Switch[1532883505] #BZ -> #AV
      <- DefaultSwitch #BZ -> #AV
      <- ConditionalJump[IF_ICMPNE] #G -> #AV
      <- ConditionalJump[IF_ICMPNE] #Q -> #AV
      <- ConditionalJump[IF_ICMPNE] #S -> #AV
===#Block BD(size=4, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -227337559)
      goto BD
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1603273991 ^ lvar125})
      goto BD
   2. _consume({1765303477 ^ lvar125});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BD -> #BD
      <- DefaultSwitch #CC -> #BD
      <- Switch[1078704770] #CC -> #BD
      <- ConditionalJump[IF_ICMPNE] #K -> #BD
      <- ConditionalJump[IF_ICMPNE] #BD -> #BD
===#Block AT(size=31, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -522532895)
      goto AT
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1251370667 ^ lvar125})
      goto AT
   2. _consume({693799604 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1356546680)
      goto AT
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1857845634 ^ lvar125})
      goto AT
   5. _consume({356857272 ^ lvar125});
   6. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -189918445)
      goto AT
   7. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1843596972 ^ lvar125})
      goto AT
   8. _consume({146586169 ^ lvar125});
   9. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 2071122152)
      goto AT
   10. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {582494509 ^ lvar125})
      goto AT
   11. _consume({728656104 ^ lvar125});
   12. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1901587742)
      goto AT
   13. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {680810264 ^ lvar125})
      goto AT
   14. _consume({1642657764 ^ lvar125});
   15. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1148314736)
      goto AT
   16. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1211919288 ^ lvar125})
      goto AT
   17. _consume({562611408 ^ lvar125});
   18. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 723303130)
      goto AT
   19. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {569551982 ^ lvar125})
      goto AT
   20. _consume({1888082657 ^ lvar125});
   21. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1891023176)
      goto AT
   22. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {607900734 ^ lvar125})
      goto AT
   23. _consume({1117118988 ^ lvar125});
   24. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1881290824)
      goto AT
   25. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {398049715 ^ lvar125})
      goto AT
   26. _consume({1516744936 ^ lvar125});
   27. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 586780752)
      goto AT
   28. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {1719039288 ^ lvar125})
      goto AT
   29. _consume({797798209 ^ lvar125});
   30. throw new java/lang/RuntimeException();
      -> ConditionalJump[IF_ICMPNE] #AT -> #AT
      <- ConditionalJump[IF_ICMPNE] #AA -> #AT
      <- ConditionalJump[IF_ICMPNE] #AC -> #AT
      <- ConditionalJump[IF_ICMPNE] #AD -> #AT
      <- ConditionalJump[IF_ICMPNE] #AE -> #AT
      <- ConditionalJump[IF_ICMPNE] #AT -> #AT
      <- ConditionalJump[IF_ICMPNE] #AS -> #AT
      <- ConditionalJump[IF_ICMPNE] #T -> #AT
      <- Switch[879778847] #CL -> #AT
      <- ConditionalJump[IF_ICMPNE] #V -> #AT
      <- ConditionalJump[IF_ICMPNE] #J -> #AT
      <- ConditionalJump[IF_ICMPNE] #AR -> #AT
      <- ConditionalJump[IF_ICMPNE] #F -> #AT
      <- DefaultSwitch #CL -> #AT
===#Block AX(size=10, flags=0)===
   0. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1001674448)
      goto AX
   1. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {532810833 ^ lvar125})
      goto AX
   2. _consume({596217420 ^ lvar125});
   3. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != 741553699)
      goto AX
   4. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {892162184 ^ lvar125})
      goto AX
   5. _consume({886601554 ^ lvar125});
   6. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != -1438220656)
      goto AX
   7. if (bzccaprudpmhbybn.hrtonbhxgrrdhtyv.incoqjyacerbayib(lvar125) != {524144821 ^ lvar125})
      goto AX
   8. _consume({1237849146 ^ lvar125});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AX -> #AX
      <- DefaultSwitch #CN -> #AX
      <- ConditionalJump[IF_ICMPNE] #AP -> #AX
      <- ConditionalJump[IF_ICMPNE] #N -> #AX
      <- Switch[1404126654] #CN -> #AX
      <- ConditionalJump[IF_ICMPNE] #A -> #AX
      <- ConditionalJump[IF_ICMPNE] #AX -> #AX
