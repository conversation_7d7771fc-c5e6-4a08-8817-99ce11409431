handler=Block #CL, types=[Ljava/io/IOException;], range=[Block #X, Block #W]
handler=Block #CP, types=[Ljava/lang/IllegalAccessException;], range=[Block #AA, Block #Z]
handler=Block #CT, types=[Ljava/lang/RuntimeException;], range=[Block #AD, Block #AC]
handler=Block #CX, types=[Ljava/io/IOException;], range=[Block #AG, Block #AF]
===#Block A(size=5, flags=1)===
   0. lvar54 = {1331320230 ^ {1000760481 ^ 800312540}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -629149990)
      goto AI
   4. lvar54 = {449041919 ^ lvar54};
      -> Immediate #A -> #B
      -> ConditionalJump[IF_ICMPNE] #A -> #AI
===#Block B(size=4, flags=0)===
   0. lvar3 = lvar1;
   1. if (lvar3 == nullconst)
      goto BR
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 207700258)
      goto AM
   3. lvar54 = {601704472 ^ lvar54};
      -> Immediate #B -> #C
      -> ConditionalJump[IFNULL] #B -> #BR
      -> ConditionalJump[IF_ICMPNE] #B -> #AM
      <- Immediate #A -> #B
===#Block BR(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 251645001:
      	 goto	#BS
      case 388024636:
      	 goto	#AI
      case 756539193:
      	 goto	#D
      case 998190511:
      	 goto	#BR
      default:
      	 goto	#AI
   }
      -> Switch[998190511] #BR -> #BR
      -> Switch[251645001] #BR -> #BS
      -> Switch[756539193] #BR -> #D
      -> Switch[388024636] #BR -> #AI
      -> DefaultSwitch #BR -> #AI
      -> Immediate #BR -> #BS
      <- Switch[998190511] #BR -> #BR
      <- ConditionalJump[IFNULL] #B -> #BR
===#Block BS(size=2, flags=100)===
   0. lvar54 = {1303626289 ^ lvar54};
   1. goto D
      -> UnconditionalJump[GOTO] #BS -> #D
      <- Switch[251645001] #BR -> #BS
      <- Immediate #BR -> #BS
===#Block C(size=5, flags=0)===
   0. lvar9 = lvar1;
   1. lvar10 = lvar9.isEmpty();
   2. if (lvar10 == {1649500220 ^ lvar54})
      goto BL
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 311099875)
      goto AI
   4. lvar54 = {1852738089 ^ lvar54};
      -> ConditionalJump[IF_ICMPNE] #C -> #AI
      -> ConditionalJump[IF_ICMPEQ] #C -> #BL
      -> Immediate #C -> #D
      <- Immediate #B -> #C
===#Block D(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar11 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.polpxguvvmdvbrx(), lvar54);
   2. return lvar11;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1644122280)
      goto AX
      -> ConditionalJump[IF_ICMPNE] #D -> #AX
      <- Switch[756539193] #BR -> #D
      <- UnconditionalJump[GOTO] #BS -> #D
      <- Immediate #C -> #D
===#Block AX(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1644122280)
      goto AX
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {2110505833 ^ lvar54})
      goto AX
   2. _consume({1454429794 ^ lvar54});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AX -> #AX
      <- ConditionalJump[IF_ICMPNE] #AX -> #AX
      <- ConditionalJump[IF_ICMPNE] #D -> #AX
===#Block BL(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 243124261:
      	 goto	#BM
      case 863568095:
      	 goto	#AV
      case 968716299:
      	 goto	#E
      case 1316462559:
      	 goto	#BL
      default:
      	 goto	#AV
   }
      -> Immediate #BL -> #BM
      -> Switch[1316462559] #BL -> #BL
      -> Switch[243124261] #BL -> #BM
      -> Switch[968716299] #BL -> #E
      -> DefaultSwitch #BL -> #AV
      -> Switch[863568095] #BL -> #AV
      <- Switch[1316462559] #BL -> #BL
      <- ConditionalJump[IF_ICMPEQ] #C -> #BL
===#Block BM(size=2, flags=100)===
   0. lvar54 = {215318065 ^ lvar54};
   1. goto E
      -> UnconditionalJump[GOTO] #BM -> #E
      <- Immediate #BL -> #BM
      <- Switch[243124261] #BL -> #BM
===#Block E(size=11, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar1;
   2. lvar13 = lvar12.toLowerCase();
   3. lvar7 = lvar13;
   4. lvar14 = {-1854152206 ^ lvar54};
   5. lvar8 = lvar14;
   6. lvar15 = lvar7;
   7. lvar16 = lvar15.hashCode();
   8. svar56 = {lvar16 ^ lvar54};
   9. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(svar56)) {
      case 30481073:
      	 goto	#CB
      case 107757332:
      	 goto	#CD
      case 108527234:
      	 goto	#CF
      case 123370998:
      	 goto	#CG
      case 201067313:
      	 goto	#CH
      default:
      	 goto	#CJ
   }
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1948315755)
      goto AP
      -> Switch[201067313] #E -> #CH
      -> Switch[107757332] #E -> #CD
      -> Switch[108527234] #E -> #CF
      -> Switch[30481073] #E -> #CB
      -> DefaultSwitch #E -> #CJ
      -> Switch[123370998] #E -> #CG
      -> ConditionalJump[IF_ICMPNE] #E -> #AP
      <- Switch[968716299] #BL -> #E
      <- UnconditionalJump[GOTO] #BM -> #E
===#Block CG(size=2, flags=10100)===
   0. lvar54 = {1860767603 ^ lvar54};
   1. goto L
      -> UnconditionalJump[GOTO] #CG -> #L
      <- Switch[123370998] #E -> #CG
===#Block L(size=7, flags=0)===
   0. // Frame: locals[2] [java/lang/String, 1] stack[0] []
   1. lvar36 = lvar7;
   2. lvar50 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.vhhyotrsydpzysb(), lvar54);
   3. lvar37 = lvar36.equals(lvar50);
   4. if (lvar37 == {7147902 ^ lvar54})
      goto BP
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 57183216)
      goto AM
   6. lvar54 = {626017693 ^ lvar54};
      -> ConditionalJump[IF_ICMPEQ] #L -> #BP
      -> ConditionalJump[IF_ICMPNE] #L -> #AM
      -> Immediate #L -> #M
      <- UnconditionalJump[GOTO] #CG -> #L
===#Block M(size=4, flags=0)===
   0. lvar38 = {624776419 ^ lvar54};
   1. lvar8 = lvar38;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 703244057)
      goto AR
   3. goto BG
      -> ConditionalJump[IF_ICMPNE] #M -> #AR
      -> UnconditionalJump[GOTO] #M -> #BG
      <- Immediate #L -> #M
===#Block BG(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 120610458:
      	 goto	#BG
      case 136769866:
      	 goto	#BH
      case 480032043:
      	 goto	#AD
      case 1926873340:
      	 goto	#AN
      default:
      	 goto	#AN
   }
      -> Switch[1926873340] #BG -> #AN
      -> Immediate #BG -> #BH
      -> DefaultSwitch #BG -> #AN
      -> Switch[120610458] #BG -> #BG
      -> Switch[480032043] #BG -> #AD
      -> Switch[136769866] #BG -> #BH
      <- Switch[120610458] #BG -> #BG
      <- UnconditionalJump[GOTO] #M -> #BG
===#Block BH(size=2, flags=100)===
   0. lvar54 = {1848170677 ^ lvar54};
   1. goto AD
      -> UnconditionalJump[GOTO] #BH -> #AD
      <- Immediate #BG -> #BH
      <- Switch[136769866] #BG -> #BH
===#Block AD(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 24769779)
      goto AC
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1487692466)
      goto AK
      -> TryCatch range: [AD...AC] -> CT ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPNE] #AD -> #AK
      -> ConditionalJump[IF_ICMPEQ] #AD -> #AC
      <- UnconditionalJump[GOTO] #BH -> #AD
      <- Switch[480032043] #BG -> #AD
===#Block AC(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1291854065)
      goto BA
      -> ConditionalJump[IF_ICMPNE] #AC -> #BA
      -> TryCatch range: [AD...AC] -> CT ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AD -> #AC
===#Block BA(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1291854065)
      goto BA
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1265160461 ^ lvar54})
      goto BA
   2. _consume({1286839069 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #AC -> #BA
      <- ConditionalJump[IF_ICMPNE] #BA -> #BA
===#Block CT(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar54)) {
      case 1291854065:
      	 goto	#CV
      case 1487692466:
      	 goto	#CU
      default:
      	 goto	#CW
   }
      -> Switch[1291854065] #CT -> #CV
      -> Switch[1487692466] #CT -> #CU
      -> DefaultSwitch #CT -> #CW
      <- TryCatch range: [AD...AC] -> CT ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AD...AC] -> CT ([Ljava/lang/RuntimeException;])
===#Block CW(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #CT -> #CW
===#Block CU(size=2, flags=10100)===
   0. lvar54 = {349345679 ^ lvar54};
   1. goto AE
      -> UnconditionalJump[GOTO] #CU -> #AE
      <- Switch[1487692466] #CT -> #CU
===#Block CV(size=2, flags=10100)===
   0. lvar54 = {1986469831 ^ lvar54};
   1. goto AE
      -> UnconditionalJump[GOTO] #CV -> #AE
      <- Switch[1291854065] #CT -> #CV
===#Block AE(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -29827382)
      goto AV
   2. goto BE
      -> UnconditionalJump[GOTO] #AE -> #BE
      -> ConditionalJump[IF_ICMPNE] #AE -> #AV
      <- UnconditionalJump[GOTO] #CV -> #AE
      <- UnconditionalJump[GOTO] #CU -> #AE
===#Block AV(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -29827382)
      goto AV
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1129929122 ^ lvar54})
      goto AV
   2. _consume({233764664 ^ lvar54});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AV -> #AV
      <- ConditionalJump[IF_ICMPNE] #AE -> #AV
      <- ConditionalJump[IF_ICMPNE] #AV -> #AV
      <- DefaultSwitch #BL -> #AV
      <- Switch[863568095] #BL -> #AV
===#Block BE(size=2, flags=10100)===
   0. lvar54 = {1576338315 ^ lvar54};
   1. goto P
      -> UnconditionalJump[GOTO] #BE -> #P
      <- UnconditionalJump[GOTO] #AE -> #BE
===#Block BP(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 6701067:
      	 goto	#BQ
      case 481131743:
      	 goto	#BP
      case 1165469668:
      	 goto	#P
      case 1945171641:
      	 goto	#AQ
      default:
      	 goto	#AQ
   }
      -> Immediate #BP -> #BQ
      -> Switch[481131743] #BP -> #BP
      -> Switch[1165469668] #BP -> #P
      -> Switch[1945171641] #BP -> #AQ
      -> DefaultSwitch #BP -> #AQ
      -> Switch[6701067] #BP -> #BQ
      <- ConditionalJump[IF_ICMPEQ] #L -> #BP
      <- Switch[481131743] #BP -> #BP
===#Block BQ(size=2, flags=100)===
   0. lvar54 = {39782700 ^ lvar54};
   1. goto P
      -> UnconditionalJump[GOTO] #BQ -> #P
      <- Immediate #BP -> #BQ
      <- Switch[6701067] #BP -> #BQ
===#Block CJ(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 102732989:
      	 goto	#CK
      case 126064420:
      	 goto	#P
      case 494377783:
      	 goto	#AW
      case 1302701004:
      	 goto	#CJ
      default:
      	 goto	#AW
   }
      -> Immediate #CJ -> #CK
      -> Switch[1302701004] #CJ -> #CJ
      -> Switch[126064420] #CJ -> #P
      -> Switch[494377783] #CJ -> #AW
      -> Switch[102732989] #CJ -> #CK
      -> DefaultSwitch #CJ -> #AW
      <- Switch[1302701004] #CJ -> #CJ
      <- DefaultSwitch #E -> #CJ
===#Block CK(size=2, flags=100)===
   0. lvar54 = {1823868511 ^ lvar54};
   1. goto P
      -> UnconditionalJump[GOTO] #CK -> #P
      <- Immediate #CJ -> #CK
      <- Switch[102732989] #CJ -> #CK
===#Block CB(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 102732989:
      	 goto	#CC
      case 201929161:
      	 goto	#N
      case 1109179904:
      	 goto	#CB
      case 1284942829:
      	 goto	#AL
      default:
      	 goto	#AL
   }
      -> Immediate #CB -> #CC
      -> Switch[102732989] #CB -> #CC
      -> Switch[201929161] #CB -> #N
      -> DefaultSwitch #CB -> #AL
      -> Switch[1109179904] #CB -> #CB
      -> Switch[1284942829] #CB -> #AL
      <- Switch[30481073] #E -> #CB
      <- Switch[1109179904] #CB -> #CB
===#Block CC(size=2, flags=100)===
   0. lvar54 = {1470019567 ^ lvar54};
   1. goto N
      -> UnconditionalJump[GOTO] #CC -> #N
      <- Immediate #CB -> #CC
      <- Switch[102732989] #CB -> #CC
===#Block N(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar39 = lvar7;
   2. lvar51 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.hbearebjtdscrey(), lvar54);
   3. lvar40 = lvar39.equals(lvar51);
   4. if (lvar40 == {958048738 ^ lvar54})
      goto BT
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -925544687)
      goto AN
   6. lvar54 = {1353522464 ^ lvar54};
      -> Immediate #N -> #O
      -> ConditionalJump[IF_ICMPNE] #N -> #AN
      -> ConditionalJump[IF_ICMPEQ] #N -> #BT
      <- UnconditionalJump[GOTO] #CC -> #N
      <- Switch[201929161] #CB -> #N
===#Block BT(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 992522672);
   1. goto P
      -> UnconditionalJump[GOTO] #BT -> #P
      <- ConditionalJump[IF_ICMPEQ] #N -> #BT
===#Block AN(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -925544687)
      goto AN
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {684588506 ^ lvar54})
      goto AN
   2. _consume({684350259 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AN -> #AN
      <- Switch[1926873340] #BG -> #AN
      <- DefaultSwitch #BG -> #AN
      <- ConditionalJump[IF_ICMPNE] #N -> #AN
      <- ConditionalJump[IF_ICMPNE] #AN -> #AN
===#Block O(size=4, flags=0)===
   0. lvar41 = {1773647046 ^ lvar54};
   1. lvar8 = lvar41;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1304274451)
      goto AJ
   3. lvar54 = {1803922576 ^ lvar54};
      -> Immediate #O -> #P
      -> ConditionalJump[IF_ICMPNE] #O -> #AJ
      <- Immediate #N -> #O
===#Block CF(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 820932679);
   1. goto J
      -> UnconditionalJump[GOTO] #CF -> #J
      <- Switch[108527234] #E -> #CF
===#Block J(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar33 = lvar7;
   2. lvar49 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.itqeehicawlbndz(), lvar54);
   3. lvar34 = lvar33.equals(lvar49);
   4. if (lvar34 == {1584033354 ^ lvar54})
      goto BO
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -212635054)
      goto AW
   6. lvar54 = {393337264 ^ lvar54};
      -> Immediate #J -> #K
      -> ConditionalJump[IF_ICMPNE] #J -> #AW
      -> ConditionalJump[IF_ICMPEQ] #J -> #BO
      <- UnconditionalJump[GOTO] #CF -> #J
===#Block BO(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 1549301272);
   1. goto P
      -> UnconditionalJump[GOTO] #BO -> #P
      <- ConditionalJump[IF_ICMPEQ] #J -> #BO
===#Block AW(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -212635054)
      goto AW
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {294219725 ^ lvar54})
      goto AW
   2. _consume({1176256468 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- ConditionalJump[IF_ICMPNE] #AW -> #AW
      <- Switch[494377783] #CJ -> #AW
      <- DefaultSwitch #CJ -> #AW
      <- ConditionalJump[IF_ICMPNE] #J -> #AW
===#Block K(size=4, flags=0)===
   0. lvar35 = {1226553339 ^ lvar54};
   1. lvar8 = lvar35;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1222492114)
      goto AI
   3. goto BF
      -> UnconditionalJump[GOTO] #K -> #BF
      -> ConditionalJump[IF_ICMPNE] #K -> #AI
      <- Immediate #J -> #K
===#Block BF(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 464715973);
   1. goto AA
      -> UnconditionalJump[GOTO] #BF -> #AA
      <- UnconditionalJump[GOTO] #K -> #BF
===#Block AA(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 2535842)
      goto Z
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1790592518)
      goto AK
      -> TryCatch range: [AA...Z] -> CP ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPNE] #AA -> #AK
      -> ConditionalJump[IF_ICMPEQ] #AA -> #Z
      <- UnconditionalJump[GOTO] #BF -> #AA
===#Block Z(size=2, flags=0)===
   0. throw new java/lang/IllegalAccessException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1293986981)
      goto BB
      -> ConditionalJump[IF_ICMPNE] #Z -> #BB
      -> TryCatch range: [AA...Z] -> CP ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AA -> #Z
===#Block BB(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1293986981)
      goto BB
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1596426717 ^ lvar54})
      goto BB
   2. _consume({1347062056 ^ lvar54});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #Z -> #BB
      <- ConditionalJump[IF_ICMPNE] #BB -> #BB
===#Block AK(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1487692466)
      goto AK
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {231633889 ^ lvar54})
      goto AK
   2. _consume({192963341 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1790592518)
      goto AK
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1814578148 ^ lvar54})
      goto AK
   5. _consume({999093720 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AK -> #AK
      <- ConditionalJump[IF_ICMPNE] #AA -> #AK
      <- ConditionalJump[IF_ICMPNE] #AD -> #AK
      <- ConditionalJump[IF_ICMPNE] #AK -> #AK
===#Block CP(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar54)) {
      case -1790592518:
      	 goto	#CQ
      case -1293986981:
      	 goto	#CR
      default:
      	 goto	#CS
   }
      -> Switch[-1790592518] #CP -> #CQ
      -> DefaultSwitch #CP -> #CS
      -> Switch[-1293986981] #CP -> #CR
      <- TryCatch range: [AA...Z] -> CP ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AA...Z] -> CP ([Ljava/lang/IllegalAccessException;])
===#Block CR(size=2, flags=10100)===
   0. lvar54 = {1915628734 ^ lvar54};
   1. goto AB
      -> UnconditionalJump[GOTO] #CR -> #AB
      <- Switch[-1293986981] #CP -> #CR
===#Block CS(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #CP -> #CS
===#Block CQ(size=2, flags=10100)===
   0. lvar54 = {1457353194 ^ lvar54};
   1. goto AB
      -> UnconditionalJump[GOTO] #CQ -> #AB
      <- Switch[-1790592518] #CP -> #CQ
===#Block AB(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 598619816)
      goto AO
   2. goto BC
      -> ConditionalJump[IF_ICMPNE] #AB -> #AO
      -> UnconditionalJump[GOTO] #AB -> #BC
      <- UnconditionalJump[GOTO] #CQ -> #AB
      <- UnconditionalJump[GOTO] #CR -> #AB
===#Block BC(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 105373319);
   1. goto P
      -> UnconditionalJump[GOTO] #BC -> #P
      <- UnconditionalJump[GOTO] #AB -> #BC
===#Block CD(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 102732989:
      	 goto	#CE
      case 699273497:
      	 goto	#CD
      case 794870695:
      	 goto	#AS
      case 2063457681:
      	 goto	#H
      default:
      	 goto	#AS
   }
      -> Immediate #CD -> #CE
      -> Switch[102732989] #CD -> #CE
      -> Switch[699273497] #CD -> #CD
      -> Switch[2063457681] #CD -> #H
      -> DefaultSwitch #CD -> #AS
      -> Switch[794870695] #CD -> #AS
      <- Switch[107757332] #E -> #CD
      <- Switch[699273497] #CD -> #CD
===#Block CE(size=2, flags=100)===
   0. lvar54 = {667351901 ^ lvar54};
   1. goto H
      -> UnconditionalJump[GOTO] #CE -> #H
      <- Immediate #CD -> #CE
      <- Switch[102732989] #CD -> #CE
===#Block H(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar30 = lvar7;
   2. lvar48 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.kwdgcomxgiwnjru(), lvar54);
   3. lvar31 = lvar30.equals(lvar48);
   4. if (lvar31 == {1229120848 ^ lvar54})
      goto BU
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1243032194)
      goto AS
   6. lvar54 = {1363836332 ^ lvar54};
      -> ConditionalJump[IF_ICMPNE] #H -> #AS
      -> ConditionalJump[IF_ICMPEQ] #H -> #BU
      -> Immediate #H -> #I
      <- Switch[2063457681] #CD -> #H
      <- UnconditionalJump[GOTO] #CE -> #H
===#Block I(size=4, flags=0)===
   0. lvar32 = {403216638 ^ lvar54};
   1. lvar8 = lvar32;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1069234208)
      goto AP
   3. goto BJ
      -> UnconditionalJump[GOTO] #I -> #BJ
      -> ConditionalJump[IF_ICMPNE] #I -> #AP
      <- Immediate #H -> #I
===#Block AP(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1069234208)
      goto AP
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1486431564 ^ lvar54})
      goto AP
   2. _consume({728105964 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1948315755)
      goto AP
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {593992557 ^ lvar54})
      goto AP
   5. _consume({204218832 ^ lvar54});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AP -> #AP
      <- ConditionalJump[IF_ICMPNE] #AP -> #AP
      <- ConditionalJump[IF_ICMPNE] #I -> #AP
      <- ConditionalJump[IF_ICMPNE] #E -> #AP
===#Block BJ(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 1158211401);
   1. goto AG
      -> UnconditionalJump[GOTO] #BJ -> #AG
      <- UnconditionalJump[GOTO] #I -> #BJ
===#Block AG(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 70298222)
      goto AF
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -402391638)
      goto AI
      -> ConditionalJump[IF_ICMPNE] #AG -> #AI
      -> ConditionalJump[IF_ICMPEQ] #AG -> #AF
      -> TryCatch range: [AG...AF] -> CX ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #BJ -> #AG
===#Block AF(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1004129421)
      goto AS
      -> TryCatch range: [AG...AF] -> CX ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AF -> #AS
      <- ConditionalJump[IF_ICMPEQ] #AG -> #AF
===#Block CX(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar54)) {
      case -1004129421:
      	 goto	#CZ
      case -402391638:
      	 goto	#CY
      default:
      	 goto	#DA
   }
      -> DefaultSwitch #CX -> #DA
      -> Switch[-402391638] #CX -> #CY
      -> Switch[-1004129421] #CX -> #CZ
      <- TryCatch range: [AG...AF] -> CX ([Ljava/io/IOException;])
      <- TryCatch range: [AG...AF] -> CX ([Ljava/io/IOException;])
===#Block CZ(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 1896108647);
   1. goto AH
      -> UnconditionalJump[GOTO] #CZ -> #AH
      <- Switch[-1004129421] #CX -> #CZ
===#Block CY(size=2, flags=10100)===
   0. lvar54 = {1417735100 ^ lvar54};
   1. goto AH
      -> UnconditionalJump[GOTO] #CY -> #AH
      <- Switch[-402391638] #CX -> #CY
===#Block AH(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1275371592)
      goto AZ
   2. goto BK
      -> UnconditionalJump[GOTO] #AH -> #BK
      -> ConditionalJump[IF_ICMPNE] #AH -> #AZ
      <- UnconditionalJump[GOTO] #CZ -> #AH
      <- UnconditionalJump[GOTO] #CY -> #AH
===#Block AZ(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1275371592)
      goto AZ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {62250713 ^ lvar54})
      goto AZ
   2. _consume({1696837492 ^ lvar54});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AH -> #AZ
===#Block BK(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 196250715);
   1. goto P
      -> UnconditionalJump[GOTO] #BK -> #P
      <- UnconditionalJump[GOTO] #AH -> #BK
===#Block DA(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #CX -> #DA
===#Block BU(size=2, flags=10100)===
   0. lvar54 = {1265696002 ^ lvar54};
   1. goto P
      -> UnconditionalJump[GOTO] #BU -> #P
      <- ConditionalJump[IF_ICMPEQ] #H -> #BU
===#Block AS(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1004129421)
      goto AS
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1946147858 ^ lvar54})
      goto AS
   2. _consume({93965127 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1243032194)
      goto AS
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {2033696230 ^ lvar54})
      goto AS
   5. _consume({621011858 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AS -> #AS
      <- ConditionalJump[IF_ICMPNE] #H -> #AS
      <- ConditionalJump[IF_ICMPNE] #AS -> #AS
      <- DefaultSwitch #CD -> #AS
      <- Switch[794870695] #CD -> #AS
      <- ConditionalJump[IF_ICMPNE] #AF -> #AS
===#Block CH(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 51020645:
      	 goto	#AO
      case 102732989:
      	 goto	#CI
      case 818711202:
      	 goto	#CH
      case 860966159:
      	 goto	#F
      default:
      	 goto	#AO
   }
      -> Switch[102732989] #CH -> #CI
      -> Switch[818711202] #CH -> #CH
      -> Switch[860966159] #CH -> #F
      -> DefaultSwitch #CH -> #AO
      -> Immediate #CH -> #CI
      -> Switch[51020645] #CH -> #AO
      <- Switch[201067313] #E -> #CH
      <- Switch[818711202] #CH -> #CH
===#Block AO(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 598619816)
      goto AO
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {132179724 ^ lvar54})
      goto AO
   2. _consume({1607729378 ^ lvar54});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AO -> #AO
      <- ConditionalJump[IF_ICMPNE] #AB -> #AO
      <- ConditionalJump[IF_ICMPNE] #AO -> #AO
      <- DefaultSwitch #CH -> #AO
      <- Switch[51020645] #CH -> #AO
===#Block CI(size=2, flags=100)===
   0. lvar54 = {750206396 ^ lvar54};
   1. goto F
      -> UnconditionalJump[GOTO] #CI -> #F
      <- Switch[102732989] #CH -> #CI
      <- Immediate #CH -> #CI
===#Block F(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar7;
   2. lvar4 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.mippdmwegkvnhta(), lvar54);
   3. lvar18 = lvar17.equals(lvar4);
   4. if (lvar18 == {1110647729 ^ lvar54})
      goto BN
   5. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 295247242)
      goto AQ
   6. lvar54 = {469318315 ^ lvar54};
      -> ConditionalJump[IF_ICMPEQ] #F -> #BN
      -> ConditionalJump[IF_ICMPNE] #F -> #AQ
      -> Immediate #F -> #G
      <- Switch[860966159] #CH -> #F
      <- UnconditionalJump[GOTO] #CI -> #F
===#Block G(size=4, flags=0)===
   0. lvar19 = {1506416921 ^ lvar54};
   1. lvar8 = lvar19;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -833566510)
      goto AJ
   3. goto BI
      -> UnconditionalJump[GOTO] #G -> #BI
      -> ConditionalJump[IF_ICMPNE] #G -> #AJ
      <- Immediate #F -> #G
===#Block AJ(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -833566510)
      goto AJ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1751138007 ^ lvar54})
      goto AJ
   2. _consume({498662923 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1304274451)
      goto AJ
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {92506847 ^ lvar54})
      goto AJ
   5. _consume({183309875 ^ lvar54});
   6. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AJ -> #AJ
      <- ConditionalJump[IF_ICMPNE] #AJ -> #AJ
      <- ConditionalJump[IF_ICMPNE] #O -> #AJ
      <- ConditionalJump[IF_ICMPNE] #G -> #AJ
===#Block BI(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 1623587342);
   1. goto X
      -> UnconditionalJump[GOTO] #BI -> #X
      <- UnconditionalJump[GOTO] #G -> #BI
===#Block X(size=3, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54) == 244252409)
      goto W
   1. throw nullconst;
   2. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -931194719)
      goto AT
      -> ConditionalJump[IF_ICMPNE] #X -> #AT
      -> TryCatch range: [X...W] -> CL ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #X -> #W
      <- UnconditionalJump[GOTO] #BI -> #X
===#Block W(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 2017287979)
      goto AY
      -> TryCatch range: [X...W] -> CL ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #W -> #AY
      <- ConditionalJump[IF_ICMPEQ] #X -> #W
===#Block AY(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 2017287979)
      goto AY
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {304444821 ^ lvar54})
      goto AY
   2. _consume({1220066806 ^ lvar54});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AY -> #AY
      <- ConditionalJump[IF_ICMPNE] #W -> #AY
      <- ConditionalJump[IF_ICMPNE] #AY -> #AY
===#Block CL(size=1, flags=0)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.skwvlwdeafraxlsz(lvar54)) {
      case -931194719:
      	 goto	#CM
      case 2017287979:
      	 goto	#CN
      default:
      	 goto	#CO
   }
      -> Switch[2017287979] #CL -> #CN
      -> Switch[-931194719] #CL -> #CM
      -> DefaultSwitch #CL -> #CO
      <- TryCatch range: [X...W] -> CL ([Ljava/io/IOException;])
      <- TryCatch range: [X...W] -> CL ([Ljava/io/IOException;])
===#Block CO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #CL -> #CO
===#Block CM(size=2, flags=10100)===
   0. lvar54 = {1769955367 ^ lvar54};
   1. goto Y
      -> UnconditionalJump[GOTO] #CM -> #Y
      <- Switch[-931194719] #CL -> #CM
===#Block CN(size=2, flags=10100)===
   0. lvar54 = {1064773846 ^ lvar54};
   1. goto Y
      -> UnconditionalJump[GOTO] #CN -> #Y
      <- Switch[2017287979] #CL -> #CN
===#Block Y(size=3, flags=0)===
   0. _consume(catch());
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -2088494694)
      goto AQ
   2. goto BD
      -> ConditionalJump[IF_ICMPNE] #Y -> #AQ
      -> UnconditionalJump[GOTO] #Y -> #BD
      <- UnconditionalJump[GOTO] #CM -> #Y
      <- UnconditionalJump[GOTO] #CN -> #Y
===#Block BD(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 1380096865);
   1. goto P
      -> UnconditionalJump[GOTO] #BD -> #P
      <- UnconditionalJump[GOTO] #Y -> #BD
===#Block AT(size=4, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -931194719)
      goto AT
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1405948618 ^ lvar54})
      goto AT
   2. _consume({102528083 ^ lvar54});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AT -> #AT
      <- ConditionalJump[IF_ICMPNE] #AT -> #AT
      <- ConditionalJump[IF_ICMPNE] #X -> #AT
===#Block AQ(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 295247242)
      goto AQ
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1702449186 ^ lvar54})
      goto AQ
   2. _consume({734811980 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -2088494694)
      goto AQ
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1514242944 ^ lvar54})
      goto AQ
   5. _consume({681580118 ^ lvar54});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #AQ -> #AQ
      <- ConditionalJump[IF_ICMPNE] #Y -> #AQ
      <- ConditionalJump[IF_ICMPNE] #F -> #AQ
      <- Switch[1945171641] #BP -> #AQ
      <- DefaultSwitch #BP -> #AQ
      <- ConditionalJump[IF_ICMPNE] #AQ -> #AQ
===#Block BN(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 1073822691);
   1. goto P
      -> UnconditionalJump[GOTO] #BN -> #P
      <- ConditionalJump[IF_ICMPEQ] #F -> #BN
===#Block P(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar20 = lvar8;
   2. svar56 = {lvar20 ^ lvar54};
   3. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(svar56)) {
      case 34534626:
      	 goto	#BV
      case 34534649:
      	 goto	#BX
      case 34534654:
      	 goto	#BY
      case 34534655:
      	 goto	#BZ
      default:
      	 goto	#CA
   }
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 294699664)
      goto AL
      -> DefaultSwitch #P -> #CA
      -> Switch[34534655] #P -> #BZ
      -> Switch[34534649] #P -> #BX
      -> ConditionalJump[IF_ICMPNE] #P -> #AL
      -> Switch[34534654] #P -> #BY
      -> Switch[34534626] #P -> #BV
      <- UnconditionalJump[GOTO] #CK -> #P
      <- Immediate #O -> #P
      <- UnconditionalJump[GOTO] #BQ -> #P
      <- Switch[1165469668] #BP -> #P
      <- UnconditionalJump[GOTO] #BN -> #P
      <- UnconditionalJump[GOTO] #BE -> #P
      <- UnconditionalJump[GOTO] #BC -> #P
      <- Switch[126064420] #CJ -> #P
      <- UnconditionalJump[GOTO] #BO -> #P
      <- UnconditionalJump[GOTO] #BU -> #P
      <- UnconditionalJump[GOTO] #BK -> #P
      <- UnconditionalJump[GOTO] #BD -> #P
      <- UnconditionalJump[GOTO] #BT -> #P
===#Block BV(size=1, flags=10100)===
   0. switch (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lgwdrkabdrbtrrol(lvar54)) {
      case 34534654:
      	 goto	#BW
      case 310867766:
      	 goto	#AR
      case 1400570155:
      	 goto	#T
      case 1763241512:
      	 goto	#BV
      default:
      	 goto	#AR
   }
      -> Switch[1763241512] #BV -> #BV
      -> Immediate #BV -> #BW
      -> DefaultSwitch #BV -> #AR
      -> Switch[310867766] #BV -> #AR
      -> Switch[34534654] #BV -> #BW
      -> Switch[1400570155] #BV -> #T
      <- Switch[1763241512] #BV -> #BV
      <- Switch[34534626] #P -> #BV
===#Block BW(size=2, flags=100)===
   0. lvar54 = {1348915155 ^ lvar54};
   1. goto T
      -> UnconditionalJump[GOTO] #BW -> #T
      <- Immediate #BV -> #BW
      <- Switch[34534654] #BV -> #BW
===#Block T(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.idjgxxbzutklhap(), lvar54);
   2. return lvar27;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1834566646)
      goto AM
      -> ConditionalJump[IF_ICMPNE] #T -> #AM
      <- UnconditionalJump[GOTO] #BW -> #T
      <- Switch[1400570155] #BV -> #T
===#Block BY(size=2, flags=10100)===
   0. lvar54 = {683552492 ^ lvar54};
   1. goto U
      -> UnconditionalJump[GOTO] #BY -> #U
      <- Switch[34534654] #P -> #BY
===#Block U(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar28 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.rtldtzemvtoevhv(), lvar54);
   2. return lvar28;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1415689713)
      goto AL
      -> ConditionalJump[IF_ICMPNE] #U -> #AL
      <- UnconditionalJump[GOTO] #BY -> #U
===#Block AL(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 294699664)
      goto AL
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1175972879 ^ lvar54})
      goto AL
   2. _consume({559046425 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1415689713)
      goto AL
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1579391327 ^ lvar54})
      goto AL
   5. _consume({1571403370 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AL -> #AL
      <- ConditionalJump[IF_ICMPNE] #U -> #AL
      <- ConditionalJump[IF_ICMPNE] #AL -> #AL
      <- ConditionalJump[IF_ICMPNE] #P -> #AL
      <- DefaultSwitch #CB -> #AL
      <- Switch[1284942829] #CB -> #AL
===#Block BX(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 138006408);
   1. goto S
      -> UnconditionalJump[GOTO] #BX -> #S
      <- Switch[34534649] #P -> #BX
===#Block S(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar26 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.mlakyhzubpftzns(), lvar54);
   2. return lvar26;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1348386512)
      goto AR
      -> ConditionalJump[IF_ICMPNE] #S -> #AR
      <- UnconditionalJump[GOTO] #BX -> #S
===#Block AR(size=7, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 703244057)
      goto AR
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {926670022 ^ lvar54})
      goto AR
   2. _consume({177052130 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1348386512)
      goto AR
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {959011896 ^ lvar54})
      goto AR
   5. _consume({449223622 ^ lvar54});
   6. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #AR -> #AR
      <- ConditionalJump[IF_ICMPNE] #M -> #AR
      <- ConditionalJump[IF_ICMPNE] #S -> #AR
      <- ConditionalJump[IF_ICMPNE] #AR -> #AR
      <- DefaultSwitch #BV -> #AR
      <- Switch[310867766] #BV -> #AR
===#Block BZ(size=2, flags=10100)===
   0. lvar54 = com.hang.plugin.manager.ChestReplacementManager.ugajmuhknkyoqtlu(lvar54, 1442752822);
   1. goto V
      -> UnconditionalJump[GOTO] #BZ -> #V
      <- Switch[34534655] #P -> #BZ
===#Block V(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = com.hang.plugin.manager.ChestReplacementManager.tomxljagxg(com.hang.plugin.manager.ChestReplacementManager.pyfhbvseqedplks(), lvar54);
   2. return lvar29;
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1100641502)
      goto AI
      -> ConditionalJump[IF_ICMPNE] #V -> #AI
      <- UnconditionalJump[GOTO] #BZ -> #V
===#Block CA(size=2, flags=10100)===
   0. lvar54 = {1974685572 ^ lvar54};
   1. goto Q
      -> UnconditionalJump[GOTO] #CA -> #Q
      <- DefaultSwitch #P -> #CA
===#Block Q(size=17, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = new java.lang.StringBuilder;
   2. _consume(lvar21.<init>());
   3. lvar42 = lvar1;
   4. lvar5 = {2004965334 ^ lvar54};
   5. lvar6 = {2004965335 ^ lvar54};
   6. lvar43 = lvar42.substring(lvar5, lvar6);
   7. lvar44 = lvar43.toUpperCase();
   8. lvar22 = lvar21.append(lvar44);
   9. lvar45 = lvar1;
   10. lvar52 = {2004965335 ^ lvar54};
   11. lvar46 = lvar45.substring(lvar52);
   12. lvar47 = lvar46.toLowerCase();
   13. lvar23 = lvar22.append(lvar47);
   14. lvar24 = lvar23.toString();
   15. return lvar24;
   16. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1140146509)
      goto AM
      -> ConditionalJump[IF_ICMPNE] #Q -> #AM
      <- UnconditionalJump[GOTO] #CA -> #Q
===#Block AM(size=13, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 57183216)
      goto AM
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1240333862 ^ lvar54})
      goto AM
   2. _consume({73452356 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1834566646)
      goto AM
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1851372764 ^ lvar54})
      goto AM
   5. _consume({281631232 ^ lvar54});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1140146509)
      goto AM
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {691746464 ^ lvar54})
      goto AM
   8. _consume({1156515898 ^ lvar54});
   9. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 207700258)
      goto AM
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {707321056 ^ lvar54})
      goto AM
   11. _consume({998746357 ^ lvar54});
   12. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #AM -> #AM
      <- ConditionalJump[IF_ICMPNE] #L -> #AM
      <- ConditionalJump[IF_ICMPNE] #AM -> #AM
      <- ConditionalJump[IF_ICMPNE] #B -> #AM
      <- ConditionalJump[IF_ICMPNE] #Q -> #AM
      <- ConditionalJump[IF_ICMPNE] #T -> #AM
===#Block AI(size=16, flags=0)===
   0. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 1222492114)
      goto AI
   1. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1474023538 ^ lvar54})
      goto AI
   2. _consume({292904095 ^ lvar54});
   3. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -402391638)
      goto AI
   4. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1687734821 ^ lvar54})
      goto AI
   5. _consume({1707933019 ^ lvar54});
   6. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -1100641502)
      goto AI
   7. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1417008392 ^ lvar54})
      goto AI
   8. _consume({322408011 ^ lvar54});
   9. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != 311099875)
      goto AI
   10. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {2088634403 ^ lvar54})
      goto AI
   11. _consume({763194949 ^ lvar54});
   12. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != -629149990)
      goto AI
   13. if (pnkmjldawzlvsiwx.yadufoufjhclwbrc.lrujnvigqujtozea(lvar54) != {1358494954 ^ lvar54})
      goto AI
   14. _consume({1806266894 ^ lvar54});
   15. throw new java/lang/RuntimeException();
      -> ConditionalJump[IF_ICMPNE] #AI -> #AI
      <- ConditionalJump[IF_ICMPNE] #AG -> #AI
      <- ConditionalJump[IF_ICMPNE] #V -> #AI
      <- ConditionalJump[IF_ICMPNE] #A -> #AI
      <- Switch[388024636] #BR -> #AI
      <- DefaultSwitch #BR -> #AI
      <- ConditionalJump[IF_ICMPNE] #AI -> #AI
      <- ConditionalJump[IF_ICMPNE] #C -> #AI
      <- ConditionalJump[IF_ICMPNE] #K -> #AI
