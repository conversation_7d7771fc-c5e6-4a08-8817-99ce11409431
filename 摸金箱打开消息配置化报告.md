# 🎯 摸金箱打开消息配置化报告

## 📋 **用户需求**

用户要求将摸金箱打开消息添加到 `messages:` 配置段中，让玩家可以自定义，并且添加摸金箱名称变量 `{chest_name}`。

## ✅ **实现内容**

### **1. 配置文件新增消息**

**文件**: `config.yml`
**新增配置**:
```yaml
messages:
  # 摸金箱打开消息
  treasure-chest-opened-manual: "§6{chest_name}已打开！§e点击未搜索的物品进行手动搜索！"
  treasure-chest-opened-auto: "§6{chest_name}已打开！正在自动搜索宝藏..."
```

**功能**：
- ✅ **可自定义消息**：玩家可以修改配置文件自定义消息内容
- ✅ **支持变量**：`{chest_name}` 变量会被替换为实际的摸金箱名称
- ✅ **模式区分**：手动搜索和自动搜索显示不同的消息

### **2. 代码实现优化**

**文件**: `PlayerListener.java`
**修改内容**:

```java
// 🔧 修复：根据搜索模式显示不同的提示消息，支持摸金箱名称变量
boolean manualSearchEnabled = plugin.getConfig().getBoolean("treasure-chest.manual-search.enabled", false);

// 获取摸金箱名称
String chestType = getChestTypeFromLocation(chestLocation);
String chestName = plugin.getTreasureItemManager().getChestName(chestType);

if (manualSearchEnabled) {
    String message = plugin.getConfig().getString("messages.treasure-chest-opened-manual", "§6{chest_name}已打开！§e点击未搜索的物品进行手动搜索！");
    message = message.replace("{chest_name}", chestName);
    player.sendMessage(message);
} else {
    String message = plugin.getConfig().getString("messages.treasure-chest-opened-auto", "§6{chest_name}已打开！正在自动搜索宝藏...");
    message = message.replace("{chest_name}", chestName);
    player.sendMessage(message);
}
```

**新增方法**:
```java
/**
 * 从位置获取摸金箱种类
 */
private String getChestTypeFromLocation(org.bukkit.Location location) {
    TreasureChestData data = getTreasureChestData(location);
    if (data != null) {
        return data.getChestType();
    } else {
        return "common"; // 默认为普通摸金箱
    }
}
```

## 🎮 **功能特性**

### **变量支持**
- **`{chest_name}`**: 显示摸金箱的实际名称
  - 普通摸金箱：`§6摸金箱`
  - 武器摸金箱：`§c武器摸金箱`
  - 医疗摸金箱：`§a医疗摸金箱`
  - 等等...

### **智能模式检测**
- **自动搜索模式**：显示 `treasure-chest-opened-auto` 消息
- **手动搜索模式**：显示 `treasure-chest-opened-manual` 消息

### **完全可配置**
- 管理员可以修改 `config.yml` 自定义消息内容
- 支持颜色代码和格式化
- 支持多语言本地化

## 📊 **使用示例**

### **默认配置效果**

**自动搜索模式**：
```
玩家打开普通摸金箱 → "§6摸金箱已打开！正在自动搜索宝藏..."
玩家打开武器摸金箱 → "§c武器摸金箱已打开！正在自动搜索宝藏..."
```

**手动搜索模式**：
```
玩家打开普通摸金箱 → "§6摸金箱已打开！§e点击未搜索的物品进行手动搜索！"
玩家打开医疗摸金箱 → "§a医疗摸金箱已打开！§e点击未搜索的物品进行手动搜索！"
```

### **自定义配置示例**

```yaml
messages:
  treasure-chest-opened-manual: "§b✨ {chest_name} §b已成功打开！§e请手动点击物品进行搜索！"
  treasure-chest-opened-auto: "§d🎁 {chest_name} §d已打开！§a系统正在自动搜索中..."
```

**效果**：
```
自动模式 → "🎁 武器摸金箱 已打开！系统正在自动搜索中..."
手动模式 → "✨ 医疗摸金箱 已成功打开！请手动点击物品进行搜索！"
```

## 🔧 **技术实现**

### **摸金箱名称获取流程**
1. **获取位置数据**：从摸金箱位置获取 `TreasureChestData`
2. **识别种类**：从数据中获取 `chestType`（如 "common", "weapon", "medical"）
3. **获取显示名称**：通过 `TreasureItemManager.getChestName()` 获取本地化名称
4. **变量替换**：将 `{chest_name}` 替换为实际名称

### **配置读取机制**
- 使用 `plugin.getConfig().getString()` 读取配置
- 提供默认值确保向下兼容
- 支持实时配置重载

### **向下兼容性**
- 如果配置文件中没有新消息，使用默认值
- 保持原有的消息格式和功能
- 不影响现有的插件功能

## 🎯 **配置指南**

### **基础配置**
```yaml
messages:
  # 手动搜索模式打开消息
  treasure-chest-opened-manual: "§6{chest_name}已打开！§e点击未搜索的物品进行手动搜索！"
  
  # 自动搜索模式打开消息  
  treasure-chest-opened-auto: "§6{chest_name}已打开！正在自动搜索宝藏..."
```

### **高级自定义**
```yaml
messages:
  # 添加更多装饰和信息
  treasure-chest-opened-manual: "§l§6▶ {chest_name} §r§6已打开！\n§e§l➤ §r§e左键点击灰色物品开始手动搜索！"
  
  # 添加进度提示
  treasure-chest-opened-auto: "§a§l✓ §r§6{chest_name} §a已激活！\n§7系统将自动搜索宝藏，请耐心等待..."
```

### **多语言支持**
```yaml
messages:
  # 英文版本
  treasure-chest-opened-manual: "§6{chest_name} §aopened! §eClick items to search manually!"
  treasure-chest-opened-auto: "§6{chest_name} §aopened! §7Auto-searching treasures..."
  
  # 中文版本
  treasure-chest-opened-manual: "§6{chest_name}已打开！§e点击未搜索的物品进行手动搜索！"
  treasure-chest-opened-auto: "§6{chest_name}已打开！正在自动搜索宝藏..."
```

## 💡 **使用建议**

1. **保持简洁**：消息不要过长，避免刷屏
2. **颜色搭配**：使用合适的颜色代码增强可读性
3. **信息明确**：清楚地告知玩家当前的搜索模式
4. **统一风格**：与其他插件消息保持一致的风格

## 🎉 **总结**

现在摸金箱打开消息已经完全配置化，支持：
- ✅ **自定义消息内容**
- ✅ **摸金箱名称变量**
- ✅ **搜索模式区分**
- ✅ **完全向下兼容**
- ✅ **实时配置重载**

管理员可以根据服务器需求自由定制摸金箱打开时的提示消息！

---

**实现完成时间**: 2025-06-15  
**影响范围**: 摸金箱打开消息显示  
**兼容性**: 完全向下兼容  
**用户体验**: 显著提升，支持个性化定制
