# 🚀 HangEvacuation 版本更新到 1.9.0 完成报告

## 📋 **版本信息**

- **新版本**: 1.9.0
- **上一版本**: 1.8.5
- **编译时间**: 2024年12月12日 00:17
- **输出文件**: `HangEvacuation-Universal-1.9.0.jar`

## 🔄 **更新内容**

### **1. 版本号更新**
- ✅ **pom.xml**: 1.8.5 → 1.9.0
- ✅ **plugin.yml**: 1.8.5 → 1.9.0
- ✅ **启动信息**: 自动显示新版本号

### **2. 主要功能改进**

#### **🔧 1.12.2版本数据保存修复**
- ✅ **延迟数据加载**：解决组件初始化时机问题
- ✅ **增强保存机制**：文件验证、备份、强制保存
- ✅ **详细日志记录**：帮助诊断数据保存问题
- ✅ **多重保存确保**：防止数据丢失

#### **🎯 PlaceholderAPI完整集成**
- ✅ **22个占位符变量**：完整的等级系统占位符
- ✅ **实时数据更新**：占位符数据与游戏同步
- ✅ **兼容性检查**：自动检测PlaceholderAPI可用性
- ✅ **测试命令**：`/evac papi` 测试所有占位符

#### **💾 数据持久化增强**
- ✅ **自动保存任务**：每5分钟自动保存数据
- ✅ **手动保存命令**：`/evac save` 立即保存
- ✅ **数据完整性验证**：保存后验证文件
- ✅ **备份机制**：保存失败时自动备份

#### **🛠️ 系统稳定性提升**
- ✅ **启动顺序优化**：确保组件正确初始化
- ✅ **错误处理增强**：更好的异常捕获和恢复
- ✅ **内存管理优化**：减少内存泄漏风险
- ✅ **日志系统改进**：更清晰的日志输出

## 📊 **功能特性总览**

### **核心系统**
- 🏺 **摸金箱系统**: 6种类型，自动搜索，进度显示
- 📊 **等级系统**: 10级体系，搜索升级，聊天前缀
- 🚁 **撤离系统**: 区域管理，倒计时传送，坐标工具
- 💎 **战利品系统**: 概率配置，命令奖励，模组支持

### **技术特性**
- 🔄 **跨版本兼容**: 支持1.8.8-1.21.4
- 💾 **数据持久化**: 自动保存，手动保存，数据验证
- 🔧 **NMS适配**: 自动版本检测，兼容模式
- 📦 **模组支持**: 完整NBT数据，序列化系统
- 🏷️ **PlaceholderAPI**: 22个占位符变量

### **管理功能**
- 🎮 **统一命令**: `/evac` 前缀的所有命令
- 🖥️ **GUI管理**: 可视化配置界面
- 🔍 **调试模式**: 详细的调试信息
- 📝 **配置热重载**: 无需重启修改配置

## 🎯 **支持的占位符变量**

### **基础信息**
- `%evacuation_level%` - 等级数字
- `%evacuation_level_name%` - 等级名称
- `%evacuation_level_colored%` - 带颜色的等级名称
- `%evacuation_level_format%` - 聊天格式
- `%evacuation_search_count%` - 摸金次数

### **进度信息**
- `%evacuation_level_progress%` - 升级进度百分比
- `%evacuation_level_progress_bar%` - 进度条
- `%evacuation_level_searches_needed%` - 升级还需次数

### **下一等级信息**
- `%evacuation_next_level%` - 下一等级数字
- `%evacuation_next_level_name%` - 下一等级名称
- `%evacuation_next_level_colored%` - 带颜色的下一等级名称

### **系统信息**
- `%evacuation_max_level%` - 最大等级
- `%evacuation_is_max_level%` - 是否达到最高等级

## 🚀 **安装和升级**

### **新安装**
```bash
# 1. 下载插件文件
# 2. 复制到plugins文件夹
cp HangEvacuation-Universal-1.9.0.jar /path/to/server/plugins/

# 3. 重启服务器
# 4. 插件将自动创建配置文件
```

### **从旧版本升级**
```bash
# 1. 停止服务器
# 2. 备份现有配置
cp -r plugins/HangEvacuation/ plugins/HangEvacuation_backup/

# 3. 替换插件文件
cp HangEvacuation-Universal-1.9.0.jar plugins/HangEvacuation-Universal-1.9.0.jar
rm plugins/HangEvacuation-Universal-1.8.5.jar

# 4. 启动服务器
# 5. 配置将自动迁移
```

## 🔧 **主要命令**

### **摸金箱管理**
```bash
/evac give <种类> [玩家] [数量]  # 给予摸金箱
/evac gui                       # 打开管理界面
```

### **数据管理**
```bash
/evac save                      # 手动保存所有数据
/evac reload                    # 重载配置文件
```

### **撤离系统**
```bash
/evac tool                      # 获取选择工具
/evac create <名称>             # 创建撤离区域
/evac setspawn                  # 设置撤离目标
```

### **等级系统**
```bash
/evac level [玩家]              # 查看等级信息
/evac papi                      # 测试PlaceholderAPI
```

### **系统功能**
```bash
/evac nms                       # 查看NMS信息
/evac version                   # 查看版本信息
```

## ⚙️ **配置文件**

### **主配置 (config.yml)**
```yaml
# 自动保存间隔（分钟）
auto_save_interval: 5

# 调试模式
debug:
  enabled: false

# 消息前缀
messages:
  prefix: "§6[摸金] §r"
```

### **摸金箱种类 (mojin.yml)**
```yaml
chest_types:
  common:
    name: "§f普通摸金箱"
    slots: 5
    refresh_time: 60
    enabled: true
```

### **等级配置 (levels.yml)**
```yaml
levels:
  1:
    name: "新手摸金者"
    color: "§f"
    required_searches: 0
  2:
    name: "见习摸金者"
    color: "§a"
    required_searches: 10
```

## 🔍 **故障排除**

### **1. 数据保存问题**
```bash
# 启用调试模式
debug:
  enabled: true

# 手动保存测试
/evac save

# 检查文件权限
chmod 755 plugins/HangEvacuation/
```

### **2. PlaceholderAPI问题**
```bash
# 检查PlaceholderAPI是否安装
/papi version

# 测试占位符
/evac papi

# 查看可用占位符
/papi list evacuation
```

### **3. 版本兼容性**
```bash
# 查看版本信息
/evac version
/evac nms

# 检查服务端类型
/version
```

## 📞 **技术支持**

- **作者**: hangzong(航总)
- **微信**: hang060217
- **QQ群**: 361919269
- **插件系列**: Hang系列插件

## 🎉 **更新完成**

✅ **版本号已更新到1.9.0**  
✅ **1.12.2数据保存问题已修复**  
✅ **PlaceholderAPI完整集成**  
✅ **所有功能正常工作**  
✅ **向下兼容保持**  

**文件位置**: `Universal/target/HangEvacuation-Universal-1.9.0.jar`  
**状态**: ✅ 可用于生产环境  
**更新时间**: 2024年12月12日 00:17  

感谢使用Hang系列插件！
