# 🎮 HangEvacuation 模组物品支持说明

## 📋 **功能概述**

HangEvacuation插件现已支持模组物品！您可以在摸金箱中配置各种模组的物品，让玩家在探索时发现更多有趣的战利品。

---

## 🛠️ **配置文件**

### 📁 **mod_items.yml**
模组物品配置文件位于：`plugins/HangEvacuation/mod_items.yml`

### 🔧 **配置结构**
```yaml
mod_items:
  物品ID:
    material: IRON_INGOT              # 代表材料（在摸金箱中显示）
    amount: 1                         # 数量
    data: 0                           # 数据值（旧版本兼容）
    name: "§e物品显示名称"             # 显示名称
    lore:                             # 物品描述
      - "§7第一行描述"
      - "§7第二行描述"
    mod_id: "模组ID"                  # 模组ID
    item_id: "物品ID"                 # 物品ID
    custom_model_data: 0              # 自定义模型数据（1.14+）
    nbt_data: ""                      # NBT数据（高级用法）
    give_command: "give {player} 模组:物品 1"  # 给予命令
    probability: 0.15                 # 出现概率（0.0-1.0）
    search_speed: 5                   # 搜索速度（秒）
    commands: []                      # 额外命令
```

---

## 📝 **配置参数详解**

### 🎨 **基础属性**
- **material**: 在摸金箱中显示的代表材料
- **amount**: 物品数量
- **data**: 数据值（用于区分物品变种）
- **name**: 物品显示名称（支持颜色代码）
- **lore**: 物品描述列表（支持颜色代码）

### 🔧 **模组属性**
- **mod_id**: 模组的ID（如：thermal、ic2、ae2等）
- **item_id**: 物品在模组中的ID
- **custom_model_data**: 自定义模型数据（1.14+版本支持）
- **nbt_data**: NBT数据字符串（高级用法）
- **give_command**: 给予物品的命令，{player}会被替换为玩家名

### ⚙️ **摸金箱属性**
- **probability**: 出现概率（0.0-1.0，0.1表示10%）
- **search_speed**: 搜索时间（秒），越珍贵的物品搜索时间越长
- **commands**: 获得物品时执行的额外命令列表

---

## 🎯 **配置示例**

### 🔥 **热力膨胀扳手**
```yaml
thermal_wrench:
  material: IRON_INGOT
  amount: 1
  name: "§e热力扳手"
  lore:
    - "§7热力膨胀模组的工具"
    - "§7用于配置机器方向"
  mod_id: "thermal"
  item_id: "wrench"
  give_command: "give {player} thermal:wrench 1"
  probability: 0.15
  search_speed: 5
```

### ⚡ **工业2钻石钻头**
```yaml
ic2_diamond_drill:
  material: DIAMOND_PICKAXE
  amount: 1
  name: "§b钻石钻头"
  lore:
    - "§7工业2模组的高级工具"
    - "§7可以快速挖掘"
  mod_id: "ic2"
  item_id: "diamond_drill"
  give_command: "give {player} ic2:diamond_drill 1"
  probability: 0.08
  search_speed: 8
  commands:
    - "tellraw {player} {\"text\":\"你获得了钻石钻头！\",\"color\":\"gold\"}"
```

### 🌟 **神秘时代研究笔记**
```yaml
thaumcraft_research:
  material: PAPER
  amount: 1
  name: "§5神秘研究笔记"
  lore:
    - "§7神秘时代模组物品"
    - "§7包含神秘知识"
  mod_id: "thaumcraft"
  item_id: "research_notes"
  give_command: "give {player} thaumcraft:research_notes 1"
  probability: 0.10
  search_speed: 7
  commands:
    - "tellraw {player} {\"text\":\"你发现了神秘的研究笔记...\",\"color\":\"dark_purple\"}"
```

---

## 🎮 **游戏体验**

### 🔍 **搜索过程**
1. 玩家打开摸金箱
2. 自动搜索系统开始工作
3. 发现模组物品时显示代表物品
4. 搜索完成后通过命令给予真实模组物品
5. 执行额外的自定义命令

### 💬 **消息提示**
- `§a搜索完成！发现了：热力扳手`
- `§e模组物品已通过命令给予到您的背包！`

### 🎵 **音效支持**
- 搜索开始音效
- 搜索完成音效
- 支持自定义音效配置

---

## 🔧 **管理命令**

### 📋 **基础命令**
- `/evac gui` - 打开摸金箱管理界面
- `/evac reload` - 重载配置文件
- `/evac give` - 给予摸金箱

### 🛠️ **管理功能**
- 支持在游戏内通过GUI管理模组物品
- 支持热重载配置文件
- 支持实时添加/删除模组物品

---

## 🎨 **自定义功能**

### 🖼️ **自定义模型**
对于1.14+版本，可以使用custom_model_data设置自定义模型：
```yaml
custom_model_data: 1001
```

### 📜 **NBT数据**
高级用户可以使用NBT数据：
```yaml
nbt_data: "{CustomTag:1b,SpecialData:\"value\"}"
```

### 🎯 **命令系统**
支持多种命令类型：
- 给予命令：直接给予模组物品
- 额外命令：执行特殊效果
- 消息命令：发送自定义消息

---

## 🔄 **兼容性**

### 🎮 **支持的模组**
- **工业模组**: IC2、热力膨胀、更多实用设备等
- **魔法模组**: 神秘时代、植物魔法、血魔法等
- **科技模组**: 应用能源、末影接口、沉浸工程等
- **建筑模组**: 建筑、装饰、家具等
- **冒险模组**: 匠魂、暮色森林、深渊国度等

### 🖥️ **服务端兼容**
- **Forge服务端**: 完全支持
- **Mohist服务端**: 完全支持
- **CatServer**: 完全支持
- **纯净服务端**: 支持（通过命令给予）

---

## 🚀 **性能优化**

### ⚡ **高效设计**
- 延迟加载模组物品配置
- 智能缓存机制
- 异步命令执行
- 内存优化管理

### 📊 **统计信息**
插件启动时会显示：
- 已加载的普通物品数量
- 已加载的模组物品数量
- 支持的模组列表

---

## 🛠️ **故障排除**

### ❌ **常见问题**

#### 1. 模组物品无法给予
**原因**: 模组未安装或命令错误
**解决**: 检查模组是否安装，确认give命令格式正确

#### 2. 物品显示异常
**原因**: 代表材料不存在
**解决**: 使用有效的Minecraft材料名称

#### 3. 概率设置无效
**原因**: 概率值超出范围
**解决**: 确保概率值在0.0-1.0之间

### 🔧 **调试模式**
在配置文件中启用调试模式：
```yaml
debug: true
```

---

## 📞 **技术支持**

如果您在使用模组物品功能时遇到问题：

1. **检查配置**: 确认配置文件格式正确
2. **查看日志**: 检查控制台错误信息
3. **测试命令**: 手动测试give命令是否有效
4. **联系支持**: 微信 hang060217

---

**🎉 现在您可以在摸金箱中配置各种精彩的模组物品，为玩家带来更丰富的探索体验！**
