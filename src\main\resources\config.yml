# 摸金插件配置文件

# 调试模式配置
debug:
  # 是否启用调试模式（显示详细的加载信息）
  enabled: false

# 摸金箱设置
treasure-chest:
  # 自动刷新设置
  auto_refresh:
    # 是否启用自动刷新功能
    enabled: true                # 启用自动刷新功能
    # 摸金箱开启后多少分钟没有刷新就自动进入刷新倒计时
    idle_minutes: 30             # 空闲30分钟后自动进入刷新倒计时
    # 检测间隔时间（分钟）- 多久检查一次所有摸金箱的自动刷新状态
    check_interval: 5            # 每5分钟检查一次

  # 默认物品数量
  default-items: 5
  # 搜索冷却时间 (秒) - 手动点击搜索的冷却时间
  search-cooldown: 1
  # 是否启用浮空字提示 (全局设置)
  hologram_enabled: true

  # 新添加物品的默认设置
  default_probability: 10.0         # 新物品的默认概率 (%)
  default_search_speed: 3           # 新物品的默认搜索速度 (秒)

  # 浮空字自定义设置
  hologram:
    # 智能距离管理
    auto_remove_distance: 32.0    # 玩家远离摸金箱多少格时自动删除浮空字 (防止浮空字异常)

    # 启动时清理设置
    startup_cleanup:
      enabled: true               # 是否在服务器启动时清除遗留的摸金插件浮空字
      delay_seconds: 1           # 延迟多少秒后开始清理 (确保所有世界都已加载)

    # 浮空字位置偏移
    position:
      x: 0.5      # X轴偏移 (默认箱子中心)
      y: 1.5      # Y轴偏移 (箱子上方高度)
      z: 0.5      # Z轴偏移 (默认箱子中心)

    # 浮空字文本自定义
    messages:
      # 未搜索状态 - 支持变量: {count} = 剩余物品数量, {total} = 总物品数量
      unsearched: "§6还有 §e{count} §6个物品未搜索"

      # 已搜索完毕状态
      fully_searched: "§a已搜索完毕"

      # 可以刷新状态
      can_refresh: "§a可以刷新！右键重新生成物品"

      # 刷新倒计时 - 支持变量: {minutes} = 分钟, {seconds} = 秒数
      refresh_countdown: "§e刷新倒计时: §c{minutes}:{seconds}"

      # 搜索中状态 - 支持变量: {player} = 搜索者名称
      searching: "§b{player} §7正在搜索中..."


    # 浮空字更新频率 (tick, 20tick = 1秒)
    update_interval: 20

    # 区块卸载保护设置
    chunk_protection:
      # 是否启用区块卸载保护
      enabled: true

      # 区块检查间隔 (tick, 20tick = 1秒)
      check_interval: 600  # 30秒

      # 玩家检测范围 (方块) - 只在此范围内有玩家时才重建浮空字
      player_detection_range: 64

      # 是否强制加载浮空字所在的区块
      force_load_chunks: true
  # 自动搜索配置
  auto-search:
    # 是否启用自动搜索
    enabled: true
    # 搜索模式: "random" 随机搜索, "sequential" 按顺序搜索
    mode: "random"
    # 自动搜索检查间隔（秒）- 每隔多少秒检查一次是否可以开始下一个搜索
    # 注意：这不是搜索时间，而是检查频率。搜索时间由物品的 search_speed 决定
    interval: 1
    # 每次自动搜索的物品数量（固定为1，一次只搜索一个物品）
    items-per-search: 1
  # 手动搜索配置
  manual-search:
    # 是否启用手动搜索（点击物品进行搜索）
    enabled: false

  # 摸金箱放置权限设置
  placement-restrictions:
    # 是否只允许OP放置摸金箱 (true=只有OP可以放置, false=所有玩家都可以放置)
    op-only: true
    # 权限节点检查 (如果不为空，将检查此权限而不是OP状态)
    permission: ""  # 例如: "evacuation.place" 或留空使用OP检查
  # 摸金箱刷新时间 (分钟) - 设置为0表示不刷新，永久禁用
  refresh-time: 5
  # 搜索动画设置
  animation:
    # 更新间隔 (tick, 20tick = 1秒) - 进度条更新频率
    # 总时间 = 物品搜索速度（秒），更新间隔只影响动画流畅度
    update-interval: 3
    # 自动搜索下一个的延迟 (tick)
    next-search-delay: 40
    # 进度条方向设置
    progress-direction:
      # 进度条方向: "countdown" (100%->0%) 或 "countup" (0%->100%)
      mode: "countup"
      # countdown模式: 倒计时，从100%递减到0%，营造紧张感
      # countup模式: 正计时，从0%递增到100%，营造期待感
    # 音效配置
    sounds:
      # 搜索物品时的提示音 (每次开始搜索时播放)
      search-start:
        enabled: true
        # 音效名称 (支持原版和模组音效)
        # 1.12.2版本请使用: "ENTITY_EXPERIENCE_ORB_PICKUP"
        # 1.13+版本请使用: "entity.experience_orb.pickup"
        sound: "ENTITY_EXPERIENCE_ORB_PICKUP"
        volume: 0.5
        pitch: 1.0

      # 搜索成功提示音 (找到物品时播放)
      search-success:
        enabled: true
        # 音效名称 (支持原版和模组音效)
        # 1.12.2版本请使用: "ENTITY_PLAYER_LEVELUP"
        # 1.13+版本请使用: "entity.player.levelup"
        sound: "ENTITY_PLAYER_LEVELUP"
        volume: 0.8
        pitch: 1.2

      # 搜索进行中音效 (进度条更新时播放，可选)
      search-progress:
        enabled: false
        # 1.12.2版本请使用: "BLOCK_NOTE_PLING"
        # 1.13+版本请使用: "block.note_block.pling"
        sound: "BLOCK_NOTE_PLING"
        volume: 0.3
        pitch: 1.5
        # 播放间隔 (每隔多少次进度更新播放一次音效，0=每次都播放)
        interval: 3

      # 摸金箱打开音效
      chest-open:
        enabled: true
        # 1.12.2版本请使用: "BLOCK_CHEST_OPEN"
        # 1.13+版本请使用: "block.chest.open"
        sound: "BLOCK_CHEST_OPEN"
        volume: 1.0
        pitch: 1.2

      # 摸金箱关闭音效
      chest-close:
        enabled: true
        # 1.12.2版本请使用: "BLOCK_CHEST_CLOSE"
        # 1.13+版本请使用: "block.chest.close"
        sound: "BLOCK_CHEST_CLOSE"
        volume: 0.8
        pitch: 1.0

      # 物品拾取音效 (玩家拿取物品时)
      item-pickup:
        enabled: true
        # 1.12.2版本请使用: "ENTITY_ITEM_PICKUP"
        # 1.13+版本请使用: "entity.item.pickup"
        sound: "ENTITY_ITEM_PICKUP"
        volume: 0.6
        pitch: 1.3

      # 搜索失败音效 (没有找到物品时，可选)
      search-fail:
        enabled: false
        sound: "entity.villager.no"
        volume: 0.4
        pitch: 0.8


# 消息设置
messages:
  prefix: "§6[摸金] §r"
  treasure-chest-title: "§6摸金箱"
  searching: "§e正在搜索..."
  search-complete: "§a搜索完成！"
  search-cooldown: "§c搜索冷却中，请等待 {time} 秒"
  no-permission: "§c你没有权限使用此功能"
  # 摸金箱打开消息
  treasure-chest-opened-manual: "§6{chest_name}已打开！§e点击未搜索的物品进行手动搜索！"
  treasure-chest-opened-auto: "§6{chest_name}已打开！正在自动搜索宝藏..."

  # 摸金箱放置权限消息
  treasure-chest-place-denied: "§c只有管理员才能放置摸金箱！"
  treasure-chest-place-denied-permission: "§c您没有权限放置摸金箱！需要权限: {permission}"
  treasure-chest-place-help: "§7如果您是管理员，请使用 /op {player} 获取权限"

  # 摸金箱搜索完成消息
  treasure-chest-fully-searched: "§a恭喜！您已搜索完这个摸金箱的所有物品！"
  treasure-chest-refresh-time: "§e箱子将在 {minutes} 分钟后刷新"

  # 摸金箱冷却期间消息
  treasure-chest-cooldown-title: "§6摸金箱已搜索完毕！"
  treasure-chest-cooldown-refresh: "§e物品将在 §c{minutes}:{seconds} §e后刷新"
  treasure-chest-cooldown-hint: "§7您可以拿走剩余的物品，但需要等待刷新时间到达才会有新物品"

  # 手动搜索消息
  manual-search-already-searched: "§c这个物品已经被搜索过了！"
  manual-search-in-progress: "§e这个物品正在搜索中！"
  manual-search-wait-current: "§e请等待当前物品搜索完成后再搜索其他物品！"
  manual-search-start: "§a开始手动搜索物品..."

  # 搜索冷却消息
  search-cooldown-remaining: "§c搜索冷却中，请等待 {seconds} 秒"

  # 物品搜索完成消息
  item-search-complete: "§a搜索完成！发现了：{item_name}"
  item-search-empty: "§7这里什么都没有..."

  # 获得物品消息 - 支持变量: {item_name} = 物品名称, {amount} = 数量
  item-obtained: "§a获得物品: §f{item_name} §ax{amount}"

  # 背包满了消息
  inventory-full: "§c背包空间不足！"

  # 模组物品消息 - 支持变量: {mod_item_id} = 模组物品ID
  mod-item-converted: "§a已转换为真实模组物品: {mod_item_id}"
  mod-item-given-by-command: "§e模组物品已通过命令给予到您的背包"

# 物品设置
items:
  # 未搜索的物品 (灰色玻璃板) - 自动搜索模式
  unsearched-item:
    material: STAINED_GLASS_PANE
    data: 7  # 灰色
    name: "§7未搜索"
    lore:
      - "§7等待自动搜索"

  # 搜索进度玻璃板颜色配置
  searching-progress:
    # 基础材质
    material: STAINED_GLASS_PANE

    # CustomModelData 设置 (仅在1.14+版本生效)
    # 设置为0表示不使用CustomModelData，使用正数值来指定自定义模型
    custom_model_data: 0

    # 搜索进度阶段配置 (按进度百分比划分)
    stages:
      # 0-25% 进度阶段
      stage_1:
        progress_range: [0, 25]
        data: 14  # 红色
        name: "§c正在搜索... {progress}%"
        lore:
          - "§7搜索中..."

      # 26-50% 进度阶段
      stage_2:
        progress_range: [26, 50]
        data: 1   # 橙色
        name: "§6正在搜索... {progress}%"
        lore:
          - "§7搜索中..."

      # 51-75% 进度阶段
      stage_3:
        progress_range: [51, 75]
        data: 4   # 黄色
        name: "§e正在搜索... {progress}%"
        lore:
          - "§7搜索中..."

      # 76-100% 进度阶段
      stage_4:
        progress_range: [76, 100]
        data: 5   # 浅绿色
        name: "§a正在搜索... {progress}%"
        lore:
          - "§7搜索中..."

  # 已搜索的物品会显示实际的战利品


# 模组兼容性设置
mod_compatibility:
  # 是否启用模组容器检测
  enable_mod_containers: true

  # 自定义容器类型关键词（用于检测模组容器）
  custom_container_keywords:
    - "CRATE"
    - "BOX"
    - "CONTAINER"
    - "STORAGE"
    - "TANK"
    - "CABINET"
    - "LOCKER"
    - "SAFE"
    - "VAULT"
    - "DRAWER"
    - "RACK"
    - "SHELF"
    - "WARDROBE"
    - "CUPBOARD"
    - "TOOLBOX"
    - "INVENTORY"
    - "DEPOT"
    - "WAREHOUSE"
    - "SILO"
    - "BIN"
    - "CASE"
    - "TRUNK"

# 性能优化配置
performance:
  # 自动保存配置
  auto_save:
    interval: 5              # 自动保存间隔（分钟），设置为0或负数禁用自动保存
    show_logs: false         # 是否显示后台操作日志（自动保存、数据处理等）

  # 异步保存配置
  async_save:
    enabled: true              # 启用异步保存
    batch_size: 50             # 批量保存数量
    save_delay_ms: 100         # 保存延迟（毫秒）
    max_queue_size: 1000       # 最大队列大小

  # 关服保存优化
  shutdown_save:
    show_progress: true        # 显示保存进度
    progress_interval: 100     # 每多少个摸金箱显示一次进度
    batch_mode: true           # 启用批量保存模式（推荐）

  # 内存缓存优化
  memory_cache:
    enabled: true              # 启用内存缓存（推荐）
    cache_only_mode: false     # 仅内存模式（不保存到文件，重启丢失）
    auto_save_interval: 300    # 自动保存到文件间隔（秒）
    max_cache_size: 10000      # 最大缓存摸金箱数量
    compress_data: true        # 压缩序列化数据

  # 数据序列化优化
  serialization:
    use_compression: false     # 使用压缩（可能影响兼容性）
    cache_serialized: true     # 缓存序列化结果