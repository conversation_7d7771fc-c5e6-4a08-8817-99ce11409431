package com.hang.plugin.gui;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.manager.TreasureItemManager;
import com.hang.plugin.utils.VersionUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 命令编辑GUI
 */
public class CommandEditGUI {
    
    private final HangPlugin plugin;
    private final Player player;
    private final Inventory inventory;
    private final TreasureItemManager.TreasureItem treasureItem;
    private final TreasureEditGUI parentGUI;
    private final List<String> commands;
    private int currentPage = 0;
    private final int COMMANDS_PER_PAGE = 21; // 3行，每行7个
    
    public CommandEditGUI(HangPlugin plugin, Player player, TreasureItemManager.TreasureItem treasureItem, TreasureEditGUI parentGUI) {
        this.plugin = plugin;
        this.player = player;
        this.treasureItem = treasureItem;
        this.parentGUI = parentGUI;
        this.commands = new ArrayList<>(treasureItem.getCommands() != null ? treasureItem.getCommands() : new ArrayList<>());
        this.inventory = Bukkit.createInventory(null, 54, "§6命令编辑: " + treasureItem.getId());
        
        setupGUI();
    }
    
    /**
     * 设置GUI
     */
    private void setupGUI() {
        // 清空界面
        inventory.clear();
        
        // 显示命令列表
        displayCommands();
        
        // 设置控制按钮
        setupControlButtons();
        
        // 设置分页按钮
        setupPaginationButtons();
    }
    
    /**
     * 显示命令列表
     */
    private void displayCommands() {
        int startIndex = currentPage * COMMANDS_PER_PAGE;
        int endIndex = Math.min(startIndex + COMMANDS_PER_PAGE, commands.size());
        
        for (int i = startIndex; i < endIndex; i++) {
            int slot = i - startIndex;
            String command = commands.get(i);
            
            ItemStack commandItem = new ItemStack(Material.PAPER);
            ItemMeta meta = commandItem.getItemMeta();
            meta.setDisplayName("§e命令 #" + (i + 1));
            
            List<String> lore = new ArrayList<>();
            lore.add("§7命令内容:");
            
            // 分割长命令显示
            if (command.length() > 30) {
                String[] parts = splitCommand(command, 30);
                for (String part : parts) {
                    lore.add("§f" + part);
                }
            } else {
                lore.add("§f" + command);
            }
            
            lore.add("");
            lore.add("§7左键: §a编辑命令");
            lore.add("§7右键: §c删除命令");
            lore.add("§7Shift+左键: §e复制命令");
            
            meta.setLore(lore);
            commandItem.setItemMeta(meta);
            
            inventory.setItem(slot, commandItem);
        }
    }
    
    /**
     * 分割命令字符串用于显示
     */
    private String[] splitCommand(String command, int maxLength) {
        List<String> parts = new ArrayList<>();
        int start = 0;
        
        while (start < command.length()) {
            int end = Math.min(start + maxLength, command.length());
            parts.add(command.substring(start, end));
            start = end;
        }
        
        return parts.toArray(new String[0]);
    }
    
    /**
     * 设置控制按钮
     */
    private void setupControlButtons() {
        // 添加命令按钮
        ItemStack addButton = new ItemStack(Material.EMERALD);
        ItemMeta addMeta = addButton.getItemMeta();
        addMeta.setDisplayName("§a添加新命令");
        addMeta.setLore(Arrays.asList(
            "§7点击添加新的执行命令",
            "§7支持变量: {player} = 玩家名称",
            "§7示例: tell {player} 恭喜获得物品！"
        ));
        addButton.setItemMeta(addMeta);
        inventory.setItem(45, addButton);
        

        
        // 测试命令按钮
        ItemStack testButton = new ItemStack(Material.REDSTONE);
        ItemMeta testMeta = testButton.getItemMeta();
        testMeta.setDisplayName("§c测试所有命令");
        testMeta.setLore(Arrays.asList(
            "§7点击测试所有命令",
            "§7将对你执行所有命令"
        ));
        testButton.setItemMeta(testMeta);
        inventory.setItem(47, testButton);
        
        // 清空所有命令按钮
        ItemStack clearButton = new ItemStack(Material.TNT);
        ItemMeta clearMeta = clearButton.getItemMeta();
        clearMeta.setDisplayName("§4清空所有命令");
        clearMeta.setLore(Arrays.asList(
            "§7点击清空所有命令",
            "§c警告: 此操作不可撤销！"
        ));
        clearButton.setItemMeta(clearMeta);
        inventory.setItem(48, clearButton);
        
        // 保存按钮
        ItemStack saveButton = new ItemStack(Material.EMERALD_BLOCK);
        ItemMeta saveMeta = saveButton.getItemMeta();
        saveMeta.setDisplayName("§a保存并返回");
        saveMeta.setLore(Arrays.asList(
            "§7保存命令修改并返回",
            "§7当前命令数: §f" + commands.size()
        ));
        saveButton.setItemMeta(saveMeta);
        inventory.setItem(52, saveButton);
        
        // 取消按钮
        ItemStack cancelButton = new ItemStack(Material.REDSTONE_BLOCK);
        ItemMeta cancelMeta = cancelButton.getItemMeta();
        cancelMeta.setDisplayName("§c取消并返回");
        cancelMeta.setLore(Arrays.asList(
            "§7放弃修改并返回"
        ));
        cancelButton.setItemMeta(cancelMeta);
        inventory.setItem(53, cancelButton);
    }
    
    /**
     * 设置分页按钮
     */
    private void setupPaginationButtons() {
        int totalPages = (int) Math.ceil((double) commands.size() / COMMANDS_PER_PAGE);
        
        // 上一页按钮
        if (currentPage > 0) {
            ItemStack prevButton = new ItemStack(Material.ARROW);
            ItemMeta prevMeta = prevButton.getItemMeta();
            prevMeta.setDisplayName("§e上一页");
            prevMeta.setLore(Arrays.asList("§7第 " + currentPage + " 页"));
            prevButton.setItemMeta(prevMeta);
            inventory.setItem(49, prevButton);
        }
        
        // 页面信息
        ItemStack pageInfo = new ItemStack(Material.BOOK);
        ItemMeta pageInfoMeta = pageInfo.getItemMeta();
        pageInfoMeta.setDisplayName("§6页面信息");
        pageInfoMeta.setLore(Arrays.asList(
            "§7当前页: §f" + (currentPage + 1) + " / " + Math.max(1, totalPages),
            "§7总命令数: §f" + commands.size()
        ));
        pageInfo.setItemMeta(pageInfoMeta);
        inventory.setItem(50, pageInfo);
        
        // 下一页按钮
        if (currentPage < totalPages - 1) {
            ItemStack nextButton = new ItemStack(Material.ARROW);
            ItemMeta nextMeta = nextButton.getItemMeta();
            nextMeta.setDisplayName("§e下一页");
            nextMeta.setLore(Arrays.asList("§7第 " + (currentPage + 2) + " 页"));
            nextButton.setItemMeta(nextMeta);
            inventory.setItem(51, nextButton);
        }
    }
    
    /**
     * 处理点击事件
     */
    public void handleClick(int slot, boolean isRightClick, boolean isShiftClick) {
        // 命令项点击
        if (slot < COMMANDS_PER_PAGE) {
            int commandIndex = currentPage * COMMANDS_PER_PAGE + slot;
            if (commandIndex < commands.size()) {
                if (isRightClick) {
                    // 删除命令
                    deleteCommand(commandIndex);
                } else if (isShiftClick) {
                    // 复制命令
                    copyCommand(commandIndex);
                } else {
                    // 编辑命令
                    editCommand(commandIndex);
                }
            }
            return;
        }
        
        // 控制按钮点击
        switch (slot) {
            case 45: addNewCommand(); break;
            case 47: testAllCommands(); break;
            case 48: clearAllCommands(); break;
            case 49: previousPage(); break;
            case 50: /* 页面信息，无操作 */ break;
            case 51: nextPage(); break;
            case 52: saveAndReturn(); break;
            case 53: cancelAndReturn(); break;
        }
    }
    
    /**
     * 添加新命令
     */
    private void addNewCommand() {
        player.closeInventory();
        player.sendMessage("§e请在聊天框中输入要添加的命令:");
        player.sendMessage("§7支持变量: {player} = 玩家名称");
        player.sendMessage("§7示例: tell {player} 恭喜获得物品！");
        player.sendMessage("§7输入 'cancel' 取消添加");
        
        // 设置输入监听
        plugin.getPlayerListener().setCommandInputListener(player, new CommandInputListener() {
            @Override
            public void onCommandInput(String input) {
                if (!"cancel".equalsIgnoreCase(input.trim())) {
                    commands.add(input.trim());
                    player.sendMessage("§a命令已添加: §f" + input.trim());
                } else {
                    player.sendMessage("§c已取消添加命令");
                }
                // 刷新GUI并重新打开
                setupGUI();
                open();
            }
        });
    }
    
    /**
     * 编辑命令
     */
    private void editCommand(int index) {
        String oldCommand = commands.get(index);
        player.closeInventory();
        player.sendMessage("§e请输入新的命令内容:");
        player.sendMessage("§7当前命令: §f" + oldCommand);
        player.sendMessage("§7输入 'cancel' 取消编辑");
        
        // 设置输入监听
        plugin.getPlayerListener().setCommandInputListener(player, new CommandInputListener() {
            @Override
            public void onCommandInput(String input) {
                if (!"cancel".equalsIgnoreCase(input.trim())) {
                    commands.set(index, input.trim());
                    player.sendMessage("§a命令已修改: §f" + input.trim());
                } else {
                    player.sendMessage("§c已取消编辑命令");
                }
                // 刷新GUI并重新打开
                setupGUI();
                open();
            }
        });
    }
    
    /**
     * 删除命令
     */
    private void deleteCommand(int index) {
        String command = commands.get(index);
        commands.remove(index);
        player.sendMessage("§c已删除命令: §f" + command);
        setupGUI();
    }
    
    /**
     * 复制命令
     */
    private void copyCommand(int index) {
        String command = commands.get(index);
        commands.add(command);
        player.sendMessage("§a已复制命令: §f" + command);
        setupGUI();
    }
    

    
    /**
     * 测试所有命令
     */
    private void testAllCommands() {
        if (commands.isEmpty()) {
            player.sendMessage("§c没有命令可以测试！");
            return;
        }
        
        player.sendMessage("§e正在测试 " + commands.size() + " 个命令...");
        for (int i = 0; i < commands.size(); i++) {
            String command = commands.get(i).replace("{player}", player.getName());
            try {
                plugin.getServer().dispatchCommand(plugin.getServer().getConsoleSender(), command);
                player.sendMessage("§a命令 " + (i + 1) + " 执行成功: §f" + command);
            } catch (Exception e) {
                player.sendMessage("§c命令 " + (i + 1) + " 执行失败: §f" + command);
            }
        }
    }
    
    /**
     * 清空所有命令
     */
    private void clearAllCommands() {
        int count = commands.size();
        commands.clear();
        player.sendMessage("§c已清空所有命令 (共 " + count + " 个)");
        setupGUI();
    }
    
    /**
     * 上一页
     */
    private void previousPage() {
        if (currentPage > 0) {
            currentPage--;
            setupGUI();
        }
    }
    
    /**
     * 下一页
     */
    private void nextPage() {
        int totalPages = (int) Math.ceil((double) commands.size() / COMMANDS_PER_PAGE);
        if (currentPage < totalPages - 1) {
            currentPage++;
            setupGUI();
        }
    }
    
    /**
     * 保存并返回
     */
    private void saveAndReturn() {
        // 更新TreasureItem的命令列表
        treasureItem.setCommands(new ArrayList<>(commands));
        player.sendMessage("§a命令已保存！共 " + commands.size() + " 个命令");
        // 重新打开父GUI（TreasureEditGUI会自动刷新显示）
        parentGUI.open();
    }
    
    /**
     * 取消并返回
     */
    private void cancelAndReturn() {
        player.sendMessage("§c已取消命令编辑");
        parentGUI.open();
    }
    
    /**
     * 添加命令到列表
     */
    public void addCommand(String command) {
        commands.add(command);
        setupGUI();
    }
    
    /**
     * 打开GUI
     */
    public void open() {
        // 注册GUI到监听器
        plugin.getPlayerListener().registerCommandEditGUI(player, this);
        player.openInventory(inventory);
    }
    
    /**
     * 刷新GUI
     */
    public void refresh() {
        setupGUI();
    }

    /**
     * 获取库存界面
     */
    public Inventory getInventory() {
        return inventory;
    }

    /**
     * 命令输入监听器接口
     */
    public interface CommandInputListener {
        void onCommandInput(String input);
    }
}
