# HangLevels - 等级系统配置文件
# 插件作者: hangzong(航总)
# 技术支持: V: hang060217 | QQ群: 361919269

# 等级系统设置
settings:
  # 是否启用等级系统
  enable_level_system: true
  # 是否广播玩家升级消息
  broadcast_levelup: true
  # 是否在聊天中显示等级前缀
  show_level_in_chat: true

# 消息配置
messages:
  # 升级广播消息 (注意: {level_name} 会自动使用等级配置中的颜色)
  levelup_broadcast: "§6玩家 §e{player} §6达到了摸金等级 {level_name}§6！"
  # 等级查询消息
  level_info: "§6您的摸金等级: §e{level_name} §6| 搜索次数: §e{searches}"

# 等级配置
# level: 等级数字
# required_searches: 达到此等级需要的搜索次数
# name: 等级名称
# color: 等级颜色代码
# display_format: 聊天中显示的格式 (支持变量: {level}, {level_name}, {searches})
# rewards: 达到等级时给予的奖励命令列表
#
# 等级颜色说明:
# §f = 白色 (新手)    §a = 绿色 (见习)    §b = 蓝色 (熟练)    §d = 紫色 (专业)
# §6 = 金色 (大师)    §c = 红色 (传奇)    §5 = 深紫 (史诗)    §4 = 深红 (神话)
# §e§l = 黄色加粗 (至尊)    §c§l = 红色加粗 (王者)
levels:
  1:
    required_searches: 0
    name: "新手摸金者"
    color: "§f"  # 白色
    display_format: "§7[§f{level_name}§7]"
    rewards: []

  2:
    required_searches: 10
    name: "见习摸金者"
    color: "§a"  # 绿色
    display_format: "§7[§a{level_name}§7]"
    rewards:
      - "/give {player} diamond 1"
      - "/tellraw {player} [\"§a恭喜达到见习摸金者等级！获得钻石奖励！\"]"

  3:
    required_searches: 25
    name: "熟练摸金者"
    color: "§b"  # 蓝色
    display_format: "§7[§b{level_name}§7]"
    rewards:
      - "/give {player} emerald 3"
      - "/give {player} gold_ingot 5"
      - "/tellraw {player} [\"§b恭喜达到熟练摸金者等级！\"]"

  4:
    required_searches: 50
    name: "专业摸金者"
    color: "§d"  # 紫色
    display_format: "§7[§d{level_name}§7]"
    rewards:
      - "/give {player} diamond 3"
      - "/give {player} emerald 5"
      - "/xp add {player} 500"
      - "/tellraw {player} [\"§d恭喜达到专业摸金者等级！\"]"

  5:
    required_searches: 100
    name: "大师摸金者"
    color: "§6"  # 金色
    display_format: "§7[§6{level_name}§7]"
    rewards:
      - "/give {player} diamond_sword 1"
      - "/give {player} diamond 5"
      - "/xp add {player} 1000"
      - "/tellraw {player} [\"§6恭喜达到大师摸金者等级！获得钻石剑！\"]"

  6:
    required_searches: 200
    name: "传奇摸金者"
    color: "§c"  # 红色
    display_format: "§7[§c{level_name}§7]"
    rewards:
      - "/give {player} diamond_pickaxe 1"
      - "/give {player} diamond 10"
      - "/give {player} emerald 10"
      - "/xp add {player} 2000"
      - "/tellraw {player} [\"§c恭喜达到传奇摸金者等级！\"]"

  7:
    required_searches: 350
    name: "史诗摸金者"
    color: "§5"  # 深紫色
    display_format: "§6[§5{level_name}§6]"
    rewards:
      - "/give {player} diamond_chestplate 1"
      - "/give {player} diamond_block 3"
      - "/give {player} emerald_block 2"
      - "/xp add {player} 3500"
      - "/tellraw {player} [\"§5恭喜达到史诗摸金者等级！\"]"

  8:
    required_searches: 500
    name: "神话摸金者"
    color: "§4"  # 深红色
    display_format: "§6[§4{level_name}§6]"
    rewards:
      - "/give {player} netherite_ingot 2"
      - "/give {player} diamond_block 5"
      - "/give {player} emerald_block 5"
      - "/xp add {player} 5000"
      - "/tellraw {player} [\"§4恭喜达到神话摸金者等级！获得下界合金！\"]"

  9:
    required_searches: 750
    name: "至尊摸金者"
    color: "§e§l"  # 黄色加粗
    display_format: "§6§l[§e§l{level_name}§6§l]"
    rewards:
      - "/give {player} netherite_sword 1"
      - "/give {player} diamond_block 10"
      - "/give {player} emerald_block 10"
      - "/xp add {player} 7500"
      - "/tellraw {player} [\"§e§l恭喜达到至尊摸金者等级！获得下界合金剑！\"]"

  10:
    required_searches: 1000
    name: "摸金王者"
    color: "§c§l"  # 红色加粗
    display_format: "§6§l[§c§l{level_name}§6§l]"
    rewards:
      - "/give {player} diamond_block 10"
      - "/give {player} emerald_block 10"
      - "/give {player} gold_block 10"
      - "/give {player} netherite_block 1"
      - "/xp add {player} 10000"
      - "/tellraw {player} [\"§6§l恭喜达到摸金王者等级！您已是摸金界的传奇！\"]"

# 可以继续添加更多等级...
# 11:
#   required_searches: 1500
#   name: "摸金帝王"
#   color: "§4§l"
#   display_format: "§4§l[§6§l{level_name}§4§l]"
#   rewards:
#     - "/give {player} nether_star 1"
#     - "/tellraw {player} [\"§4§l恭喜达到摸金帝王等级！\"]"
