# 🏗️ HangEvacuation 插件模块化设计方案

## 🎯 **设计目标**

将当前的HangEvacuation插件拆分为3个独立的插件：
1. **HangEvacuation-Core** (核心插件) - 摸金箱系统
2. **HangEvacuation-Evacuation** (撤离插件) - 撤离点系统  
3. **HangEvacuation-Levels** (等级插件) - 等级系统

## 📋 **模块划分**

### **HangEvacuation-Core (核心插件)**
```
功能范围：
├── 摸金箱系统 (完整保留)
├── 浮空字管理
├── 音效系统
├── 公共API接口
├── 事件系统
├── 配置管理
├── 权限系统
└── 数据存储

依赖关系：
└── 无依赖 (独立运行)

文件大小：约80% (主要功能)
```

### **HangEvacuation-Evacuation (撤离插件)**
```
功能范围：
├── 撤离点管理
├── 倒计时系统
├── 传送功能
├── 撤离区域检测
└── 撤离相关命令

依赖关系：
└── depend: [HangEvacuation-Core]

文件大小：约15%
```

### **HangEvacuation-Levels (等级插件)**
```
功能范围：
├── 等级系统
├── 经验管理
├── 等级奖励
├── 等级相关命令
└── PlaceholderAPI集成

依赖关系：
└── depend: [HangEvacuation-Core]

文件大小：约5%
```

## 🔌 **API接口设计**

### **核心API接口**
```java
// HangEvacuation-Core/src/main/java/com/hang/plugin/api/HangEvacuationAPI.java
public interface HangEvacuationAPI {
    
    /**
     * 获取插件实例
     */
    HangPlugin getPlugin();
    
    /**
     * 玩家数据管理
     */
    PlayerDataManager getPlayerDataManager();
    
    /**
     * 摸金箱管理
     */
    TreasureChestManager getTreasureChestManager();
    
    /**
     * 配置管理
     */
    ConfigManager getConfigManager();
    
    /**
     * 权限检查
     */
    boolean hasPermission(Player player, String permission);
    
    /**
     * 发送消息
     */
    void sendMessage(Player player, String messageKey, String... replacements);
    
    /**
     * 注册扩展插件
     */
    void registerExtension(String name, HangExtension extension);
    
    /**
     * 获取扩展插件
     */
    HangExtension getExtension(String name);
}
```

### **扩展插件接口**
```java
// HangEvacuation-Core/src/main/java/com/hang/plugin/api/HangExtension.java
public interface HangExtension {
    
    /**
     * 扩展插件名称
     */
    String getName();
    
    /**
     * 扩展插件版本
     */
    String getVersion();
    
    /**
     * 初始化扩展
     */
    void initialize(HangEvacuationAPI api);
    
    /**
     * 关闭扩展
     */
    void shutdown();
    
    /**
     * 是否启用
     */
    boolean isEnabled();
}
```

## 📡 **事件系统设计**

### **核心事件**
```java
// 摸金箱相关事件
public class TreasureChestOpenEvent extends Event { }
public class TreasureFoundEvent extends Event { 
    private Player player;
    private ItemStack treasure;
    private int experience; // 供等级插件使用
}
public class TreasureSearchCompleteEvent extends Event { }

// 玩家相关事件
public class PlayerDataLoadEvent extends Event { }
public class PlayerDataSaveEvent extends Event { }
```

### **撤离插件事件**
```java
// HangEvacuation-Evacuation 监听和发布的事件
public class EvacuationStartEvent extends Event { }
public class EvacuationCompleteEvent extends Event { }
public class EvacuationCancelEvent extends Event { }
```

### **等级插件事件**
```java
// HangEvacuation-Levels 监听和发布的事件
public class PlayerLevelUpEvent extends Event { }
public class ExperienceGainEvent extends Event { }
```

## 🗂️ **文件结构**

### **HangEvacuation-Core**
```
HangEvacuation-Core/
├── src/main/java/com/hang/plugin/
│   ├── HangPlugin.java (主类)
│   ├── api/ (API接口)
│   │   ├── HangEvacuationAPI.java
│   │   └── HangExtension.java
│   ├── manager/ (管理器)
│   │   ├── TreasureChestManager.java
│   │   ├── HologramManager.java
│   │   └── ConfigManager.java
│   ├── gui/ (GUI界面)
│   ├── listeners/ (事件监听)
│   └── utils/ (工具类)
├── src/main/resources/
│   ├── plugin.yml
│   ├── config.yml
│   └── treasure_types/
└── pom.xml
```

### **HangEvacuation-Evacuation**
```
HangEvacuation-Evacuation/
├── src/main/java/com/hang/evacuation/
│   ├── EvacuationPlugin.java (主类)
│   ├── manager/
│   │   ├── EvacuationManager.java
│   │   └── CountdownManager.java
│   ├── commands/
│   │   └── EvacuationCommand.java
│   └── listeners/
│       └── EvacuationListener.java
├── src/main/resources/
│   ├── plugin.yml (depend: [HangEvacuation-Core])
│   └── evacuation-config.yml
└── pom.xml
```

### **HangEvacuation-Levels**
```
HangEvacuation-Levels/
├── src/main/java/com/hang/levels/
│   ├── LevelsPlugin.java (主类)
│   ├── manager/
│   │   └── LevelManager.java
│   ├── commands/
│   │   └── LevelCommand.java
│   └── listeners/
│       └── LevelListener.java
├── src/main/resources/
│   ├── plugin.yml (depend: [HangEvacuation-Core])
│   └── levels-config.yml
└── pom.xml
```

## 🔄 **数据共享机制**

### **配置文件共享**
```java
// 核心插件提供配置访问
public class ConfigManager {
    public FileConfiguration getConfig(String pluginName) {
        // 返回指定插件的配置文件
    }
    
    public void saveConfig(String pluginName, FileConfiguration config) {
        // 保存指定插件的配置文件
    }
}
```

### **数据存储共享**
```java
// 核心插件提供数据存储
public class PlayerDataManager {
    public PlayerData getPlayerData(UUID playerId) {
        // 获取玩家数据，包含所有模块的数据
    }
    
    public void setPlayerData(UUID playerId, String module, Object data) {
        // 设置特定模块的玩家数据
    }
}
```

## 🚀 **迁移步骤**

### **第一阶段：准备工作**
1. **备份当前代码**
2. **设计API接口**
3. **创建新的项目结构**

### **第二阶段：核心插件重构**
1. **提取公共API**
2. **重构事件系统**
3. **保留摸金箱功能**
4. **测试核心功能**

### **第三阶段：撤离插件拆分**
1. **创建独立项目**
2. **迁移撤离相关代码**
3. **实现API调用**
4. **测试撤离功能**

### **第四阶段：等级插件拆分**
1. **创建独立项目**
2. **迁移等级相关代码**
3. **实现API调用**
4. **测试等级功能**

### **第五阶段：集成测试**
1. **三个插件联合测试**
2. **性能测试**
3. **兼容性测试**
4. **文档编写**

## 📦 **部署方案**

### **完整安装**
```
plugins/
├── HangEvacuation-Core-1.0.0.jar (必需)
├── HangEvacuation-Evacuation-1.0.0.jar (可选)
└── HangEvacuation-Levels-1.0.0.jar (可选)
```

### **最小安装**
```
plugins/
└── HangEvacuation-Core-1.0.0.jar (仅摸金箱功能)
```

### **自定义安装**
```
plugins/
├── HangEvacuation-Core-1.0.0.jar (必需)
└── HangEvacuation-Evacuation-1.0.0.jar (仅需撤离功能)
```

## 🎯 **优势总结**

### **开发优势**
- ✅ **代码清晰**：每个插件职责单一
- ✅ **独立开发**：可以并行开发不同模块
- ✅ **版本管理**：每个模块可以独立版本控制

### **用户优势**
- ✅ **按需安装**：只安装需要的功能
- ✅ **性能优化**：减少不必要的资源占用
- ✅ **灵活配置**：可以单独配置每个模块

### **维护优势**
- ✅ **故障隔离**：一个模块出问题不影响其他模块
- ✅ **独立更新**：可以单独更新某个功能模块
- ✅ **测试简化**：可以独立测试每个模块

## 🤔 **注意事项**

### **复杂度增加**
- ⚠️ **依赖管理**：需要确保插件加载顺序
- ⚠️ **API维护**：需要保持API的向后兼容性
- ⚠️ **调试复杂**：跨插件调试可能更复杂

### **用户体验**
- ⚠️ **安装复杂**：用户需要安装多个插件
- ⚠️ **配置分散**：配置文件分散在多个插件中
- ⚠️ **错误处理**：需要处理依赖插件缺失的情况

## 💡 **建议**

### **推荐方案**
我建议采用这种模块化设计，因为：

1. **当前插件已经比较复杂**，拆分有助于维护
2. **功能相对独立**，撤离和等级系统可以独立运行
3. **用户需求多样**，有些用户可能只需要摸金箱功能
4. **便于后续扩展**，可以更容易添加新的功能模块

### **实施建议**
1. **先保留当前版本**作为备份
2. **逐步迁移**，确保每个阶段都能正常工作
3. **充分测试**，特别是插件间的交互
4. **编写详细文档**，帮助用户理解新的架构

这种模块化设计是现代插件开发的最佳实践，值得投入时间来实现！🎉
