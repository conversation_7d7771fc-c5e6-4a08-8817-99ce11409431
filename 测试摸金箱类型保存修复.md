# 🧪 测试摸金箱类型保存修复

## 📋 **测试计划**

### **测试目标**
验证修复后的摸金箱类型保存功能是否正常工作，确保重启服务器后不同类型的摸金箱能够保持其特定配置。

## 🔧 **测试步骤**

### **第一阶段：放置不同类型摸金箱**

1. **获取各种类型摸金箱**：
   ```
   /evac give common     # 普通摸金箱
   /evac give weapon     # 武器箱
   /evac give ammo       # 弹药箱
   /evac give medical    # 医疗箱
   /evac give supply     # 补给箱
   /evac give equipment  # 装备箱
   ```

2. **放置摸金箱**：
   - 在不同位置放置各种类型的摸金箱
   - 记录每个摸金箱的位置和类型
   - 确认放置时显示正确的类型名称

3. **验证初始配置**：
   - 打开每个摸金箱，检查槽位数量是否正确
   - 检查生成的物品是否符合类型配置
   - 记录每种类型的配置参数

### **第二阶段：验证数据保存**

4. **检查配置文件**：
   - 打开 `plugins/HangEvacuation/chests.yml`
   - 确认每个摸金箱都有 `chestType` 字段
   - 验证保存的类型是否正确

5. **预期的配置文件结构**：
   ```yaml
   chests:
     world_100_64_200:
       world: "world"
       x: 100
       y: 64
       z: 200
       chestType: "weapon"        # ✅ 应该有这个字段
       lastRefreshTime: 1703123456789
       nextRefreshTime: 1703123756789
       originalItemCount: 8
       # ... 其他数据
   ```

### **第三阶段：重启测试**

6. **重启服务器**：
   ```
   /stop
   # 重新启动服务器
   ```

7. **验证修复效果**：
   - 检查每个摸金箱是否保持原有类型
   - 验证槽位数量是否正确
   - 验证刷新时间是否正确
   - 验证物品生成是否符合类型

## ✅ **预期结果**

### **修复前的问题**
- ❌ 重启后所有摸金箱都变成普通摸金箱
- ❌ 武器箱变成5个槽位（应该是8个）
- ❌ 医疗箱刷新时间变成5分钟（应该是3分钟）
- ❌ 物品生成不再按类型分配

### **修复后的预期**
- ✅ 武器箱重启后仍然是武器箱（8个槽位，15分钟刷新）
- ✅ 医疗箱重启后仍然是医疗箱（4个槽位，3分钟刷新）
- ✅ 弹药箱重启后仍然是弹药箱（6个槽位，10分钟刷新）
- ✅ 补给箱重启后仍然是补给箱（7个槽位，8分钟刷新）
- ✅ 装备箱重启后仍然是装备箱（9个槽位，20分钟刷新）
- ✅ 普通摸金箱保持不变（5个槽位，5分钟刷新）

## 📊 **测试数据记录表**

| 摸金箱类型 | 位置坐标 | 预期槽位 | 预期刷新时间 | 重启前状态 | 重启后状态 | 测试结果 |
|-----------|---------|---------|-------------|-----------|-----------|----------|
| common    | (100,64,200) | 5 | 5分钟 | ✅ | ? | ? |
| weapon    | (110,64,200) | 8 | 15分钟 | ✅ | ? | ? |
| ammo      | (120,64,200) | 6 | 10分钟 | ✅ | ? | ? |
| medical   | (130,64,200) | 4 | 3分钟 | ✅ | ? | ? |
| supply    | (140,64,200) | 7 | 8分钟 | ✅ | ? | ? |
| equipment | (150,64,200) | 9 | 20分钟 | ✅ | ? | ? |

## 🔍 **调试信息**

### **启用调试模式**
在 `config.yml` 中设置：
```yaml
debug:
  enabled: true
```

### **查看调试日志**
重启服务器时查看控制台输出，应该看到类似信息：
```
[INFO] 已加载模组物品数据: xxx (槽位: 0, 类型: weapon)
[INFO] 摸金箱数据加载完成
[INFO] 修复originalItemCount: 8 (位置: world_110_64_200)
```

## 🚨 **故障排除**

### **如果测试失败**
1. **检查配置文件**：
   - 确认 `chests.yml` 中有 `chestType` 字段
   - 检查字段值是否正确

2. **检查代码修复**：
   - 确认 `ChestManager.java` 中的保存逻辑已修复
   - 确认加载逻辑已修复

3. **清理测试**：
   - 删除 `chests.yml` 文件
   - 重新放置摸金箱进行测试

### **回滚方案**
如果修复导致问题：
1. 备份当前的 `chests.yml`
2. 恢复之前的版本
3. 重启服务器

## 📝 **测试报告模板**

```
测试日期：____
测试人员：____
服务器版本：____
插件版本：v1.7.2

测试结果：
□ 通过 - 所有摸金箱类型正确保存和加载
□ 部分通过 - 部分类型有问题：____
□ 失败 - 修复无效，问题依然存在

详细说明：
____

建议：
____
```

## 🎯 **成功标准**

测试被认为成功当且仅当：
1. 所有6种类型的摸金箱都能正确保存到配置文件
2. 重启服务器后所有摸金箱保持原有类型
3. 每种类型的槽位数量和刷新时间都正确
4. 物品生成符合各自类型的配置
5. 没有出现新的错误或异常
