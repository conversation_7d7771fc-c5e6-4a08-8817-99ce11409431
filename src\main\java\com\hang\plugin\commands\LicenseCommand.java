package com.hang.plugin.commands;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.license.LicenseManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 许可证管理命令
 */
public class LicenseCommand implements CommandExecutor, TabCompleter {
    
    private final HangPlugin plugin;
    
    public LicenseCommand(HangPlugin plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("hang.admin")) {
            sender.sendMessage("§c您没有权限使用此命令！");
            return true;
        }
        
        if (args.length == 0) {
            showHelp(sender);
            return true;
        }
        
        switch (args[0].toLowerCase()) {
            case "status":
                showLicenseStatus(sender);
                break;
                
            case "set":
                if (args.length < 2) {
                    sender.sendMessage("§c用法: /license set <许可证密钥>");
                    return true;
                }
                setLicenseKey(sender, args[1]);
                break;
                
            case "verify":
                verifyLicense(sender);
                break;
                
            case "info":
                showLicenseInfo(sender);
                break;
                
            case "reload":
                reloadLicense(sender);
                break;
                
            default:
                showHelp(sender);
                break;
        }
        
        return true;
    }
    
    private void showHelp(CommandSender sender) {
        sender.sendMessage("§6╔══════════════════════════════════════════════════════════════╗");
        sender.sendMessage("§6║                    🔐 许可证管理命令 🔐                      ║");
        sender.sendMessage("§6╠══════════════════════════════════════════════════════════════╣");
        sender.sendMessage("§6║  §f/license status  §7- 查看许可证状态                       §6║");
        sender.sendMessage("§6║  §f/license set <key> §7- 设置许可证密钥                     §6║");
        sender.sendMessage("§6║  §f/license verify  §7- 重新验证许可证                       §6║");
        sender.sendMessage("§6║  §f/license info    §7- 查看许可证详细信息                   §6║");
        sender.sendMessage("§6║  §f/license reload  §7- 重新加载许可证配置                   §6║");
        sender.sendMessage("§6╚══════════════════════════════════════════════════════════════╝");
    }
    
    private void showLicenseStatus(CommandSender sender) {
        LicenseManager licenseManager = plugin.getLicenseManager();
        
        if (licenseManager == null) {
            sender.sendMessage("§c许可证管理器未初始化！");
            return;
        }
        
        boolean isValid = licenseManager.isValid();
        String status = isValid ? "§a✅ 有效" : "§c❌ 无效";
        
        sender.sendMessage("§6╔══════════════════════════════════════════════════════════════╗");
        sender.sendMessage("§6║                    📊 许可证状态 📊                          ║");
        sender.sendMessage("§6╠══════════════════════════════════════════════════════════════╣");
        sender.sendMessage("§6║  状态: " + status + "                                        §6║");
        sender.sendMessage("§6║  验证服务器: §fcn.HangZong.com                               §6║");
        sender.sendMessage("§6║  技术支持: §fQQ **********                                    §6║");
        sender.sendMessage("§6╚══════════════════════════════════════════════════════════════╝");
    }
    
    private void setLicenseKey(CommandSender sender, String key) {
        if (key.trim().isEmpty()) {
            sender.sendMessage("§c许可证密钥不能为空！");
            return;
        }
        
        // 保存许可证密钥到配置文件
        plugin.getLicenseConfig().setLicenseKey(key);
        
        sender.sendMessage("§a许可证密钥已保存！正在验证...");
        
        // 重新验证许可证
        verifyLicense(sender);
    }
    
    private void verifyLicense(CommandSender sender) {
        LicenseManager licenseManager = plugin.getLicenseManager();
        
        if (licenseManager == null) {
            sender.sendMessage("§c许可证管理器未初始化！");
            return;
        }
        
        String licenseKey = plugin.getLicenseConfig().getLicenseKey();
        if (licenseKey.trim().isEmpty()) {
            sender.sendMessage("§c许可证密钥为空，请先设置许可证密钥！");
            return;
        }
        
        sender.sendMessage("§e正在验证许可证，请稍候...");
        
        // 异步验证许可证
        plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
            boolean isValid = licenseManager.validateLicenseKey(licenseKey);
            
            // 回到主线程发送结果
            plugin.getServer().getScheduler().runTask(plugin, () -> {
                if (isValid) {
                    sender.sendMessage("§a✅ 许可证验证成功！");
                    sender.sendMessage("§a插件功能已解锁，可以正常使用。");
                } else {
                    sender.sendMessage("§c❌ 许可证验证失败！");
                    sender.sendMessage("§c请检查许可证密钥是否正确，或联系技术支持 QQ: **********");
                }
            });
        });
    }
    
    private void showLicenseInfo(CommandSender sender) {
        LicenseManager licenseManager = plugin.getLicenseManager();
        
        if (licenseManager == null) {
            sender.sendMessage("§c许可证管理器未初始化！");
            return;
        }
        
        LicenseManager.LicenseInfo info = licenseManager.getLicenseInfo();
        
        sender.sendMessage("§6╔══════════════════════════════════════════════════════════════╗");
        sender.sendMessage("§6║                    📋 许可证信息 📋                          ║");
        sender.sendMessage("§6╠══════════════════════════════════════════════════════════════╣");
        
        if (info != null) {
            sender.sendMessage("§6║  服务器名称: §f" + info.getServerName() + "                  §6║");
            sender.sendMessage("§6║  许可证版本: §f" + info.getVersion() + "                     §6║");
            sender.sendMessage("§6║  签发日期: §f" + info.getIssuedDate() + "                    §6║");
            
            if (info.getExpiryDate() != null) {
                sender.sendMessage("§6║  到期日期: §f" + info.getExpiryDate() + "                §6║");
            } else {
                sender.sendMessage("§6║  到期日期: §a永久有效                                       §6║");
            }
            
            sender.sendMessage("§6║  最大玩家数: §f" + (info.getMaxPlayers() == -1 ? "无限制" : info.getMaxPlayers()) + "§6║");
            sender.sendMessage("§6║  功能权限: §f" + String.join(", ", info.getFeatures()) + "  §6║");
        } else {
            sender.sendMessage("§6║  §c暂无许可证信息                                           §6║");
        }
        
        sender.sendMessage("§6╚══════════════════════════════════════════════════════════════╝");
    }
    
    private void reloadLicense(CommandSender sender) {
        try {
            plugin.getLicenseConfig().reload();
            sender.sendMessage("§a许可证配置已重新加载！");
            
            // 重新验证许可证
            verifyLicense(sender);
        } catch (Exception e) {
            sender.sendMessage("§c重新加载许可证配置失败: " + e.getMessage());
        }
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("hang.admin")) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            return Arrays.asList("status", "set", "verify", "info", "reload");
        }
        
        return new ArrayList<>();
    }
}
