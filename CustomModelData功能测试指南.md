# 🧪 CustomModelData 功能测试指南

## 📋 测试前准备

### 🔧 环境要求
- **服务器版本**: Minecraft 1.14+ (推荐 1.20.1+)
- **插件版本**: HangEvacuation v1.7.5+
- **测试工具**: 
  - 管理员权限账号
  - 可选: ItemsAdder 插件
  - 可选: 自定义材质包

### 📁 文件检查
确认以下文件已正确更新：
- `config.yml` - 包含 `custom_model_data` 配置项
- `VersionUtils.java` - 包含 CustomModelData 相关方法
- `TreasureChestGUI.java` - 更新了进度显示逻辑

---

## 🧪 基础功能测试

### 1️⃣ **版本检测测试**

#### 测试步骤:
1. 启动服务器
2. 执行命令: `/evac nms`
3. 查看输出信息

#### 预期结果:
```
✅ Minecraft 1.14+: 应显示 "CustomModelData: ✅ 支持"
❌ Minecraft 1.8-1.13: 应显示 "CustomModelData: ❌ 不支持"
```

### 2️⃣ **配置文件测试**

#### 测试步骤:
1. 打开 `config.yml`
2. 找到 `items.searching-item.custom_model_data` 配置项
3. 设置不同的值进行测试

#### 测试配置:
```yaml
# 测试 1: 禁用 CustomModelData
custom_model_data: 0

# 测试 2: 启用 CustomModelData
custom_model_data: 100001

# 测试 3: 大数值测试
custom_model_data: 999999
```

### 3️⃣ **游戏内功能测试**

#### 测试步骤:
1. 给自己一个摸金箱: `/evac give common`
2. 放置摸金箱
3. 右键打开摸金箱
4. 观察搜索动画

#### 预期结果:
- **颜色变化**: 红→橙→黄→绿 (正常)
- **CustomModelData**: 在支持的版本中应用自定义模型
- **兼容性**: 低版本应正常显示，不报错

---

## 🎨 高级功能测试

### 🔧 **ItemsAdder 集成测试**

#### 前置条件:
- 安装 ItemsAdder 插件
- 配置自定义物品

#### 测试配置:
```yaml
# ItemsAdder 配置示例
items:
  searching_glass_animation:
    display_name: "§c搜索动画测试"
    material: RED_STAINED_GLASS_PANE
    custom_model_data: 100001
```

#### 测试步骤:
1. 在 HangEvacuation 配置中设置: `custom_model_data: 100001`
2. 重载插件: `/evac reload`
3. 测试摸金箱搜索功能
4. 观察是否显示 ItemsAdder 定义的自定义模型

### 🎨 **材质包测试**

#### 材质包配置:
创建文件: `assets/minecraft/models/item/red_stained_glass_pane.json`
```json
{
  "parent": "item/generated",
  "textures": {
    "layer0": "item/red_stained_glass_pane"
  },
  "overrides": [
    {
      "predicate": {
        "custom_model_data": 100001
      },
      "model": "custom/searching_animation"
    }
  ]
}
```

#### 测试步骤:
1. 安装测试材质包
2. 设置 `custom_model_data: 100001`
3. 重载插件并测试
4. 确认客户端显示自定义模型

---

## 🔍 兼容性测试

### 📊 **多版本测试矩阵**

| 版本 | CustomModelData 支持 | 预期行为 | 测试状态 |
|------|---------------------|----------|----------|
| 1.8.8 | ❌ | 忽略设置，正常显示 | ⏳ 待测试 |
| 1.12.2 | ❌ | 忽略设置，正常显示 | ⏳ 待测试 |
| 1.14.4 | ✅ | 应用 CustomModelData | ⏳ 待测试 |
| 1.16.5 | ✅ | 应用 CustomModelData | ⏳ 待测试 |
| 1.18.2 | ✅ | 应用 CustomModelData | ⏳ 待测试 |
| 1.20.1 | ✅ | 应用 CustomModelData | ⏳ 待测试 |
| 1.21.1 | ✅ | 应用 CustomModelData | ⏳ 待测试 |

### 🧪 **兼容性测试步骤**

#### 对于每个版本:
1. 启动对应版本的服务器
2. 安装插件
3. 设置 `custom_model_data: 100001`
4. 测试摸金箱功能
5. 记录测试结果

#### 检查项目:
- [ ] 插件正常启动
- [ ] 配置文件正确加载
- [ ] 摸金箱功能正常
- [ ] 搜索动画正常显示
- [ ] 无错误日志输出

---

## 🚨 错误排查

### ❌ **常见问题及解决方案**

#### 问题 1: CustomModelData 不生效
**症状**: 设置了 custom_model_data 但没有效果
**排查步骤**:
1. 检查服务器版本是否为 1.14+
2. 确认配置文件语法正确
3. 检查 custom_model_data 值是否大于 0
4. 重载插件配置

#### 问题 2: 插件报错
**症状**: 控制台出现 CustomModelData 相关错误
**排查步骤**:
1. 检查 Java 版本兼容性
2. 确认服务端类型 (Spigot/Paper)
3. 查看完整错误堆栈
4. 尝试设置 custom_model_data: 0 禁用功能

#### 问题 3: 动画不显示
**症状**: CustomModelData 设置正确但客户端看不到效果
**排查步骤**:
1. 确认客户端加载了正确的材质包
2. 检查 ItemsAdder 配置是否正确
3. 验证模型文件路径
4. 测试其他 CustomModelData 值

### 📝 **调试日志**

#### 启用调试模式:
```yaml
# config.yml
debug:
  enabled: true
```

#### 查看关键日志:
- 插件启动信息
- 版本检测结果
- CustomModelData 设置过程
- 错误异常信息

---

## 📊 性能测试

### ⚡ **性能基准测试**

#### 测试场景:
1. **单人测试**: 1个玩家搜索摸金箱
2. **多人测试**: 10个玩家同时搜索
3. **压力测试**: 50个玩家同时搜索

#### 监控指标:
- CPU 使用率
- 内存使用量
- TPS (每秒刻数)
- 响应延迟

#### 测试步骤:
1. 记录基准性能数据
2. 启用 CustomModelData 功能
3. 执行测试场景
4. 对比性能差异

### 📈 **性能优化建议**

如果发现性能问题:
1. 减少同时显示的动画数量
2. 简化自定义模型复杂度
3. 优化材质包大小
4. 考虑禁用 CustomModelData 功能

---

## ✅ 测试完成检查清单

### 🎯 **基础功能**
- [ ] 版本检测正常
- [ ] 配置文件加载正确
- [ ] 摸金箱搜索功能正常
- [ ] 颜色变化正确 (红→橙→黄→绿)
- [ ] CustomModelData 正确应用

### 🔧 **兼容性**
- [ ] 高版本 (1.14+) 正常工作
- [ ] 低版本 (1.8-1.13) 兼容运行
- [ ] 不同服务端类型兼容
- [ ] 无错误日志输出

### 🎨 **集成功能**
- [ ] ItemsAdder 集成正常
- [ ] 材质包兼容性良好
- [ ] 自定义动画显示正确

### ⚡ **性能**
- [ ] 无明显性能影响
- [ ] 多人同时使用正常
- [ ] 内存使用合理

---

## 📞 问题反馈

如果在测试过程中发现问题，请提供以下信息:

### 📋 **反馈模板**
```
🐛 问题描述:
[详细描述遇到的问题]

🔧 环境信息:
- 服务器版本: 
- 插件版本: 
- Java 版本: 
- 服务端类型: 

⚙️ 配置信息:
- custom_model_data 设置值: 
- 是否使用 ItemsAdder: 
- 是否使用自定义材质包: 

📝 错误日志:
[粘贴相关错误日志]

🔄 复现步骤:
1. 
2. 
3. 

💡 预期结果:
[描述期望的正确行为]
```

### 📞 **联系方式**
- **QQ**: hang060217
- **微信**: hang060217  
- **QQ群**: 361919269

---

**🎯 测试目标**: 确保 CustomModelData 功能在所有支持的版本中正常工作，同时保持与低版本的完全兼容性。
