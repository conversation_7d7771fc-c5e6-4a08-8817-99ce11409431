package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;

/**
 * 等级系统管理器
 * 
 * <AUTHOR>
 * @version 1.4.0
 */
public class LevelManager {
    
    private final HangPlugin plugin;
    private FileConfiguration levelsConfig;
    private File levelsFile;
    private FileConfiguration playerDataConfig;
    private File playerDataFile;
    
    // 等级配置缓存
    private final Map<Integer, LevelInfo> levelInfoMap = new HashMap<>();
    // 玩家数据缓存
    private final Map<UUID, PlayerLevelData> playerDataMap = new HashMap<>();
    
    public LevelManager(HangPlugin plugin) {
        this.plugin = plugin;
        loadConfigs();
        loadLevelConfigs();
        loadPlayerData();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfigs() {
        // 创建levels.yml配置文件
        levelsFile = new File(plugin.getDataFolder(), "levels.yml");
        if (!levelsFile.exists()) {
            plugin.saveResource("levels.yml", false);
        }

        try {
            levelsConfig = YamlConfiguration.loadConfiguration(levelsFile);
        } catch (Exception e) {
            plugin.getLogger().severe("等级配置文件格式错误: " + levelsFile.getName());
            plugin.getLogger().severe("错误详情: " + e.getMessage());
            plugin.getLogger().info("正在重新创建等级配置文件...");

            // 备份错误的配置文件
            try {
                java.io.File backupFile = new java.io.File(levelsFile.getParent(), levelsFile.getName() + ".backup");
                if (levelsFile.renameTo(backupFile)) {
                    plugin.getLogger().info("已备份错误配置文件为: " + backupFile.getName());
                }
            } catch (Exception backupError) {
                plugin.getLogger().warning("备份配置文件失败: " + backupError.getMessage());
                levelsFile.delete();
            }

            // 重新创建配置文件
            plugin.saveResource("levels.yml", false);
            levelsConfig = YamlConfiguration.loadConfiguration(levelsFile);
        }
        
        // 创建player_levels.yml数据文件
        playerDataFile = new File(plugin.getDataFolder(), "player_levels.yml");
        if (!playerDataFile.exists()) {
            try {
                playerDataFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("无法创建玩家等级数据文件: " + e.getMessage());
            }
        }

        try {
            playerDataConfig = YamlConfiguration.loadConfiguration(playerDataFile);
        } catch (Exception e) {
            plugin.getLogger().severe("玩家等级数据文件格式错误: " + playerDataFile.getName());
            plugin.getLogger().severe("错误详情: " + e.getMessage());
            plugin.getLogger().info("正在重新创建玩家等级数据文件...");

            // 备份错误的数据文件
            try {
                java.io.File backupFile = new java.io.File(playerDataFile.getParent(), playerDataFile.getName() + ".backup");
                if (playerDataFile.renameTo(backupFile)) {
                    plugin.getLogger().info("已备份错误数据文件为: " + backupFile.getName());
                }
            } catch (Exception backupError) {
                plugin.getLogger().warning("备份数据文件失败: " + backupError.getMessage());
                playerDataFile.delete();
            }

            // 重新创建数据文件
            try {
                playerDataFile.createNewFile();
                playerDataConfig = YamlConfiguration.loadConfiguration(playerDataFile);
            } catch (Exception createError) {
                plugin.getLogger().severe("重新创建玩家等级数据文件失败: " + createError.getMessage());
                playerDataConfig = new YamlConfiguration(); // 使用空配置
            }
        }
    }
    
    /**
     * 加载等级配置
     */
    private void loadLevelConfigs() {
        levelInfoMap.clear();
        
        ConfigurationSection levelsSection = levelsConfig.getConfigurationSection("levels");
        if (levelsSection == null) {
            plugin.getLogger().warning("levels.yml中未找到levels配置节！");
            return;
        }
        
        for (String levelKey : levelsSection.getKeys(false)) {
            try {
                int level = Integer.parseInt(levelKey);
                ConfigurationSection levelSection = levelsSection.getConfigurationSection(levelKey);
                
                if (levelSection != null) {
                    LevelInfo levelInfo = new LevelInfo(
                        level,
                        levelSection.getInt("required_searches", 0),
                        levelSection.getString("name", "等级" + level),
                        levelSection.getString("color", "§f"),
                        levelSection.getString("display_format", "§7[§e{level_name}§7]"),
                        levelSection.getStringList("rewards")
                    );

                    levelInfoMap.put(level, levelInfo);
                }
            } catch (NumberFormatException e) {
                plugin.getLogger().warning("无效的等级配置: " + levelKey);
            }
        }
    }
    
    /**
     * 加载玩家数据
     */
    private void loadPlayerData() {
        playerDataMap.clear();
        
        ConfigurationSection playersSection = playerDataConfig.getConfigurationSection("players");
        if (playersSection == null) {
            return;
        }
        
        for (String uuidString : playersSection.getKeys(false)) {
            try {
                UUID uuid = UUID.fromString(uuidString);
                ConfigurationSection playerSection = playersSection.getConfigurationSection(uuidString);
                
                if (playerSection != null) {
                    PlayerLevelData data = new PlayerLevelData(
                        uuid,
                        playerSection.getInt("searches", 0),
                        playerSection.getInt("level", 1)
                    );
                    
                    playerDataMap.put(uuid, data);
                }
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的玩家UUID: " + uuidString);
            }
        }
    }
    
    /**
     * 保存玩家数据
     */
    public void savePlayerData() {
        for (Map.Entry<UUID, PlayerLevelData> entry : playerDataMap.entrySet()) {
            UUID uuid = entry.getKey();
            PlayerLevelData data = entry.getValue();
            
            String path = "players." + uuid.toString();
            playerDataConfig.set(path + ".searches", data.getSearches());
            playerDataConfig.set(path + ".level", data.getLevel());
        }
        
        try {
            playerDataConfig.save(playerDataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("无法保存玩家等级数据: " + e.getMessage());
        }
    }
    
    /**
     * 获取玩家数据
     */
    public PlayerLevelData getPlayerData(UUID uuid) {
        return playerDataMap.computeIfAbsent(uuid, k -> new PlayerLevelData(uuid, 0, 1));
    }
    
    /**
     * 增加玩家搜索次数
     */
    public void addPlayerSearch(Player player) {
        // 检查等级系统是否启用
        if (!isLevelSystemEnabled()) {
            return;
        }

        UUID uuid = player.getUniqueId();
        PlayerLevelData data = getPlayerData(uuid);

        data.addSearch();

        // 检查是否可以升级
        int newLevel = calculateLevel(data.getSearches());
        if (newLevel > data.getLevel()) {
            levelUp(player, data, newLevel);
        }

        // 异步保存数据
        Bukkit.getScheduler().runTaskAsynchronously(plugin, this::savePlayerData);
    }
    
    /**
     * 计算等级
     */
    private int calculateLevel(int searches) {
        int level = 1;
        
        for (Map.Entry<Integer, LevelInfo> entry : levelInfoMap.entrySet()) {
            LevelInfo levelInfo = entry.getValue();
            if (searches >= levelInfo.getRequiredSearches() && levelInfo.getLevel() > level) {
                level = levelInfo.getLevel();
            }
        }
        
        return level;
    }
    
    /**
     * 玩家升级
     */
    private void levelUp(Player player, PlayerLevelData data, int newLevel) {
        int oldLevel = data.getLevel();
        data.setLevel(newLevel);
        
        LevelInfo levelInfo = levelInfoMap.get(newLevel);
        LevelInfo oldLevelInfo = levelInfoMap.get(oldLevel);
        if (levelInfo != null) {
            // 发送升级消息
            player.sendMessage("§6§l恭喜！您的摸金等级提升了！");
            String oldLevelName = oldLevelInfo != null ? getColoredLevelName(oldLevelInfo) : "§7等级" + oldLevel;
            String newLevelName = getColoredLevelName(levelInfo);
            player.sendMessage("§e" + oldLevelName + " → " + newLevelName);
            
            // 播放升级音效
            try {
                player.playSound(player.getLocation(), "entity.player.levelup", 1.0f, 1.0f);
            } catch (Exception ignored) {
                // 兼容不同版本的音效
            }
            
            // 给予奖励
            giveRewards(player, levelInfo);
            
            // 广播升级消息（如果配置启用）
            if (levelsConfig.getBoolean("settings.broadcast_levelup", true)) {
                String broadcastMessage = levelsConfig.getString("messages.levelup_broadcast",
                    "§6玩家 §e{player} §6达到了摸金等级 {level_name}§6！");
                // 获取带颜色的等级名称
                String coloredLevelName = getColoredLevelName(levelInfo);
                broadcastMessage = broadcastMessage
                    .replace("{player}", player.getName())
                    .replace("{level}", String.valueOf(newLevel))
                    .replace("{level_name}", coloredLevelName);

                Bukkit.broadcastMessage(broadcastMessage);
            }
        }
    }

    /**
     * 给予等级奖励
     */
    private void giveRewards(Player player, LevelInfo levelInfo) {
        List<String> rewards = levelInfo.getRewards();
        if (rewards == null || rewards.isEmpty()) {
            return;
        }

        for (String reward : rewards) {
            // 替换变量
            reward = reward.replace("{player}", player.getName());

            // 执行命令奖励
            if (reward.startsWith("/")) {
                String command = reward.substring(1);
                Bukkit.getScheduler().runTask(plugin, () -> {
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
                });
            }
        }
    }

    /**
     * 获取玩家等级显示格式
     */
    public String getPlayerLevelDisplay(Player player) {
        // 检查等级系统是否启用
        if (!isLevelSystemEnabled()) {
            return "";
        }

        PlayerLevelData data = getPlayerData(player.getUniqueId());
        LevelInfo levelInfo = levelInfoMap.get(data.getLevel());

        if (levelInfo != null) {
            return levelInfo.getDisplayFormat()
                .replace("{level}", String.valueOf(data.getLevel()))
                .replace("{level_name}", levelInfo.getName())
                .replace("{searches}", String.valueOf(data.getSearches()));
        }

        return "§7[§e等级" + data.getLevel() + "§7]";
    }

    /**
     * 重载配置
     */
    public void reload() {
        loadConfigs();
        loadLevelConfigs();
        loadPlayerData();
    }

    /**
     * 获取所有等级信息
     */
    public Map<Integer, LevelInfo> getAllLevels() {
        return new HashMap<>(levelInfoMap);
    }

    /**
     * 是否启用等级系统
     */
    public boolean isLevelSystemEnabled() {
        return levelsConfig.getBoolean("settings.enable_level_system", true);
    }

    /**
     * 是否在聊天中显示等级
     */
    public boolean isShowLevelInChat() {
        return levelsConfig.getBoolean("settings.show_level_in_chat", true);
    }

    /**
     * 获取带颜色的等级名称
     * 直接从配置文件中获取颜色代码
     */
    public String getColoredLevelName(LevelInfo levelInfo) {
        String color = levelInfo.getColor();
        String levelName = levelInfo.getName();
        return color + levelName + "§r";
    }

    /**
     * 获取最大等级
     */
    public int getMaxLevel() {
        int maxLevel = 1;
        for (LevelInfo levelInfo : levelInfoMap.values()) {
            if (levelInfo.getLevel() > maxLevel) {
                maxLevel = levelInfo.getLevel();
            }
        }
        return maxLevel;
    }

    /**
     * 等级信息类
     */
    public static class LevelInfo {
        private final int level;
        private final int requiredSearches;
        private final String name;
        private final String color;
        private final String displayFormat;
        private final List<String> rewards;

        public LevelInfo(int level, int requiredSearches, String name, String color, String displayFormat, List<String> rewards) {
            this.level = level;
            this.requiredSearches = requiredSearches;
            this.name = name;
            this.color = color != null ? color : "§f"; // 默认白色
            this.displayFormat = displayFormat;
            this.rewards = rewards != null ? rewards : new ArrayList<>();
        }

        // Getters
        public int getLevel() { return level; }
        public int getRequiredSearches() { return requiredSearches; }
        public String getName() { return name; }
        public String getColor() { return color; }
        public String getDisplayFormat() { return displayFormat; }
        public List<String> getRewards() { return rewards; }
    }

    /**
     * 玩家等级数据类
     */
    public static class PlayerLevelData {
        private final UUID uuid;
        private int searches;
        private int level;

        public PlayerLevelData(UUID uuid, int searches, int level) {
            this.uuid = uuid;
            this.searches = searches;
            this.level = level;
        }

        public void addSearch() {
            this.searches++;
        }

        // Getters and Setters
        public UUID getUuid() { return uuid; }
        public int getSearches() { return searches; }
        public int getLevel() { return level; }
        public void setLevel(int level) { this.level = level; }
    }
}
