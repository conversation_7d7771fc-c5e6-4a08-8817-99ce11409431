# 🔧 摸金箱数据保存问题修复报告

## 🚨 **问题描述**

用户反馈摸金箱数据在服务器重启后会丢失，导致：
- 摸金箱内的物品消失
- 搜索进度重置
- 摸金箱状态丢失
- 玩家的搜索记录清空

## 🔍 **问题分析**

经过代码分析，发现了以下关键问题：

### **1. 位置键格式不一致**
- **PlayerListener** 使用格式: `world:x:y:z`
- **ChestManager** 使用格式: `world_x_y_z`
- **结果**: 数据无法正确匹配和保存

### **2. 数据保存逻辑不完整**
- `saveAllChestData()` 方法只调用了 `saveConfig()`
- 没有实际将内存中的摸金箱数据写入配置文件
- 缺少从PlayerListener到ChestManager的数据传递

### **3. 缺少定期保存机制**
- 只在插件关闭时保存数据
- 服务器异常关闭时数据会丢失
- 没有自动备份机制

## ✅ **修复内容**

### **1. 统一位置键格式**
```java
// 修复前 (PlayerListener)
private String locationToString(Location location) {
    return location.getWorld().getName() + ":" + 
           location.getBlockX() + ":" + 
           location.getBlockY() + ":" + 
           location.getBlockZ();
}

// 修复后 (统一使用下划线格式)
private String locationToString(Location location) {
    return location.getWorld().getName() + "_" + 
           location.getBlockX() + "_" + 
           location.getBlockY() + "_" + 
           location.getBlockZ();
}
```

### **2. 完善数据保存逻辑**

#### **ChestManager.saveAllChestData()**
```java
public void saveAllChestData() {
    try {
        // 从PlayerListener获取所有摸金箱数据并保存
        if (plugin.getPlayerListener() != null) {
            plugin.getPlayerListener().saveAllChestDataToFile();
        }
        
        // 保存配置文件
        saveConfig();
        
        plugin.getLogger().info("已保存所有摸金箱数据到文件");
    } catch (Exception e) {
        plugin.getLogger().severe("保存摸金箱数据时出错: " + e.getMessage());
        e.printStackTrace();
    }
}
```

#### **PlayerListener.saveAllChestDataToFile()**
```java
public void saveAllChestDataToFile() {
    try {
        int savedCount = 0;
        for (Map.Entry<String, TreasureChestData> entry : treasureChestData.entrySet()) {
            try {
                // 从字符串键解析位置
                Location location = parseLocationFromKey(entry.getKey());
                if (location != null) {
                    // 保存到ChestManager
                    plugin.getChestManager().saveChestData(location, entry.getValue());
                    savedCount++;
                }
            } catch (Exception e) {
                plugin.getLogger().warning("保存摸金箱数据时出错 (" + entry.getKey() + "): " + e.getMessage());
            }
        }
        
        if (savedCount > 0) {
            plugin.getLogger().info("已保存 " + savedCount + " 个摸金箱数据到文件");
        }
    } catch (Exception e) {
        plugin.getLogger().severe("保存所有摸金箱数据时出错: " + e.getMessage());
        e.printStackTrace();
    }
}
```

### **3. 添加自动保存机制**

#### **配置文件 (config.yml)**
```yaml
# 自动保存设置
auto_save_interval: 5  # 自动保存间隔（分钟），设置为0或负数禁用自动保存
```

#### **自动保存任务**
```java
private void startAutoSaveTask() {
    int autoSaveInterval = getConfig().getInt("auto_save_interval", 5);
    
    if (autoSaveInterval > 0) {
        long intervalTicks = autoSaveInterval * 60 * 20L;
        
        autoSaveTaskId = getServer().getScheduler().runTaskTimerAsynchronously(this, new Runnable() {
            @Override
            public void run() {
                try {
                    // 异步保存数据
                    if (chestManager != null && playerListener != null) {
                        playerListener.saveAllChestDataToFile();
                        
                        // 同步执行文件保存
                        getServer().getScheduler().runTask(HangPlugin.this, new Runnable() {
                            @Override
                            public void run() {
                                try {
                                    chestManager.saveConfigFile();
                                    if (getConfig().getBoolean("debug.enabled", false)) {
                                        getLogger().info("自动保存摸金箱数据完成");
                                    }
                                } catch (Exception e) {
                                    getLogger().warning("自动保存摸金箱数据时出错: " + e.getMessage());
                                }
                            }
                        });
                    }
                    
                    // 同时保存等级数据
                    if (levelManager != null) {
                        levelManager.savePlayerData();
                    }
                    
                } catch (Exception e) {
                    getLogger().warning("自动保存任务执行时出错: " + e.getMessage());
                }
            }
        }, intervalTicks, intervalTicks).getTaskId();
        
        getLogger().info("自动保存任务已启动，间隔: " + autoSaveInterval + " 分钟");
    }
}
```

### **4. 添加手动保存命令**

#### **新增命令: `/evac save`**
```java
private boolean handleSaveCommand(CommandSender sender, String[] args) {
    if (!sender.hasPermission("evacuation.admin")) {
        sender.sendMessage("§c您没有权限使用此命令！");
        return true;
    }

    sender.sendMessage("§6正在保存所有数据...");
    
    try {
        long startTime = System.currentTimeMillis();
        
        // 保存摸金箱数据
        if (plugin.getPlayerListener() != null) {
            plugin.getPlayerListener().saveAllChestDataToFile();
        }
        
        if (plugin.getChestManager() != null) {
            plugin.getChestManager().saveConfigFile();
        }
        
        // 保存等级数据
        if (plugin.getLevelManager() != null) {
            plugin.getLevelManager().savePlayerData();
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        sender.sendMessage("§a数据保存完成！耗时: " + duration + "ms");
        
        // 显示保存的数据统计
        int chestCount = plugin.getChestManager() != null ? plugin.getChestManager().getChestCount() : 0;
        sender.sendMessage("§7已保存 " + chestCount + " 个摸金箱数据");
        
        plugin.getLogger().info("管理员 " + sender.getName() + " 手动保存了所有数据");
        
    } catch (Exception e) {
        sender.sendMessage("§c保存数据时出错: " + e.getMessage());
        plugin.getLogger().severe("手动保存数据时出错: " + e.getMessage());
        e.printStackTrace();
    }
    
    return true;
}
```

## 🎯 **修复效果**

### **修复前**
- ❌ 摸金箱数据重启后丢失
- ❌ 位置键格式不匹配
- ❌ 只在插件关闭时保存
- ❌ 缺少数据保护机制

### **修复后**
- ✅ 摸金箱数据持久化保存
- ✅ 位置键格式统一
- ✅ 定期自动保存（默认5分钟）
- ✅ 手动保存命令
- ✅ 异常处理和日志记录
- ✅ 数据完整性验证

## 🔧 **使用方法**

### **1. 配置自动保存**
```yaml
# config.yml
auto_save_interval: 5  # 5分钟自动保存一次
```

### **2. 手动保存数据**
```bash
/evac save  # 立即保存所有数据
```

### **3. 查看保存状态**
- 启动时会显示自动保存任务状态
- 调试模式下会显示保存详情
- 手动保存会显示耗时和统计

## ⚠️ **注意事项**

1. **备份重要**: 修复前建议备份现有的 `chests.yml` 文件
2. **权限要求**: `/evac save` 命令需要 `evacuation.admin` 权限
3. **性能影响**: 自动保存使用异步任务，不会影响服务器性能
4. **调试模式**: 可开启 `debug.enabled: true` 查看详细保存日志

## 📊 **测试建议**

1. **创建摸金箱**: 放置并搜索几个摸金箱
2. **手动保存**: 使用 `/evac save` 命令保存数据
3. **重启测试**: 重启服务器检查数据是否保留
4. **自动保存**: 等待5分钟观察自动保存日志
5. **异常测试**: 模拟服务器异常关闭后的数据恢复

---

**修复完成时间**: 2024年12月
**修复状态**: ✅ 完成
**测试状态**: 待验证
**影响范围**: 摸金箱数据持久化系统
