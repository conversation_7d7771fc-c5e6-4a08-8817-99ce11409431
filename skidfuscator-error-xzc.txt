handler=Block #AK, types=[Ljava/lang/RuntimeException;], range=[Block #AJ, Block #AI]
handler=Block #AN, types=[Ljava/lang/IllegalAccessException;], range=[Block #AM, Block #AL]
handler=Block #AQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #AP, Block #AO]
handler=Block #AT, types=[Ljava/lang/IllegalAccessException;], range=[Block #AS, Block #AR]
handler=Block #AW, types=[Ljava/lang/RuntimeException;], range=[Block #AV, Block #AU]
handler=Block #AZ, types=[Ljava/lang/IllegalAccessException;], range=[Block #AY, Block #AX]
handler=Block #BC, types=[Ljava/lang/IllegalAccessException;], range=[Block #BB, Block #BA]
handler=Block #BF, types=[Ljava/lang/IllegalAccessException;], range=[Block #BE, Block #BD]
handler=Block #BI, types=[Ljava/lang/IllegalAccessException;], range=[Block #BH, Block #BG]
===#Block A(size=5, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
   2. synth(lvar2 = lvar2);
   3. synth(lvar3 = lvar3);
   4. synth(lvar4 = lvar4);
      -> Immediate #A -> #B
===#Block B(size=4, flags=0)===
   0. lvar5 = lvar1;
   1. lvar6 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.tvljcviqcfwmgah(), lvar70);
   2. lvar11 = lvar5.hasPermission(lvar6);
   3. if (lvar11 != {********** ^ lvar70})
      goto BN
      -> Immediate #B -> #AG
      -> ConditionalJump[IF_ICMPNE] #B -> #BN
      <- Immediate #A -> #B
===#Block BN(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto C
   1. goto BK
      -> ConditionalJump[IF_ICMPEQ] #BN -> #C
      -> UnconditionalJump[GOTO] #BN -> #BK
      <- ConditionalJump[IF_ICMPNE] #B -> #BN
===#Block C(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar4;
   2. lvar13 = lvar12.length;
   3. if (lvar13 != {********** ^ lvar70})
      goto BO
      -> ConditionalJump[IF_ICMPNE] #C -> #BO
      -> Immediate #C -> #D
      <- ConditionalJump[IF_ICMPEQ] #BN -> #C
===#Block D(size=3, flags=0)===
   0. lvar14 = lvar0;
   1. lvar51 = lvar1;
   2. _consume(lvar14.showHelp(lvar51, 743956132));
      -> Immediate #D -> #E
      <- Immediate #C -> #D
===#Block E(size=2, flags=0)===
   0. lvar15 = {181611034 ^ lvar70};
   1. return lvar15;
      <- Immediate #D -> #E
===#Block BO(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -793725703)
      goto F
   1. goto BK
      -> ConditionalJump[IF_ICMPEQ] #BO -> #F
      -> UnconditionalJump[GOTO] #BO -> #BK
      <- ConditionalJump[IF_ICMPNE] #C -> #BO
===#Block F(size=12, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar4;
   2. lvar52 = {1745574826 ^ lvar70};
   3. lvar17 = lvar16[lvar52];
   4. lvar18 = lvar17.toLowerCase();
   5. lvar9 = lvar18;
   6. lvar19 = {-1745574827 ^ lvar70};
   7. lvar10 = lvar19;
   8. lvar20 = lvar9;
   9. lvar21 = lvar20.hashCode();
   10. svar72 = {lvar21 ^ lvar70};
   11. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(svar72)) {
      case 65293635:
      	 goto	#M
      case 109000323:
      	 goto	#K
      case 160778521:
      	 goto	#I
      case 166769197:
      	 goto	#G
      case 190848428:
      	 goto	#O
      default:
      	 goto	#Q
   }
      -> Switch[190848428] #F -> #O
      -> Switch[65293635] #F -> #M
      -> Switch[109000323] #F -> #K
      -> DefaultSwitch #F -> #Q
      -> Switch[160778521] #F -> #I
      -> Switch[166769197] #F -> #G
      <- ConditionalJump[IF_ICMPEQ] #BO -> #F
===#Block G(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar9;
   2. lvar53 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.nsbhiwmudczlsab(), lvar70);
   3. lvar23 = lvar22.equals(lvar53);
   4. if (lvar23 == {********** ^ lvar70})
      goto BP
      -> Immediate #G -> #H
      -> ConditionalJump[IF_ICMPEQ] #G -> #BP
      <- Switch[166769197] #F -> #G
===#Block BP(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto Q
   1. goto BK
      -> UnconditionalJump[GOTO] #BP -> #BK
      -> ConditionalJump[IF_ICMPEQ] #BP -> #Q
      <- ConditionalJump[IF_ICMPEQ] #G -> #BP
===#Block H(size=3, flags=0)===
   0. lvar24 = {839212678 ^ lvar70};
   1. lvar10 = lvar24;
   2. goto BE
      -> UnconditionalJump[GOTO] #H -> #BE
      <- Immediate #G -> #H
===#Block BE(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 78353148)
      goto BD
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BE -> #BD
      -> TryCatch range: [BE...BD] -> BF ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #H -> #BE
===#Block BD(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BE...BD] -> BF ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BE -> #BD
===#Block BF(size=2, flags=0)===
   0. _consume(catch());
   1. goto Q
      -> UnconditionalJump[GOTO] #BF -> #Q
      <- TryCatch range: [BE...BD] -> BF ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BE...BD] -> BF ([Ljava/lang/IllegalAccessException;])
===#Block I(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar37 = lvar9;
   2. lvar62 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.eupimtfqgdskenf(), lvar70);
   3. lvar38 = lvar37.equals(lvar62);
   4. if (lvar38 == {********** ^ lvar70})
      goto BQ
      -> Immediate #I -> #J
      -> ConditionalJump[IF_ICMPEQ] #I -> #BQ
      <- Switch[160778521] #F -> #I
===#Block BQ(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto Q
   1. goto BK
      -> UnconditionalJump[GOTO] #BQ -> #BK
      -> ConditionalJump[IF_ICMPEQ] #BQ -> #Q
      <- ConditionalJump[IF_ICMPEQ] #I -> #BQ
===#Block J(size=3, flags=0)===
   0. lvar39 = {652502746 ^ lvar70};
   1. lvar10 = lvar39;
   2. goto AV
      -> UnconditionalJump[GOTO] #J -> #AV
      <- Immediate #I -> #J
===#Block AV(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 33535211)
      goto AU
   1. throw nullconst;
      -> TryCatch range: [AV...AU] -> AW ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AV -> #AU
      <- UnconditionalJump[GOTO] #J -> #AV
===#Block AU(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AV...AU] -> AW ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AV -> #AU
===#Block AW(size=2, flags=0)===
   0. _consume(catch());
   1. goto Q
      -> UnconditionalJump[GOTO] #AW -> #Q
      <- TryCatch range: [AV...AU] -> AW ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AV...AU] -> AW ([Ljava/lang/RuntimeException;])
===#Block K(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar40 = lvar9;
   2. lvar63 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.xdslacyytnbpwyb(), lvar70);
   3. lvar41 = lvar40.equals(lvar63);
   4. if (lvar41 == {981226482 ^ lvar70})
      goto BR
      -> ConditionalJump[IF_ICMPEQ] #K -> #BR
      -> Immediate #K -> #L
      <- Switch[109000323] #F -> #K
===#Block L(size=2, flags=0)===
   0. lvar42 = {********** ^ lvar70};
   1. lvar10 = lvar42;
      -> Immediate #L -> #Q
      <- Immediate #K -> #L
===#Block BR(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto Q
   1. goto BK
      -> ConditionalJump[IF_ICMPEQ] #BR -> #Q
      -> UnconditionalJump[GOTO] #BR -> #BK
      <- ConditionalJump[IF_ICMPEQ] #K -> #BR
===#Block M(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar43 = lvar9;
   2. lvar64 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.bkkhbfvokomcdki(), lvar70);
   3. lvar44 = lvar43.equals(lvar64);
   4. if (lvar44 == {********** ^ lvar70})
      goto BL
      -> Immediate #M -> #N
      -> ConditionalJump[IF_ICMPEQ] #M -> #BL
      <- Switch[65293635] #F -> #M
===#Block BL(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == 630468552)
      goto Q
   1. goto BK
      -> UnconditionalJump[GOTO] #BL -> #BK
      -> ConditionalJump[IF_ICMPEQ] #BL -> #Q
      <- ConditionalJump[IF_ICMPEQ] #M -> #BL
===#Block N(size=3, flags=0)===
   0. lvar45 = {********** ^ lvar70};
   1. lvar10 = lvar45;
   2. goto BB
      -> UnconditionalJump[GOTO] #N -> #BB
      <- Immediate #M -> #N
===#Block BB(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 30170682)
      goto BA
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BB -> #BA
      -> TryCatch range: [BB...BA] -> BC ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #N -> #BB
===#Block BA(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BB...BA] -> BC ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BB -> #BA
===#Block BC(size=2, flags=0)===
   0. _consume(catch());
   1. goto Q
      -> UnconditionalJump[GOTO] #BC -> #Q
      <- TryCatch range: [BB...BA] -> BC ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BB...BA] -> BC ([Ljava/lang/IllegalAccessException;])
===#Block O(size=5, flags=0)===
   0. // Frame: locals[2] [java/lang/String, 1] stack[0] []
   1. lvar46 = lvar9;
   2. lvar65 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.vgbbbwuywmlwxps(), lvar70);
   3. lvar47 = lvar46.equals(lvar65);
   4. if (lvar47 == {********** ^ lvar70})
      goto BM
      -> Immediate #O -> #P
      -> ConditionalJump[IF_ICMPEQ] #O -> #BM
      <- Switch[190848428] #F -> #O
===#Block BM(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == **********)
      goto Q
   1. goto BK
      -> UnconditionalJump[GOTO] #BM -> #BK
      -> ConditionalJump[IF_ICMPEQ] #BM -> #Q
      <- ConditionalJump[IF_ICMPEQ] #O -> #BM
===#Block P(size=3, flags=0)===
   0. lvar48 = {********** ^ lvar70};
   1. lvar10 = lvar48;
   2. goto AM
      -> UnconditionalJump[GOTO] #P -> #AM
      <- Immediate #O -> #P
===#Block AM(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 43729726)
      goto AL
   1. throw nullconst;
      -> TryCatch range: [AM...AL] -> AN ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AM -> #AL
      <- UnconditionalJump[GOTO] #P -> #AM
===#Block AL(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AM...AL] -> AN ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AM -> #AL
===#Block AN(size=2, flags=0)===
   0. _consume(catch());
   1. goto Q
      -> UnconditionalJump[GOTO] #AN -> #Q
      <- TryCatch range: [AM...AL] -> AN ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AM...AL] -> AN ([Ljava/lang/IllegalAccessException;])
===#Block Q(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = lvar10;
   2. svar72 = {lvar25 ^ lvar70};
   3. switch (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(svar72)) {
      case 46185441:
      	 goto	#AB
      case 46185442:
      	 goto	#R
      case 46185443:
      	 goto	#T
      case 46185444:
      	 goto	#W
      default:
      	 goto	#V
   }
      -> Switch[46185441] #Q -> #AB
      -> Switch[46185444] #Q -> #W
      -> DefaultSwitch #Q -> #V
      -> Switch[46185443] #Q -> #T
      -> Switch[46185442] #Q -> #R
      <- ConditionalJump[IF_ICMPEQ] #BR -> #Q
      <- UnconditionalJump[GOTO] #AW -> #Q
      <- DefaultSwitch #F -> #Q
      <- UnconditionalJump[GOTO] #BF -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BM -> #Q
      <- UnconditionalJump[GOTO] #BC -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BQ -> #Q
      <- Immediate #L -> #Q
      <- UnconditionalJump[GOTO] #AN -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BP -> #Q
      <- ConditionalJump[IF_ICMPEQ] #BL -> #Q
===#Block R(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar26 = lvar0;
   2. lvar54 = lvar1;
   3. _consume(lvar26.showLicenseInfo(lvar54, 698594572));
      -> Immediate #R -> #S
      <- Switch[46185442] #Q -> #R
===#Block S(size=1, flags=0)===
   0. goto AJ
      -> UnconditionalJump[GOTO] #S -> #AJ
      <- Immediate #R -> #S
===#Block AJ(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 129343421)
      goto AI
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AJ -> #AI
      -> TryCatch range: [AJ...AI] -> AK ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #S -> #AJ
===#Block AI(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AJ...AI] -> AK ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AJ -> #AI
===#Block AK(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #AK -> #AF
      <- TryCatch range: [AJ...AI] -> AK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AJ...AI] -> AK ([Ljava/lang/RuntimeException;])
===#Block T(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar28 = lvar0;
   2. lvar55 = lvar1;
   3. _consume(lvar28.verifyLicense(lvar55, 544591340));
      -> Immediate #T -> #U
      <- Switch[46185443] #Q -> #T
===#Block U(size=1, flags=0)===
   0. goto AP
      -> UnconditionalJump[GOTO] #U -> #AP
      <- Immediate #T -> #U
===#Block AP(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 250926013)
      goto AO
   1. throw nullconst;
      -> TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AP -> #AO
      <- UnconditionalJump[GOTO] #U -> #AP
===#Block AO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AP -> #AO
===#Block AQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #AQ -> #AF
      <- TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AP...AO] -> AQ ([Ljava/lang/IllegalAccessException;])
===#Block V(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar0;
   2. lvar56 = lvar1;
   3. _consume(lvar29.showHelp(lvar56, 743956132));
      -> Immediate #V -> #AF
      <- DefaultSwitch #Q -> #V
===#Block W(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar30 = lvar4;
   2. lvar31 = lvar30.length;
   3. lvar57 = {********** ^ lvar70};
   4. if (lvar31 >= lvar57)
      goto BJ
      -> ConditionalJump[IF_ICMPGE] #W -> #BJ
      -> Immediate #W -> #X
      <- Switch[46185444] #Q -> #W
===#Block X(size=3, flags=0)===
   0. lvar32 = lvar1;
   1. lvar58 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.ncjwiedqjrrprii(), lvar70);
   2. _consume(lvar32.sendMessage(lvar58));
      -> Immediate #X -> #Y
      <- Immediate #W -> #X
===#Block Y(size=2, flags=0)===
   0. lvar33 = {********** ^ lvar70};
   1. return lvar33;
      <- Immediate #X -> #Y
===#Block BJ(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.xrgjlifncsczqejg(lvar70) == -**********)
      goto Z
   1. goto BK
      -> ConditionalJump[IF_ICMPEQ] #BJ -> #Z
      -> UnconditionalJump[GOTO] #BJ -> #BK
      <- ConditionalJump[IF_ICMPGE] #W -> #BJ
===#Block BK(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      <- UnconditionalJump[GOTO] #BO -> #BK
      <- UnconditionalJump[GOTO] #BR -> #BK
      <- UnconditionalJump[GOTO] #BQ -> #BK
      <- UnconditionalJump[GOTO] #BM -> #BK
      <- UnconditionalJump[GOTO] #BP -> #BK
      <- UnconditionalJump[GOTO] #BN -> #BK
      <- UnconditionalJump[GOTO] #BJ -> #BK
      <- UnconditionalJump[GOTO] #BL -> #BK
===#Block Z(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar34 = lvar0;
   2. lvar59 = lvar1;
   3. lvar7 = lvar4;
   4. lvar8 = {284854601 ^ lvar70};
   5. lvar67 = lvar7[lvar8];
   6. _consume(lvar34.setLicenseKey(lvar59, lvar67, **********));
      -> Immediate #Z -> #AA
      <- ConditionalJump[IF_ICMPEQ] #BJ -> #Z
===#Block AA(size=1, flags=0)===
   0. goto AS
      -> UnconditionalJump[GOTO] #AA -> #AS
      <- Immediate #Z -> #AA
===#Block AS(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 165388476)
      goto AR
   1. throw nullconst;
      -> TryCatch range: [AS...AR] -> AT ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AS -> #AR
      <- UnconditionalJump[GOTO] #AA -> #AS
===#Block AR(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AS...AR] -> AT ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AS -> #AR
===#Block AT(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #AT -> #AF
      <- TryCatch range: [AS...AR] -> AT ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AS...AR] -> AT ([Ljava/lang/IllegalAccessException;])
===#Block AB(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar35 = lvar0;
   2. lvar60 = lvar1;
   3. _consume(lvar35.reloadLicense(lvar60, **********));
      -> Immediate #AB -> #AC
      <- Switch[46185441] #Q -> #AB
===#Block AC(size=1, flags=0)===
   0. goto BH
      -> UnconditionalJump[GOTO] #AC -> #BH
      <- Immediate #AB -> #AC
===#Block BH(size=2, flags=0)===
   0. if (jyuggekqztwpctck.fqppxskiymtfbbek.eutdqkygtzoqrvcz(lvar70) == 174777508)
      goto BG
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BH -> #BG
      -> TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #AC -> #BH
===#Block BG(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BH -> #BG
===#Block BI(size=2, flags=0)===
   0. _consume(catch());
   1. goto AF
      -> UnconditionalJump[GOTO] #BI -> #AF
      <- TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BH...BG] -> BI ([Ljava/lang/IllegalAccessException;])
===#Block AF(size=3, flags=0)===
   0. // Frame: locals[2] [null, null] stack[0] []
   1. lvar27 = {133462594 ^ lvar70};
   2. return lvar27;
      <- Immediate #V -> #AF
      <- UnconditionalJump[GOTO] #AZ -> #AF
      <- UnconditionalJump[GOTO] #AT -> #AF
      <- UnconditionalJump[GOTO] #BI -> #AF
      <- UnconditionalJump[GOTO] #AQ -> #AF
      <- UnconditionalJump[GOTO] #AK -> #AF
===#Block AG(size=3, flags=0)===
   0. lvar49 = lvar1;
   1. lvar66 = com.hang.plugin.commands.LicenseCommand.eolixazyeq(com.hang.plugin.commands.LicenseCommand.ixxdivzehgcrwag(), lvar70);
   2. _consume(lvar49.sendMessage(lvar66));
      -> Immediate #AG -> #AH
      <- Immediate #B -> #AG
===#Block AH(size=2, flags=0)===
   0. lvar50 = {********** ^ lvar70};
   1. return lvar50;
      <- Immediate #AG -> #AH
