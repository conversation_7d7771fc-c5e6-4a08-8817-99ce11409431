# 📋 摸金箱配置系统说明

## 🔄 配置优先级

### 当前配置系统架构

```
mojin.yml (摸金箱种类配置) 
    ↓ 优先级最高
treasure_items.yml (默认配置)
    ↓ 仅用于向下兼容
config.yml (全局配置)
```

## 📁 配置文件说明

### 1. `mojin.yml` - 摸金箱种类配置 (主要配置)
```yaml
chest_types:
  common:
    name: "§6摸金箱"
    display_name: "§6摸金箱"
    slots: 5          # ✅ 实际使用的槽位数量
    refresh_time: 300 # ✅ 实际使用的刷新时间
    
  weapon:
    name: "§c武器箱"
    display_name: "§c武器箱"
    slots: 6          # ✅ 武器箱专用槽位数量
    refresh_time: 600 # ✅ 武器箱专用刷新时间
```

### 2. `treasure_items.yml` - 物品配置 + 默认设置
```yaml
items:
  # 物品配置...

chest_settings:  # ⚠️ 已弃用，仅用于向下兼容
  slots: 5       # 仅在 mojin.yml 未配置时使用
  search_time: 3 # 仅在 mojin.yml 未配置时使用
```

### 3. `config.yml` - 全局配置
```yaml
treasure-chest:
  refresh-time: 5  # ✅ 全局默认刷新时间
  auto-search:
    enabled: true  # ✅ 全局自动搜索开关
```

## 🎯 实际使用逻辑

### 槽位数量获取顺序：
1. **优先**: `mojin.yml` 中对应摸金箱种类的 `slots` 配置
2. **降级**: `treasure_items.yml` 中的 `chest_settings.slots` 配置
3. **默认**: 硬编码默认值 5

### 刷新时间获取顺序：
1. **优先**: `mojin.yml` 中对应摸金箱种类的 `refresh_time` 配置
2. **降级**: `config.yml` 中的 `treasure-chest.refresh-time` 配置
3. **默认**: 硬编码默认值 5分钟

## ✅ 推荐配置方式

### 新服务器配置：
- 在 `mojin.yml` 中配置所有摸金箱种类的参数
- `treasure_items.yml` 只配置物品，忽略 `chest_settings`
- `config.yml` 配置全局开关和默认值

### 旧服务器迁移：
- 保留现有 `treasure_items.yml` 配置作为兼容
- 逐步将配置迁移到 `mojin.yml`
- 迁移完成后可以删除 `treasure_items.yml` 中的 `chest_settings`

## 🔧 配置验证

检查配置是否生效：
1. 启动插件时查看日志中的加载信息
2. 使用 `/evac give <type>` 获取不同类型的摸金箱
3. 放置并打开摸金箱，验证槽位数量是否正确
4. 等待刷新时间，验证刷新机制是否正确

## 📝 注意事项

- `treasure_items.yml` 中的 `chest_settings` 现在主要用于向下兼容
- 新功能开发应该优先使用 `mojin.yml` 配置
- 如果某个摸金箱种类在 `mojin.yml` 中没有配置，会自动降级到默认配置
- 建议定期检查配置文件，确保配置的一致性
