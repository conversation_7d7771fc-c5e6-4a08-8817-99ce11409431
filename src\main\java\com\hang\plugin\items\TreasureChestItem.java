package com.hang.plugin.items;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.utils.VersionUtils;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;

/**
 * 摸金箱物品工具类
 *
 * <AUTHOR>
 */
public class TreasureChestItem {

    private static final String TREASURE_CHEST_NBT = "TREASURE_CHEST";

    /**
     * 创建摸金箱物品
     */
    public static ItemStack createTreasureChest(int amount) {
        ItemStack chest = new ItemStack(Material.CHEST, amount);
        ItemMeta meta = chest.getItemMeta();

        meta.setDisplayName("§6摸金箱");
        meta.setLore(Arrays.asList(
            "§7右键放置后可以搜索宝藏",
            "§7每个箱子包含随机物品",
            "§e点击搜索获得奖励！"
        ));

        chest.setItemMeta(meta);
        return chest;
    }

    /**
     * 检查是否为摸金箱物品（动态检测）
     * 修复：添加异常处理，防止LinkageError
     */
    public static boolean isTreasureChest(ItemStack item) {
        try {
            return getChestTypeFromItem(item) != null;
        } catch (LinkageError | Exception e) {
            // 修复：如果出现类加载错误，使用降级检测
            if (item == null) {
                return false;
            }

            ItemMeta meta = item.getItemMeta();
            if (meta == null || !meta.hasDisplayName()) {
                return false;
            }

            String displayName = meta.getDisplayName();

            // 降级检测：使用硬编码的默认名称
            return displayName.equals("§6摸金箱") ||
                   displayName.equals("§c武器箱") ||
                   displayName.equals("§e弹药箱") ||
                   displayName.equals("§a医疗箱") ||
                   displayName.equals("§b补给箱") ||
                   displayName.equals("§d装备箱");
        }
    }

    /**
     * 从物品获取摸金箱种类（动态检测）
     */
    public static String getChestTypeFromItem(ItemStack item) {
        if (item == null) {
            return null;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return null;
        }

        String displayName = meta.getDisplayName();

        // 修复：添加异常处理，防止LinkageError
        try {
            // 尝试从插件实例获取摸金箱种类管理器
            HangPlugin plugin = HangPlugin.getInstance();
            if (plugin != null && plugin.getChestTypeManager() != null) {
                // 动态检测：遍历所有配置的摸金箱种类
                for (com.hang.plugin.manager.ChestTypeManager.ChestType chestType :
                     plugin.getChestTypeManager().getAllChestTypes()) {
                    if (chestType != null && chestType.getName().equals(displayName)) {
                        return chestType.getTypeId();
                    }
                }
            }
        } catch (LinkageError | Exception e) {
            // 修复：如果出现类加载错误，记录日志并继续使用降级检测
            try {
                HangPlugin plugin = HangPlugin.getInstance();
                if (plugin != null) {
                    plugin.getLogger().warning("ChestTypeManager访问失败，使用降级检测: " + e.getMessage());
                }
            } catch (Exception logError) {
                // 静默处理日志错误
            }
            // 继续执行降级检测逻辑
        }

        // 降级检测：使用硬编码的默认名称（向下兼容）
        if (displayName.equals("§6摸金箱")) return "common";
        if (displayName.equals("§c武器箱")) return "weapon";
        if (displayName.equals("§e弹药箱")) return "ammo";
        if (displayName.equals("§a医疗箱")) return "medical";
        if (displayName.equals("§b补给箱")) return "supply";
        if (displayName.equals("§d装备箱")) return "equipment";

        // 修复：移除过于宽泛的通用检查，避免误识别
        // 不再使用 displayName.contains("箱") 的检查，因为这会导致
        // 任何包含"箱"字的物品都被识别为摸金箱

        return null;
    }

    /**
     * 创建撤离点选择工具
     */
    public static ItemStack createEvacuationTool() {
        ItemStack tool = new ItemStack(VersionUtils.getCompatibleMaterial("GOLD_AXE"), 1);
        ItemMeta meta = tool.getItemMeta();

        meta.setDisplayName("§6撤离点选择工具");
        meta.setLore(Arrays.asList(
            "§7左键选择第一个坐标",
            "§7右键选择第二个坐标",
            "§7选择完成后使用 §e/evac create <名称> §7创建撤离点",
            "§c注意: 这是管理员专用工具"
        ));

        tool.setItemMeta(meta);
        return tool;
    }

    /**
     * 检查是否为撤离点选择工具
     */
    public static boolean isEvacuationTool(ItemStack item) {
        if (item == null || item.getType() != VersionUtils.getCompatibleMaterial("GOLD_AXE")) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }

        return "§6撤离点选择工具".equals(meta.getDisplayName());
    }
}
