package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import org.bukkit.Location;
import org.bukkit.scheduler.BukkitTask;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;

/**
 * 内存缓存管理器
 * 优化摸金箱数据存储性能，减少文件I/O操作
 *
 * <AUTHOR>
 */
public class MemoryCacheManager {

    private final HangPlugin plugin;

    // 内存缓存存储
    private final Map<String, com.hang.plugin.listeners.PlayerListener.TreasureChestData> memoryCache;

    // 脏数据标记（需要保存到文件的数据）
    private final Set<String> dirtyKeys;

    // 缓存统计
    private final AtomicLong cacheHits = new AtomicLong(0);
    private final AtomicLong cacheMisses = new AtomicLong(0);
    private final AtomicLong totalSaves = new AtomicLong(0);

    // 自动保存任务
    private BukkitTask autoSaveTask;

    public MemoryCacheManager(HangPlugin plugin) {
        this.plugin = plugin;
        this.memoryCache = new ConcurrentHashMap<>();
        this.dirtyKeys = ConcurrentHashMap.newKeySet();

        // 启动自动保存任务
        startAutoSaveTask();
    }

    /**
     * 保存数据到内存缓存
     */
    public void saveToCache(String locationKey, com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
        // 检查缓存大小限制
        int maxCacheSize = plugin.getConfig().getInt("performance.memory_cache.max_cache_size", 10000);
        if (memoryCache.size() >= maxCacheSize && !memoryCache.containsKey(locationKey)) {
            plugin.getLogger().warning("警告：内存缓存已满 (" + memoryCache.size() + "/" + maxCacheSize + ")，跳过缓存");
            return;
        }

        // 保存到内存缓存
        memoryCache.put(locationKey, data);

        // 标记为脏数据
        dirtyKeys.add(locationKey);

        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("数据已保存到内存缓存: " + locationKey);
        }
    }

    /**
     * 从内存缓存读取数据
     */
    public com.hang.plugin.listeners.PlayerListener.TreasureChestData loadFromCache(String locationKey) {
        com.hang.plugin.listeners.PlayerListener.TreasureChestData data = memoryCache.get(locationKey);

        if (data != null) {
            cacheHits.incrementAndGet();
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("🎯 从内存缓存命中: " + locationKey);
            }
        } else {
            cacheMisses.incrementAndGet();
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("错误：内存缓存未命中: " + locationKey);
            }
        }

        return data;
    }

    /**
     * 检查缓存中是否存在数据
     */
    public boolean existsInCache(String locationKey) {
        return memoryCache.containsKey(locationKey);
    }

    /**
     * 从缓存中移除数据
     */
    public void removeFromCache(String locationKey) {
        memoryCache.remove(locationKey);
        dirtyKeys.remove(locationKey);

        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("已从内存缓存移除: " + locationKey);
        }
    }

    /**
     * 获取所有缓存的数据
     */
    public Map<String, com.hang.plugin.listeners.PlayerListener.TreasureChestData> getAllCachedData() {
        return new ConcurrentHashMap<>(memoryCache);
    }

    /**
     * 强制保存所有脏数据到文件
     */
    public void flushDirtyData() {
        if (dirtyKeys.isEmpty()) {
            return;
        }

        boolean cacheOnlyMode = plugin.getConfig().getBoolean("performance.memory_cache.cache_only_mode", false);
        if (cacheOnlyMode) {
            plugin.getLogger().info("🔒 仅内存模式已启用，跳过文件保存");
            return;
        }

        long startTime = System.currentTimeMillis();
        int savedCount = 0;

        // 只在启用日志时显示开始保存信息
        if (plugin.getConfig().getBoolean("performance.auto_save.show_logs", true)) {
            plugin.getLogger().info("开始保存脏数据到文件... (共 " + dirtyKeys.size() + " 个)");
        }

        // 创建脏数据副本，避免并发修改
        Set<String> dirtyKeysCopy = new HashSet<>(dirtyKeys);

        for (String locationKey : dirtyKeysCopy) {
            try {
                com.hang.plugin.listeners.PlayerListener.TreasureChestData data = memoryCache.get(locationKey);
                if (data != null) {
                    // 解析位置
                    Location location = parseLocationFromKey(locationKey);
                    if (location != null) {
                        // 保存到文件
                        plugin.getChestManager().saveChestDataBatch(location, data);
                        savedCount++;

                        // 从脏数据集合中移除
                        dirtyKeys.remove(locationKey);
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("保存脏数据失败 (" + locationKey + "): " + e.getMessage());
            }
        }

        // 最后写入配置文件
        if (savedCount > 0) {
            try {
                plugin.getChestManager().saveConfigFile();
                totalSaves.addAndGet(savedCount);

                long duration = System.currentTimeMillis() - startTime;
                if (plugin.getConfig().getBoolean("performance.auto_save.show_logs", true)) {
                    plugin.getLogger().info("完成：脏数据保存完成: " + savedCount + " 个 (耗时: " + duration + "ms)");
                }
            } catch (Exception e) {
                plugin.getLogger().severe("保存配置文件失败: " + e.getMessage());
            }
        }
    }

    /**
     * 启动自动保存任务
     */
    private void startAutoSaveTask() {
        boolean enabled = plugin.getConfig().getBoolean("performance.memory_cache.enabled", true);
        if (!enabled) {
            return;
        }

        int interval = plugin.getConfig().getInt("performance.memory_cache.auto_save_interval", 300);

        autoSaveTask = plugin.getServer().getScheduler().runTaskTimerAsynchronously(plugin, new Runnable() {
            @Override
            public void run() {
                try {
                    if (!dirtyKeys.isEmpty()) {
                        flushDirtyData();
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("自动保存脏数据失败: " + e.getMessage());
                }
            }
        }, interval * 20L, interval * 20L);

        // 只在调试模式下显示启动信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("🔄 内存缓存自动保存任务已启动 (间隔: " + interval + "秒)");
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        long hits = cacheHits.get();
        long misses = cacheMisses.get();
        long total = hits + misses;
        double hitRate = total > 0 ? (hits * 100.0 / total) : 0.0;

        return String.format("缓存统计: 命中率=%.1f%% (%d/%d), 缓存大小=%d, 脏数据=%d, 总保存=%d",
            hitRate, hits, total, memoryCache.size(), dirtyKeys.size(), totalSaves.get());
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        try {
            // 保存所有脏数据
            flushDirtyData();

            // 停止自动保存任务
            if (autoSaveTask != null) {
                try {
                    autoSaveTask.cancel();
                } catch (Exception e) {
                    plugin.getLogger().warning("停止自动保存任务时出错: " + e.getMessage());
                }
            }

            // 清空缓存
            memoryCache.clear();
            dirtyKeys.clear();

            plugin.getLogger().info("🧹 内存缓存管理器已清理");
        } catch (Exception e) {
            plugin.getLogger().severe("清理内存缓存管理器失败: " + e.getMessage());
        }
    }

    /**
     * 解析位置字符串
     */
    private Location parseLocationFromKey(String locationKey) {
        try {
            String[] parts = locationKey.split("_");
            if (parts.length >= 4) {
                String worldName = parts[0];
                int x = Integer.parseInt(parts[1]);
                int y = Integer.parseInt(parts[2]);
                int z = Integer.parseInt(parts[3]);

                org.bukkit.World world = plugin.getServer().getWorld(worldName);
                if (world != null) {
                    return new Location(world, x, y, z);
                }
            }
        } catch (Exception e) {
            plugin.getLogger().warning("解析位置字符串失败: " + locationKey);
        }
        return null;
    }
}
