package com.hang.plugin.gui;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.manager.ChestTypeManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 摸金箱种类选择GUI
 * 让玩家选择要管理的摸金箱种类
 */
public class ChestTypeSelectionGUI {
    
    private final HangPlugin plugin;
    private final Player player;
    private final Inventory inventory;
    private int currentPage = 0;
    private final int itemsPerPage = 21; // 3行7列
    
    public ChestTypeSelectionGUI(HangPlugin plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
        
        String title = plugin.getChestTypeManager().getConfig()
                .getString("gui_settings.selection_title", "§6选择摸金箱种类");
        this.inventory = Bukkit.createInventory(null, 54, title);
        
        updateDisplay();
    }
    
    /**
     * 更新显示
     */
    private void updateDisplay() {
        inventory.clear();
        
        List<ChestTypeManager.ChestType> enabledTypes = new ArrayList<>(plugin.getChestTypeManager().getEnabledChestTypes());
        
        // 计算分页
        int startIndex = currentPage * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, enabledTypes.size());
        
        // 显示摸金箱种类
        int slot = 10; // 从第二行第二列开始
        for (int i = startIndex; i < endIndex; i++) {
            ChestTypeManager.ChestType chestType = enabledTypes.get(i);
            ItemStack displayItem = createTypeDisplayItem(chestType);
            
            inventory.setItem(slot, displayItem);
            
            // 计算下一个槽位
            slot++;
            if (slot % 9 == 8) { // 跳过右边界
                slot += 2;
            }
            if (slot >= 44) { // 不超过第五行
                break;
            }
        }
        
        // 添加控制按钮
        addControlButtons(enabledTypes.size());
    }
    
    /**
     * 创建种类显示物品
     */
    private ItemStack createTypeDisplayItem(ChestTypeManager.ChestType chestType) {
        ItemStack item = new ItemStack(chestType.getMaterial(), 1);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(chestType.getDisplayName());
            
            List<String> lore = new ArrayList<>();
            lore.addAll(chestType.getDescription());
            lore.add("");
            lore.add("§7种类: " + chestType.getRarity());
            lore.add("§7ID: §f" + chestType.getTypeId());
            lore.add("");
            lore.add("§a左键: §f管理此种类的战利品");
            lore.add("§e右键: §f获得此种类的摸金箱");
            
            meta.setLore(lore);
            
            // 设置自定义模型数据（1.14+版本支持）
            if (chestType.getCustomModelData() > 0) {
                try {
                    // 使用反射来调用setCustomModelData方法，兼容1.12.2
                    java.lang.reflect.Method setCustomModelDataMethod = meta.getClass().getMethod("setCustomModelData", int.class);
                    setCustomModelDataMethod.invoke(meta, chestType.getCustomModelData());
                } catch (Exception ignored) {
                    // 1.14以下版本不支持，忽略
                }
            }
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * 添加控制按钮
     */
    private void addControlButtons(int totalTypes) {
        // 上一页按钮
        if (currentPage > 0) {
            ItemStack prevButton = new ItemStack(Material.ARROW, 1);
            ItemMeta prevMeta = prevButton.getItemMeta();
            if (prevMeta != null) {
                prevMeta.setDisplayName("§a上一页");
                prevMeta.setLore(Arrays.asList("§7点击查看上一页"));
                prevButton.setItemMeta(prevMeta);
            }
            inventory.setItem(45, prevButton);
        }
        
        // 下一页按钮
        if ((currentPage + 1) * itemsPerPage < totalTypes) {
            ItemStack nextButton = new ItemStack(Material.ARROW, 1);
            ItemMeta nextMeta = nextButton.getItemMeta();
            if (nextMeta != null) {
                nextMeta.setDisplayName("§a下一页");
                nextMeta.setLore(Arrays.asList("§7点击查看下一页"));
                nextButton.setItemMeta(nextMeta);
            }
            inventory.setItem(53, nextButton);
        }
        
        // 信息按钮
        ItemStack infoButton = new ItemStack(Material.BOOK, 1);
        ItemMeta infoMeta = infoButton.getItemMeta();
        if (infoMeta != null) {
            infoMeta.setDisplayName("§6摸金箱种类管理");
            List<String> infoLore = new ArrayList<>();
            infoLore.add("§7当前页: §e" + (currentPage + 1));
            infoLore.add("§7总种类: §e" + totalTypes);
            infoLore.add("");
            infoLore.add("§a左键种类: §f管理战利品");
            infoLore.add("§e右键种类: §f获得摸金箱");
            infoMeta.setLore(infoLore);
            infoButton.setItemMeta(infoMeta);
        }
        inventory.setItem(49, infoButton);

        // 默认概率设置按钮（第47格）
        ItemStack probabilityButton = new ItemStack(Material.GOLD_INGOT, 1);
        ItemMeta probabilityMeta = probabilityButton.getItemMeta();
        if (probabilityMeta != null) {
            probabilityMeta.setDisplayName("§6设置默认概率");
            List<String> probabilityLore = new ArrayList<>();
            probabilityLore.add("§7设置新添加物品的默认概率");
            probabilityLore.add("§7当前默认概率: §e" + getDefaultProbability() + "%");
            probabilityLore.add("");
            probabilityLore.add("§a左键: §f增加概率 (+1%)");
            probabilityLore.add("§c右键: §f减少概率 (-1%)");
            probabilityMeta.setLore(probabilityLore);
            probabilityButton.setItemMeta(probabilityMeta);
        }
        inventory.setItem(47, probabilityButton);

        // 默认搜索速度设置按钮（第48格）
        ItemStack speedButton = new ItemStack(Material.REDSTONE, 1);
        ItemMeta speedMeta = speedButton.getItemMeta();
        if (speedMeta != null) {
            speedMeta.setDisplayName("§b设置默认搜索速度");
            List<String> speedLore = new ArrayList<>();
            speedLore.add("§7设置新添加物品的默认搜索速度");
            speedLore.add("§7当前默认速度: §e" + getDefaultSearchSpeed() + " 秒");
            speedLore.add("");
            speedLore.add("§a左键: §f增加速度 (+1秒)");
            speedLore.add("§c右键: §f减少速度 (-1秒)");
            speedMeta.setLore(speedLore);
            speedButton.setItemMeta(speedMeta);
        }
        inventory.setItem(48, speedButton);

        // 关闭按钮
        ItemStack closeButton = new ItemStack(Material.BARRIER, 1);
        ItemMeta closeMeta = closeButton.getItemMeta();
        if (closeMeta != null) {
            closeMeta.setDisplayName("§c关闭界面");
            closeMeta.setLore(Arrays.asList("§7点击关闭此界面"));
            closeButton.setItemMeta(closeMeta);
        }
        inventory.setItem(50, closeButton);
    }
    
    /**
     * 处理点击事件
     */
    public void handleClick(int slot, boolean isRightClick) {
        // 控制按钮处理
        if (slot == 45 && currentPage > 0) {
            // 上一页
            currentPage--;
            updateDisplay();
            return;
        }
        
        if (slot == 53) {
            // 下一页
            List<ChestTypeManager.ChestType> enabledTypes = new ArrayList<>(plugin.getChestTypeManager().getEnabledChestTypes());
            if ((currentPage + 1) * itemsPerPage < enabledTypes.size()) {
                currentPage++;
                updateDisplay();
            }
            return;
        }
        
        if (slot == 47) {
            // 默认概率设置按钮
            handleDefaultProbabilityClick(isRightClick);
            return;
        }

        if (slot == 48) {
            // 默认搜索速度设置按钮
            handleDefaultSearchSpeedClick(isRightClick);
            return;
        }

        if (slot == 50) {
            // 关闭界面
            player.closeInventory();
            return;
        }
        
        // 种类选择处理
        if (slot >= 10 && slot < 44 && slot % 9 != 0 && slot % 9 != 8) {
            List<ChestTypeManager.ChestType> enabledTypes = new ArrayList<>(plugin.getChestTypeManager().getEnabledChestTypes());
            
            // 计算实际的种类索引
            int row = (slot - 10) / 9;
            int col = (slot - 10) % 9;
            if (col >= 7) return; // 超出有效列范围
            
            int typeIndex = currentPage * itemsPerPage + row * 7 + col;
            
            if (typeIndex >= 0 && typeIndex < enabledTypes.size()) {
                ChestTypeManager.ChestType selectedType = enabledTypes.get(typeIndex);
                
                if (isRightClick) {
                    // 右键：给予摸金箱
                    giveChestToPlayer(selectedType);
                } else {
                    // 左键：打开管理界面
                    openManagementGUI(selectedType);
                }
            }
        }
    }
    
    /**
     * 给予玩家摸金箱
     */
    private void giveChestToPlayer(ChestTypeManager.ChestType chestType) {
        ItemStack chestItem = plugin.getChestTypeManager().createChestItem(chestType.getTypeId(), 1);
        player.getInventory().addItem(chestItem);
        player.sendMessage("§a已给予您 " + chestType.getDisplayName() + " §ax1");
        
        // 播放音效
        try {
            player.playSound(player.getLocation(), 
                org.bukkit.Sound.valueOf("ENTITY_ITEM_PICKUP"), 0.8f, 1.2f);
        } catch (Exception ignored) {
            // 音效播放失败，忽略
        }
    }
    
    /**
     * 打开管理界面
     */
    private void openManagementGUI(ChestTypeManager.ChestType chestType) {
        // 创建特定种类的管理GUI
        TreasureManagementGUI managementGUI = new TreasureManagementGUI(plugin, player, chestType.getTypeId());
        
        // 注册GUI到监听器
        plugin.getPlayerListener().registerManagementGUI(player, managementGUI);
        
        managementGUI.open();
        
        player.sendMessage("§e正在打开 " + chestType.getDisplayName() + " §e的战利品管理界面...");
    }
    
    /**
     * 打开GUI
     */
    public void open() {
        player.openInventory(inventory);
    }
    
    /**
     * 获取库存对象
     */
    public Inventory getInventory() {
        return inventory;
    }

    /**
     * 获取默认概率
     */
    private double getDefaultProbability() {
        return plugin.getConfig().getDouble("treasure-chest.default_probability", 10.0);
    }

    /**
     * 设置默认概率
     */
    private void setDefaultProbability(double probability) {
        plugin.getConfig().set("treasure-chest.default_probability", probability);
        plugin.saveConfig();
    }

    /**
     * 获取默认搜索速度
     */
    private int getDefaultSearchSpeed() {
        return plugin.getConfig().getInt("treasure-chest.default_search_speed", 3);
    }

    /**
     * 设置默认搜索速度
     */
    private void setDefaultSearchSpeed(int speed) {
        plugin.getConfig().set("treasure-chest.default_search_speed", speed);
        plugin.saveConfig();
    }

    /**
     * 处理默认概率按钮点击
     */
    private void handleDefaultProbabilityClick(boolean isRightClick) {
        double currentProbability = getDefaultProbability();
        double newProbability;

        if (isRightClick) {
            // 右键：减少概率
            newProbability = Math.max(0.1, currentProbability - 1.0);
        } else {
            // 左键：增加概率
            newProbability = Math.min(100.0, currentProbability + 1.0);
        }

        setDefaultProbability(newProbability);
        player.sendMessage("§a默认概率已设置为: §e" + newProbability + "%");

        // 刷新显示
        updateDisplay();
    }

    /**
     * 处理默认搜索速度按钮点击
     */
    private void handleDefaultSearchSpeedClick(boolean isRightClick) {
        int currentSpeed = getDefaultSearchSpeed();
        int newSpeed;

        if (isRightClick) {
            // 右键：减少速度（最小1秒）
            newSpeed = Math.max(1, currentSpeed - 1);
        } else {
            // 左键：增加速度（最大60秒）
            newSpeed = Math.min(60, currentSpeed + 1);
        }

        setDefaultSearchSpeed(newSpeed);
        player.sendMessage("§a默认搜索速度已设置为: §e" + newSpeed + " 秒");

        // 刷新显示
        updateDisplay();
    }
}
