# 🔧 HangEvacuation插件编译说明

## 🎯 **编译状态**

✅ **修复已完成** - 摸金箱种类选择功能和物品类型判断已修复
✅ **代码已更新** - 所有必要的代码修改已应用
⚠️ **需要重新编译** - 需要生成包含修复的新jar文件

## 🐛 **pom.xml问题修复**

发现原pom.xml文件第12行有错误：
```xml
<n>HangEvacuation</n>  <!-- 错误：应该是<name> -->
```

已创建修复版本：`pom_fixed.xml`

## 🚀 **编译步骤**

### **方法1：使用修复后的pom.xml**
```bash
# 1. 备份原pom.xml
copy pom.xml pom_backup.xml

# 2. 使用修复后的pom.xml
copy pom_fixed.xml pom.xml

# 3. 清理并编译
mvn clean package

# 4. 如果成功，删除备份；如果失败，恢复备份
```

### **方法2：手动修复pom.xml**
1. 打开 `1.12.2/pom.xml`
2. 找到第12行：`<n>HangEvacuation</n>`
3. 修改为：`<name>HangEvacuation</name>`
4. 保存文件
5. 运行：`mvn clean package`

### **方法3：使用现有jar文件**
如果编译环境有问题，现有的 `target/HangEvacuation-Universal-1.7.0.jar` 可能已经包含了修复。

## 📋 **编译验证**

编译成功后，应该生成：
- `target/HangEvacuation-Universal-1.7.0.jar` - 主插件文件
- 文件大小应该在几MB左右
- 包含所有修复的功能

## 🧪 **测试新插件**

1. **备份现有插件**
   ```bash
   # 备份服务器上的旧插件
   copy plugins\HangEvacuation-Universal-1.7.0.jar plugins\HangEvacuation-Universal-1.7.0-backup.jar
   ```

2. **替换插件文件**
   ```bash
   # 复制新编译的插件到服务器
   copy 1.12.2\target\HangEvacuation-Universal-1.7.0.jar 服务器路径\plugins\
   ```

3. **重启服务器**

4. **测试功能**
   - 手持物品（如沙子）
   - 执行 `/evac gui`
   - 点击"添加新物品"
   - 应该弹出摸金箱种类选择界面
   - 选择种类后确认物品正确添加

## 🔍 **预期修复效果**

### **修复前：**
- ❌ 所有物品只能添加到普通摸金箱
- ❌ 沙子等原版物品显示为"序列化物品（模组物品）"

### **修复后：**
- ✅ 可以选择物品添加到6种摸金箱类型
- ✅ 原版物品正确显示为"普通物品"
- ✅ 摸金箱种类选择界面正常工作

## 🆘 **如果编译失败**

### **常见问题：**

1. **Maven未安装**
   - 下载并安装Maven
   - 添加到PATH环境变量

2. **Java版本问题**
   - 确保使用Java 8或更高版本
   - 检查JAVA_HOME环境变量

3. **网络问题**
   - Maven需要下载依赖
   - 确保网络连接正常

4. **pom.xml语法错误**
   - 使用提供的 `pom_fixed.xml`
   - 检查XML语法是否正确

### **备用方案：**

如果无法编译，可以：
1. 使用现有的jar文件测试
2. 手动修改Java源码后使用IDE编译
3. 请求帮助进行远程编译

## 📞 **需要帮助？**

如果遇到编译问题，请提供：
1. 编译错误信息
2. Maven版本：`mvn --version`
3. Java版本：`java -version`
4. 操作系统信息

我会立即帮你解决编译问题！

## 🎉 **编译成功后**

编译成功后，你就可以：
- ✅ 为不同种类摸金箱添加专属物品
- ✅ 正确识别原版物品类型
- ✅ 享受完整的摸金箱种类选择功能

**祝编译顺利！** 🚀✨
