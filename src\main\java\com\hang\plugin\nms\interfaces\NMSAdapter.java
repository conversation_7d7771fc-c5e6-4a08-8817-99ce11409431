package com.hang.plugin.nms.interfaces;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * NMS适配器接口
 * 定义所有版本需要实现的方法
 * 
 * <AUTHOR>
 */
public interface NMSAdapter {
    
    /**
     * 获取适配器版本
     */
    String getVersion();
    
    /**
     * 创建全息图盔甲架
     */
    ArmorStand createHologram(Location location, String text);
    
    /**
     * 设置盔甲架属性
     */
    void setupArmorStand(ArmorStand armorStand);
    
    /**
     * 发送动作栏消息
     */
    void sendActionBar(Player player, String message);
    
    /**
     * 发送标题消息
     */
    void sendTitle(Player player, String title, String subtitle, int fadeIn, int stay, int fadeOut);
    
    /**
     * 播放音效
     */
    void playSound(Player player, String sound, float volume, float pitch);
    
    /**
     * 获取物品的本地化名称
     */
    String getItemDisplayName(ItemStack item);
    
    /**
     * 创建NBT标签
     */
    ItemStack setNBTTag(ItemStack item, String key, String value);
    
    /**
     * 获取NBT标签
     */
    String getNBTTag(ItemStack item, String key);
    
    /**
     * 检查物品是否有NBT标签
     */
    boolean hasNBTTag(ItemStack item, String key);
    
    /**
     * 发送数据包
     */
    void sendPacket(Player player, Object packet);
    
    /**
     * 获取玩家的Ping值
     */
    int getPing(Player player);
    
    /**
     * 设置方块数据（用于进度显示）
     */
    void setBlockData(Location location, String blockData);
    
    /**
     * 创建粒子效果
     */
    void spawnParticle(Location location, String particle, int count, double offsetX, double offsetY, double offsetZ);
    
    /**
     * 获取实体的自定义名称可见性
     */
    boolean isCustomNameVisible(ArmorStand armorStand);
    
    /**
     * 设置实体的自定义名称可见性
     */
    void setCustomNameVisible(ArmorStand armorStand, boolean visible);
    
    /**
     * 检查版本兼容性
     */
    boolean isCompatible();
    
    /**
     * 初始化适配器
     */
    void initialize();
    
    /**
     * 通过字符串创建物品（支持模组物品）
     */
    ItemStack createItemFromString(String itemString, int amount);

    /**
     * 检查物品是否为模组物品
     */
    boolean isModItem(ItemStack item);

    /**
     * 获取模组物品的ID
     */
    String getModItemId(ItemStack item);

    /**
     * 通过名称获取材料（支持模组材料）
     */
    Material getMaterialByName(String materialName);

    /**
     * 清理资源
     */
    void cleanup();
}
