# hangevacuation插件

一个为Minecraft 1.12.2设计的摸金插件，包含摸金箱系统和撤离点系统。

## 功能特性

### 摸金箱系统
- 管理员可以给予玩家摸金箱物品
- 玩家可以放置摸金箱方块并进行物品搜索
- 默认包含5个可搜索的物品槽位
- 搜索过程中显示红色玻璃板，未搜索显示灰色玻璃板
- 搜索完成后显示随机战利品
- 包含搜索冷却时间机制
- 支持自定义战利品配置和概率系统
- 支持自定义命令执行

### 撤离点系统
- 管理员可以使用金斧子工具选择撤离区域坐标
- 支持创建多个撤离区域
- 玩家进入撤离区域时自动开始倒计时
- 倒计时期间显示剩余时间提示
- 离开撤离区域会取消倒计时
- 倒计时完成后触发撤离成功事件
- 所有撤离点共享同一个最终撤离目标位置

## 命令

### 基础命令
- `/evacuation` - 显示插件帮助信息
- `/evacuation reload` - 重载配置文件 (需要管理员权限)
- `/evacuation help` - 显示详细帮助信息

### 摸金箱命令
- `/givechest [玩家] [数量]` - 给予摸金箱物品 (需要管理员权限)

### 撤离点管理命令
- `/evac tool` - 获取撤离点选择工具 (金斧子) (需要管理员权限)
- `/evac create <名称>` - 使用选择的坐标创建撤离区域 (需要管理员权限)
- `/evac remove <名称>` - 移除指定撤离区域 (需要管理员权限)
- `/evac list` - 列出所有撤离区域和最终目标 (需要管理员权限)
- `/evac destination` - 设置最终撤离目标位置 (需要管理员权限)

## 权限

- `evacuation.use` - 使用插件基础功能 (默认: true)
- `evacuation.chest` - 使用摸金箱功能 (默认: true)
- `evacuation.admin` - 管理撤离点和摸金箱 (默认: op)
- `evacuation.reload` - 重载插件配置 (默认: op)

## 配置文件

### config.yml
```yaml
# 摸金箱设置
treasure-chest:
  default-items: 5        # 默认物品数量
  search-time: 3          # 搜索时间 (秒)
  search-cooldown: 1      # 搜索冷却时间 (秒)

# 撤离点设置
evacuation:
  countdown-time: 10      # 撤离倒计时时间 (秒)
  detection-range: 5      # 检测范围 (方块)
  check-interval: 20      # 检测间隔 (tick)

# 消息设置
messages:
  prefix: "§6[摸金] §r"
  # ... 其他消息配置
```

## 安装说明

1. 确保服务器运行Minecraft 1.12.2
2. 将编译好的jar文件放入服务器的`plugins`文件夹
3. 重启服务器或使用`/reload`命令
4. 插件将自动生成配置文件

## 编译说明

### 前置要求
- Java 8 或更高版本
- Maven 3.6 或更高版本

### 编译步骤
```bash
# 克隆项目
git clone <repository-url>
cd 摸金

# 编译项目
mvn clean package

# 编译完成后，jar文件位于 target/ 目录下
```

## 使用示例

### 设置撤离点
```
/evacuation set 主要撤离点
/evacuation set 紧急撤离点
```

### 查看撤离点
```
/evacuation list
```

### 打开摸金箱
```
/treasure
```

## 技术细节

### 项目结构
```
src/main/java/com/hang/plugin/
├── HangPlugin.java              # 主插件类
├── commands/
│   └── HangCommand.java         # 命令处理器
├── gui/
│   └── TreasureChestGUI.java    # 摸金箱GUI
├── listeners/
│   └── PlayerListener.java     # 事件监听器
├── manager/
│   └── CountdownManager.java   # 倒计时管理器
└── system/
    └── EvacuationSystem.java   # 撤离系统
```

### 依赖
- Spigot API 1.12.2-R0.1-SNAPSHOT
- Bukkit API 1.12.2-R0.1-SNAPSHOT

## 版本历史

### v1.0.0
- 初始版本
- 实现摸金箱系统
- 实现撤离点系统
- 基础命令和权限系统

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进这个插件。

## 支持

如果您在使用过程中遇到问题，请：
1. 检查服务器日志中的错误信息
2. 确认Minecraft版本为1.12.2
3. 验证权限配置是否正确
4. 提交Issue并附上详细的错误信息
