package com.hang.plugin.config;

import com.hang.plugin.HangPlugin;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;

/**
 * 许可证配置管理器
 */
public class LicenseConfig {
    
    private final HangPlugin plugin;
    private File configFile;
    private FileConfiguration config;
    
    public LicenseConfig(HangPlugin plugin) {
        this.plugin = plugin;
        this.configFile = new File(plugin.getDataFolder(), "license.yml");
        loadConfig();
    }
    
    /**
     * 加载配置文件
     */
    private void loadConfig() {
        if (!configFile.exists()) {
            createDefaultConfig();
        }
        config = YamlConfiguration.loadConfiguration(configFile);
    }
    
    /**
     * 创建默认配置文件
     */
    private void createDefaultConfig() {
        try {
            // 确保数据文件夹存在
            if (!plugin.getDataFolder().exists()) {
                plugin.getDataFolder().mkdirs();
            }
            
            // 创建配置文件
            configFile.createNewFile();
            
            // 直接写入配置文件内容（兼容旧版本 Bukkit）
            java.io.FileWriter writer = new java.io.FileWriter(configFile);
            writer.write("# ╔══════════════════════════════════════════════════════════════╗\n");
            writer.write("# ║                    🔐 HangZong 许可证配置 🔐                 ║\n");
            writer.write("# ╠══════════════════════════════════════════════════════════════╣\n");
            writer.write("# ║  插件名称: HangEvacuation (摸金插件)                         ║\n");
            writer.write("# ║  开发者: hangzong (航总)                                     ║\n");
            writer.write("# ║  技术支持 QQ: **********                                      ║\n");
            writer.write("# ╚══════════════════════════════════════════════════════════════╝\n");
            writer.write("\n");
            writer.write("# 许可证密钥配置\n");
            writer.write("# 请将下方的空字符串替换为您的真实许可证密钥\n");
            writer.write("# 示例: \"857ff611-618f-40db-8fe2-291b388f0cf0\"\n");
            writer.write("# 如需购买许可证，请联系 QQ: **********\n");
            writer.write("license-key: \"\"\n");
            writer.write("\n");
            writer.write("# ╔══════════════════════════════════════════════════════════════╗\n");
            writer.write("# ║                        ⚠️ 重要提醒 ⚠️                        ║\n");
            writer.write("# ╠══════════════════════════════════════════════════════════════╣\n");
            writer.write("# ║  • 请勿与他人分享您的许可证密钥                               ║\n");
            writer.write("# ║  • 每个许可证密钥都有设备数量限制                             ║\n");
            writer.write("# ║  • 如遇到验证问题，请联系技术支持 QQ: **********               ║\n");
            writer.write("# ║  • 许可证验证需要服务器能够访问互联网                         ║\n");
            writer.write("# ║  • 必须输入有效的许可证密钥才能使用插件                       ║\n");
            writer.write("# ╚══════════════════════════════════════════════════════════════╝\n");
            writer.close();
            
            plugin.getLogger().info("已创建许可证配置文件: license.yml");
            plugin.getLogger().info("请在配置文件中填入您的许可证密钥！");
            
        } catch (IOException e) {
            plugin.getLogger().severe("创建许可证配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 重新加载配置
     */
    public void reload() {
        config = YamlConfiguration.loadConfiguration(configFile);
    }
    
    /**
     * 保存配置
     */
    public void save() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("保存许可证配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 获取许可证密钥
     */
    public String getLicenseKey() {
        return config.getString("license-key", "");
    }

    /**
     * 设置许可证密钥
     */
    public void setLicenseKey(String key) {
        config.set("license-key", key);
        save();
    }
    
    /**
     * 检查许可证密钥是否为空
     */
    public boolean isLicenseKeyEmpty() {
        String key = getLicenseKey();
        return key == null || key.trim().isEmpty();
    }
}
