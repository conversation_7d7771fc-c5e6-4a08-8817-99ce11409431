# 🔧 战利品GUI显示实际方块材质修复

## 🚨 **问题描述**

用户反馈：用沙子创造了摸金箱，但在战利品GUI里面创造的摸金箱的材质显示为箱子而不是沙子。

## 🔍 **问题分析**

### **根本原因**
战利品管理GUI显示的是**摸金箱种类配置**中的材质，而不是**实际摸金箱方块**的材质：

- **摸金箱种类**：在配置文件中定义，有固定的材质（如 CHEST）
- **实际摸金箱**：可以是任何材质的方块（如沙子、石头等）

### **问题流程**
1. 玩家用沙子创建摸金箱
2. 打开 `/evac gui` → 显示摸金箱种类选择界面
3. 选择种类 → 显示配置中的材质（CHEST）
4. 用户看到的是箱子图标，而不是沙子图标

## ✅ **修复方案**

### **1. 增强TreasureManagementGUI构造函数**

添加了支持摸金箱位置的构造函数：

```java
/**
 * 🆕 构造函数：支持指定摸金箱位置，显示实际方块材质
 */
public TreasureManagementGUI(HangPlugin plugin, Player player, String chestType, org.bukkit.Location chestLocation) {
    this.plugin = plugin;
    this.player = player;
    this.chestType = chestType;
    this.chestLocation = chestLocation; // 🆕 保存摸金箱位置
    
    // 设置标题和初始化...
}
```

### **2. 修改摸金箱种类选择GUI显示逻辑**

在种类选择界面中，优先使用实际方块材质：

```java
// 🔧 修复：优先使用实际摸金箱的方块材质，如果没有则使用配置中的材质
org.bukkit.Material displayMaterial = chestType.getMaterial();
if (chestLocation != null) {
    try {
        org.bukkit.Material actualMaterial = chestLocation.getBlock().getType();
        if (actualMaterial != org.bukkit.Material.AIR) {
            displayMaterial = actualMaterial; // 使用实际方块材质
        }
    } catch (Exception e) {
        // 如果获取实际材质失败，使用配置中的材质
    }
}

ItemStack typeItem = new ItemStack(displayMaterial); // 显示实际材质
```

### **3. 在Lore中显示实际方块信息**

添加实际方块材质的显示信息：

```java
// 🆕 显示实际方块材质信息
if (chestLocation != null) {
    try {
        org.bukkit.Material actualMaterial = chestLocation.getBlock().getType();
        if (actualMaterial != org.bukkit.Material.AIR) {
            lore.add("§7实际方块: §a" + actualMaterial.name());
        }
    } catch (Exception e) {
        // 静默处理错误
    }
}
```

### **4. 新增Shift+右键管理功能**

添加了直接从摸金箱打开管理界面的功能：

```java
// 🆕 检查是否为Shift+右键点击，如果是则打开管理界面
if (event.getAction() == Action.RIGHT_CLICK_BLOCK && player.isSneaking() && 
    player.hasPermission("evacuation.admin")) {
    
    event.setCancelled(true);
    
    // 获取摸金箱种类
    String chestType = existingData.getChestType();
    if (chestType == null) {
        chestType = "common";
    }
    
    // 打开特定摸金箱的管理界面，传递位置信息
    TreasureManagementGUI managementGUI = new TreasureManagementGUI(plugin, player, chestType, chestLocation);
    managementGUI.open();
    
    player.sendMessage("§a已打开摸金箱管理界面！");
    player.sendMessage("§7摸金箱位置: " + chestLocation.getBlockX() + ", " + 
        chestLocation.getBlockY() + ", " + chestLocation.getBlockZ());
    player.sendMessage("§7实际方块: §e" + chestLocation.getBlock().getType().name());
    
    return;
}
```

## 🎯 **修复效果**

### **修复前**
- ❌ 战利品GUI中所有摸金箱都显示为配置中的材质（如箱子）
- ❌ 无法区分不同材质的摸金箱
- ❌ 用户困惑：沙子摸金箱显示为箱子图标

### **修复后**
- ✅ 战利品GUI中显示实际方块材质（如沙子）
- ✅ 可以清楚看到摸金箱的真实材质
- ✅ Lore中显示"实际方块: SAND"等信息
- ✅ 支持Shift+右键直接打开管理界面

## 🎮 **使用方法**

### **方法1：通过命令打开（通用管理）**
```bash
/evac gui
# 显示所有摸金箱种类，使用配置中的材质
```

### **方法2：通过摸金箱直接打开（显示实际材质）** 🆕
```bash
# 1. 找到您创建的摸金箱（如沙子摸金箱）
# 2. 按住Shift键 + 右键点击摸金箱
# 3. 自动打开该摸金箱的管理界面
# 4. 界面中显示实际方块材质（沙子图标）
```

### **显示效果对比**

#### **沙子摸金箱示例**
```
修复前：
┌─────────────────┐
│  [箱子图标]     │  ← 显示配置中的材质
│  §6普通摸金箱   │
│  §7种类ID: common │
└─────────────────┘

修复后：
┌─────────────────┐
│  [沙子图标]     │  ← 显示实际方块材质 ✅
│  §6普通摸金箱   │
│  §7种类ID: common │
│  §7实际方块: §aSAND │  ← 新增信息 ✅
└─────────────────┘
```

## 🔧 **技术细节**

### **修复位置**
1. **TreasureManagementGUI.java** - 添加位置参数支持
2. **PlayerListener.java** - 添加Shift+右键管理功能
3. **摸金箱种类选择GUI** - 修改材质显示逻辑

### **兼容性**
- ✅ **向后兼容**：原有的 `/evac gui` 命令仍然正常工作
- ✅ **功能扩展**：新增Shift+右键直接管理功能
- ✅ **材质支持**：支持任何材质的摸金箱显示
- ✅ **权限控制**：需要 `evacuation.admin` 权限

### **错误处理**
- 如果获取实际材质失败，自动降级到配置材质
- 如果摸金箱位置为空，使用配置中的默认材质
- 静默处理所有异常，确保GUI正常显示

## 📦 **部署信息**

- **修复版本**：HangEvacuation-Universal-1.9.9.jar
- **修复类型**：功能增强 + 用户体验改进
- **配置变更**：无需修改配置文件
- **使用说明**：支持两种打开方式

## 🎯 **总结**

现在您可以：
1. **看到真实材质**：沙子摸金箱在GUI中显示为沙子图标
2. **快速管理**：Shift+右键直接打开特定摸金箱的管理界面
3. **清楚识别**：Lore中显示实际方块材质信息
4. **保持兼容**：原有功能完全保留

这个修复让摸金箱管理更加直观和用户友好！🎉✨
