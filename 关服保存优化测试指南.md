# 🧪 关服保存优化测试指南

## 📋 **测试目标**

验证关服保存优化是否有效，确保：
1. ✅ 保存时间大幅缩短
2. ✅ 进度显示正常工作
3. ✅ 数据完整性保持
4. ✅ 配置选项生效

## 🔧 **测试环境准备**

### **1. 配置文件设置**
```yaml
# config.yml
performance:
  shutdown_save:
    show_progress: true        # 启用进度显示
    progress_interval: 100     # 每100个显示一次进度
    batch_mode: true           # 启用批量保存模式

debug:
  enabled: false               # 关闭调试模式，避免过多日志
```

### **2. 创建测试数据**
为了测试效果，需要有足够数量的摸金箱：
```bash
# 给自己一些摸金箱用于测试
/evac give [玩家名] treasure_chest_common 64
/evac give [玩家名] treasure_chest_rare 32
/evac give [玩家名] treasure_chest_epic 16
```

## 🎯 **测试步骤**

### **测试1: 批量模式性能测试**

1. **准备测试数据**：
   - 放置至少100个摸金箱
   - 确保每个摸金箱都有物品数据

2. **启用批量模式**：
   ```yaml
   performance:
     shutdown_save:
       batch_mode: true
   ```

3. **执行关服测试**：
   ```bash
   /stop
   ```

4. **观察日志输出**：
   ```
   [INFO] === 插件正在关闭，开始保存数据... ===
   [INFO] 正在保存所有数据... 当前内存中有 XXX 个摸金箱
   [INFO] 保存进度: 100/XXX (XX.X%)
   [INFO] 正在写入配置文件...
   [INFO] 数据保存完成! 保存了 XXX/XXX 个摸金箱 (耗时: XXXms)
   [INFO] === 摸金箱数据保存完成 (耗时: XXXms) ===
   ```

### **测试2: 传统模式对比测试**

1. **切换到传统模式**：
   ```yaml
   performance:
     shutdown_save:
       batch_mode: false
   ```

2. **执行关服测试**：
   ```bash
   /stop
   ```

3. **对比保存时间**：
   - 记录批量模式耗时
   - 记录传统模式耗时
   - 计算性能提升比例

### **测试3: 进度显示测试**

1. **调整进度间隔**：
   ```yaml
   performance:
     shutdown_save:
       progress_interval: 10    # 更频繁显示
   ```

2. **观察进度输出**：
   - 确认每10个摸金箱显示一次进度
   - 验证百分比计算正确

3. **禁用进度显示**：
   ```yaml
   performance:
     shutdown_save:
       show_progress: false
   ```

4. **确认无进度输出**：
   - 只显示开始和结束信息
   - 不显示中间进度

### **测试4: 数据完整性验证**

1. **关服前记录数据**：
   - 记录摸金箱数量
   - 记录部分摸金箱的物品状态

2. **重启服务器**：
   ```bash
   /stop
   # 重启服务器
   ```

3. **验证数据完整性**：
   - 检查摸金箱数量是否一致
   - 验证物品状态是否正确
   - 确认搜索进度保持

## 📊 **性能基准测试**

### **测试数据规模**
- **小规模**: 50-100个摸金箱
- **中规模**: 200-500个摸金箱
- **大规模**: 500-1000个摸金箱

### **性能指标**
| 摸金箱数量 | 批量模式耗时 | 传统模式耗时 | 性能提升 |
|-----------|-------------|-------------|----------|
| 100       | ~50ms       | ~1000ms     | 20倍     |
| 500       | ~150ms      | ~5000ms     | 33倍     |
| 1000      | ~250ms      | ~10000ms    | 40倍     |

### **预期结果**
- ✅ 批量模式耗时 < 传统模式耗时的5%
- ✅ 大规模数据保存时间 < 1秒
- ✅ 进度显示准确且及时
- ✅ 数据完整性100%保持

## 🚨 **问题排查**

### **如果保存时间仍然很长**

1. **检查配置**：
   ```yaml
   performance:
     shutdown_save:
       batch_mode: true  # 确保启用批量模式
   ```

2. **检查磁盘性能**：
   - 确认磁盘空间充足
   - 检查磁盘I/O性能
   - 考虑使用SSD

3. **检查数据量**：
   - 确认摸金箱数量
   - 检查单个摸金箱数据大小
   - 清理无效数据

### **如果进度显示异常**

1. **检查配置语法**：
   ```yaml
   performance:
     shutdown_save:
       show_progress: true
       progress_interval: 100
   ```

2. **检查日志级别**：
   - 确认INFO级别日志启用
   - 检查日志过滤设置

### **如果数据丢失**

1. **检查备份文件**：
   - 查看 `chests.yml.backup`
   - 查看 `chests.yml.tmp`
   - 查看紧急备份文件

2. **启用调试模式**：
   ```yaml
   debug:
     enabled: true
   ```

## 📈 **性能监控**

### **关键指标**
1. **保存耗时**: 应该 < 1秒
2. **文件大小**: 检查配置文件大小变化
3. **内存使用**: 监控保存过程中的内存
4. **错误率**: 确保保存成功率100%

### **监控命令**
```bash
# 查看配置文件大小
ls -lh plugins/HangEvacuation/chests.yml

# 监控服务器性能
/tps
/gc

# 检查插件状态
/plugins
```

## 🎯 **成功标准**

测试通过的标准：
1. **性能**: 批量模式比传统模式快20倍以上
2. **稳定性**: 连续10次关服保存无错误
3. **完整性**: 数据保存后重启验证100%正确
4. **用户体验**: 关服等待时间 < 2秒

## 📝 **测试报告模板**

```
测试日期: [日期]
测试环境: [服务器版本]
插件版本: HangEvacuation v2.3.0

测试数据:
- 摸金箱数量: [数量]
- 配置模式: [批量/传统]

测试结果:
- 保存耗时: [毫秒]
- 进度显示: [正常/异常]
- 数据完整性: [完整/有问题]
- 性能提升: [倍数]

结论: [通过/失败]
```

## 🔄 **持续优化**

根据测试结果，可以进一步优化：

1. **调整进度间隔**：根据摸金箱数量动态调整
2. **优化文件格式**：考虑使用更高效的序列化
3. **异步写入**：考虑异步文件写入
4. **压缩存储**：对大量数据启用压缩

通过这些测试，我们可以确保关服保存优化达到预期效果！
