# HangEvacuation 版本更新 - v2.1.0

## 🎯 **版本信息**

- **插件名称**: HangEvacuation (Hang摸金撤离)
- **新版本号**: 2.1.0
- **更新日期**: 2024-12-18
- **兼容版本**: Minecraft 1.8.8-1.21.4
- **作者**: hangzong(航总)
- **技术支持**: 微信 hang060217

## 🆕 **v2.1.0 新增功能**

### **1. 启动时浮空字自动清理**
- ✨ **自动清理遗留浮空字**：服务器启动时自动清除所有摸金插件相关的浮空字
- 🎯 **智能识别**：精确识别摸金插件浮空字，不影响其他插件
- ⚙️ **可配置控制**：可通过配置文件启用/禁用此功能
- 🌍 **全世界支持**：自动扫描所有已加载的世界

### **2. 增强的版本兼容性**
- 🔧 **1.16.x特殊处理**：针对1.16版本的浮空字移除优化
- 🛡️ **LinkageError修复**：解决1.12.2版本中的类加载错误
- 🎵 **音效兼容性**：修复1.12.2版本中音效无法播放的问题

### **3. 日志系统优化**
- 📊 **减少刷屏**：将验证日志移到调试模式下
- 🔍 **详细反馈**：启动清理过程提供详细信息
- 🎯 **智能日志**：只在必要时显示警告和错误

## 🔧 **配置文件更新**

### **新增配置项**
```yaml
treasure-chest:
  hologram:
    # 启动时清理设置
    startup_cleanup:
      enabled: true               # 是否在服务器启动时清除遗留的摸金插件浮空字
      delay_seconds: 1           # 延迟多少秒后开始清理 (确保所有世界都已加载)
```

### **保留的配置项**
```yaml
treasure-chest:
  hologram:
    auto_remove_distance: 32.0    # 玩家远离摸金箱多少格时自动删除浮空字 (防止浮空字异常)
```

## 🐛 **修复的问题**

### **1. LinkageError错误修复**
- **问题**: 1.12.2版本中出现`java.lang.LinkageError: com/hang/plugin/manager/ChestTypeManager`
- **修复**: 添加异常处理和降级检测机制
- **影响**: 确保插件在1.12.2版本中稳定运行

### **2. 音效兼容性修复**
- **问题**: 1.12.2版本中`entity.experience_orb.pickup`和`entity.item.pickup`音效无法播放
- **修复**: 添加版本检测和音效名称映射
- **影响**: 1.12.2版本中音效功能完全正常

### **3. 浮空字遗留问题修复**
- **问题**: 服务器重启后可能遗留浮空字实体
- **修复**: 启动时自动清理所有摸金插件浮空字
- **影响**: 彻底解决浮空字异常和重复显示问题

### **4. 日志刷屏问题修复**
- **问题**: 控制台频繁显示"配置验证"和"数据验证完成"信息
- **修复**: 将这些日志移到调试模式下
- **影响**: 控制台日志更加简洁

## 🚀 **性能优化**

### **1. 启动优化**
- ⚡ **延迟清理**：可配置的延迟时间，确保所有世界加载完成
- 🎯 **智能检测**：高效的浮空字识别算法
- 🛡️ **异常隔离**：单个实体处理失败不影响整体清理

### **2. 内存优化**
- 🧹 **自动清理**：及时清理无用的浮空字实体
- 📊 **统计信息**：提供详细的清理统计
- 🔄 **智能重建**：需要时自动重建浮空字

## 📋 **升级指南**

### **从v2.0.0升级到v2.1.0**

1. **备份数据**：
   ```
   - 备份 plugins/HangEvacuation/ 整个文件夹
   - 备份服务器存档（可选）
   ```

2. **停止服务器**：
   ```
   - 正常关闭服务器
   - 确保所有数据已保存
   ```

3. **替换插件**：
   ```
   - 删除旧的 HangEvacuation-Universal-2.0.0.jar
   - 放入新的 HangEvacuation-Universal-2.1.0.jar
   ```

4. **启动服务器**：
   ```
   - 启动服务器
   - 观察启动日志，确认版本为2.1.0
   - 检查浮空字清理日志
   ```

5. **验证功能**：
   ```
   - 测试摸金箱功能
   - 检查浮空字显示
   - 验证音效播放（1.12.2版本）
   ```

### **配置文件兼容性**
- ✅ **完全兼容**：现有配置文件无需修改
- ✅ **自动添加**：新配置项会自动添加默认值
- ✅ **向后兼容**：保留所有现有功能

## 🎮 **新功能使用说明**

### **启动时浮空字清理**

**默认行为**：
- 服务器启动1秒后自动开始清理
- 扫描所有已加载的世界
- 只清理摸金插件相关的浮空字
- 提供详细的清理日志

**自定义配置**：
```yaml
# 禁用启动清理
startup_cleanup:
  enabled: false

# 延长清理延迟（适用于大型服务器）
startup_cleanup:
  delay_seconds: 5
```

**日志示例**：
```
[INFO] 🧹 开始清除遗留的摸金插件浮空字...
[INFO]   世界 world: 清除了 3 个浮空字
[INFO] ✅ 浮空字清除完成: 共检查 3 个世界，清除了 3 个遗留浮空字
```

## 🔍 **故障排除**

### **常见问题**

**Q: 启动时没有看到清理日志？**
A: 检查配置文件中 `startup_cleanup.enabled` 是否为 `true`

**Q: 1.12.2版本音效仍然无法播放？**
A: 确认使用的是v2.1.0版本，并重启服务器

**Q: LinkageError错误仍然存在？**
A: 完全重启服务器，确保使用新版本插件

**Q: 浮空字被误删？**
A: 检查被删除的浮空字是否符合摸金插件特征，可以临时禁用启动清理

### **调试模式**
启用调试模式查看详细信息：
```yaml
debug:
  enabled: true
```

## 📊 **版本对比**

| 功能 | v2.0.0 | v2.1.0 |
|------|--------|--------|
| 基础摸金箱功能 | ✅ | ✅ |
| 浮空字系统 | ✅ | ✅ |
| 1.12.2音效支持 | ❌ | ✅ |
| LinkageError修复 | ❌ | ✅ |
| 启动时浮空字清理 | ❌ | ✅ |
| 日志优化 | ❌ | ✅ |
| 版本兼容性 | 1.8.8-1.21.4 | 1.8.8-1.21.4 |

## 🎉 **总结**

v2.1.0版本主要专注于**稳定性提升**和**用户体验优化**：

- 🛡️ **更稳定**：修复了1.12.2版本的关键错误
- 🧹 **更干净**：自动清理遗留浮空字，避免异常
- 📊 **更简洁**：优化日志输出，减少刷屏
- 🎵 **更完整**：全版本音效支持

这是一个**强烈推荐的更新版本**，特别是对于使用1.12.2版本的服务器！

---

**技术支持**: 微信 hang060217  
**交流Q群**: 361919269  
**插件作者**: hangzong(航总)
