# 智能浮空字距离管理系统

## 🎯 **功能概述**

为了彻底解决浮空字异常残留问题，新增了基于玩家距离的智能浮空字管理系统。当玩家远离摸金箱超过指定距离时，系统会自动删除浮空字，防止浮空字异常堆积。

## ⚙️ **核心特性**

### **1. 自动距离检测**
- **实时监控**：浮空字更新任务会实时检测玩家与摸金箱的距离
- **智能清理**：当没有玩家在指定距离内时，自动删除浮空字
- **动态重建**：当玩家重新接近时，自动重建浮空字

### **2. 双重距离机制**
- **自动移除距离**：`auto_remove_distance` (默认32格) - 超过此距离自动删除浮空字
- **检测重建距离**：`player_detection_range` (默认64格) - 在此距离内重建浮空字

### **3. 性能优化**
- **提前退出**：一旦找到符合条件的玩家，立即停止检测
- **批量处理**：在单次循环中同时处理删除和重建逻辑
- **调试日志**：可选的详细日志记录，便于问题排查

## 🔧 **配置设置**

### **config.yml 配置**
```yaml
treasure-chest:
  hologram:
    # 🆕 智能距离管理
    auto_remove_distance: 32.0    # 玩家远离摸金箱多少格时自动删除浮空字
    
    # 区块保护设置
    chunk_protection:
      player_detection_range: 64.0  # 玩家检测范围 (用于重建浮空字)
```

### **配置说明**
- `auto_remove_distance`: 自动删除距离，建议设置为32格
- `player_detection_range`: 检测重建距离，建议设置为64格
- 重建距离应该大于删除距离，避免频繁创建/删除

## 🎮 **管理命令**

### **1. 查看浮空字状态**
```
/evac hologram stats
```
显示当前浮空字的统计信息，包括活跃、死亡、丢失和备份数量。

### **2. 基于距离清理浮空字**
```
/evac hologram distance [距离]
```
- **示例**：`/evac hologram distance 32` - 清理距离玩家超过32格的浮空字
- **默认距离**：32格
- **距离范围**：1-200格

### **3. 清理孤立浮空字**
```
/evac hologram cleanup
```
清理没有对应摸金箱数据的孤立浮空字。

### **4. 强制重建所有浮空字**
```
/evac hologram rebuild
```
强制重建所有浮空字，用于修复异常状态。

## 🔍 **工作原理**

### **自动清理流程**
```
1. 浮空字更新任务每20tick执行一次
2. 遍历所有摸金箱位置
3. 检查是否有玩家在auto_remove_distance范围内
4. 如果没有玩家在范围内：
   - 删除现有浮空字
   - 记录调试日志
5. 如果有玩家在player_detection_range范围内：
   - 重建丢失的浮空字
   - 更新现有浮空字文本
```

### **距离检测优化**
```java
// 同时检测两个距离条件
for (Player onlinePlayer : plugin.getServer().getOnlinePlayers()) {
    if (onlinePlayer.getWorld().equals(world)) {
        double distance = onlinePlayer.getLocation().distance(location);
        if (distance <= detectionRange) {
            hasPlayersNearby = true;
        }
        if (distance <= autoRemoveDistance) {
            hasPlayersInRemoveRange = true;
        }
        // 提前退出优化
        if (hasPlayersNearby && hasPlayersInRemoveRange) {
            break;
        }
    }
}
```

## 📊 **效果对比**

### **修复前的问题**
- ❌ 浮空字残留不清理
- ❌ 玩家离开后浮空字仍然存在
- ❌ 浮空字重复创建导致堆积
- ❌ 服务器性能受影响

### **修复后的效果**
- ✅ 玩家远离32格自动删除浮空字
- ✅ 玩家接近64格自动重建浮空字
- ✅ 防止浮空字重复创建
- ✅ 智能性能优化
- ✅ 完整的管理命令支持

## 🚀 **使用建议**

### **1. 推荐配置**
```yaml
treasure-chest:
  hologram:
    auto_remove_distance: 32.0      # 删除距离：32格
    chunk_protection:
      player_detection_range: 64.0  # 重建距离：64格
```

### **2. 日常维护**
- 定期使用 `/evac hologram stats` 检查浮空字状态
- 如发现异常，使用 `/evac hologram cleanup` 清理孤立浮空字
- 必要时使用 `/evac hologram distance 32` 手动清理远距离浮空字

### **3. 调试模式**
```yaml
debug:
  enabled: true  # 启用调试日志
```
启用后可以在控制台看到详细的浮空字管理日志。

## 🔧 **技术实现**

### **核心修改文件**
1. **PlayerListener.java** - 浮空字更新任务中添加距离检测
2. **HologramManager.java** - 新增基于距离的清理方法
3. **HangCommand.java** - 新增距离清理命令
4. **config.yml** - 新增配置选项

### **关键代码片段**
```java
// 智能距离检测
double autoRemoveDistance = plugin.getConfig().getDouble("treasure-chest.hologram.auto_remove_distance", 32.0);

// 如果浮空字存在但没有玩家在移除距离内，删除浮空字
if (plugin.getHologramManager().hasHologram(location)) {
    if (!hasPlayersInRemoveRange) {
        plugin.getHologramManager().removeHologram(location);
        // 记录调试日志
    }
}
```

## 📋 **总结**

这个智能浮空字距离管理系统通过以下方式彻底解决了浮空字异常问题：

1. **预防性清理**：主动删除远离玩家的浮空字
2. **智能重建**：玩家接近时自动重建浮空字
3. **性能优化**：高效的距离检测算法
4. **管理工具**：完整的命令行管理界面
5. **可配置性**：灵活的距离参数设置

现在您可以放心使用摸金箱系统，不再担心浮空字异常残留的问题！
