package com.hang.plugin.utils;

import org.bukkit.inventory.ItemStack;
import org.bukkit.util.io.BukkitObjectInputStream;
import org.bukkit.util.io.BukkitObjectOutputStream;
import org.yaml.snakeyaml.external.biz.base64Coder.Base64Coder;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

/**
 * 物品序列化工具类
 * 用于完整保存和恢复ItemStack，包括所有NBT数据
 * 
 * <AUTHOR>
 */
public class ItemSerializer {
    
    private static final Logger logger = Logger.getLogger("HangEvacuation");
    
    /**
     * 将ItemStack序列化为Base64字符串
     * 完整保留所有数据，包括NBT标签
     * 增强版本兼容性
     */
    public static String serializeItemStack(ItemStack item) {
        if (item == null) {
            return null;
        }

        try {
            // 优先使用版本兼容的序列化方法
            return serializeItemStackCompatible(item);

        } catch (Exception e) {
            logger.warning("序列化物品失败: " + e.getMessage());
            return null;
        }
    }

    /**
     * 版本兼容的物品序列化
     */
    public static String serializeItemStackCompatible(ItemStack item) {
        if (item == null) {
            return null;
        }

        try {

            // 优先使用新版本的序列化方法
            if (com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 20)) {
                return serializeItemStack_1_20(item);
            } else if (com.hang.plugin.utils.VersionUtils.isVersionAtLeast(1, 13)) {
                return serializeItemStack_1_13(item);
            } else {
                return serializeItemStack_Legacy(item);
            }
        } catch (Exception e) {
            // 降级到基础序列化
            return serializeItemStack_Legacy(item);
        }
    }

    /**
     * 1.20+ 版本的序列化方法
     */
    private static String serializeItemStack_1_20(ItemStack item) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);

            // 添加版本标识
            dataOutput.writeUTF("HANG_ITEM_1_20");
            dataOutput.writeObject(item);
            dataOutput.close();

            return Base64Coder.encodeLines(outputStream.toByteArray());

        } catch (Exception e) {
            throw new RuntimeException("1.20版本序列化失败", e);
        }
    }

    /**
     * 1.13+ 版本的序列化方法
     */
    private static String serializeItemStack_1_13(ItemStack item) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);

            // 添加版本标识
            dataOutput.writeUTF("HANG_ITEM_1_13");
            dataOutput.writeObject(item);
            dataOutput.close();

            return Base64Coder.encodeLines(outputStream.toByteArray());

        } catch (Exception e) {
            throw new RuntimeException("1.13版本序列化失败", e);
        }
    }

    /**
     * 传统版本的序列化方法 (1.8-1.12)
     */
    private static String serializeItemStack_Legacy(ItemStack item) {
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);

            // 添加版本标识
            dataOutput.writeUTF("HANG_ITEM_LEGACY");
            dataOutput.writeObject(item);
            dataOutput.close();

            return Base64Coder.encodeLines(outputStream.toByteArray());

        } catch (Exception e) {
            throw new RuntimeException("传统版本序列化失败", e);
        }
    }
    
    /**
     * 从Base64字符串反序列化ItemStack
     * 完整恢复所有数据，包括NBT标签
     * 增强版本兼容性
     */
    public static ItemStack deserializeItemStack(String data) {
        if (data == null || data.trim().isEmpty()) {
            return null;
        }

        try {
            byte[] bytes = Base64Coder.decodeLines(data);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            BukkitObjectInputStream dataInput = new BukkitObjectInputStream(inputStream);

            // 检查版本标识
            try {
                String versionMarker = dataInput.readUTF();
                ItemStack item;

                switch (versionMarker) {
                    case "HANG_ITEM_1_20":
                        item = (ItemStack) dataInput.readObject();
                        break;
                    case "HANG_ITEM_1_13":
                        item = (ItemStack) dataInput.readObject();
                        break;
                    case "HANG_ITEM_LEGACY":
                        item = (ItemStack) dataInput.readObject();
                        break;
                    default:
                        // 可能是旧格式，重新读取
                        dataInput.close();
                        inputStream = new ByteArrayInputStream(bytes);
                        dataInput = new BukkitObjectInputStream(inputStream);
                        item = (ItemStack) dataInput.readObject();
                        break;
                }

                dataInput.close();



                return item;

            } catch (Exception e) {
                // 如果读取版本标识失败，尝试直接反序列化（兼容旧格式）
                dataInput.close();
                inputStream = new ByteArrayInputStream(bytes);
                dataInput = new BukkitObjectInputStream(inputStream);

                ItemStack item = (ItemStack) dataInput.readObject();
                dataInput.close();



                return item;
            }

        } catch (Exception e) {
            logger.warning("反序列化物品失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 序列化ItemStack数组
     */
    public static List<String> serializeItemStackArray(ItemStack[] items) {
        List<String> serializedItems = new ArrayList<>();
        
        if (items != null) {
            for (ItemStack item : items) {
                serializedItems.add(serializeItemStack(item));
            }
        }
        
        return serializedItems;
    }
    
    /**
     * 反序列化ItemStack数组
     */
    public static ItemStack[] deserializeItemStackArray(List<String> serializedItems) {
        if (serializedItems == null) {
            return new ItemStack[0];
        }
        
        ItemStack[] items = new ItemStack[serializedItems.size()];
        
        for (int i = 0; i < serializedItems.size(); i++) {
            items[i] = deserializeItemStack(serializedItems.get(i));
        }
        
        return items;
    }
    
    /**
     * 检查序列化数据是否有效
     */
    public static boolean isValidSerializedData(String data) {
        if (data == null || data.trim().isEmpty()) {
            return false;
        }
        
        try {
            ItemStack item = deserializeItemStack(data);
            return item != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 增强：获取物品的详细描述（用于日志和调试）
     */
    public static String getItemDescription(ItemStack item) {
        if (item == null) {
            return "null";
        }

        StringBuilder desc = new StringBuilder();
        desc.append(item.getType().name());
        desc.append(" x").append(item.getAmount());

        // 自定义名称
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            desc.append(" (").append(item.getItemMeta().getDisplayName()).append(")");
        }

        // 附魔信息
        if (!item.getEnchantments().isEmpty()) {
            desc.append(" [附魔: ").append(item.getEnchantments().size()).append("个]");
        }

        // 模组物品标识
        if (hasModData(item)) {
            desc.append(" [模组物品]");
        }

        // Lore信息
        if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
            desc.append(" [Lore: ").append(item.getItemMeta().getLore().size()).append("行]");
        }

        return desc.toString();
    }
    
    /**
     * 比较两个ItemStack是否相同（包括NBT）
     */
    public static boolean isSimilar(ItemStack item1, ItemStack item2) {
        if (item1 == null && item2 == null) {
            return true;
        }
        
        if (item1 == null || item2 == null) {
            return false;
        }
        
        // 使用序列化比较确保NBT数据也被考虑
        String serialized1 = serializeItemStack(item1);
        String serialized2 = serializeItemStack(item2);
        
        return serialized1 != null && serialized1.equals(serialized2);
    }
    
    /**
     * 克隆ItemStack（深拷贝，包括NBT）
     */
    public static ItemStack cloneItemStack(ItemStack original) {
        if (original == null) {
            return null;
        }
        
        String serialized = serializeItemStack(original);
        return deserializeItemStack(serialized);
    }
    
    /**
     * 检查ItemStack是否包含模组数据
     */
    public static boolean hasModData(ItemStack item) {
        if (item == null) {
            return false;
        }

        // 修复：对于所有有复杂NBT数据的物品，都认为是需要序列化的物品
        // 这样可以确保电池护盾等模组物品被正确处理

        // 首先检查序列化数据长度，如果很长说明有复杂NBT
        try {
            String serialized = serializeItemStack(item);
            if (serialized != null && serialized.length() > 500) {
                // 序列化数据较长，可能包含复杂NBT，强制认为是模组物品
                return true;
            }
        } catch (Exception e) {
            // 序列化失败，可能是复杂物品，保险起见认为是模组物品
            return true;
        }

        // 检查物品是否有自定义名称包含模组特征
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            String displayName = item.getItemMeta().getDisplayName();
            // 检查是否包含模组特征词汇
            if (displayName.contains("电池") || displayName.contains("护盾") ||
                displayName.contains("Battery") || displayName.contains("Shield") ||
                displayName.contains("Mod") || displayName.contains("模组")) {
                return true;
            }
        }

        // 检查是否有复杂的Lore（可能包含模组信息）
        if (item.hasItemMeta() && item.getItemMeta().hasLore()) {
            List<String> lore = item.getItemMeta().getLore();
            if (lore.size() > 3) { // 超过3行Lore，可能是模组物品
                for (String line : lore) {
                    if (line.contains("护盾") || line.contains("Shield") ||
                        line.contains("电池") || line.contains("Battery") ||
                        line.contains("能量") || line.contains("Energy") ||
                        line.contains("[") || line.contains("/")) { // 包含数值显示
                        return true;
                    }
                }
            }
        }

        // 检查材料名称特征
        String materialName = item.getType().name();
        if (materialName.contains(":") && !materialName.startsWith("minecraft:")) {
            return true;
        }

        if (materialName.startsWith("MOD_") ||
            materialName.startsWith("FORGE_") ||
            materialName.startsWith("FABRIC_") ||
            materialName.length() > 50) {
            return true;
        }

        // 增强：检查是否有附魔或特殊属性
        if (!item.getEnchantments().isEmpty()) {
            return true; // 有附魔的物品也需要序列化保存
        }

        // 对于其他情况，认为是普通物品
        return false;
    }
    
    /**
     * 压缩序列化数据（可选，用于减少存储空间）
     */
    public static String compressSerializedData(String data) {
        // 这里可以添加压缩算法，暂时直接返回
        return data;
    }
    
    /**
     * 解压序列化数据
     */
    public static String decompressSerializedData(String compressedData) {
        // 这里可以添加解压算法，暂时直接返回
        return compressedData;
    }


}
