package com.hang.plugin;

import com.hang.plugin.commands.HangCommand;
import com.hang.plugin.listeners.PlayerListener;
import com.hang.plugin.listeners.ChatListener;
import com.hang.plugin.manager.ChestManager;
import com.hang.plugin.manager.ChestTypeManager;
import com.hang.plugin.manager.TreasureItemManager;
import com.hang.plugin.manager.LevelManager;
import com.hang.plugin.manager.CountdownManager;
import com.hang.plugin.nms.NMSManager;
import com.hang.plugin.placeholders.LevelPlaceholderExpansion;
import com.hang.plugin.system.EvacuationSystem;
import com.hang.plugin.utils.VersionUtils;
import com.hang.plugin.config.LicenseConfig;
import com.hang.plugin.license.LicenseManager;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * 摸金插件主类
 *
 * <AUTHOR>
 * @version 1.9.2
 */
public class HangPlugin extends JavaPlugin {

    private static HangPlugin instance;
    private TreasureItemManager treasureItemManager;
    private PlayerListener playerListener;
    private ChatListener chatListener;

    private ChestManager chestManager;
    private ChestTypeManager chestTypeManager;
    private NMSManager nmsManager;
    private com.hang.plugin.manager.HologramManager hologramManager;
    private com.hang.plugin.manager.MemoryCacheManager memoryCacheManager;
    private com.hang.plugin.manager.ChestReplacementManager chestReplacementManager;

    // 新增的管理器
    private LevelManager levelManager;
    private CountdownManager countdownManager;
    private EvacuationSystem evacuationSystem;
    private LevelPlaceholderExpansion placeholderExpansion;

    // 许可证系统
    private LicenseConfig licenseConfig;
    private LicenseManager licenseManager;

    private int autoSaveTaskId = -1;
    private int autoRefreshTaskId = -1;

    @Override
    public void onEnable() {
        instance = this;

        // 初始化许可证系统
        getLogger().info("╔══════════════════════════════════════════════════════════════╗");
        getLogger().info("║                    🔐 HangZong 许可证系统 🔐                 ║");
        getLogger().info("╠══════════════════════════════════════════════════════════════╣");
        getLogger().info("║  🚀 正在启动 HangZong 许可证验证...                          ║");
        getLogger().info("║  🌐 验证服务器: cn.HangZong.com                              ║");
        getLogger().info("║  📞 技术支持 QQ: **********                                   ║");
        getLogger().info("╚══════════════════════════════════════════════════════════════╝");

        // 初始化许可证配置
        licenseConfig = new LicenseConfig(this);

        // 验证许可证
        if (!initializeLicense()) {
            return; // 许可证验证失败，插件已被禁用
        }

        // 版本信息将在showStartupInfo()中显示

        // 初始化NMS适配器
        nmsManager = new NMSManager(this);
        if (!nmsManager.initialize()) {
            getLogger().warning("NMS适配器初始化失败，插件将以兼容模式运行");
        }

        // 保存默认配置文件
        try {
            saveDefaultConfig();
        } catch (Exception e) {
            getLogger().warning("保存默认配置文件失败: " + e.getMessage());
        }

        // 初始化系统
        initializeSystems();

        // 注册命令
        registerCommands();

        // 注册事件监听器
        registerListeners();

        // 启动定期保存任务
        startAutoSaveTask();

        // 启动自动刷新检查任务
        startAutoRefreshCheckTask();

        // 显示详细的启动信息
        showStartupInfo();
    }

    @Override
    public void onDisable() {
        getLogger().info("=== 插件正在关闭，开始保存数据... ===");

        try {
            // 关闭许可证管理器
            if (licenseManager != null) {
                licenseManager.shutdown();
            }
        } catch (Exception e) {
            getLogger().warning("关闭许可证管理器时出错: " + e.getMessage());
        }

        // 优化：使用内存缓存系统
        if (memoryCacheManager != null) {
            try {
                long startTime = System.currentTimeMillis();

                // 优化：强制保存所有内存缓存数据到文件
                memoryCacheManager.flushDirtyData();

                long duration = System.currentTimeMillis() - startTime;
                getLogger().info("=== 内存缓存数据保存完成 (耗时: " + duration + "ms) ===");

                // 显示缓存统计信息
                getLogger().info("=== " + memoryCacheManager.getCacheStats() + " ===");
            } catch (Exception e) {
                getLogger().severe("保存内存缓存数据时出错: " + e.getMessage());
                e.printStackTrace();
            }
        } else if (chestManager != null && playerListener != null) {
            // 降级到传统保存方式
            try {
                long startTime = System.currentTimeMillis();

                playerListener.saveAllChestDataToFile();

                long duration = System.currentTimeMillis() - startTime;
                getLogger().info("=== 摸金箱数据保存完成 (耗时: " + duration + "ms) ===");
            } catch (Exception e) {
                getLogger().severe("保存摸金箱数据时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 保存等级数据
        if (levelManager != null) {
            try {
                levelManager.savePlayerData();
                getLogger().info("=== 等级数据保存完成 ===");
            } catch (Exception e) {
                getLogger().severe("保存等级数据时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }

        // 优化：首先停止PlayerListener的异步任务
        if (playerListener != null) {
            playerListener.shutdown();
        }

        // 停止倒计时管理器
        if (countdownManager != null) {
            countdownManager.shutdown();
        }

        // 注销PlaceholderAPI扩展
        if (placeholderExpansion != null) {
            placeholderExpansion.unregister();
        }

        // 清理资源
        if (memoryCacheManager != null) {
            memoryCacheManager.cleanup();
        }

        if (chestManager != null) {
            chestManager.cleanup();
        }

        if (hologramManager != null) {
            hologramManager.cleanup();
        }

        if (nmsManager != null) {
            nmsManager.cleanup();
        }

        // 停止自动保存任务
        if (autoSaveTaskId != -1) {
            getServer().getScheduler().cancelTask(autoSaveTaskId);
        }

        // 停止自动刷新任务
        if (autoRefreshTaskId != -1) {
            getServer().getScheduler().cancelTask(autoRefreshTaskId);
        }

        getLogger().info("=== HangEvacuation " + getDescription().getVersion() + "-1.8.8-1.21.4 已禁用! ===");
        getLogger().info("感谢使用Hang系列插件 | 技术支持V: hang060217");
    }

    /**
     * 初始化系统
     */
    private void initializeSystems() {
        // 按照依赖关系正确的初始化顺序
        chestTypeManager = new ChestTypeManager(this);  // 先初始化摸金箱种类管理器
        treasureItemManager = new TreasureItemManager(this);  // 再初始化物品管理器

        chestManager = new ChestManager(this);
        memoryCacheManager = new com.hang.plugin.manager.MemoryCacheManager(this);
        chestReplacementManager = new com.hang.plugin.manager.ChestReplacementManager(this);
        playerListener = new PlayerListener(this);
        hologramManager = new com.hang.plugin.manager.HologramManager(this);

        // 初始化新功能
        levelManager = new LevelManager(this);
        countdownManager = new CountdownManager(this);
        evacuationSystem = new EvacuationSystem(this);
        chatListener = new ChatListener(this);

        // 初始化PlaceholderAPI扩展
        if (getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
            placeholderExpansion = new LevelPlaceholderExpansion(this);
            if (!placeholderExpansion.register()) {
                getLogger().warning("PlaceholderAPI扩展注册失败");
            }
            // 简化日志：成功时不显示信息
        }
        // 简化日志：未检测到PlaceholderAPI时不显示信息

        // 延迟加载摸金箱数据，确保所有组件都已初始化
        // 修复：从配置文件读取延迟时间
        long delayTicks = getConfig().getLong("treasure-chest.hologram.startup_cleanup.delay_seconds", 1) * 20L;

        getServer().getScheduler().runTaskLater(this, new Runnable() {
            @Override
            public void run() {
                try {
                    // 新增：启动时清除所有遗留的摸金插件浮空字（可配置）
                    if (getConfig().getBoolean("treasure-chest.hologram.startup_cleanup.enabled", true)) {
                        HangPlugin.this.clearAllTreasureHolograms();
                    }
                    // 简化日志：不显示浮空字清理禁用信息

                    chestManager.loadAllChestData();
                    // 简化日志：不显示摸金箱数据加载完成信息
                } catch (Exception e) {
                    getLogger().severe("加载摸金箱数据时出错: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }, delayTicks);
    }

    /**
     * 注册命令
     */
    private void registerCommands() {
        HangCommand hangCommand = new HangCommand(this);
        getCommand("evacuation").setExecutor(hangCommand);
        getCommand("evac").setExecutor(hangCommand);

        // 注册许可证命令
        LicenseCommand licenseCommand = new LicenseCommand(this);
        getCommand("license").setExecutor(licenseCommand);
        getCommand("license").setTabCompleter(licenseCommand);
    }

    /**
     * 注册事件监听器
     */
    private void registerListeners() {
        getServer().getPluginManager().registerEvents(playerListener, this);
        getServer().getPluginManager().registerEvents(chatListener, this);
    }



    /**
     * 启动自动保存任务
     */
    private void startAutoSaveTask() {
        // 从配置文件读取自动保存间隔（分钟），默认5分钟
        int autoSaveInterval = getConfig().getInt("performance.auto_save.interval", 5);

        if (autoSaveInterval > 0) {
            // 转换为tick（20 tick = 1秒）
            long intervalTicks = autoSaveInterval * 60 * 20L;

            autoSaveTaskId = getServer().getScheduler().runTaskTimerAsynchronously(this, new Runnable() {
                @Override
                public void run() {
                    try {
                        // 增强的自动保存逻辑
                        if (chestManager != null && playerListener != null) {
                            long startTime = System.currentTimeMillis();

                            // 获取保存前的数据统计
                            int memoryChestCount = playerListener.getTreasureChestDataSize();

                            // 修复：隐藏自动保存开始日志，只在调试模式下显示
                            if (getConfig().getBoolean("debug.enabled", false)) {
                                getLogger().info("🔄 开始自动保存摸金箱数据 (内存中: " + memoryChestCount + " 个)");
                            }

                            // 异步保存数据
                            playerListener.saveAllChestDataToFile();

                            // 同步执行文件保存
                            getServer().getScheduler().runTask(HangPlugin.this, new Runnable() {
                                @Override
                                public void run() {
                                    try {
                                        chestManager.saveConfigFile();

                                        long duration = System.currentTimeMillis() - startTime;
                                        int savedChestCount = chestManager.getChestCount();

                                        // 修复：隐藏自动保存完成日志，只在调试模式下显示
                                        if (getConfig().getBoolean("debug.enabled", false)) {
                                            getLogger().info("完成：自动保存完成 (耗时: " + duration + "ms, 保存: " + savedChestCount + " 个摸金箱)");
                                        }

                                        // 验证保存结果
                                        // 修复：使用实时的配置文件数据进行验证
                                        int actualSavedCount = chestManager.getConfigChestCount();
                                        if (actualSavedCount != memoryChestCount) {
                                            // 修复：刷新后的数据不匹配是正常的，只在调试模式下显示
                                            if (getConfig().getBoolean("debug.enabled", false)) {
                                                getLogger().info("数据验证: 内存=" + memoryChestCount + ", 文件=" + actualSavedCount +
                                                    " (刷新后的数据不匹配是正常的)");
                                            }
                                        }

                                    } catch (Exception e) {
                                        getLogger().severe("错误：自动保存摸金箱数据时出错: " + e.getMessage());
                                        e.printStackTrace();
                                    }
                                }
                            });
                        } else {
                            getLogger().warning("警告：自动保存跳过: 管理器未初始化");
                        }

                    } catch (Exception e) {
                        getLogger().severe("错误：自动保存任务执行时出错: " + e.getMessage());
                        e.printStackTrace();
                    }
                }
            }, intervalTicks, intervalTicks).getTaskId();

            // 简化日志：不显示自动保存任务启动信息
        }
        // 简化日志：不显示自动保存禁用信息
    }

    /**
     * 启动自动刷新检查任务
     */
    private void startAutoRefreshCheckTask() {
        // 检查自动刷新功能是否启用
        if (!getConfig().getBoolean("treasure-chest.auto_refresh.enabled", true)) {
            // 简化日志：不显示自动刷新禁用信息
            return;
        }

        // 从配置文件读取检查间隔（分钟），默认5分钟
        int checkIntervalMinutes = getConfig().getInt("treasure-chest.auto_refresh.check_interval", 5);
        if (checkIntervalMinutes <= 0) {
            getLogger().warning("自动刷新检查间隔配置无效 (check_interval <= 0)，使用默认值5分钟");
            checkIntervalMinutes = 5;
        }

        // 转换为ticks（分钟 * 60秒 * 20ticks）
        long checkInterval = checkIntervalMinutes * 60 * 20L;

        autoRefreshTaskId = getServer().getScheduler().runTaskTimer(this, new Runnable() {
            @Override
            public void run() {
                checkAllChestsForAutoRefresh();
            }
        }, checkInterval, checkInterval).getTaskId();

        // 简化日志：不显示自动刷新任务启动信息
    }

    /**
     * 检查所有摸金箱的自动刷新状态
     */
    private void checkAllChestsForAutoRefresh() {
        try {
            // 获取空闲时间配置（分钟）
            int idleMinutes = getConfig().getInt("treasure-chest.auto_refresh.idle_minutes", 30);
            if (idleMinutes <= 0) {
                return; // 设置为0或负数表示禁用
            }

            long currentTime = System.currentTimeMillis();
            int checkedCount = 0;
            int triggeredCount = 0;

            // 遍历所有摸金箱数据
            for (java.util.Map.Entry<String, com.hang.plugin.listeners.PlayerListener.TreasureChestData> entry :
                 playerListener.getTreasureChestDataMap().entrySet()) {

                com.hang.plugin.listeners.PlayerListener.TreasureChestData data = entry.getValue();
                checkedCount++;

                // 如果已经在刷新倒计时中，跳过
                if (data.getNextRefreshTime() > 0) {
                    // 调试：显示跳过的原因
                    if (getConfig().getBoolean("debug.enabled", false)) {
                        long remainingTime = data.getNextRefreshTime() - currentTime;
                        if (remainingTime > 0) {
                            getLogger().info("跳过已在倒计时的摸金箱: " + entry.getKey() +
                                " (剩余: " + (remainingTime / 60000) + "分钟)");
                        }
                    }
                    continue;
                }

                // 检查摸金箱是否已开启
                long openTime = data.getOpenTime();
                if (openTime == 0) {
                    continue; // 还未开启，无需检查
                }

                // 计算空闲时间
                long idleTime = currentTime - openTime;
                long idleMinutesActual = idleTime / (60 * 1000L);

                // 如果超过空闲时间，自动进入刷新倒计时（无论是否搜索完毕）
                if (idleMinutesActual >= idleMinutes) {
                    // 解析位置
                    org.bukkit.Location location = playerListener.parseLocationFromString(entry.getKey());
                    if (location == null) {
                        continue;
                    }

                    // 根据摸金箱种类设置刷新时间
                    String chestType = data.getChestType();
                    int refreshMinutes = treasureItemManager.getRefreshTime(chestType);
                    long refreshTime = refreshMinutes * 60 * 1000L; // 转换为毫秒
                    long nextRefreshTime = currentTime + refreshTime;
                    data.setNextRefreshTime(nextRefreshTime);

                    // 调试日志 - 保存前
                    if (getConfig().getBoolean("debug.enabled", false)) {
                        getLogger().info("摸金箱自动进入刷新倒计时: " + entry.getKey() +
                            " (开启时间: " + idleMinutesActual + "分钟, 刷新时间: " + refreshMinutes + "分钟)");
                        getLogger().info("倒计时详情: 当前时间=" + currentTime + ", 刷新时间=" + nextRefreshTime +
                            ", 倒计时=" + (nextRefreshTime - currentTime) + "ms");
                        getLogger().info("保存前检查: data.getNextRefreshTime()=" + data.getNextRefreshTime());
                    }

                    // 保存数据
                    playerListener.saveTreasureChestData(location, data);
                    triggeredCount++;

                    // 调试日志 - 保存后验证
                    if (getConfig().getBoolean("debug.enabled", false)) {
                        // 重新读取数据验证是否保存成功
                        com.hang.plugin.listeners.PlayerListener.TreasureChestData savedData =
                            playerListener.getTreasureChestData(location);
                        if (savedData != null) {
                            getLogger().info("保存后验证: savedData.getNextRefreshTime()=" + savedData.getNextRefreshTime());
                            if (savedData.getNextRefreshTime() != nextRefreshTime) {
                                getLogger().warning("数据保存异常！设置的时间=" + nextRefreshTime + ", 保存后的时间=" + savedData.getNextRefreshTime());
                            }
                        } else {
                            getLogger().warning("数据保存失败！无法读取保存后的数据");
                        }
                    }
                }
            }

            // 如果有触发自动刷新的摸金箱，记录日志
            if (triggeredCount > 0) {
                getLogger().info("自动刷新检查完成: 检查了 " + checkedCount + " 个摸金箱, " +
                    triggeredCount + " 个进入刷新倒计时");
            }

        } catch (Exception e) {
            getLogger().warning("自动刷新检查时出错: " + e.getMessage());
            if (getConfig().getBoolean("debug.enabled", false)) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 重启自动刷新任务
     */
    private void restartAutoRefreshTask() {
        // 停止现有的自动刷新任务
        if (autoRefreshTaskId != -1) {
            getServer().getScheduler().cancelTask(autoRefreshTaskId);
            autoRefreshTaskId = -1;
        }

        // 重新启动自动刷新任务
        startAutoRefreshCheckTask();
    }

    /**
     * 获取插件实例
     *
     * @return 插件实例
     */
    public static HangPlugin getInstance() {
        return instance;
    }



    /**
     * 获取玩家监听器
     *
     * @return 玩家监听器实例
     */
    public PlayerListener getPlayerListener() {
        return playerListener;
    }

    public com.hang.plugin.manager.HologramManager getHologramManager() {
        return hologramManager;
    }



    /**
     * 获取摸金箱物品管理器
     *
     * @return 摸金箱物品管理器实例
     */
    public TreasureItemManager getTreasureItemManager() {
        return treasureItemManager;
    }





    /**
     * 获取摸金箱管理器
     *
     * @return 摸金箱管理器实例
     */
    public ChestManager getChestManager() {
        return chestManager;
    }

    /**
     * 获取摸金箱种类管理器
     *
     * @return 摸金箱种类管理器实例
     */
    public ChestTypeManager getChestTypeManager() {
        return chestTypeManager;
    }

    /**
     * 获取NMS管理器
     *
     * @return NMS管理器实例
     */
    public NMSManager getNMSManager() {
        return nmsManager;
    }





    /**
     * 重载配置文件
     */
    public void reloadPluginConfig() {
        try {
            reloadConfig();
            treasureItemManager.reload();
            chestTypeManager.reloadConfig();

            // 重载箱子替换配置
            if (chestReplacementManager != null) {
                chestReplacementManager.reloadConfig();
            }

            // 重新加载许可证配置
            if (licenseConfig != null) {
                licenseConfig.reload();

                // 重新验证许可证
                if (licenseManager != null) {
                    if (licenseConfig.isLicenseKeyEmpty()) {
                        getLogger().severe("许可证密钥为空，重新加载失败");
                        return;
                    }

                    if (!licenseManager.validateLicenseKey(licenseConfig.getLicenseKey())) {
                        getLogger().severe("许可证验证失败，重新加载失败");
                        return;
                    }
                }
            }

            // 修复：重载配置后重新检查全息图任务状态
            if (playerListener != null) {
                playerListener.recheckHologramTaskStatus();
            }

            // 重启自动刷新任务（如果配置有变化）
            restartAutoRefreshTask();

            getLogger().info("配置文件已重载！");
        } catch (Exception e) {
            getLogger().severe("重载配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 获取带前缀的消息
     *
     * @param key 消息键
     * @return 格式化的消息
     */
    public String getMessage(String key) {
        String prefix = getConfig().getString("messages.prefix", "§6[摸金] §r");
        String message = getConfig().getString("messages." + key, key);
        return prefix + message;
    }

    /**
     * 获取带前缀和参数替换的消息
     *
     * @param key 消息键
     * @param replacements 替换参数 (key, value, key, value, ...)
     * @return 格式化的消息
     */
    public String getMessage(String key, String... replacements) {
        String message = getMessage(key);

        for (int i = 0; i < replacements.length - 1; i += 2) {
            String placeholder = "{" + replacements[i] + "}";
            String replacement = replacements[i + 1];
            message = message.replace(placeholder, replacement);
        }

        return message;
    }

    /**
     * 获取不带前缀的纯消息（用于Title和ActionBar）
     *
     * @param key 消息键
     * @return 纯消息内容
     */
    public String getRawMessage(String key) {
        return getConfig().getString("messages." + key, key);
    }

    /**
     * 获取不带前缀的纯消息并替换参数（用于Title和ActionBar）
     *
     * @param key 消息键
     * @param replacements 替换参数 (key, value, key, value, ...)
     * @return 纯消息内容
     */
    public String getRawMessage(String key, String... replacements) {
        String message = getRawMessage(key);

        for (int i = 0; i < replacements.length - 1; i += 2) {
            String placeholder = "{" + replacements[i] + "}";
            String replacement = replacements[i + 1];
            message = message.replace(placeholder, replacement);
        }

        return message;
    }

    /**
     * 新增：发送消息给玩家（如果消息为空则不发送）
     *
     * @param player 玩家
     * @param messageKey 消息键
     * @param defaultMessage 默认消息
     */
    public void sendMessageIfNotEmpty(org.bukkit.entity.Player player, String messageKey, String defaultMessage) {
        String message = getConfig().getString("messages." + messageKey, defaultMessage);
        if (message != null && !message.trim().isEmpty()) {
            player.sendMessage(message);
        }
    }

    /**
     * 新增：发送带参数替换的消息给玩家（如果消息为空则不发送）
     *
     * @param player 玩家
     * @param messageKey 消息键
     * @param defaultMessage 默认消息
     * @param replacements 替换参数 (key, value, key, value, ...)
     */
    public void sendMessageIfNotEmpty(org.bukkit.entity.Player player, String messageKey, String defaultMessage, String... replacements) {
        String message = getConfig().getString("messages." + messageKey, defaultMessage);
        if (message != null && !message.trim().isEmpty()) {
            // 执行参数替换
            for (int i = 0; i < replacements.length - 1; i += 2) {
                String placeholder = "{" + replacements[i] + "}";
                String replacement = replacements[i + 1];
                message = message.replace(placeholder, replacement);
            }
            player.sendMessage(message);
        }
    }

    /**
     * 显示简化的启动信息
     */
    private void showStartupInfo() {
        // 简化启动信息，只显示关键内容
        getLogger().info("=== HangEvacuation " + getDescription().getVersion() + " 已启用! ===");

        // 显示加载统计
        int chestTypeCount = chestTypeManager.getChestTypeCount();
        int treasureItemCount = treasureItemManager.getTreasureItemCount();
        getLogger().info("已加载: " + chestTypeCount + " 个摸金箱种类, " + treasureItemCount + " 个物品");

        // 显示功能状态
        StringBuilder features = new StringBuilder("功能: 摸金箱");
        if (levelManager != null) {
            features.append(" | 等级系统");
        }
        if (evacuationSystem != null) {
            features.append(" | 撤离系统");
        }
        if (nmsManager.isInitialized()) {
            features.append(" | NMS适配器");
        }
        getLogger().info(features.toString());

        // 作者信息
        getLogger().info("作者: hangzong(航总) | 技术支持V: hang060217");
    }

    /**
     * 清除所有摸金插件相关的浮空字（启动时调用）
     * 防止服务器重启后遗留浮空字实体
     */
    private void clearAllTreasureHolograms() {
        // 简化日志：不显示开始清除的消息

        try {
            int totalCleared = 0;
            int worldCount = 0;

            // 遍历所有已加载的世界
            for (org.bukkit.World world : getServer().getWorlds()) {
                worldCount++;
                int worldCleared = 0;

                try {
                    // 获取世界中的所有实体
                    for (org.bukkit.entity.Entity entity : world.getEntities()) {
                        if (entity instanceof org.bukkit.entity.ArmorStand) {
                            org.bukkit.entity.ArmorStand armorStand = (org.bukkit.entity.ArmorStand) entity;

                            // 检查是否是摸金插件的浮空字
                            if (isTreasureHologram(armorStand)) {
                                try {
                                    // 修复：版本兼容性：1.16.x版本特殊处理
                                    if (VersionUtils.isVersionAtLeast(1, 16) &&
                                        !VersionUtils.isVersionAtLeast(1, 17)) {
                                        armorStand.setCustomNameVisible(false);
                                        armorStand.setVisible(false);
                                    }

                                    armorStand.remove();
                                    worldCleared++;
                                    totalCleared++;

                                } catch (Exception e) {
                                    // 忽略单个实体移除失败
                                    if (getConfig().getBoolean("debug.enabled", false)) {
                                        getLogger().warning("移除浮空字实体时出错: " + e.getMessage());
                                    }
                                }
                            }
                        }
                    }

                    // 简化日志：不显示每个世界的清除详情

                } catch (Exception e) {
                    getLogger().warning("清除世界 " + world.getName() + " 中的浮空字时出错: " + e.getMessage());
                }
            }

            // 只在有清除内容时显示简化日志
            if (totalCleared > 0) {
                getLogger().info("清除了 " + totalCleared + " 个遗留浮空字");
            }

        } catch (Exception e) {
            getLogger().warning("清除遗留浮空字时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 检查ArmorStand是否是摸金插件的浮空字
     */
    private boolean isTreasureHologram(org.bukkit.entity.ArmorStand armorStand) {
        try {
            // 检查基本属性：必须有自定义名称且可见
            if (!armorStand.isCustomNameVisible() || armorStand.getCustomName() == null) {
                return false;
            }

            String customName = armorStand.getCustomName();

            // 检查是否是摸金插件的浮空字特征
            // 1. 检查是否包含摸金箱相关的文本
            if (customName.contains("摸金箱") ||
                customName.contains("武器箱") ||
                customName.contains("弹药箱") ||
                customName.contains("医疗箱") ||
                customName.contains("补给箱") ||
                customName.contains("装备箱")) {
                return true;
            }

            // 2. 检查是否包含搜索相关的文本
            if (customName.contains("搜索中") ||
                customName.contains("搜索完成") ||
                customName.contains("剩余时间") ||
                customName.contains("冷却中")) {
                return true;
            }

            // 3. 检查是否包含倒计时格式 (例如: "05:30")
            if (customName.matches(".*\\d{2}:\\d{2}.*")) {
                return true;
            }

            // 4. 检查ArmorStand的其他特征（摸金插件浮空字的典型设置）
            if (armorStand.isSmall() &&
                armorStand.isMarker() &&
                !armorStand.isVisible() &&
                !armorStand.hasGravity()) {
                // 这些设置组合很可能是摸金插件的浮空字
                return true;
            }

        } catch (Exception e) {
            // 如果检查过程中出错，为了安全起见不删除
            return false;
        }

        return false;
    }

    /**
     * 获取内存缓存管理器
     */
    public com.hang.plugin.manager.MemoryCacheManager getMemoryCacheManager() {
        return memoryCacheManager;
    }

    /**
     * 获取箱子替换管理器
     */
    public com.hang.plugin.manager.ChestReplacementManager getChestReplacementManager() {
        return chestReplacementManager;
    }

    /**
     * 获取等级管理器
     */
    public LevelManager getLevelManager() {
        return levelManager;
    }

    /**
     * 获取倒计时管理器
     */
    public CountdownManager getCountdownManager() {
        return countdownManager;
    }

    /**
     * 获取撤离系统
     */
    public EvacuationSystem getEvacuationSystem() {
        return evacuationSystem;
    }

    /**
     * 获取PlaceholderAPI扩展
     */
    public LevelPlaceholderExpansion getPlaceholderExpansion() {
        return placeholderExpansion;
    }

    /**
     * 获取撤离配置文件
     */
    private org.bukkit.configuration.file.FileConfiguration getEvacuationConfig() {
        java.io.File evacuationFile = new java.io.File(getDataFolder(), "evacuation.yml");
        if (!evacuationFile.exists()) {
            saveResource("evacuation.yml", false);
        }
        return org.bukkit.configuration.file.YamlConfiguration.loadConfiguration(evacuationFile);
    }

    /**
     * 获取撤离消息（带前缀）
     */
    public String getEvacuationMessage(String key, String... replacements) {
        org.bukkit.configuration.file.FileConfiguration evacuationConfig = getEvacuationConfig();
        String prefix = evacuationConfig.getString("messages.prefix", "§6[撤离] §r");
        String message = evacuationConfig.getString("messages." + key, "§c消息未找到: " + key);

        // 替换变量
        for (int i = 0; i < replacements.length; i += 2) {
            if (i + 1 < replacements.length) {
                message = message.replace("{" + replacements[i] + "}", replacements[i + 1]);
            }
        }

        return prefix + message;
    }

    /**
     * 获取撤离原始消息（不带前缀）
     */
    public String getRawEvacuationMessage(String key, String... replacements) {
        org.bukkit.configuration.file.FileConfiguration evacuationConfig = getEvacuationConfig();
        String message = evacuationConfig.getString("messages." + key, "§c消息未找到: " + key);

        // 替换变量
        for (int i = 0; i < replacements.length; i += 2) {
            if (i + 1 < replacements.length) {
                message = message.replace("{" + replacements[i] + "}", replacements[i + 1]);
            }
        }

        return message;
    }

    /**
     * 初始化许可证系统
     */
    private boolean initializeLicense() {
        try {
            // 检查许可证密钥是否为空
            if (licenseConfig.isLicenseKeyEmpty()) {
                getLogger().severe("╔══════════════════════════════════════════════════════════════╗");
                getLogger().severe("║                    🚫 许可证验证失败 🚫                      ║");
                getLogger().severe("╠══════════════════════════════════════════════════════════════╣");
                getLogger().severe("║  ❌ 错误：未配置许可证密钥！                                  ║");
                getLogger().severe("║                                                              ║");
                getLogger().severe("║  📝 解决方案：                                               ║");
                getLogger().severe("║     请在 license.yml 中的 license-key 字段填写您的许可证密钥 ║");
                getLogger().severe("║                                                              ║");
                getLogger().severe("║  📋 示例配置：                                               ║");
                getLogger().severe("║     license-key: \"857ff611-618f-40db-8fe2-291b388f0cf0\"     ║");
                getLogger().severe("║                                                              ║");
                getLogger().severe("║  ⚠️  插件将被禁用，直到配置有效的许可证密钥                   ║");
                getLogger().severe("╚══════════════════════════════════════════════════════════════╝");
                getServer().getPluginManager().disablePlugin(this);
                return false;
            }

            // 初始化许可证管理器
            licenseManager = new LicenseManager(this);
            boolean isValid = licenseManager.validateLicenseKey(licenseConfig.getLicenseKey());

            if (!isValid) {
                getLogger().severe("╔══════════════════════════════════════════════════════════════╗");
                getLogger().severe("║                    🚫 许可证验证失败 🚫                      ║");
                getLogger().severe("╠══════════════════════════════════════════════════════════════╣");
                getLogger().severe("║  ❌ 错误：提供的许可证密钥无效或已过期                        ║");
                getLogger().severe("║                                                              ║");
                getLogger().severe("║  🔍 可能的原因：                                             ║");
                getLogger().severe("║     • 许可证密钥输入错误                                     ║");
                getLogger().severe("║     • 许可证已过期                                           ║");
                getLogger().severe("║     • 网络连接问题                                           ║");
                getLogger().severe("║     • 服务器无法访问 HangZong 许可证服务                     ║");
                getLogger().severe("║                                                              ║");
                getLogger().severe("║  📞 联系支持：                                               ║");
                getLogger().severe("║     QQ: ********** (HangZong 技术支持)                       ║");
                getLogger().severe("║                                                              ║");
                getLogger().severe("║  ⚠️  插件将被禁用                                            ║");
                getLogger().severe("╚══════════════════════════════════════════════════════════════╝");
                getServer().getPluginManager().disablePlugin(this);
                return false;
            }

            getLogger().info("╔══════════════════════════════════════════════════════════════╗");
            getLogger().info("║                    ✅ 许可证验证成功 ✅                      ║");
            getLogger().info("╠══════════════════════════════════════════════════════════════╣");
            getLogger().info("║  🎉 恭喜！您的许可证已通过 HangZong 服务器验证                ║");
            getLogger().info("║  🔒 插件功能已解锁，可以正常使用                              ║");
            getLogger().info("║  💓 心跳监控已启动，每15分钟自动验证                          ║");
            getLogger().info("╚══════════════════════════════════════════════════════════════╝");
            return true;

        } catch (Exception e) {
            getLogger().severe("╔══════════════════════════════════════════════════════════════╗");
            getLogger().severe("║                    💥 系统初始化错误 💥                      ║");
            getLogger().severe("╠══════════════════════════════════════════════════════════════╣");
            getLogger().severe("║  ❌ 初始化许可证系统时发生错误: " + e.getMessage());
            getLogger().severe("║                                                              ║");
            getLogger().severe("║  🔧 可能的解决方案：                                         ║");
            getLogger().severe("║     • 检查网络连接                                           ║");
            getLogger().severe("║     • 重启服务器                                             ║");
            getLogger().severe("║     • 联系技术支持 QQ: **********                             ║");
            getLogger().severe("║                                                              ║");
            getLogger().severe("║  ⚠️  插件将被禁用                                            ║");
            getLogger().severe("╚══════════════════════════════════════════════════════════════╝");
            e.printStackTrace();
            getServer().getPluginManager().disablePlugin(this);
            return false;
        }
    }

    /**
     * 检查许可证是否有效
     */
    public boolean isLicenseValid() {
        return licenseManager != null && licenseManager.isValid();
    }

    /**
     * 获取许可证管理器
     */
    public LicenseManager getLicenseManager() {
        return licenseManager;
    }

    /**
     * 获取许可证配置
     */
    public LicenseConfig getLicenseConfig() {
        return licenseConfig;
    }

    
}
