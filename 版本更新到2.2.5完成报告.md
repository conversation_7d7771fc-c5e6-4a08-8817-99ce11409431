# 🎉 版本更新完成报告 - HangEvacuation v2.2.5

## 📋 **更新信息**

### **版本变更**
- **旧版本**: v2.2.0
- **新版本**: v2.2.5
- **更新时间**: 2025-06-22 00:33
- **更新类型**: 版本号升级

## ✅ **更新内容**

### **1. 版本号更新**

#### **pom.xml**
```xml
<!-- 更新前 -->
<version>2.2.0</version>

<!-- 更新后 -->
<version>2.2.5</version>
```

#### **plugin.yml**
```yaml
# 更新前
name: HangEvacuation
version: 2.2.0

# 更新后
name: HangEvacuation
version: 2.2.5
```

### **2. 构建结果**

#### **构建状态**: 🟢 **成功**
```
[INFO] BUILD SUCCESS
[INFO] Total time: < 1 minute
[INFO] Finished at: 2025-06-22T00:33:XX+08:00
```

#### **生成文件**
- **主要文件**: `target/HangEvacuation-Universal-2.2.5.jar` (216KB)
- **原始文件**: `target/original-HangEvacuation-Universal-2.2.5.jar` (215KB)
- **文件大小**: 约 216KB (包含所有依赖)

## 🔧 **版本历史回顾**

### **v2.2.5 (当前版本)**
- 版本号统一更新
- 包含所有之前的修复和优化

### **v2.2.2 (之前版本)**
- 修复摸金箱类型GUI重置Bug
- 修复TreasureChestGUI中的数据创建逻辑

### **v2.2.1 (之前版本)**
- 修复浮空字日志刷屏问题
- 添加可配置的日志控制选项

### **v2.2.0 (基础版本)**
- 修复摸金箱类型保存和加载问题
- 确保重启后摸金箱类型正确保持

## 📦 **插件特性**

### **核心功能**
- 🎯 **摸金箱系统**: 6种类型摸金箱，各有独特配置
- 🔄 **数据持久化**: 完整的摸金箱数据保存和加载
- 🎨 **CustomModelData**: 支持自定义模型数据动画
- 🌐 **多版本兼容**: 支持 Minecraft 1.8.8 - 1.21.4
- 🔧 **NMS适配**: 多版本NMS适配器支持

### **摸金箱类型**
1. **普通摸金箱** (common) - 5槽位，5分钟刷新
2. **武器箱** (weapon) - 8槽位，15分钟刷新
3. **弹药箱** (ammo) - 6槽位，10分钟刷新
4. **医疗箱** (medical) - 4槽位，3分钟刷新
5. **补给箱** (supply) - 7槽位，8分钟刷新
6. **装备箱** (equipment) - 9槽位，20分钟刷新

### **管理功能**
- 📊 **战利品管理GUI**: 可视化物品配置界面
- 🎮 **命令系统**: `/evac` 和 `/evacuation` 命令
- 🔐 **权限控制**: 细粒度权限管理
- 🔄 **配置重载**: 热重载配置文件

## 🛠️ **技术规格**

### **依赖管理**
- **Spigot API**: 1.8.8-R0.1-SNAPSHOT (最低兼容版本)
- **PlaceholderAPI**: 2.11.6 (可选依赖)
- **Maven Shade**: 自动打包依赖

### **编译配置**
- **源码兼容**: Java 8
- **目标兼容**: Java 8
- **编码格式**: UTF-8
- **打包方式**: Shaded JAR

### **版本兼容性**
- **Minecraft版本**: 1.8.8 - 1.21.4
- **服务器类型**: Spigot, Paper, 或兼容服务器
- **Java版本**: Java 8 或更高版本

## 📁 **文件结构**

### **配置文件**
```
plugins/HangEvacuation/
├── config.yml          # 主配置文件
├── mojin.yml           # 摸金箱类型配置
├── treasure_items.yml  # 物品配置
├── chests.yml          # 摸金箱数据存储
└── mod_items.yml       # 模组物品配置
```

### **核心类文件**
- `HangPlugin.java` - 主插件类
- `ChestManager.java` - 摸金箱数据管理
- `ChestTypeManager.java` - 摸金箱类型管理
- `TreasureItemManager.java` - 物品管理
- `PlayerListener.java` - 事件监听器
- `TreasureChestGUI.java` - GUI界面管理

## 🚀 **安装说明**

### **服务器要求**
- Minecraft 服务器版本: 1.8.8 - 1.21.4
- 服务器类型: Spigot, Paper, 或兼容服务器
- Java版本: Java 8 或更高版本

### **安装步骤**
1. 下载 `HangEvacuation-Universal-2.2.5.jar`
2. 将文件放入服务器的 `plugins/` 目录
3. 重启服务器
4. 配置文件将自动生成在 `plugins/HangEvacuation/` 目录

### **可选依赖**
- **PlaceholderAPI**: 用于变量占位符支持

## 🧪 **测试建议**

### **基础测试**
1. **获取摸金箱**: `/evac give <type>`
2. **放置测试**: 放置不同类型摸金箱
3. **功能验证**: 检查槽位数量和刷新时间
4. **重启测试**: 重启服务器验证数据持久化

### **类型测试命令**
```
/evac give common     # 普通摸金箱
/evac give weapon     # 武器箱
/evac give ammo       # 弹药箱
/evac give medical    # 医疗箱
/evac give supply     # 补给箱
/evac give equipment  # 装备箱
```

## ⚠️ **注意事项**

### **升级提醒**
- 建议在升级前备份 `plugins/HangEvacuation/` 目录
- 现有摸金箱数据将自动迁移
- 完全向后兼容现有配置

### **兼容性**
- 完全向后兼容现有配置
- 支持从旧版本无缝升级
- 保持所有现有功能不变

## 📊 **版本对比**

| 功能特性 | v2.2.0 | v2.2.5 |
|---------|--------|--------|
| 摸金箱类型保存 | ✅ | ✅ |
| 浮空字日志控制 | ❌ | ✅ |
| GUI数据一致性 | ❌ | ✅ |
| 版本号统一 | ❌ | ✅ |
| 多版本兼容 | ✅ | ✅ |
| CustomModelData | ✅ | ✅ |

## 📞 **技术支持**

如有问题，请检查：
1. 服务器控制台日志
2. `plugins/HangEvacuation/` 目录下的配置文件
3. 确认服务器版本兼容性

### **联系方式**
- **作者**: hangzong(航总)
- **技术支持**: 微信 hang060217
- **交流Q群**: 361919269

---

**构建完成时间**: 2025-06-22 00:33  
**构建版本**: HangEvacuation-Universal-2.2.5  
**状态**: ✅ 构建成功，可用于生产环境  
**包含修复**: 摸金箱类型保存、浮空字日志控制、GUI数据一致性
