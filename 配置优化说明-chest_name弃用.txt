===============================================
     HangEvacuation 配置优化说明 - chest_name弃用
===============================================

🎮 插件名称: HangEvacuation (Hang摸金撤离)
📅 更新日期: 2024-12-19
🔧 版本号: 1.6.0+
👨‍💻 作者: hangzong(航总)
📞 技术支持: 微信 hang060217

===============================================
              📋 配置变更说明
===============================================

🎯 【变更内容】
❌ treasure_items.yml 中的 chest_name 配置已弃用
✅ 现在使用 mojin.yml 中的摸金箱种类系统

🔄 【变更原因】
由于新增了摸金箱种类系统，每种摸金箱都有独立的名称：
- 普通摸金箱: "§6摸金箱"
- 武器箱: "§c武器箱"  
- 弹药箱: "§e弹药箱"
- 医疗箱: "§a医疗箱"
- 补给箱: "§b补给箱"
- 装备箱: "§d装备箱"

全局的 chest_name 配置已经不再适用。

===============================================
              🔧 配置对比
===============================================

🚫 【旧配置方式 - treasure_items.yml】
```yaml
chest_settings:
  chest_name: "§6摸金箱"  # 所有摸金箱都使用同一个名称
```

✅ 【新配置方式 - mojin.yml】
```yaml
chest_types:
  common:
    name: "§6摸金箱"           # 普通摸金箱名称
    display_name: "§6普通摸金箱"
  weapon:
    name: "§c武器箱"           # 武器箱名称
    display_name: "§c军用武器箱"
  ammo:
    name: "§e弹药箱"           # 弹药箱名称
    display_name: "§e军用弹药箱"
```

===============================================
              🔄 兼容性处理
===============================================

🛡️ 【向下兼容】
- chest_name 配置仍然保留，不会导致错误
- 旧的摸金箱仍然可以正常使用
- 代码会自动降级到默认名称

📝 【代码处理】
```java
public String getChestName() {
    // 已弃用：现在使用摸金箱种类系统的名称
    // 保留此方法用于向下兼容，返回默认名称
    return "§6摸金箱";
}

public String getChestName(String chestType) {
    if (chestType != null && plugin.getChestTypeManager() != null) {
        ChestType type = plugin.getChestTypeManager().getChestType(chestType);
        if (type != null) {
            return type.getName();
        }
    }
    // 降级到默认名称
    return getChestName();
}
```

===============================================
              🎯 迁移指南
===============================================

📋 【无需手动迁移】
- 现有配置文件无需修改
- 插件会自动处理兼容性
- 新功能会自动生效

🔧 【推荐操作】
1. 保持现有的 treasure_items.yml 不变
2. 通过 mojin.yml 配置新的摸金箱种类
3. 使用 /evac gui 管理不同种类的摸金箱

===============================================
              📊 功能对比
===============================================

🔄 【功能变化】

| 功能 | 旧系统 | 新系统 |
|------|--------|--------|
| 摸金箱名称 | 统一名称 | 按种类区分 |
| 配置文件 | treasure_items.yml | mojin.yml |
| 管理方式 | 手动编辑 | GUI + 配置文件 |
| 扩展性 | 有限 | 无限扩展 |

✅ 【新增优势】
- 支持多种摸金箱类型
- 每种摸金箱独立配置
- GUI可视化管理
- 更好的用户体验

===============================================
              🎮 使用建议
===============================================

🎯 【最佳实践】
1. **新服务器**: 直接使用摸金箱种类系统
2. **现有服务器**: 逐步迁移到新系统
3. **配置管理**: 优先使用 mojin.yml

🔧 【配置建议】
- 保留 treasure_items.yml 中的其他配置
- 只有 chest_name 被标记为弃用
- 其他配置（slots、search_time等）仍然有效

===============================================
              ❓ 常见问题
===============================================

Q: 需要删除 chest_name 配置吗？
A: 不需要，保留它不会有任何问题，只是不再使用

Q: 旧的摸金箱还能正常工作吗？
A: 能，完全向下兼容，不影响现有功能

Q: 如何自定义新的摸金箱种类？
A: 编辑 mojin.yml 文件，在 chest_types 节点下添加新种类

Q: 可以同时使用新旧系统吗？
A: 可以，但建议逐步迁移到新系统以获得更好体验

===============================================
              📝 更新记录
===============================================

🔄 【v1.6.0】
- 新增摸金箱种类系统
- 弃用全局 chest_name 配置
- 保持向下兼容性
- 添加配置迁移说明

🎯 【未来计划】
- 可能在未来版本中完全移除 chest_name
- 会提前通知并提供迁移工具
- 确保平滑过渡

===============================================
              🔧 技术支持
===============================================

🎮 如有疑问，请联系：
- 微信: hang060217
- QQ群: 361919269
- 作者: hangzong(航总)
- 标签: Hang系列插件

💡 建议：
- 定期备份配置文件
- 测试新功能后再应用到生产环境
- 关注插件更新日志
