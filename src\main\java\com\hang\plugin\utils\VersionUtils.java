package com.hang.plugin.utils;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;

/**
 * 版本工具类
 * 用于检测服务器版本并提供版本相关的工具方法
 *
 * <AUTHOR>
 */
public class VersionUtils {

    private static String serverVersion;
    private static int majorVersion;
    private static int minorVersion;
    private static int patchVersion;
    private static String nmsVersion;

    static {
        detectVersion();
    }

    /**
     * 检测服务器版本
     */
    private static void detectVersion() {
        String version = Bukkit.getServer().getClass().getPackage().getName();
        nmsVersion = version.substring(version.lastIndexOf('.') + 1);

        // 解析版本号
        String bukkitVersion = Bukkit.getBukkitVersion();
        serverVersion = bukkitVersion.split("-")[0];

        String[] versionParts = serverVersion.split("\\.");
        majorVersion = Integer.parseInt(versionParts[0]);
        minorVersion = Integer.parseInt(versionParts[1]);

        if (versionParts.length > 2) {
            patchVersion = Integer.parseInt(versionParts[2]);
        } else {
            patchVersion = 0;
        }
    }

    /**
     * 获取服务器版本字符串
     */
    public static String getServerVersion() {
        return serverVersion;
    }

    /**
     * 获取主版本号
     */
    public static int getMajorVersion() {
        return majorVersion;
    }

    /**
     * 获取次版本号
     */
    public static int getMinorVersion() {
        return minorVersion;
    }

    /**
     * 获取补丁版本号
     */
    public static int getPatchVersion() {
        return patchVersion;
    }

    /**
     * 获取NMS版本字符串
     */
    public static String getNMSVersion() {
        return nmsVersion;
    }

    /**
     * 检查是否为指定版本或更高
     */
    public static boolean isVersionAtLeast(int major, int minor) {
        return isVersionAtLeast(major, minor, 0);
    }

    /**
     * 检查是否为指定版本或更高
     */
    public static boolean isVersionAtLeast(int major, int minor, int patch) {
        if (majorVersion > major) return true;
        if (majorVersion < major) return false;

        if (minorVersion > minor) return true;
        if (minorVersion < minor) return false;

        return patchVersion >= patch;
    }

    /**
     * 检查是否为指定版本或更低
     */
    public static boolean isVersionAtMost(int major, int minor) {
        return isVersionAtMost(major, minor, 999);
    }

    /**
     * 检查是否为指定版本或更低
     */
    public static boolean isVersionAtMost(int major, int minor, int patch) {
        if (majorVersion < major) return true;
        if (majorVersion > major) return false;

        if (minorVersion < minor) return true;
        if (minorVersion > minor) return false;

        return patchVersion <= patch;
    }

    /**
     * 检查是否为指定版本范围内
     */
    public static boolean isVersionBetween(int minMajor, int minMinor, int maxMajor, int maxMinor) {
        return isVersionAtLeast(minMajor, minMinor) && !isVersionAtLeast(maxMajor, maxMinor + 1);
    }

    /**
     * 获取版本适配器类名
     */
    public static String getAdapterClassName() {
        // 目前只支持基础适配器，适用于所有版本
        return "com.hang.plugin.nms.versions.NMSAdapter_1_8_R3";
    }

    /**
     * 检查是否支持当前版本
     */
    public static boolean isSupportedVersion() {
        return isVersionAtLeast(1, 8) && !isVersionAtLeast(1, 22);
    }

    /**
     * 获取详细的版本兼容性信息
     */
    public static String getCompatibilityInfo() {
        StringBuilder info = new StringBuilder();
        info.append("=== 版本兼容性信息 ===\n");
        info.append("Minecraft版本: ").append(serverVersion).append("\n");
        info.append("NMS版本: ").append(nmsVersion).append("\n");
        info.append("服务端类型: ").append(getServerType()).append("\n");
        info.append("版本支持: ").append(isSupportedVersion() ? "完成：支持" : "错误：不支持").append("\n");
        info.append("Paper支持: ").append(isPaper() ? "完成：是" : "错误：否").append("\n");
        info.append("Spigot支持: ").append(isSpigot() ? "完成：是" : "错误：否").append("\n");

        // 检查关键功能支持
        info.append("\n=== 功能支持 ===\n");
        info.append("动作栏: ").append(isVersionAtLeast(1, 8) ? "完成：支持" : "错误：不支持").append("\n");
        info.append("标题API: ").append(isVersionAtLeast(1, 8) ? "完成：支持" : "错误：不支持").append("\n");
        info.append("粒子效果: ").append(isVersionAtLeast(1, 9) ? "完成：支持" : "错误：不支持").append("\n");
        info.append("方块数据: ").append(isVersionAtLeast(1, 13) ? "完成：支持" : "错误：不支持").append("\n");
        info.append("持久化数据: ").append(isVersionAtLeast(1, 14) ? "完成：支持" : "错误：不支持").append("\n");

        return info.toString();
    }

    /**
     * 获取版本信息字符串
     */
    public static String getVersionInfo() {
        return String.format("Minecraft %s (NMS: %s)", serverVersion, nmsVersion);
    }

    /**
     * 检查是否为Paper服务端
     */
    public static boolean isPaper() {
        try {
            Class.forName("com.destroystokyo.paper.PaperConfig");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 检查是否为Spigot服务端
     */
    public static boolean isSpigot() {
        try {
            Class.forName("org.spigotmc.SpigotConfig");
            return true;
        } catch (ClassNotFoundException e) {
            return false;
        }
    }

    /**
     * 获取服务端类型
     */
    public static String getServerType() {
        if (isPaper()) {
            return "Paper";
        } else if (isSpigot()) {
            return "Spigot";
        } else {
            return "CraftBukkit";
        }
    }

    /**
     * 获取兼容的材料
     * 处理不同版本间的材料名称差异
     */
    public static Material getCompatibleMaterial(String materialName) {
        try {
            // 首先尝试直接获取
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            // 如果失败，尝试替代材料
            return getAlternativeMaterial(materialName);
        }
    }

    /**
     * 获取兼容的彩色材料
     * 处理带有颜色数据值的材料兼容性
     */
    public static Material getCompatibleColoredMaterial(String baseMaterial, int colorData) {
        // 尝试获取基础材料
        try {
            Material base = Material.valueOf(baseMaterial);
            return base;
        } catch (IllegalArgumentException e) {
            // 如果基础材料不存在，尝试获取带颜色的新版本材料
            return getColoredMaterialByData(baseMaterial, colorData);
        }
    }

    /**
     * 根据数据值获取对应颜色的材料
     */
    private static Material getColoredMaterialByData(String baseMaterial, int colorData) {
        String[] colors = {
            "WHITE", "ORANGE", "MAGENTA", "LIGHT_BLUE", "YELLOW", "LIME",
            "PINK", "GRAY", "LIGHT_GRAY", "CYAN", "PURPLE", "BLUE",
            "BROWN", "GREEN", "RED", "BLACK"
        };

        if (colorData < 0 || colorData >= colors.length) {
            colorData = 0; // 默认白色
        }

        String colorName = colors[colorData];

        switch (baseMaterial) {
            case "WOOL":
                try {
                    return Material.valueOf(colorName + "_WOOL");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("WOOL");
                }
            case "STAINED_CLAY":
                try {
                    return Material.valueOf(colorName + "_TERRACOTTA");
                } catch (IllegalArgumentException e) {
                    try {
                        return Material.valueOf(colorName + "_STAINED_HARDENED_CLAY");
                    } catch (IllegalArgumentException e2) {
                        return Material.valueOf("CLAY");
                    }
                }
            case "STAINED_GLASS":
                try {
                    return Material.valueOf(colorName + "_STAINED_GLASS");
                } catch (IllegalArgumentException e) {
                    return Material.GLASS;
                }
            case "STAINED_GLASS_PANE":
                try {
                    return Material.valueOf(colorName + "_STAINED_GLASS_PANE");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("GLASS_PANE");
                }
            default:
                return getCompatibleMaterial(baseMaterial);
        }
    }

    /**
     * 获取替代材料
     */
    private static Material getAlternativeMaterial(String materialName) {
        switch (materialName) {
            case "BOOK_AND_QUILL":
                // 1.20.1中BOOK_AND_QUILL改名为WRITABLE_BOOK
                try {
                    return Material.valueOf("WRITABLE_BOOK");
                } catch (IllegalArgumentException e) {
                    return Material.BOOK;
                }
            case "WRITABLE_BOOK":
                // 反向兼容
                try {
                    return Material.valueOf("BOOK_AND_QUILL");
                } catch (IllegalArgumentException e) {
                    return Material.BOOK;
                }
            case "WOOL":
                // 1.13+中WOOL改为具体颜色的羊毛
                try {
                    return Material.valueOf("WHITE_WOOL");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("WOOL");
                }
            case "STAINED_CLAY":
                // 1.13+中STAINED_CLAY改为TERRACOTTA
                try {
                    return Material.valueOf("WHITE_TERRACOTTA");
                } catch (IllegalArgumentException e) {
                    try {
                        return Material.valueOf("STAINED_HARDENED_CLAY");
                    } catch (IllegalArgumentException e2) {
                        return Material.valueOf("CLAY");
                    }
                }
            case "STAINED_GLASS":
                // 1.13+中STAINED_GLASS改为具体颜色的玻璃
                try {
                    return Material.valueOf("WHITE_STAINED_GLASS");
                } catch (IllegalArgumentException e) {
                    return Material.GLASS;
                }
            case "STAINED_GLASS_PANE":
                // 1.13+中STAINED_GLASS_PANE改为具体颜色的玻璃板
                try {
                    return Material.valueOf("WHITE_STAINED_GLASS_PANE");
                } catch (IllegalArgumentException e) {
                    try {
                        return Material.valueOf("GLASS_PANE");
                    } catch (IllegalArgumentException e2) {
                        return Material.valueOf("THIN_GLASS");
                    }
                }
            case "COMMAND":
                // 1.13+中COMMAND改为COMMAND_BLOCK
                try {
                    return Material.valueOf("COMMAND_BLOCK");
                } catch (IllegalArgumentException e) {
                    return Material.STONE;
                }
            case "GOLD_AXE":
                // 1.20.1中GOLD_AXE改为GOLDEN_AXE
                try {
                    return Material.valueOf("GOLDEN_AXE");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("GOLD_AXE");
                }
            case "GOLDEN_AXE":
                // 反向兼容
                try {
                    return Material.valueOf("GOLD_AXE");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("GOLDEN_AXE");
                }
            case "EXPERIENCE_BOTTLE":
                // 1.20.1中EXP_BOTTLE改为EXPERIENCE_BOTTLE
                try {
                    return Material.valueOf("EXP_BOTTLE");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("EXPERIENCE_BOTTLE");
                }
            case "EXP_BOTTLE":
                // 反向兼容
                try {
                    return Material.valueOf("EXPERIENCE_BOTTLE");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("EXP_BOTTLE");
                }
            case "LAPIS_LAZULI":
                // 1.20.1中INK_SACK改为LAPIS_LAZULI
                try {
                    return Material.valueOf("INK_SACK");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("LAPIS_LAZULI");
                }
            case "INK_SACK":
                // 反向兼容
                try {
                    return Material.valueOf("LAPIS_LAZULI");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("INK_SACK");
                }
            case "GRASS_BLOCK":
                // 1.13+中GRASS改为GRASS_BLOCK
                try {
                    return Material.valueOf("GRASS");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("GRASS_BLOCK");
                }
            case "GRASS":
                // 反向兼容
                try {
                    return Material.valueOf("GRASS_BLOCK");
                } catch (IllegalArgumentException e) {
                    return Material.valueOf("GRASS");
                }
            default:
                // 默认返回石头作为安全选择
                return Material.STONE;
        }
    }

    /**
     * 播放兼容的声音
     * 处理不同版本间的声音名称差异
     */
    public static void playCompatibleSound(Player player, String soundName, float volume, float pitch) {
        try {
            // 1.12.2版本特殊处理
            if (isVersionAtLeast(1, 12) && !isVersionAtLeast(1, 13)) {
                Sound sound = get1_12_2CompatibleSound(soundName);
                if (sound != null) {
                    player.playSound(player.getLocation(), sound, volume, pitch);
                    return;
                }
            }

            // 修复：首先尝试作为字符串直接播放（支持模组音效）
            try {
                player.playSound(player.getLocation(), soundName, volume, pitch);
                return; // 成功播放，直接返回
            } catch (Exception e) {
                // 字符串播放失败，继续尝试枚举方式
            }

            // 尝试转换为枚举格式播放
            String enumSoundName = convertToEnumFormat(soundName);
            Sound sound = Sound.valueOf(enumSoundName);
            player.playSound(player.getLocation(), sound, volume, pitch);
        } catch (IllegalArgumentException e) {
            // 如果失败，尝试替代声音
            Sound alternativeSound = getAlternativeSound(soundName);
            if (alternativeSound != null) {
                try {
                    player.playSound(player.getLocation(), alternativeSound, volume, pitch);
                } catch (Exception e2) {
                    // 静默处理声音播放失败
                }
            }
        } catch (Exception e) {
            // 静默处理其他声音播放错误
        }
    }

    /**
     * 获取1.12.2版本兼容的音效
     */
    private static Sound get1_12_2CompatibleSound(String soundName) {
        switch (soundName.toLowerCase()) {
            case "entity.experience_orb.pickup":
            case "entity_experience_orb_pickup":
                // 1.12.2中经验球拾取音效
                try {
                    return Sound.valueOf("ENTITY_EXPERIENCE_ORB_PICKUP");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("ORB_PICKUP");
                    } catch (IllegalArgumentException e2) {
                        return Sound.valueOf("CLICK");
                    }
                }
            case "entity.item.pickup":
            case "entity_item_pickup":
                // 1.12.2中物品拾取音效
                try {
                    return Sound.valueOf("ENTITY_ITEM_PICKUP");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("ITEM_PICKUP");
                    } catch (IllegalArgumentException e2) {
                        try {
                            return Sound.valueOf("ENTITY_EXPERIENCE_ORB_PICKUP"); // 备用音效
                        } catch (IllegalArgumentException e3) {
                            return Sound.valueOf("CLICK");
                        }
                    }
                }
            case "entity.player.levelup":
            case "entity_player_levelup":
                // 1.12.2中升级音效
                try {
                    return Sound.valueOf("ENTITY_PLAYER_LEVELUP");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("LEVEL_UP");
                    } catch (IllegalArgumentException e2) {
                        return Sound.valueOf("CLICK");
                    }
                }
            case "block.chest.open":
            case "block_chest_open":
                // 1.12.2中箱子打开音效
                try {
                    return Sound.valueOf("BLOCK_CHEST_OPEN");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("CHEST_OPEN");
                    } catch (IllegalArgumentException e2) {
                        return Sound.valueOf("CLICK");
                    }
                }
            case "block.chest.close":
            case "block_chest_close":
                // 1.12.2中箱子关闭音效
                try {
                    return Sound.valueOf("BLOCK_CHEST_CLOSE");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("CHEST_CLOSE");
                    } catch (IllegalArgumentException e2) {
                        return Sound.valueOf("CLICK");
                    }
                }
            case "block.note_block.pling":
            case "block_note_block_pling":
                // 1.12.2中音符盒音效
                try {
                    return Sound.valueOf("BLOCK_NOTE_PLING");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("NOTE_PLING");
                    } catch (IllegalArgumentException e2) {
                        try {
                            // 1.12.2版本可能使用的其他名称
                            return Sound.valueOf("BLOCK_NOTE_BLOCK_PLING");
                        } catch (IllegalArgumentException e3) {
                            try {
                                return Sound.valueOf("ENTITY_EXPERIENCE_ORB_PICKUP"); // 备用音效
                            } catch (IllegalArgumentException e4) {
                                return Sound.valueOf("CLICK");
                            }
                        }
                    }
                }
            default:
                return null;
        }
    }

    /**
     * 将字符串格式的音效名称转换为枚举格式
     */
    private static String convertToEnumFormat(String soundName) {
        // 将点号替换为下划线，并转换为大写
        return soundName.replace(".", "_").toUpperCase();
    }

    /**
     * 获取替代声音
     */
    private static Sound getAlternativeSound(String soundName) {
        switch (soundName.toLowerCase()) {
            case "note_pling":
            case "block.note_block.pling":
                // 1.13+中NOTE_PLING改为BLOCK_NOTE_BLOCK_PLING
                try {
                    return Sound.valueOf("BLOCK_NOTE_BLOCK_PLING");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("NOTE_PLING");
                    } catch (IllegalArgumentException e2) {
                        return null;
                    }
                }
            case "level_up":
            case "entity.player.levelup":
            case "entity_player_levelup":
                // 1.13+中LEVEL_UP改为ENTITY_PLAYER_LEVELUP
                try {
                    return Sound.valueOf("ENTITY_PLAYER_LEVELUP");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("LEVEL_UP");
                    } catch (IllegalArgumentException e2) {
                        return null;
                    }
                }
            case "orb_pickup":
            case "entity.experience_orb.pickup":
            case "entity_experience_orb_pickup":
                // 经验球拾取音效
                try {
                    return Sound.valueOf("ENTITY_EXPERIENCE_ORB_PICKUP");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("ORB_PICKUP");
                    } catch (IllegalArgumentException e2) {
                        // 1.12.2版本兼容
                        try {
                            return Sound.valueOf("ENTITY_EXPERIENCE_ORB_PICKUP");
                        } catch (IllegalArgumentException e3) {
                            // 最后尝试通用音效
                            try {
                                return Sound.valueOf("CLICK");
                            } catch (IllegalArgumentException e4) {
                                return null;
                            }
                        }
                    }
                }
            case "item_pickup":
            case "entity.item.pickup":
            case "entity_item_pickup":
                // 物品拾取音效
                try {
                    return Sound.valueOf("ENTITY_ITEM_PICKUP");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("ITEM_PICKUP");
                    } catch (IllegalArgumentException e2) {
                        // 1.12.2版本兼容
                        try {
                            return Sound.valueOf("ENTITY_ITEM_PICKUP");
                        } catch (IllegalArgumentException e3) {
                            // 最后尝试通用音效
                            try {
                                return Sound.valueOf("CLICK");
                            } catch (IllegalArgumentException e4) {
                                return null;
                            }
                        }
                    }
                }
            case "click":
            case "ui.button.click":
            case "ui_button_click":
                // 按钮点击音效
                try {
                    return Sound.valueOf("UI_BUTTON_CLICK");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("CLICK");
                    } catch (IllegalArgumentException e2) {
                        return null;
                    }
                }
            case "chest_open":
            case "block.chest.open":
            case "block_chest_open":
                // 箱子打开音效
                try {
                    return Sound.valueOf("BLOCK_CHEST_OPEN");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("CHEST_OPEN");
                    } catch (IllegalArgumentException e2) {
                        return null;
                    }
                }
            case "chest_close":
            case "block.chest.close":
            case "block_chest_close":
                // 箱子关闭音效
                try {
                    return Sound.valueOf("BLOCK_CHEST_CLOSE");
                } catch (IllegalArgumentException e) {
                    try {
                        return Sound.valueOf("CHEST_CLOSE");
                    } catch (IllegalArgumentException e2) {
                        return null;
                    }
                }
            default:
                return null;
        }
    }

    /**
     * 获取兼容的实体类型
     * 处理不同版本间的实体类型差异
     */
    public static org.bukkit.entity.EntityType getCompatibleEntityType(String entityName) {
        try {
            return org.bukkit.entity.EntityType.valueOf(entityName);
        } catch (IllegalArgumentException e) {
            // 尝试替代实体类型
            switch (entityName) {
                case "ARMOR_STAND":
                    return org.bukkit.entity.EntityType.ARMOR_STAND;
                case "PLAYER":
                    return org.bukkit.entity.EntityType.PLAYER;
                default:
                    return org.bukkit.entity.EntityType.ARMOR_STAND; // 默认返回盔甲架
            }
        }
    }

    /**
     * 检查是否为有效的物品材料
     * 避免使用无效材料导致的问题
     */
    public static boolean isValidItemMaterial(Material material) {
        if (material == null) return false;

        try {
            // 尝试创建物品来验证材料是否有效
            new org.bukkit.inventory.ItemStack(material, 1);

            // 排除一些不应该作为物品的材料
            String name = material.name();
            if (name.contains("AIR") ||
                name.contains("VOID") ||
                name.contains("CAVE_AIR") ||
                name.contains("STRUCTURE_VOID")) {
                return false;
            }

            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取安全的默认材料
     * 当所有其他材料都失败时使用
     */
    public static Material getSafeFallbackMaterial() {
        // 按优先级尝试安全材料
        String[] safeMaterials = {
            "STONE", "COBBLESTONE", "DIRT", "WOOD", "OAK_WOOD",
            "PLANKS", "OAK_PLANKS", "STICK", "APPLE"
        };

        for (String materialName : safeMaterials) {
            try {
                Material material = Material.valueOf(materialName);
                if (isValidItemMaterial(material)) {
                    return material;
                }
            } catch (Exception e) {
                // 继续尝试下一个
            }
        }

        // 最后的保险措施
        return Material.values()[0];
    }

    /**
     * 检查当前版本是否支持 CustomModelData
     * CustomModelData 在 1.14+ 版本中可用
     */
    public static boolean supportsCustomModelData() {
        return isVersionAtLeast(1, 14);
    }

    /**
     * 为物品设置 CustomModelData (仅在支持的版本中生效)
     * @param item 要设置的物品
     * @param customModelData CustomModelData 值 (0表示不设置)
     * @return 设置后的物品
     */
    public static org.bukkit.inventory.ItemStack setCustomModelData(org.bukkit.inventory.ItemStack item, int customModelData) {
        if (item == null || customModelData <= 0 || !supportsCustomModelData()) {
            return item;
        }

        try {
            org.bukkit.inventory.meta.ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                // 使用反射设置 CustomModelData，确保在不支持的版本中不会出错
                java.lang.reflect.Method setCustomModelDataMethod = meta.getClass().getMethod("setCustomModelData", Integer.class);
                setCustomModelDataMethod.invoke(meta, customModelData);
                item.setItemMeta(meta);
            }
        } catch (Exception e) {
            // 静默处理不支持 CustomModelData 的版本
            // 不输出错误信息，保持向后兼容
        }

        return item;
    }

    /**
     * 获取物品的 CustomModelData 值
     * @param item 要检查的物品
     * @return CustomModelData 值，如果不存在或不支持则返回0
     */
    public static int getCustomModelData(org.bukkit.inventory.ItemStack item) {
        if (item == null || !supportsCustomModelData()) {
            return 0;
        }

        try {
            org.bukkit.inventory.meta.ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                // 使用反射获取 CustomModelData
                java.lang.reflect.Method hasCustomModelDataMethod = meta.getClass().getMethod("hasCustomModelData");
                boolean hasCustomModelData = (Boolean) hasCustomModelDataMethod.invoke(meta);

                if (hasCustomModelData) {
                    java.lang.reflect.Method getCustomModelDataMethod = meta.getClass().getMethod("getCustomModelData");
                    return (Integer) getCustomModelDataMethod.invoke(meta);
                }
            }
        } catch (Exception e) {
            // 静默处理不支持 CustomModelData 的版本
        }

        return 0;
    }

    /**
     * 检查物品是否有 CustomModelData
     * @param item 要检查的物品
     * @return 如果有 CustomModelData 则返回 true
     */
    public static boolean hasCustomModelData(org.bukkit.inventory.ItemStack item) {
        return getCustomModelData(item) > 0;
    }
}
