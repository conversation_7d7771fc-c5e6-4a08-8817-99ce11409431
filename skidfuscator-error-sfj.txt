handler=Block #X, types=[Ljava/io/IOException;], range=[Block #W, Block #V]
handler=Block #AA, types=[Ljava/lang/RuntimeException;], range=[Block #Z, Block #Y]
handler=Block #AD, types=[Ljava/lang/IllegalAccessException;], range=[Block #AC, Block #AB]
handler=Block #AG, types=[Ljava/io/IOException;], range=[Block #AF, Block #AE]
handler=Block #AJ, types=[Ljava/lang/RuntimeException;], range=[Block #AI, Block #AH]
handler=Block #AM, types=[Ljava/lang/IllegalAccessException;], range=[Block #AL, Block #AK]
handler=Block #AP, types=[Ljava/lang/RuntimeException;], range=[Block #AO, Block #AN]
handler=Block #AS, types=[Ljava/lang/IllegalAccessException;], range=[Block #AR, Block #AQ]
handler=Block #AV, types=[Ljava/io/IOException;], range=[Block #AU, Block #AT]
handler=Block #AY, types=[Ljava/lang/RuntimeException;], range=[Block #AX, Block #AW]
handler=Block #BB, types=[Ljava/io/IOException;], range=[Block #BA, Block #AZ]
handler=Block #BE, types=[Ljava/lang/IllegalAccessException;], range=[Block #BD, Block #BC]
handler=Block #BH, types=[Ljava/io/IOException;], range=[Block #BG, Block #BF]
handler=Block #BK, types=[Ljava/lang/IllegalAccessException;], range=[Block #BJ, Block #BI]
handler=Block #BN, types=[Ljava/lang/RuntimeException;], range=[Block #BM, Block #BL]
handler=Block #BQ, types=[Ljava/lang/IllegalAccessException;], range=[Block #BP, Block #BO]
handler=Block #BT, types=[Ljava/io/IOException;], range=[Block #BS, Block #BR]
===#Block A(size=2, flags=1)===
   0. synth(lvar0 = lvar0);
   1. synth(lvar1 = lvar1);
      -> Immediate #A -> #B
===#Block B(size=3, flags=0)===
   0. lvar3 = lvar1;
   1. svar37 = {lvar3 ^ lvar35};
   2. switch (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(svar37)) {
      case 111071648:
      	 goto	#E
      case 111071649:
      	 goto	#K
      case 111071650:
      	 goto	#R
      case 111071651:
      	 goto	#C
      case 111071652:
      	 goto	#P
      case 111071653:
      	 goto	#H
      case 111071654:
      	 goto	#T
      case 111071659:
      	 goto	#J
      case 111071660:
      	 goto	#I
      case 111071661:
      	 goto	#O
      case 111071672:
      	 goto	#M
      case 111071673:
      	 goto	#L
      case 111071674:
      	 goto	#F
      case 111071675:
      	 goto	#D
      case 111071676:
      	 goto	#S
      case 111071678:
      	 goto	#G
      case 111071679:
      	 goto	#N
      default:
      	 goto	#U
   }
      -> Switch[111071654] #B -> #T
      -> Switch[111071676] #B -> #S
      -> Switch[111071650] #B -> #R
      -> Switch[111071652] #B -> #P
      -> Switch[111071661] #B -> #O
      -> Switch[111071679] #B -> #N
      -> Switch[111071672] #B -> #M
      -> Switch[111071673] #B -> #L
      -> Switch[111071649] #B -> #K
      -> Switch[111071659] #B -> #J
      -> Switch[111071660] #B -> #I
      -> DefaultSwitch #B -> #U
      -> Switch[111071653] #B -> #H
      -> Switch[111071678] #B -> #G
      -> Switch[111071674] #B -> #F
      -> Switch[111071648] #B -> #E
      -> Switch[111071675] #B -> #D
      -> Switch[111071651] #B -> #C
      <- Immediate #A -> #B
===#Block C(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar5 = lvar0;
   2. lvar4 = {1670605979 ^ lvar35};
   3. _consume(lvar5.adjustSearchSpeed(lvar4, 83052625));
   4. goto AI
      -> UnconditionalJump[GOTO] #C -> #AI
      <- Switch[111071651] #B -> #C
===#Block AI(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 7748119)
      goto AH
   1. throw nullconst;
      -> TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AI -> #AH
      <- UnconditionalJump[GOTO] #C -> #AI
===#Block AH(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AI -> #AH
===#Block AJ(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AJ -> #U
      <- TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AI...AH] -> AJ ([Ljava/lang/RuntimeException;])
===#Block D(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar6 = lvar0;
   2. lvar23 = {-326628916 ^ lvar35};
   3. _consume(lvar6.adjustSearchSpeed(lvar23, 83052625));
   4. goto BA
      -> UnconditionalJump[GOTO] #D -> #BA
      <- Switch[111071675] #B -> #D
===#Block BA(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 241976705)
      goto AZ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BA -> #AZ
      -> TryCatch range: [BA...AZ] -> BB ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #D -> #BA
===#Block AZ(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BA...AZ] -> BB ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BA -> #AZ
===#Block BB(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #BB -> #U
      <- TryCatch range: [BA...AZ] -> BB ([Ljava/io/IOException;])
      <- TryCatch range: [BA...AZ] -> BB ([Ljava/io/IOException;])
===#Block E(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar7 = lvar0;
   2. lvar24 = {-1910196313 ^ lvar35};
   3. _consume(lvar7.adjustAmount(lvar24, 1029574434));
   4. goto AF
      -> UnconditionalJump[GOTO] #E -> #AF
      <- Switch[111071648] #B -> #E
===#Block AF(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 116651892)
      goto AE
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AF -> #AE
      -> TryCatch range: [AF...AE] -> AG ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #E -> #AF
===#Block AE(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AF...AE] -> AG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AF -> #AE
===#Block AG(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AG -> #U
      <- TryCatch range: [AF...AE] -> AG ([Ljava/io/IOException;])
      <- TryCatch range: [AF...AE] -> AG ([Ljava/io/IOException;])
===#Block F(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar8 = lvar0;
   2. lvar25 = {-837359178 ^ lvar35};
   3. _consume(lvar8.adjustSearchSpeed(lvar25, 83052625));
   4. goto AL
      -> UnconditionalJump[GOTO] #F -> #AL
      <- Switch[111071674] #B -> #F
===#Block AL(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 218938604)
      goto AK
   1. throw nullconst;
      -> TryCatch range: [AL...AK] -> AM ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AL -> #AK
      <- UnconditionalJump[GOTO] #F -> #AL
===#Block AK(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AL...AK] -> AM ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AL -> #AK
===#Block AM(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AM -> #U
      <- TryCatch range: [AL...AK] -> AM ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AL...AK] -> AM ([Ljava/lang/IllegalAccessException;])
===#Block G(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar9 = lvar0;
   2. _consume(lvar9.resetChance(1386570215));
   3. goto AC
      -> UnconditionalJump[GOTO] #G -> #AC
      <- Switch[111071678] #B -> #G
===#Block AC(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 161964607)
      goto AB
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AC -> #AB
      -> TryCatch range: [AC...AB] -> AD ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #G -> #AC
===#Block AB(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AC...AB] -> AD ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AC -> #AB
===#Block AD(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AD -> #U
      <- TryCatch range: [AC...AB] -> AD ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AC...AB] -> AD ([Ljava/lang/IllegalAccessException;])
===#Block H(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar10 = lvar0;
   2. lvar26 = {-825252785 ^ lvar35};
   3. _consume(lvar10.adjustAmount(lvar26, 1029574434));
   4. goto BP
      -> UnconditionalJump[GOTO] #H -> #BP
      <- Switch[111071653] #B -> #H
===#Block BP(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 260122699)
      goto BO
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BP -> #BO
      -> TryCatch range: [BP...BO] -> BQ ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #H -> #BP
===#Block BO(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BP...BO] -> BQ ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BP -> #BO
===#Block BQ(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #BQ -> #U
      <- TryCatch range: [BP...BO] -> BQ ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BP...BO] -> BQ ([Ljava/lang/IllegalAccessException;])
===#Block I(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar11 = lvar0;
   2. _consume(lvar11.editCommands(1997560677));
   3. goto BD
      -> UnconditionalJump[GOTO] #I -> #BD
      <- Switch[111071660] #B -> #I
===#Block BD(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 192758733)
      goto BC
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BD -> #BC
      -> TryCatch range: [BD...BC] -> BE ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #I -> #BD
===#Block BC(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BD...BC] -> BE ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BD -> #BC
===#Block BE(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #BE -> #U
      <- TryCatch range: [BD...BC] -> BE ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BD...BC] -> BE ([Ljava/lang/IllegalAccessException;])
===#Block J(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar12 = lvar0;
   2. _consume(lvar12.saveAndReturn(819145927));
   3. goto Z
      -> UnconditionalJump[GOTO] #J -> #Z
      <- Switch[111071659] #B -> #J
===#Block Z(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 65763716)
      goto Y
   1. throw nullconst;
      -> TryCatch range: [Z...Y] -> AA ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #Z -> #Y
      <- UnconditionalJump[GOTO] #J -> #Z
===#Block Y(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [Z...Y] -> AA ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #Z -> #Y
===#Block AA(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AA -> #U
      <- TryCatch range: [Z...Y] -> AA ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [Z...Y] -> AA ([Ljava/lang/RuntimeException;])
===#Block K(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar13 = lvar0;
   2. lvar27 = {1865823829 ^ lvar35};
   3. _consume(lvar13.adjustAmount(lvar27, 1029574434));
   4. goto AX
      -> UnconditionalJump[GOTO] #K -> #AX
      <- Switch[111071649] #B -> #K
===#Block AX(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 174480570)
      goto AW
   1. throw nullconst;
      -> TryCatch range: [AX...AW] -> AY ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AX -> #AW
      <- UnconditionalJump[GOTO] #K -> #AX
===#Block AW(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AX...AW] -> AY ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AX -> #AW
===#Block AY(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AY -> #U
      <- TryCatch range: [AX...AW] -> AY ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AX...AW] -> AY ([Ljava/lang/RuntimeException;])
===#Block L(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar14 = lvar0;
   2. lvar28 = {535124776 ^ lvar35};
   3. _consume(lvar14.adjustChance(lvar28, 1079902377));
   4. goto BS
      -> UnconditionalJump[GOTO] #L -> #BS
      <- Switch[111071673] #B -> #L
===#Block BS(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 12104661)
      goto BR
   1. throw nullconst;
      -> TryCatch range: [BS...BR] -> BT ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #BS -> #BR
      <- UnconditionalJump[GOTO] #L -> #BS
===#Block BR(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [BS...BR] -> BT ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #BS -> #BR
===#Block BT(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #BT -> #U
      <- TryCatch range: [BS...BR] -> BT ([Ljava/io/IOException;])
      <- TryCatch range: [BS...BR] -> BT ([Ljava/io/IOException;])
===#Block M(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar15 = lvar0;
   2. lvar29 = {1232766211 ^ lvar35};
   3. _consume(lvar15.adjustChance(lvar29, 1079902377));
   4. goto BJ
      -> UnconditionalJump[GOTO] #M -> #BJ
      <- Switch[111071672] #B -> #M
===#Block BJ(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 6023525)
      goto BI
   1. throw nullconst;
      -> TryCatch range: [BJ...BI] -> BK ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #BJ -> #BI
      <- UnconditionalJump[GOTO] #M -> #BJ
===#Block BI(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [BJ...BI] -> BK ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #BJ -> #BI
===#Block BK(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #BK -> #U
      <- TryCatch range: [BJ...BI] -> BK ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [BJ...BI] -> BK ([Ljava/lang/IllegalAccessException;])
===#Block N(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar16 = lvar0;
   2. _consume(lvar16.resetAmount(657008041));
   3. goto AU
      -> UnconditionalJump[GOTO] #N -> #AU
      <- Switch[111071679] #B -> #N
===#Block AU(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 85000027)
      goto AT
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AU -> #AT
      -> TryCatch range: [AU...AT] -> AV ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #N -> #AU
===#Block AT(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [AU...AT] -> AV ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AU -> #AT
===#Block AV(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AV -> #U
      <- TryCatch range: [AU...AT] -> AV ([Ljava/io/IOException;])
      <- TryCatch range: [AU...AT] -> AV ([Ljava/io/IOException;])
===#Block O(size=3, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar17 = lvar0;
   2. _consume(lvar17.cancelAndReturn(675615461));
      -> Immediate #O -> #U
      <- Switch[111071661] #B -> #O
===#Block P(size=4, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar18 = lvar0;
   2. _consume(lvar18.resetSearchSpeed(236281246));
   3. goto AR
      -> UnconditionalJump[GOTO] #P -> #AR
      <- Switch[111071652] #B -> #P
===#Block AR(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 243766119)
      goto AQ
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #AR -> #AQ
      -> TryCatch range: [AR...AQ] -> AS ([Ljava/lang/IllegalAccessException;])
      <- UnconditionalJump[GOTO] #P -> #AR
===#Block AQ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException();
      -> TryCatch range: [AR...AQ] -> AS ([Ljava/lang/IllegalAccessException;])
      <- ConditionalJump[IF_ICMPEQ] #AR -> #AQ
===#Block AS(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AS -> #U
      <- TryCatch range: [AR...AQ] -> AS ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AR...AQ] -> AS ([Ljava/lang/IllegalAccessException;])
===#Block R(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar20 = lvar0;
   2. lvar31 = {855172162 ^ lvar35};
   3. _consume(lvar20.adjustAmount(lvar31, 1029574434));
   4. goto W
      -> UnconditionalJump[GOTO] #R -> #W
      <- Switch[111071650] #B -> #R
===#Block W(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 95510175)
      goto V
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #W -> #V
      -> TryCatch range: [W...V] -> X ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #R -> #W
===#Block V(size=1, flags=0)===
   0. throw new java/io/IOException();
      -> TryCatch range: [W...V] -> X ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #W -> #V
===#Block X(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #X -> #U
      <- TryCatch range: [W...V] -> X ([Ljava/io/IOException;])
      <- TryCatch range: [W...V] -> X ([Ljava/io/IOException;])
===#Block S(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar21 = lvar0;
   2. lvar32 = {-1773621525 ^ lvar35};
   3. _consume(lvar21.adjustChance(lvar32, 1079902377));
   4. goto AO
      -> UnconditionalJump[GOTO] #S -> #AO
      <- Switch[111071676] #B -> #S
===#Block AO(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 169482724)
      goto AN
   1. throw nullconst;
      -> TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AO -> #AN
      <- UnconditionalJump[GOTO] #S -> #AO
===#Block AN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AO -> #AN
===#Block AP(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #AP -> #U
      <- TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AO...AN] -> AP ([Ljava/lang/RuntimeException;])
===#Block T(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar0;
   2. lvar33 = {844646707 ^ lvar35};
   3. _consume(lvar22.adjustSearchSpeed(lvar33, 83052625));
   4. goto BM
      -> UnconditionalJump[GOTO] #T -> #BM
      <- Switch[111071654] #B -> #T
===#Block BM(size=2, flags=0)===
   0. if (ixyzbarjojujhhtx.bagnqhxomqwuqono.yntgqpwrrswclyol(lvar35) == 211561359)
      goto BL
   1. throw nullconst;
      -> ConditionalJump[IF_ICMPEQ] #BM -> #BL
      -> TryCatch range: [BM...BL] -> BN ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #T -> #BM
===#Block BL(size=1, flags=0)===
   0. throw new java/lang/RuntimeException();
      -> TryCatch range: [BM...BL] -> BN ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #BM -> #BL
===#Block BN(size=2, flags=0)===
   0. _consume(catch());
   1. goto U
      -> UnconditionalJump[GOTO] #BN -> #U
      <- TryCatch range: [BM...BL] -> BN ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [BM...BL] -> BN ([Ljava/lang/RuntimeException;])
===#Block U(size=2, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. return;
      <- UnconditionalJump[GOTO] #AS -> #U
      <- UnconditionalJump[GOTO] #AM -> #U
      <- UnconditionalJump[GOTO] #BT -> #U
      <- UnconditionalJump[GOTO] #BE -> #U
      <- UnconditionalJump[GOTO] #AD -> #U
      <- UnconditionalJump[GOTO] #BQ -> #U
      <- UnconditionalJump[GOTO] #BK -> #U
      <- UnconditionalJump[GOTO] #AJ -> #U
      <- UnconditionalJump[GOTO] #AV -> #U
      <- UnconditionalJump[GOTO] #AP -> #U
      <- UnconditionalJump[GOTO] #BN -> #U
      <- UnconditionalJump[GOTO] #AY -> #U
      <- UnconditionalJump[GOTO] #BH -> #U
      <- Immediate #O -> #U
      <- UnconditionalJump[GOTO] #AG -> #U
      <- UnconditionalJump[GOTO] #BB -> #U
      <- DefaultSwitch #B -> #U
      <- UnconditionalJump[GOTO] #X -> #U
      <- UnconditionalJump[GOTO] #AA -> #U
