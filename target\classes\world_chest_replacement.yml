# 世界箱子替换配置
# 当玩家在指定世界打开箱子时，100%替换成摸金箱（除了已经是摸金箱的）
# 概率只用于决定摸金箱的种类

# 全局设置
global:
  # 是否启用箱子替换功能
  enabled: true
  # 是否在替换时播放音效
  play_sound: true
  # 替换音效类型 (BLOCK_CHEST_OPEN, ENTITY_EXPERIENCE_ORB_PICKUP, etc.)
  replacement_sound: "ENTITY_EXPERIENCE_ORB_PICKUP"
  # 是否显示替换消息
  show_message: true
  # 替换消息 - 支持变量: {type} = 摸金箱种类名称
  replacement_message: "§6这个箱子被神秘力量转化为了{type}摸金箱！"

# 世界配置 - 支持每个世界多个替换规则
worlds:
  # 示例世界配置
  world:
    # 是否在此世界启用替换功能
    enabled: false

    # 替换规则列表 - 可以配置多个规则
    rules:
      # 规则1：普通箱子替换
      - enabled: true
        # 可替换的方块类型
        replaceable_blocks:
          - "CHEST"
        # 摸金箱类型权重配置
        chest_types:
          common: 50
          rare: 30
          epic: 15
          legendary: 4
          mythic: 1
        # 排除的坐标范围 (可选)
        excluded_areas: []

      # 规则2：模组容器替换
      - enabled: false
        # 模组容器的方块ID（需要根据实际模组调整）
        replaceable_blocks:
          # Iron Chests 模组
          - "ironchest:iron_chest"
          - "ironchest:gold_chest"
          - "ironchest:diamond_chest"
          - "ironchest:crystal_chest"
          - "ironchest:obsidian_chest"
          # Sophisticated Storage 模组
          - "sophisticatedstorage:barrel"
          - "sophisticatedstorage:chest"
          - "sophisticatedstorage:limited_barrel"
          # Storage Drawers 模组
          - "storagedrawers:basicdrawers"
          - "storagedrawers:compdrawers"
          # 其他常见模组容器（根据需要添加）
          # - "模组名:容器名"
        # 模组容器替换为更高级的摸金箱
        chest_types:
          rare: 30
          epic: 40
          legendary: 25
          mythic: 5
        excluded_areas: []

  # 地狱世界示例
  world_nether:
    enabled: false
    rules:
      - enabled: true
        replaceable_blocks:
          - "CHEST"
        chest_types:
          rare: 40
          epic: 35
          legendary: 20
          mythic: 5
        excluded_areas: []

  # 末地世界示例
  world_the_end:
    enabled: false
    rules:
      - enabled: true
        replaceable_blocks:
          - "CHEST"
        chest_types:
          epic: 30
          legendary: 40
          mythic: 30
        excluded_areas: []


