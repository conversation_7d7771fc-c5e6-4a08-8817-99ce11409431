===============================================
        HangEvacuation 模组容器支持配置指南
===============================================

🎮 插件名称: HangEvacuation (Hang摸金撤离)
📅 更新日期: 2024-12-19
🔧 版本号: 1.7.0+
👨‍💻 作者: hangzong(航总)
📞 技术支持: 微信 hang060217

===============================================
              🎯 模组容器支持说明
===============================================

✅ **支持状态**: 已支持模组容器作为摸金箱材质
✅ **兼容性**: 自动降级到相似的原版容器
✅ **智能识别**: 多种方式尝试获取模组材料
✅ **错误处理**: 无法识别时使用默认材料

===============================================
              📋 配置方法
===============================================

🔧 【基本配置格式】
在 mojin.yml 中设置 material 字段：

```yaml
chest_types:
  modded_chest:
    name: "§d模组容器"
    display_name: "§d特殊模组容器"
    material: "modname:container_name"  # 模组容器ID
    # 其他配置...
```

🎯 【常见模组容器示例】

📦 **Iron Chests 模组**
```yaml
  iron_chest:
    name: "§7铁箱子"
    material: "ironchest:iron_chest"
    
  diamond_chest:
    name: "§b钻石箱子"
    material: "ironchest:diamond_chest"
    
  obsidian_chest:
    name: "§5黑曜石箱子"
    material: "ironchest:obsidian_chest"
```

🏭 **Industrial Craft 模组**
```yaml
  personal_safe:
    name: "§a个人保险箱"
    material: "ic2:personal_safe"
    
  trade_o_mat:
    name: "§e自动交易机"
    material: "ic2:trade_o_mat"
```

🔧 **Thermal Expansion 模组**
```yaml
  strongbox:
    name: "§6保险箱"
    material: "thermalexpansion:strongbox"
    
  cache:
    name: "§7缓存箱"
    material: "thermalexpansion:cache"
```

🎒 **Storage Drawers 模组**
```yaml
  drawer_1x1:
    name: "§e储物抽屉"
    material: "storagedrawers:basicdrawers"
```

===============================================
              🔧 技术实现原理
===============================================

🎯 【材料获取流程】

1. **原版材料检查**
   ```java
   // 首先尝试原版材料
   Material.valueOf(materialName.toUpperCase())
   ```

2. **NMS模组材料获取**
   ```java
   // 通过NMS获取模组材料
   plugin.getNMSManager().getMaterialByName(materialName)
   ```

3. **Bukkit命名空间获取**
   ```java
   // 尝试Bukkit的命名空间材料
   Material.getMaterial(materialName.toUpperCase())
   ```

4. **智能映射降级**
   ```java
   // 根据名称特征映射到相似原版容器
   if (name.contains("chest")) return Material.CHEST;
   if (name.contains("trapped")) return Material.TRAPPED_CHEST;
   if (name.contains("ender")) return Material.ENDER_CHEST;
   ```

===============================================
              ⚠️ 注意事项
===============================================

🚨 **兼容性问题**

1. **客户端兼容性**
   - 没有安装模组的玩家可能看到错误材质
   - 建议在模组服务器中使用

2. **版本兼容性**
   - 不同模组版本的容器ID可能不同
   - 需要根据实际模组版本调整配置

3. **服务端兼容性**
   - 确保服务端已安装对应模组
   - Forge/Fabric服务端支持更好

🛡️ **安全降级机制**

如果模组容器无法识别，插件会：
1. 记录警告日志
2. 自动使用默认的 CHEST 材料
3. 保证功能正常运行

===============================================
              📝 配置示例
===============================================

🎯 【完整配置示例】

```yaml
# mojin.yml 模组容器配置示例
chest_types:
  # 原版容器
  vanilla_chest:
    name: "§6普通摸金箱"
    material: "CHEST"
    slots: 5
    refresh_time: 5
    
  # Iron Chests 模组容器
  iron_treasure:
    name: "§7铁制宝箱"
    display_name: "§7强化铁制宝箱"
    description:
      - "§7使用铁制材料强化的宝箱"
      - "§7容量更大，更加坚固"
    material: "ironchest:iron_chest"
    custom_model_data: 2001
    rarity: "§7普通+"
    rarity_color: "§7"
    enabled: true
    slots: 8                    # 铁箱子容量更大
    refresh_time: 8
    
  diamond_treasure:
    name: "§b钻石宝箱"
    display_name: "§b奢华钻石宝箱"
    description:
      - "§7使用钻石打造的豪华宝箱"
      - "§b蕴含着强大的魔法力量"
      - "§e传说中的终极宝箱"
    material: "ironchest:diamond_chest"
    custom_model_data: 2002
    rarity: "§b史诗"
    rarity_color: "§b"
    enabled: true
    slots: 12                   # 钻石箱子容量最大
    refresh_time: 30            # 刷新时间最长
    
  # Thermal Expansion 模组容器
  thermal_strongbox:
    name: "§6热力保险箱"
    display_name: "§6热力学保险箱"
    description:
      - "§7热力膨胀模组的高级容器"
      - "§6具有自动分类功能"
    material: "thermalexpansion:strongbox"
    rarity: "§6稀有"
    enabled: true
    slots: 10
    refresh_time: 15
```

===============================================
              🎮 使用方法
===============================================

📋 【管理员操作】

1. **配置模组容器**
   ```bash
   # 编辑 mojin.yml 文件
   # 添加模组容器配置
   ```

2. **重载配置**
   ```bash
   /evac reload
   ```

3. **给予模组摸金箱**
   ```bash
   /evac give iron_treasure player1 1
   /evac give diamond_treasure player1 1
   ```

4. **检查日志**
   ```bash
   # 查看服务器日志确认模组容器是否正确加载
   # 如果看到警告，说明模组容器无法识别，已降级到默认材料
   ```

===============================================
              🔍 故障排除
===============================================

❓ **常见问题**

Q: 配置了模组容器但显示的还是普通箱子？
A: 检查模组是否正确安装，容器ID是否正确

Q: 玩家看到的是错误的材质？
A: 确保所有玩家都安装了对应的模组

Q: 服务器报错无法识别材料？
A: 插件会自动降级到默认材料，功能不受影响

🔧 **调试方法**

1. **启用调试模式**
   ```yaml
   # 在 mojin.yml 中添加
   debug:
     enabled: true
   ```

2. **查看日志输出**
   ```
   [INFO] 摸金箱种类 iron_treasure 使用模组材料: ironchest:iron_chest
   [WARN] 摸金箱种类 unknown_chest 的材料无效: unknown:container，使用默认材料 CHEST
   ```

===============================================
              📦 模组推荐
===============================================

🎯 **推荐的容器模组**

1. **Iron Chests** - 经典的箱子扩展模组
   - 提供多种材质的箱子
   - 容量递增，适合做稀有度区分

2. **Storage Drawers** - 储物抽屉模组
   - 独特的抽屉外观
   - 适合特殊主题的摸金箱

3. **Thermal Expansion** - 热力膨胀模组
   - 科技风格的容器
   - 适合现代/科幻主题服务器

4. **Applied Energistics 2** - 应用能源模组
   - 高科技存储设备
   - 适合高级摸金箱

===============================================
              🎉 总结
===============================================

🎯 **模组容器支持特性**
✅ 支持任意模组容器作为摸金箱材质
✅ 智能降级机制保证兼容性
✅ 多种获取方式确保成功率
✅ 详细日志帮助调试问题

🔧 **使用建议**
- 在模组服务器中使用效果最佳
- 确保所有玩家安装相同模组
- 测试配置后再正式使用
- 关注日志输出确认加载状态

🎮 **扩展可能**
- 可以为不同模组设计专门的摸金箱主题
- 结合模组特色设计独特的奖励系统
- 利用模组容器的特殊功能增强游戏体验

===============================================
              🔧 技术支持
===============================================

🎮 如有疑问，请联系：
- 微信: hang060217
- QQ群: 361919269
- 作者: hangzong(航总)
- 标签: Hang系列插件

💡 建议：
- 在测试服务器上先测试模组容器配置
- 备份配置文件以防出错
- 关注插件更新获得更好的模组支持
