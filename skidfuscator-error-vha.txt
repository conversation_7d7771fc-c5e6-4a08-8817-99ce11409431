handler=Block #DZ, types=[Ljava/lang/IllegalArgumentException;], range=[#B...#B]
handler=Block #EC, types=[Ljava/lang/RuntimeException;], range=[Block #AI, Block #AH]
handler=Block #EG, types=[Ljava/io/IOException;], range=[Block #AL, Block #AK]
handler=Block #EK, types=[Ljava/lang/RuntimeException;], range=[Block #AO, Block #AN]
handler=Block #EO, types=[Ljava/lang/RuntimeException;], range=[Block #AR, Block #AQ]
handler=Block #ES, types=[Ljava/lang/IllegalAccessException;], range=[Block #AU, Block #AT]
handler=Block #EW, types=[Ljava/io/IOException;], range=[Block #AX, Block #AW]
===#Block A(size=5, flags=1)===
   0. lvar78 = {32702804 ^ {1633809901 ^ 1944000365}};
   1. synth(lvar0 = lvar0);
   2. synth(lvar1 = lvar1);
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1703993696)
      goto BK
   4. lvar78 = {1207062967 ^ lvar78};
      -> ConditionalJump[IF_ICMPNE] #A -> #BK
      -> Immediate #A -> #B
===#Block B(size=5, flags=0)===
   0. lvar3 = lvar1;
   1. lvar10 = lvar3.toUpperCase();
   2. lvar11 = org.bukkit.Material.valueOf(lvar10);
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1510108390)
      goto BT
   4. lvar78 = {1092541633 ^ lvar78};
      -> Immediate #B -> #AG
      -> TryCatch range: [B...B] -> DZ ([Ljava/lang/IllegalArgumentException;])
      -> ConditionalJump[IF_ICMPNE] #B -> #BT
      <- Immediate #A -> #B
===#Block DZ(size=1, flags=0)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.ffnfysxybrbcrlmb(lvar78)) {
      case -1510108390:
      	 goto	#EA
      default:
      	 goto	#EB
   }
      -> Switch[-1510108390] #DZ -> #EA
      -> DefaultSwitch #DZ -> #EB
      <- TryCatch range: [B...B] -> DZ ([Ljava/lang/IllegalArgumentException;])
===#Block EB(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #DZ -> #EB
===#Block EA(size=2, flags=10100)===
   0. lvar78 = {1527765557 ^ lvar78};
   1. goto C
      -> UnconditionalJump[GOTO] #EA -> #C
      <- Switch[-1510108390] #DZ -> #EA
===#Block C(size=5, flags=0)===
   0. lvar12 = catch();
   1. // Frame: locals[0] [] stack[1] [java/lang/IllegalArgumentException]
   2. lvar5 = lvar12;
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2105754288)
      goto BL
   4. lvar78 = {990746635 ^ lvar78};
      -> Immediate #C -> #D
      -> ConditionalJump[IF_ICMPNE] #C -> #BL
      <- UnconditionalJump[GOTO] #EA -> #C
===#Block D(size=5, flags=0)===
   0. lvar13 = lvar1;
   1. lvar14 = lvar13.toUpperCase();
   2. lvar6 = lvar14;
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1511030039)
      goto BG
   4. lvar78 = {1709706652 ^ lvar78};
      -> ConditionalJump[IF_ICMPNE] #D -> #BG
      -> Immediate #D -> #E
      <- Immediate #C -> #D
===#Block E(size=9, flags=0)===
   0. lvar15 = lvar6;
   1. lvar7 = lvar15;
   2. lvar16 = {-1364590018 ^ lvar78};
   3. lvar8 = lvar16;
   4. lvar17 = lvar7;
   5. lvar18 = lvar17.hashCode();
   6. svar80 = {lvar18 ^ lvar78};
   7. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(svar80)) {
      case 24472792:
      	 goto	#DG
      case 102479455:
      	 goto	#DH
      case 110867538:
      	 goto	#DI
      case 156907340:
      	 goto	#DK
      case 197868356:
      	 goto	#DL
      case 227817807:
      	 goto	#DN
      case 232973276:
      	 goto	#DO
      default:
      	 goto	#DQ
   }
   8. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1968181750)
      goto BD
      -> Switch[156907340] #E -> #DK
      -> Switch[110867538] #E -> #DI
      -> Switch[197868356] #E -> #DL
      -> ConditionalJump[IF_ICMPNE] #E -> #BD
      -> DefaultSwitch #E -> #DQ
      -> Switch[227817807] #E -> #DN
      -> Switch[24472792] #E -> #DG
      -> Switch[232973276] #E -> #DO
      -> Switch[102479455] #E -> #DH
      <- Immediate #D -> #E
===#Block DH(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 668779789);
   1. goto H
      -> UnconditionalJump[GOTO] #DH -> #H
      <- Switch[102479455] #E -> #DH
===#Block H(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar45 = lvar7;
   2. lvar68 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yovpiklqomirgvh(), lvar78);
   3. lvar46 = lvar45.equals(lvar68);
   4. if (lvar46 == {1988704460 ^ lvar78})
      goto CY
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1270233501)
      goto BB
   6. lvar78 = {1650707909 ^ lvar78};
      -> Immediate #H -> #I
      -> ConditionalJump[IF_ICMPEQ] #H -> #CY
      -> ConditionalJump[IF_ICMPNE] #H -> #BB
      <- UnconditionalJump[GOTO] #DH -> #H
===#Block CY(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 2000818041);
   1. goto T
      -> UnconditionalJump[GOTO] #CY -> #T
      <- ConditionalJump[IF_ICMPEQ] #H -> #CY
===#Block I(size=4, flags=0)===
   0. lvar47 = {350942479 ^ lvar78};
   1. lvar8 = lvar47;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1487427512)
      goto AZ
   3. lvar78 = {354534076 ^ lvar78};
      -> Immediate #I -> #T
      -> ConditionalJump[IF_ICMPNE] #I -> #AZ
      <- Immediate #H -> #I
===#Block DO(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 227986160:
      	 goto	#DP
      case 254375611:
      	 goto	#BX
      case 939878210:
      	 goto	#DO
      case 1839410949:
      	 goto	#J
      default:
      	 goto	#BX
   }
      -> Immediate #DO -> #DP
      -> Switch[1839410949] #DO -> #J
      -> DefaultSwitch #DO -> #BX
      -> Switch[254375611] #DO -> #BX
      -> Switch[939878210] #DO -> #DO
      -> Switch[227986160] #DO -> #DP
      <- Switch[939878210] #DO -> #DO
      <- Switch[232973276] #E -> #DO
===#Block DP(size=2, flags=100)===
   0. lvar78 = {1382986312 ^ lvar78};
   1. goto J
      -> UnconditionalJump[GOTO] #DP -> #J
      <- Immediate #DO -> #DP
      <- Switch[227986160] #DO -> #DP
===#Block J(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar48 = lvar7;
   2. lvar69 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.uugjldhzkttpuzb(), lvar78);
   3. lvar49 = lvar48.equals(lvar69);
   4. if (lvar49 == {54218633 ^ lvar78})
      goto DB
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 433749064)
      goto BC
   6. lvar78 = {854331294 ^ lvar78};
      -> ConditionalJump[IF_ICMPEQ] #J -> #DB
      -> ConditionalJump[IF_ICMPNE] #J -> #BC
      -> Immediate #J -> #K
      <- Switch[1839410949] #DO -> #J
      <- UnconditionalJump[GOTO] #DP -> #J
===#Block K(size=4, flags=0)===
   0. lvar50 = {836190228 ^ lvar78};
   1. lvar8 = lvar50;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1900412743)
      goto BN
   3. goto CJ
      -> ConditionalJump[IF_ICMPNE] #K -> #BN
      -> UnconditionalJump[GOTO] #K -> #CJ
      <- Immediate #J -> #K
===#Block CJ(size=2, flags=10100)===
   0. lvar78 = {524179225 ^ lvar78};
   1. goto AX
      -> UnconditionalJump[GOTO] #CJ -> #AX
      <- UnconditionalJump[GOTO] #K -> #CJ
===#Block AX(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 182694466)
      goto AW
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2001254513)
      goto BP
      -> ConditionalJump[IF_ICMPNE] #AX -> #BP
      -> ConditionalJump[IF_ICMPEQ] #AX -> #AW
      -> TryCatch range: [AX...AW] -> EW ([Ljava/io/IOException;])
      <- UnconditionalJump[GOTO] #CJ -> #AX
===#Block AW(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -256907456)
      goto CD
      -> TryCatch range: [AX...AW] -> EW ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPNE] #AW -> #CD
      <- ConditionalJump[IF_ICMPEQ] #AX -> #AW
===#Block CD(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -256907456)
      goto CD
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {167772058 ^ lvar78})
      goto CD
   2. _consume({1804540114 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #CD -> #CD
      <- ConditionalJump[IF_ICMPNE] #CD -> #CD
      <- ConditionalJump[IF_ICMPNE] #AW -> #CD
===#Block EW(size=1, flags=0)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.ffnfysxybrbcrlmb(lvar78)) {
      case -256907456:
      	 goto	#EY
      case 2001254513:
      	 goto	#EX
      default:
      	 goto	#EZ
   }
      -> Switch[-256907456] #EW -> #EY
      -> DefaultSwitch #EW -> #EZ
      -> Switch[2001254513] #EW -> #EX
      <- TryCatch range: [AX...AW] -> EW ([Ljava/io/IOException;])
      <- TryCatch range: [AX...AW] -> EW ([Ljava/io/IOException;])
===#Block EX(size=2, flags=10100)===
   0. lvar78 = {1165334136 ^ lvar78};
   1. goto AY
      -> UnconditionalJump[GOTO] #EX -> #AY
      <- Switch[2001254513] #EW -> #EX
===#Block EZ(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #EW -> #EZ
===#Block EY(size=2, flags=10100)===
   0. lvar78 = {1971944862 ^ lvar78};
   1. goto AY
      -> UnconditionalJump[GOTO] #EY -> #AY
      <- Switch[-256907456] #EW -> #EY
===#Block AY(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1558449075)
      goto BQ
   2. goto CW
      -> ConditionalJump[IF_ICMPNE] #AY -> #BQ
      -> UnconditionalJump[GOTO] #AY -> #CW
      <- UnconditionalJump[GOTO] #EY -> #AY
      <- UnconditionalJump[GOTO] #EX -> #AY
===#Block CW(size=2, flags=10100)===
   0. lvar78 = {1784133315 ^ lvar78};
   1. goto T
      -> UnconditionalJump[GOTO] #CW -> #T
      <- UnconditionalJump[GOTO] #AY -> #CW
===#Block BP(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2001254513)
      goto BP
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {3946138 ^ lvar78})
      goto BP
   2. _consume({1161545956 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BP -> #BP
      <- ConditionalJump[IF_ICMPNE] #BP -> #BP
      <- ConditionalJump[IF_ICMPNE] #AX -> #BP
===#Block DB(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 50829739:
      	 goto	#DC
      case 702119633:
      	 goto	#DB
      case 961016464:
      	 goto	#BA
      case 1334413037:
      	 goto	#T
      default:
      	 goto	#BA
   }
      -> Switch[1334413037] #DB -> #T
      -> Switch[702119633] #DB -> #DB
      -> Immediate #DB -> #DC
      -> DefaultSwitch #DB -> #BA
      -> Switch[50829739] #DB -> #DC
      -> Switch[961016464] #DB -> #BA
      <- ConditionalJump[IF_ICMPEQ] #J -> #DB
      <- Switch[702119633] #DB -> #DB
===#Block DC(size=2, flags=100)===
   0. lvar78 = {49313852 ^ lvar78};
   1. goto T
      -> UnconditionalJump[GOTO] #DC -> #T
      <- Immediate #DB -> #DC
      <- Switch[50829739] #DB -> #DC
===#Block DG(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 574302900);
   1. goto R
      -> UnconditionalJump[GOTO] #DG -> #R
      <- Switch[24472792] #E -> #DG
===#Block R(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar60 = lvar7;
   2. lvar73 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.bectnpvogneupnu(), lvar78);
   3. lvar61 = lvar60.equals(lvar73);
   4. if (lvar61 == {1936643957 ^ lvar78})
      goto DE
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1686717525)
      goto BF
   6. lvar78 = {1057776938 ^ lvar78};
      -> ConditionalJump[IF_ICMPEQ] #R -> #DE
      -> Immediate #R -> #S
      -> ConditionalJump[IF_ICMPNE] #R -> #BF
      <- UnconditionalJump[GOTO] #DG -> #R
===#Block S(size=4, flags=0)===
   0. lvar62 = {1281536602 ^ lvar78};
   1. lvar8 = lvar62;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1662358266)
      goto BS
   3. goto CO
      -> UnconditionalJump[GOTO] #S -> #CO
      -> ConditionalJump[IF_ICMPNE] #S -> #BS
      <- Immediate #R -> #S
===#Block BS(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1662358266)
      goto BS
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {146954481 ^ lvar78})
      goto BS
   2. _consume({286671700 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BS -> #BS
      <- ConditionalJump[IF_ICMPNE] #S -> #BS
      <- ConditionalJump[IF_ICMPNE] #BS -> #BS
===#Block CO(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 67073530:
      	 goto	#CP
      case 313889267:
      	 goto	#BB
      case 1438903666:
      	 goto	#AO
      case 1926796189:
      	 goto	#CO
      default:
      	 goto	#BB
   }
      -> Switch[1926796189] #CO -> #CO
      -> Switch[313889267] #CO -> #BB
      -> Switch[1438903666] #CO -> #AO
      -> Immediate #CO -> #CP
      -> DefaultSwitch #CO -> #BB
      -> Switch[67073530] #CO -> #CP
      <- UnconditionalJump[GOTO] #S -> #CO
      <- Switch[1926796189] #CO -> #CO
===#Block CP(size=2, flags=100)===
   0. lvar78 = {1864073238 ^ lvar78};
   1. goto AO
      -> UnconditionalJump[GOTO] #CP -> #AO
      <- Immediate #CO -> #CP
      <- Switch[67073530] #CO -> #CP
===#Block AO(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 79360532)
      goto AN
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 466195017)
      goto BJ
      -> ConditionalJump[IF_ICMPEQ] #AO -> #AN
      -> ConditionalJump[IF_ICMPNE] #AO -> #BJ
      -> TryCatch range: [AO...AN] -> EK ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #CP -> #AO
      <- Switch[1438903666] #CO -> #AO
===#Block AN(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -766768223)
      goto BN
      -> TryCatch range: [AO...AN] -> EK ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPNE] #AN -> #BN
      <- ConditionalJump[IF_ICMPEQ] #AO -> #AN
===#Block BN(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -766768223)
      goto BN
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {125397084 ^ lvar78})
      goto BN
   2. _consume({1023240847 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1900412743)
      goto BN
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1211717695 ^ lvar78})
      goto BN
   5. _consume({519105134 ^ lvar78});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BN -> #BN
      <- ConditionalJump[IF_ICMPNE] #BN -> #BN
      <- ConditionalJump[IF_ICMPNE] #K -> #BN
      <- ConditionalJump[IF_ICMPNE] #AN -> #BN
===#Block EK(size=1, flags=0)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.ffnfysxybrbcrlmb(lvar78)) {
      case -766768223:
      	 goto	#EM
      case 466195017:
      	 goto	#EL
      default:
      	 goto	#EN
   }
      -> Switch[-766768223] #EK -> #EM
      -> DefaultSwitch #EK -> #EN
      -> Switch[466195017] #EK -> #EL
      <- TryCatch range: [AO...AN] -> EK ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AO...AN] -> EK ([Ljava/lang/RuntimeException;])
===#Block EL(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 676395419);
   1. goto AP
      -> UnconditionalJump[GOTO] #EL -> #AP
      <- Switch[466195017] #EK -> #EL
===#Block EN(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #EK -> #EN
===#Block EM(size=2, flags=10100)===
   0. lvar78 = {828394150 ^ lvar78};
   1. goto AP
      -> UnconditionalJump[GOTO] #EM -> #AP
      <- Switch[-766768223] #EK -> #EM
===#Block AP(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1498308240)
      goto BM
   2. goto CI
      -> ConditionalJump[IF_ICMPNE] #AP -> #BM
      -> UnconditionalJump[GOTO] #AP -> #CI
      <- UnconditionalJump[GOTO] #EL -> #AP
      <- UnconditionalJump[GOTO] #EM -> #AP
===#Block CI(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 182647911);
   1. goto T
      -> UnconditionalJump[GOTO] #CI -> #T
      <- UnconditionalJump[GOTO] #AP -> #CI
===#Block DE(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 1923473600);
   1. goto T
      -> UnconditionalJump[GOTO] #DE -> #T
      <- ConditionalJump[IF_ICMPEQ] #R -> #DE
===#Block DN(size=2, flags=10100)===
   0. lvar78 = {1245961705 ^ lvar78};
   1. goto L
      -> UnconditionalJump[GOTO] #DN -> #L
      <- Switch[227817807] #E -> #DN
===#Block L(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar51 = lvar7;
   2. lvar70 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yhkazdalezultca(), lvar78);
   3. lvar52 = lvar51.equals(lvar70);
   4. if (lvar52 == {454434856 ^ lvar78})
      goto DA
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -659488448)
      goto CB
   6. lvar78 = {110855656 ^ lvar78};
      -> ConditionalJump[IF_ICMPEQ] #L -> #DA
      -> Immediate #L -> #M
      -> ConditionalJump[IF_ICMPNE] #L -> #CB
      <- UnconditionalJump[GOTO] #DN -> #L
===#Block CB(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -659488448)
      goto CB
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1662068816 ^ lvar78})
      goto CB
   2. _consume({919460236 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #CB -> #CB
      <- ConditionalJump[IF_ICMPNE] #CB -> #CB
      <- ConditionalJump[IF_ICMPNE] #L -> #CB
===#Block M(size=4, flags=0)===
   0. lvar53 = {495822276 ^ lvar78};
   1. lvar8 = lvar53;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -328389120)
      goto BO
   3. goto CK
      -> UnconditionalJump[GOTO] #M -> #CK
      -> ConditionalJump[IF_ICMPNE] #M -> #BO
      <- Immediate #L -> #M
===#Block CK(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 148679376:
      	 goto	#AI
      case 155348137:
      	 goto	#CL
      case 439865337:
      	 goto	#CK
      case 1716273423:
      	 goto	#BK
      default:
      	 goto	#BK
   }
      -> Immediate #CK -> #CL
      -> Switch[439865337] #CK -> #CK
      -> Switch[155348137] #CK -> #CL
      -> Switch[1716273423] #CK -> #BK
      -> DefaultSwitch #CK -> #BK
      -> Switch[148679376] #CK -> #AI
      <- Switch[439865337] #CK -> #CK
      <- UnconditionalJump[GOTO] #M -> #CK
===#Block CL(size=2, flags=100)===
   0. lvar78 = {1184970854 ^ lvar78};
   1. goto AI
      -> UnconditionalJump[GOTO] #CL -> #AI
      <- Immediate #CK -> #CL
      <- Switch[155348137] #CK -> #CL
===#Block AI(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 10889821)
      goto AH
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -647697102)
      goto BH
      -> ConditionalJump[IF_ICMPEQ] #AI -> #AH
      -> ConditionalJump[IF_ICMPNE] #AI -> #BH
      -> TryCatch range: [AI...AH] -> EC ([Ljava/lang/RuntimeException;])
      <- UnconditionalJump[GOTO] #CL -> #AI
      <- Switch[148679376] #CK -> #AI
===#Block AH(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 856142680)
      goto BV
      -> TryCatch range: [AI...AH] -> EC ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPNE] #AH -> #BV
      <- ConditionalJump[IF_ICMPEQ] #AI -> #AH
===#Block EC(size=1, flags=0)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.ffnfysxybrbcrlmb(lvar78)) {
      case -647697102:
      	 goto	#ED
      case 856142680:
      	 goto	#EE
      default:
      	 goto	#EF
   }
      -> Switch[-647697102] #EC -> #ED
      -> DefaultSwitch #EC -> #EF
      -> Switch[856142680] #EC -> #EE
      <- TryCatch range: [AI...AH] -> EC ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AI...AH] -> EC ([Ljava/lang/RuntimeException;])
===#Block EE(size=2, flags=10100)===
   0. lvar78 = {100662234 ^ lvar78};
   1. goto AJ
      -> UnconditionalJump[GOTO] #EE -> #AJ
      <- Switch[856142680] #EC -> #EE
===#Block EF(size=1, flags=0)===
   0. throw new java/io/IOException("Error in hash");
      <- DefaultSwitch #EC -> #EF
===#Block ED(size=2, flags=10100)===
   0. lvar78 = {1488162839 ^ lvar78};
   1. goto AJ
      -> UnconditionalJump[GOTO] #ED -> #AJ
      <- Switch[-647697102] #EC -> #ED
===#Block AJ(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486043016)
      goto BW
   2. goto CQ
      -> ConditionalJump[IF_ICMPNE] #AJ -> #BW
      -> UnconditionalJump[GOTO] #AJ -> #CQ
      <- UnconditionalJump[GOTO] #EE -> #AJ
      <- UnconditionalJump[GOTO] #ED -> #AJ
===#Block CQ(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 56958794:
      	 goto	#CR
      case 1135952342:
      	 goto	#BU
      case 1423338429:
      	 goto	#CQ
      case 2058849637:
      	 goto	#T
      default:
      	 goto	#BU
   }
      -> Switch[1423338429] #CQ -> #CQ
      -> Switch[56958794] #CQ -> #CR
      -> Switch[2058849637] #CQ -> #T
      -> Switch[1135952342] #CQ -> #BU
      -> DefaultSwitch #CQ -> #BU
      -> Immediate #CQ -> #CR
      <- UnconditionalJump[GOTO] #AJ -> #CQ
      <- Switch[1423338429] #CQ -> #CQ
===#Block CR(size=2, flags=100)===
   0. lvar78 = {39074308 ^ lvar78};
   1. goto T
      -> UnconditionalJump[GOTO] #CR -> #T
      <- Switch[56958794] #CQ -> #CR
      <- Immediate #CQ -> #CR
===#Block DA(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 450697117);
   1. goto T
      -> UnconditionalJump[GOTO] #DA -> #T
      <- ConditionalJump[IF_ICMPEQ] #L -> #DA
===#Block DQ(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 1352583796);
   1. goto T
      -> UnconditionalJump[GOTO] #DQ -> #T
      <- DefaultSwitch #E -> #DQ
===#Block DL(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 227986160:
      	 goto	#DM
      case 898406427:
      	 goto	#DL
      case 1377058214:
      	 goto	#F
      case 2078537247:
      	 goto	#BM
      default:
      	 goto	#BM
   }
      -> Switch[227986160] #DL -> #DM
      -> Switch[898406427] #DL -> #DL
      -> Immediate #DL -> #DM
      -> Switch[2078537247] #DL -> #BM
      -> Switch[1377058214] #DL -> #F
      -> DefaultSwitch #DL -> #BM
      <- Switch[898406427] #DL -> #DL
      <- Switch[197868356] #E -> #DL
===#Block BM(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1498308240)
      goto BM
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1124462258 ^ lvar78})
      goto BM
   2. _consume({1508363018 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BM -> #BM
      <- ConditionalJump[IF_ICMPNE] #BM -> #BM
      <- ConditionalJump[IF_ICMPNE] #AP -> #BM
      <- Switch[2078537247] #DL -> #BM
      <- DefaultSwitch #DL -> #BM
===#Block DM(size=2, flags=100)===
   0. lvar78 = {523617676 ^ lvar78};
   1. goto F
      -> UnconditionalJump[GOTO] #DM -> #F
      <- Switch[227986160] #DL -> #DM
      <- Immediate #DL -> #DM
===#Block F(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar19 = lvar7;
   2. lvar4 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.xaqnqktvbkqgxqr(), lvar78);
   3. lvar20 = lvar19.equals(lvar4);
   4. if (lvar20 == {1314928717 ^ lvar78})
      goto CZ
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1929495146)
      goto BC
   6. lvar78 = {218789692 ^ lvar78};
      -> ConditionalJump[IF_ICMPEQ] #F -> #CZ
      -> Immediate #F -> #G
      -> ConditionalJump[IF_ICMPNE] #F -> #BC
      <- UnconditionalJump[GOTO] #DM -> #F
      <- Switch[1377058214] #DL -> #F
===#Block G(size=4, flags=0)===
   0. lvar21 = {1131040627 ^ lvar78};
   1. lvar8 = lvar21;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 458390410)
      goto BJ
   3. goto CM
      -> UnconditionalJump[GOTO] #G -> #CM
      -> ConditionalJump[IF_ICMPNE] #G -> #BJ
      <- Immediate #F -> #G
===#Block CM(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 43891328:
      	 goto	#CN
      case 159118220:
      	 goto	#BT
      case 743631063:
      	 goto	#AL
      case 1616942020:
      	 goto	#CM
      default:
      	 goto	#BT
   }
      -> Switch[43891328] #CM -> #CN
      -> Switch[1616942020] #CM -> #CM
      -> Switch[743631063] #CM -> #AL
      -> Switch[159118220] #CM -> #BT
      -> Immediate #CM -> #CN
      -> DefaultSwitch #CM -> #BT
      <- UnconditionalJump[GOTO] #G -> #CM
      <- Switch[1616942020] #CM -> #CM
===#Block BT(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1510108390)
      goto BT
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {485002264 ^ lvar78})
      goto BT
   2. _consume({2092454279 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BT -> #BT
      <- ConditionalJump[IF_ICMPNE] #B -> #BT
      <- Switch[159118220] #CM -> #BT
      <- DefaultSwitch #CM -> #BT
      <- ConditionalJump[IF_ICMPNE] #BT -> #BT
===#Block CN(size=2, flags=100)===
   0. lvar78 = {87357849 ^ lvar78};
   1. goto AL
      -> UnconditionalJump[GOTO] #CN -> #AL
      <- Switch[43891328] #CM -> #CN
      <- Immediate #CM -> #CN
===#Block AL(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 139969599)
      goto AK
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 854964034)
      goto BH
      -> ConditionalJump[IF_ICMPNE] #AL -> #BH
      -> TryCatch range: [AL...AK] -> EG ([Ljava/io/IOException;])
      -> ConditionalJump[IF_ICMPEQ] #AL -> #AK
      <- UnconditionalJump[GOTO] #CN -> #AL
      <- Switch[743631063] #CM -> #AL
===#Block AK(size=2, flags=0)===
   0. throw new java/io/IOException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486359395)
      goto CC
      -> ConditionalJump[IF_ICMPNE] #AK -> #CC
      -> TryCatch range: [AL...AK] -> EG ([Ljava/io/IOException;])
      <- ConditionalJump[IF_ICMPEQ] #AL -> #AK
===#Block CC(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486359395)
      goto CC
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2065479897 ^ lvar78})
      goto CC
   2. _consume({63045588 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #CC -> #CC
      <- ConditionalJump[IF_ICMPNE] #CC -> #CC
      <- ConditionalJump[IF_ICMPNE] #AK -> #CC
===#Block EG(size=1, flags=0)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.ffnfysxybrbcrlmb(lvar78)) {
      case 486359395:
      	 goto	#EI
      case 854964034:
      	 goto	#EH
      default:
      	 goto	#EJ
   }
      -> Switch[486359395] #EG -> #EI
      -> DefaultSwitch #EG -> #EJ
      -> Switch[854964034] #EG -> #EH
      <- TryCatch range: [AL...AK] -> EG ([Ljava/io/IOException;])
      <- TryCatch range: [AL...AK] -> EG ([Ljava/io/IOException;])
===#Block EH(size=2, flags=10100)===
   0. lvar78 = {569793790 ^ lvar78};
   1. goto AM
      -> UnconditionalJump[GOTO] #EH -> #AM
      <- Switch[854964034] #EG -> #EH
===#Block EJ(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #EG -> #EJ
===#Block EI(size=2, flags=10100)===
   0. lvar78 = {70730298 ^ lvar78};
   1. goto AM
      -> UnconditionalJump[GOTO] #EI -> #AM
      <- Switch[486359395] #EG -> #EI
===#Block AM(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1028083891)
      goto BZ
   2. goto CS
      -> ConditionalJump[IF_ICMPNE] #AM -> #BZ
      -> UnconditionalJump[GOTO] #AM -> #CS
      <- UnconditionalJump[GOTO] #EH -> #AM
      <- UnconditionalJump[GOTO] #EI -> #AM
===#Block CS(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 148316706:
      	 goto	#CT
      case 1383785215:
      	 goto	#BL
      case 1385439645:
      	 goto	#CS
      case 1502276722:
      	 goto	#T
      default:
      	 goto	#BL
   }
      -> Switch[1385439645] #CS -> #CS
      -> Immediate #CS -> #CT
      -> Switch[1502276722] #CS -> #T
      -> Switch[148316706] #CS -> #CT
      -> Switch[1383785215] #CS -> #BL
      -> DefaultSwitch #CS -> #BL
      <- Switch[1385439645] #CS -> #CS
      <- UnconditionalJump[GOTO] #AM -> #CS
===#Block BL(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2105754288)
      goto BL
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1961494076 ^ lvar78})
      goto BL
   2. _consume({1994296950 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BL -> #BL
      <- ConditionalJump[IF_ICMPNE] #C -> #BL
      <- Switch[1383785215] #CS -> #BL
      <- DefaultSwitch #CS -> #BL
      <- ConditionalJump[IF_ICMPNE] #BL -> #BL
===#Block CT(size=2, flags=100)===
   0. lvar78 = {1717820835 ^ lvar78};
   1. goto T
      -> UnconditionalJump[GOTO] #CT -> #T
      <- Immediate #CS -> #CT
      <- Switch[148316706] #CS -> #CT
===#Block BZ(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1028083891)
      goto BZ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {121317528 ^ lvar78})
      goto BZ
   2. _consume({1921197660 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BZ -> #BZ
      <- ConditionalJump[IF_ICMPNE] #BZ -> #BZ
      <- ConditionalJump[IF_ICMPNE] #AM -> #BZ
===#Block CZ(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 1336610808);
   1. goto T
      -> UnconditionalJump[GOTO] #CZ -> #T
      <- ConditionalJump[IF_ICMPEQ] #F -> #CZ
===#Block DI(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 24651635:
      	 goto	#DI
      case 227986160:
      	 goto	#DJ
      case 441261170:
      	 goto	#BW
      case 1382362388:
      	 goto	#P
      default:
      	 goto	#BW
   }
      -> Immediate #DI -> #DJ
      -> Switch[441261170] #DI -> #BW
      -> DefaultSwitch #DI -> #BW
      -> Switch[1382362388] #DI -> #P
      -> Switch[24651635] #DI -> #DI
      -> Switch[227986160] #DI -> #DJ
      <- Switch[110867538] #E -> #DI
      <- Switch[24651635] #DI -> #DI
===#Block BW(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 486043016)
      goto BW
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {904193609 ^ lvar78})
      goto BW
   2. _consume({415347106 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BW -> #BW
      <- ConditionalJump[IF_ICMPNE] #AJ -> #BW
      <- Switch[441261170] #DI -> #BW
      <- DefaultSwitch #DI -> #BW
      <- ConditionalJump[IF_ICMPNE] #BW -> #BW
===#Block DJ(size=2, flags=100)===
   0. lvar78 = {107313031 ^ lvar78};
   1. goto P
      -> UnconditionalJump[GOTO] #DJ -> #P
      <- Immediate #DI -> #DJ
      <- Switch[227986160] #DI -> #DJ
===#Block P(size=7, flags=0)===
   0. // Frame: locals[6] [com/hang/plugin/manager/TreasureItemManager, java/lang/String, java/lang/IllegalArgumentException, java/lang/String, java/lang/String, 1] stack[0] []
   1. lvar57 = lvar7;
   2. lvar72 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.ftzbearmuokvzqe(), lvar78);
   3. lvar58 = lvar57.equals(lvar72);
   4. if (lvar58 == {1462798918 ^ lvar78})
      goto CX
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1182510542)
      goto AZ
   6. lvar78 = {990533182 ^ lvar78};
      -> ConditionalJump[IF_ICMPEQ] #P -> #CX
      -> Immediate #P -> #Q
      -> ConditionalJump[IF_ICMPNE] #P -> #AZ
      <- Switch[1382362388] #DI -> #P
      <- UnconditionalJump[GOTO] #DJ -> #P
===#Block Q(size=4, flags=0)===
   0. lvar59 = {1815796856 ^ lvar78};
   1. lvar8 = lvar59;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1641472963)
      goto BC
   3. goto CH
      -> ConditionalJump[IF_ICMPNE] #Q -> #BC
      -> UnconditionalJump[GOTO] #Q -> #CH
      <- Immediate #P -> #Q
===#Block CH(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 415368273);
   1. goto AU
      -> UnconditionalJump[GOTO] #CH -> #AU
      <- UnconditionalJump[GOTO] #Q -> #CH
===#Block AU(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 44193335)
      goto AT
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1480146613)
      goto BD
      -> ConditionalJump[IF_ICMPNE] #AU -> #BD
      -> TryCatch range: [AU...AT] -> ES ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPEQ] #AU -> #AT
      <- UnconditionalJump[GOTO] #CH -> #AU
===#Block AT(size=2, flags=0)===
   0. throw new java/lang/IllegalAccessException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1671324573)
      goto BI
      -> TryCatch range: [AU...AT] -> ES ([Ljava/lang/IllegalAccessException;])
      -> ConditionalJump[IF_ICMPNE] #AT -> #BI
      <- ConditionalJump[IF_ICMPEQ] #AU -> #AT
===#Block BI(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1671324573)
      goto BI
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {713909545 ^ lvar78})
      goto BI
   2. _consume({1156449947 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- ConditionalJump[IF_ICMPNE] #BI -> #BI
      <- ConditionalJump[IF_ICMPNE] #AT -> #BI
===#Block ES(size=1, flags=0)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.ffnfysxybrbcrlmb(lvar78)) {
      case -1671324573:
      	 goto	#EU
      case -1480146613:
      	 goto	#ET
      default:
      	 goto	#EV
   }
      -> DefaultSwitch #ES -> #EV
      -> Switch[-1480146613] #ES -> #ET
      -> Switch[-1671324573] #ES -> #EU
      <- TryCatch range: [AU...AT] -> ES ([Ljava/lang/IllegalAccessException;])
      <- TryCatch range: [AU...AT] -> ES ([Ljava/lang/IllegalAccessException;])
===#Block EU(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 370149532);
   1. goto AV
      -> UnconditionalJump[GOTO] #EU -> #AV
      <- Switch[-1671324573] #ES -> #EU
===#Block ET(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 291824441);
   1. goto AV
      -> UnconditionalJump[GOTO] #ET -> #AV
      <- Switch[-1480146613] #ES -> #ET
===#Block AV(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 752998531)
      goto BO
   2. goto CU
      -> ConditionalJump[IF_ICMPNE] #AV -> #BO
      -> UnconditionalJump[GOTO] #AV -> #CU
      <- UnconditionalJump[GOTO] #EU -> #AV
      <- UnconditionalJump[GOTO] #ET -> #AV
===#Block CU(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 46301036:
      	 goto	#BV
      case 81713139:
      	 goto	#CV
      case 100726530:
      	 goto	#CU
      case 1561137317:
      	 goto	#T
      default:
      	 goto	#BV
   }
      -> Switch[1561137317] #CU -> #T
      -> Immediate #CU -> #CV
      -> Switch[100726530] #CU -> #CU
      -> DefaultSwitch #CU -> #BV
      -> Switch[46301036] #CU -> #BV
      -> Switch[81713139] #CU -> #CV
      <- Switch[100726530] #CU -> #CU
      <- UnconditionalJump[GOTO] #AV -> #CU
===#Block BV(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 856142680)
      goto BV
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1413532324 ^ lvar78})
      goto BV
   2. _consume({1900707578 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BV -> #BV
      <- ConditionalJump[IF_ICMPNE] #BV -> #BV
      <- DefaultSwitch #CU -> #BV
      <- ConditionalJump[IF_ICMPNE] #AH -> #BV
      <- Switch[46301036] #CU -> #BV
===#Block CV(size=2, flags=100)===
   0. lvar78 = {1683426469 ^ lvar78};
   1. goto T
      -> UnconditionalJump[GOTO] #CV -> #T
      <- Immediate #CU -> #CV
      <- Switch[81713139] #CU -> #CV
===#Block BO(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 752998531)
      goto BO
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1881659440 ^ lvar78})
      goto BO
   2. _consume({547062424 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -328389120)
      goto BO
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {753325861 ^ lvar78})
      goto BO
   5. _consume({946477553 ^ lvar78});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BO -> #BO
      <- ConditionalJump[IF_ICMPNE] #AV -> #BO
      <- ConditionalJump[IF_ICMPNE] #BO -> #BO
      <- ConditionalJump[IF_ICMPNE] #M -> #BO
===#Block EV(size=1, flags=0)===
   0. throw new java/lang/RuntimeException("Error in hash");
      <- DefaultSwitch #ES -> #EV
===#Block BC(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1929495146)
      goto BC
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {231884989 ^ lvar78})
      goto BC
   2. _consume({1462970804 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1641472963)
      goto BC
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {478421264 ^ lvar78})
      goto BC
   5. _consume({317227234 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 433749064)
      goto BC
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {160018041 ^ lvar78})
      goto BC
   8. _consume({2125906394 ^ lvar78});
   9. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- ConditionalJump[IF_ICMPNE] #BC -> #BC
      <- ConditionalJump[IF_ICMPNE] #J -> #BC
      <- ConditionalJump[IF_ICMPNE] #Q -> #BC
      <- ConditionalJump[IF_ICMPNE] #F -> #BC
===#Block CX(size=2, flags=10100)===
   0. lvar78 = {1459338739 ^ lvar78};
   1. goto T
      -> UnconditionalJump[GOTO] #CX -> #T
      <- ConditionalJump[IF_ICMPEQ] #P -> #CX
===#Block DK(size=2, flags=10100)===
   0. lvar78 = {932140122 ^ lvar78};
   1. goto N
      -> UnconditionalJump[GOTO] #DK -> #N
      <- Switch[156907340] #E -> #DK
===#Block N(size=7, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar54 = lvar7;
   2. lvar71 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yjxsfpuegqomqtc(), lvar78);
   3. lvar55 = lvar54.equals(lvar71);
   4. if (lvar55 == {1725606299 ^ lvar78})
      goto DD
   5. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 919948507)
      goto BE
   6. lvar78 = {2049787150 ^ lvar78};
      -> ConditionalJump[IF_ICMPEQ] #N -> #DD
      -> ConditionalJump[IF_ICMPNE] #N -> #BE
      -> Immediate #N -> #O
      <- UnconditionalJump[GOTO] #DK -> #N
===#Block O(size=4, flags=0)===
   0. lvar56 = {486010004 ^ lvar78};
   1. lvar8 = lvar56;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -406887256)
      goto BB
   3. goto CG
      -> UnconditionalJump[GOTO] #O -> #CG
      -> ConditionalJump[IF_ICMPNE] #O -> #BB
      <- Immediate #N -> #O
===#Block CG(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 933714825);
   1. goto AR
      -> UnconditionalJump[GOTO] #CG -> #AR
      <- UnconditionalJump[GOTO] #O -> #CG
===#Block AR(size=3, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78) == 65816982)
      goto AQ
   1. throw nullconst;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1518713057)
      goto BQ
      -> TryCatch range: [AR...AQ] -> EO ([Ljava/lang/RuntimeException;])
      -> ConditionalJump[IF_ICMPEQ] #AR -> #AQ
      -> ConditionalJump[IF_ICMPNE] #AR -> #BQ
      <- UnconditionalJump[GOTO] #CG -> #AR
===#Block BQ(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1558449075)
      goto BQ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {341426397 ^ lvar78})
      goto BQ
   2. _consume({368654051 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 1518713057)
      goto BQ
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1905219472 ^ lvar78})
      goto BQ
   5. _consume({1402203533 ^ lvar78});
   6. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BQ -> #BQ
      <- ConditionalJump[IF_ICMPNE] #BQ -> #BQ
      <- ConditionalJump[IF_ICMPNE] #AY -> #BQ
      <- ConditionalJump[IF_ICMPNE] #AR -> #BQ
===#Block AQ(size=2, flags=0)===
   0. throw new java/lang/RuntimeException();
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1591771375)
      goto BB
      -> ConditionalJump[IF_ICMPNE] #AQ -> #BB
      -> TryCatch range: [AR...AQ] -> EO ([Ljava/lang/RuntimeException;])
      <- ConditionalJump[IF_ICMPEQ] #AR -> #AQ
===#Block BB(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1591771375)
      goto BB
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {558263189 ^ lvar78})
      goto BB
   2. _consume({336583340 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -406887256)
      goto BB
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {495698447 ^ lvar78})
      goto BB
   5. _consume({754669794 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1270233501)
      goto BB
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1300752702 ^ lvar78})
      goto BB
   8. _consume({1160696589 ^ lvar78});
   9. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- ConditionalJump[IF_ICMPNE] #BB -> #BB
      <- Switch[313889267] #CO -> #BB
      <- ConditionalJump[IF_ICMPNE] #O -> #BB
      <- ConditionalJump[IF_ICMPNE] #AQ -> #BB
      <- DefaultSwitch #CO -> #BB
      <- ConditionalJump[IF_ICMPNE] #H -> #BB
===#Block EO(size=1, flags=0)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.ffnfysxybrbcrlmb(lvar78)) {
      case -1591771375:
      	 goto	#EQ
      case 1518713057:
      	 goto	#EP
      default:
      	 goto	#ER
   }
      -> Switch[-1591771375] #EO -> #EQ
      -> DefaultSwitch #EO -> #ER
      -> Switch[1518713057] #EO -> #EP
      <- TryCatch range: [AR...AQ] -> EO ([Ljava/lang/RuntimeException;])
      <- TryCatch range: [AR...AQ] -> EO ([Ljava/lang/RuntimeException;])
===#Block EP(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 363927417);
   1. goto AS
      -> UnconditionalJump[GOTO] #EP -> #AS
      <- Switch[1518713057] #EO -> #EP
===#Block ER(size=1, flags=0)===
   0. throw new java/lang/IllegalAccessException("Error in hash");
      <- DefaultSwitch #EO -> #ER
===#Block EQ(size=2, flags=10100)===
   0. lvar78 = {180506503 ^ lvar78};
   1. goto AS
      -> UnconditionalJump[GOTO] #EQ -> #AS
      <- Switch[-1591771375] #EO -> #EQ
===#Block AS(size=3, flags=0)===
   0. _consume(catch());
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -150142167)
      goto AZ
   2. goto CF
      -> UnconditionalJump[GOTO] #AS -> #CF
      -> ConditionalJump[IF_ICMPNE] #AS -> #AZ
      <- UnconditionalJump[GOTO] #EQ -> #AS
      <- UnconditionalJump[GOTO] #EP -> #AS
===#Block CF(size=2, flags=10100)===
   0. lvar78 = {1059755984 ^ lvar78};
   1. goto T
      -> UnconditionalJump[GOTO] #CF -> #T
      <- UnconditionalJump[GOTO] #AS -> #CF
===#Block BE(size=7, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 141206393)
      goto BE
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {443605690 ^ lvar78})
      goto BE
   2. _consume({63999251 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 919948507)
      goto BE
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {170860988 ^ lvar78})
      goto BE
   5. _consume({1445069689 ^ lvar78});
   6. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BE -> #BE
      <- ConditionalJump[IF_ICMPNE] #N -> #BE
      <- ConditionalJump[IF_ICMPNE] #BE -> #BE
      <- ConditionalJump[IF_ICMPNE] #AC -> #BE
===#Block DD(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 1729207854);
   1. goto T
      -> UnconditionalJump[GOTO] #DD -> #T
      <- ConditionalJump[IF_ICMPEQ] #N -> #DD
===#Block T(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar22 = lvar8;
   2. svar80 = {lvar22 ^ lvar78};
   3. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(svar80)) {
      case 28214768:
      	 goto	#DR
      case 28214769:
      	 goto	#DT
      case 28214770:
      	 goto	#DU
      case 28214771:
      	 goto	#DV
      case 28214780:
      	 goto	#DW
      case 28214782:
      	 goto	#DX
      default:
      	 goto	#DY
   }
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 240762280)
      goto BU
      -> Switch[28214768] #T -> #DR
      -> Switch[28214782] #T -> #DX
      -> ConditionalJump[IF_ICMPNE] #T -> #BU
      -> Switch[28214770] #T -> #DU
      -> Switch[28214780] #T -> #DW
      -> Switch[28214771] #T -> #DV
      -> DefaultSwitch #T -> #DY
      -> Switch[28214769] #T -> #DT
      <- UnconditionalJump[GOTO] #DD -> #T
      <- UnconditionalJump[GOTO] #CZ -> #T
      <- Immediate #I -> #T
      <- UnconditionalJump[GOTO] #CR -> #T
      <- UnconditionalJump[GOTO] #CT -> #T
      <- UnconditionalJump[GOTO] #DA -> #T
      <- Switch[1502276722] #CS -> #T
      <- UnconditionalJump[GOTO] #CV -> #T
      <- Switch[2058849637] #CQ -> #T
      <- UnconditionalJump[GOTO] #DE -> #T
      <- UnconditionalJump[GOTO] #CX -> #T
      <- Switch[1561137317] #CU -> #T
      <- UnconditionalJump[GOTO] #CY -> #T
      <- UnconditionalJump[GOTO] #DC -> #T
      <- Switch[1334413037] #DB -> #T
      <- UnconditionalJump[GOTO] #CF -> #T
      <- UnconditionalJump[GOTO] #DQ -> #T
      <- UnconditionalJump[GOTO] #CW -> #T
      <- UnconditionalJump[GOTO] #CI -> #T
===#Block DT(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 1079592578);
   1. goto AF
      -> UnconditionalJump[GOTO] #DT -> #AF
      <- Switch[28214769] #T -> #DT
===#Block AF(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar43 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.wurcyacdyjmgmou(), lvar78);
   2. lvar44 = org.bukkit.Material.valueOf(lvar43);
   3. return lvar44;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 210995642)
      goto BR
      -> ConditionalJump[IF_ICMPNE] #AF -> #BR
      <- UnconditionalJump[GOTO] #DT -> #AF
===#Block BR(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 210995642)
      goto BR
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {661958823 ^ lvar78})
      goto BR
   2. _consume({1272044175 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BR -> #BR
      <- ConditionalJump[IF_ICMPNE] #BR -> #BR
      <- ConditionalJump[IF_ICMPNE] #AF -> #BR
===#Block DY(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 1574166488);
   1. goto X
      -> UnconditionalJump[GOTO] #DY -> #X
      <- DefaultSwitch #T -> #DY
===#Block X(size=6, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar29 = lvar1;
   2. lvar30 = org.bukkit.Material.matchMaterial(lvar29);
   3. lvar9 = lvar30;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -523811990)
      goto BJ
   5. lvar78 = {1122992332 ^ lvar78};
      -> Immediate #X -> #Y
      -> ConditionalJump[IF_ICMPNE] #X -> #BJ
      <- UnconditionalJump[GOTO] #DY -> #X
===#Block BJ(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -523811990)
      goto BJ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {937614200 ^ lvar78})
      goto BJ
   2. _consume({1153887896 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 458390410)
      goto BJ
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2080180205 ^ lvar78})
      goto BJ
   5. _consume({954370257 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 466195017)
      goto BJ
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1229602279 ^ lvar78})
      goto BJ
   8. _consume({1354336768 ^ lvar78});
   9. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BJ -> #BJ
      <- ConditionalJump[IF_ICMPNE] #BJ -> #BJ
      <- ConditionalJump[IF_ICMPNE] #AO -> #BJ
      <- ConditionalJump[IF_ICMPNE] #G -> #BJ
      <- ConditionalJump[IF_ICMPNE] #X -> #BJ
===#Block Y(size=4, flags=0)===
   0. lvar31 = lvar9;
   1. if (lvar31 == nullconst)
      goto DF
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -138722040)
      goto BH
   3. lvar78 = {1622795301 ^ lvar78};
      -> ConditionalJump[IF_ICMPNE] #Y -> #BH
      -> Immediate #Y -> #AB
      -> ConditionalJump[IFNULL] #Y -> #DF
      <- Immediate #X -> #Y
===#Block DF(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 7183942);
   1. goto Z
      -> UnconditionalJump[GOTO] #DF -> #Z
      <- ConditionalJump[IFNULL] #Y -> #DF
===#Block Z(size=16, flags=0)===
   0. // Frame: locals[1] [org/bukkit/Material] stack[0] []
   1. lvar32 = lvar0;
   2. lvar33 = lvar32.plugin;
   3. lvar34 = lvar33.getLogger();
   4. lvar63 = new java.lang.StringBuilder;
   5. _consume(lvar63.<init>());
   6. lvar74 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.yibvjbwmhuiuhns(), lvar78);
   7. lvar64 = lvar63.append(lvar74);
   8. lvar75 = lvar1;
   9. lvar65 = lvar64.append(lvar75);
   10. lvar76 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.tzqznptxyjijuzc(), lvar78);
   11. lvar66 = lvar65.append(lvar76);
   12. lvar67 = lvar66.toString();
   13. _consume(lvar34.warning(lvar67));
   14. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -187189448)
      goto BF
   15. lvar78 = {602508924 ^ lvar78};
      -> ConditionalJump[IF_ICMPNE] #Z -> #BF
      -> Immediate #Z -> #AA
      <- UnconditionalJump[GOTO] #DF -> #Z
===#Block AA(size=3, flags=0)===
   0. lvar35 = org.bukkit.Material.STONE;
   1. return lvar35;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -342113063)
      goto CE
      -> ConditionalJump[IF_ICMPNE] #AA -> #CE
      <- Immediate #Z -> #AA
===#Block CE(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -342113063)
      goto CE
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1309035836 ^ lvar78})
      goto CE
   2. _consume({1502736793 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #CE -> #CE
      <- ConditionalJump[IF_ICMPNE] #AA -> #CE
      <- ConditionalJump[IF_ICMPNE] #CE -> #CE
===#Block AB(size=3, flags=0)===
   0. lvar36 = lvar9;
   1. return lvar36;
   2. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -227253213)
      goto BA
      -> ConditionalJump[IF_ICMPNE] #AB -> #BA
      <- Immediate #Y -> #AB
===#Block BA(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -227253213)
      goto BA
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1732202856 ^ lvar78})
      goto BA
   2. _consume({1268268387 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- ConditionalJump[IF_ICMPNE] #BA -> #BA
      <- DefaultSwitch #DB -> #BA
      <- ConditionalJump[IF_ICMPNE] #AB -> #BA
      <- Switch[961016464] #DB -> #BA
===#Block BH(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 854964034)
      goto BH
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {409650150 ^ lvar78})
      goto BH
   2. _consume({311993592 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -138722040)
      goto BH
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1748883515 ^ lvar78})
      goto BH
   5. _consume({2038232330 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -647697102)
      goto BH
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2068093208 ^ lvar78})
      goto BH
   8. _consume({1519443261 ^ lvar78});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BH -> #BH
      <- ConditionalJump[IF_ICMPNE] #AL -> #BH
      <- ConditionalJump[IF_ICMPNE] #Y -> #BH
      <- ConditionalJump[IF_ICMPNE] #BH -> #BH
      <- ConditionalJump[IF_ICMPNE] #AI -> #BH
===#Block DV(size=2, flags=10100)===
   0. lvar78 = {930442764 ^ lvar78};
   1. goto W
      -> UnconditionalJump[GOTO] #DV -> #W
      <- Switch[28214771] #T -> #DV
===#Block W(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar27 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.cqllbpqxzblrrxy(), lvar78);
   2. lvar28 = org.bukkit.Material.valueOf(lvar27);
   3. return lvar28;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1242378807)
      goto CA
      -> ConditionalJump[IF_ICMPNE] #W -> #CA
      <- UnconditionalJump[GOTO] #DV -> #W
===#Block CA(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1242378807)
      goto CA
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1275584679 ^ lvar78})
      goto CA
   2. _consume({1531933032 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #CA -> #CA
      <- ConditionalJump[IF_ICMPNE] #CA -> #CA
      <- ConditionalJump[IF_ICMPNE] #W -> #CA
===#Block DW(size=2, flags=10100)===
   0. lvar78 = {598217332 ^ lvar78};
   1. goto U
      -> UnconditionalJump[GOTO] #DW -> #U
      <- Switch[28214780] #T -> #DW
===#Block U(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar23 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.prtwnvioasyjvha(), lvar78);
   2. lvar24 = org.bukkit.Material.valueOf(lvar23);
   3. return lvar24;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 320417289)
      goto AZ
      -> ConditionalJump[IF_ICMPNE] #U -> #AZ
      <- UnconditionalJump[GOTO] #DW -> #U
===#Block AZ(size=13, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 320417289)
      goto AZ
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {481149474 ^ lvar78})
      goto AZ
   2. _consume({1065549739 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -150142167)
      goto AZ
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1202868137 ^ lvar78})
      goto AZ
   5. _consume({2050735800 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1182510542)
      goto AZ
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {972456556 ^ lvar78})
      goto AZ
   8. _consume({1571224845 ^ lvar78});
   9. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1487427512)
      goto AZ
   10. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {662728743 ^ lvar78})
      goto AZ
   11. _consume({1205739256 ^ lvar78});
   12. throw new java/lang/RuntimeException();
      -> ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AZ -> #AZ
      <- ConditionalJump[IF_ICMPNE] #AS -> #AZ
      <- ConditionalJump[IF_ICMPNE] #U -> #AZ
      <- ConditionalJump[IF_ICMPNE] #P -> #AZ
      <- ConditionalJump[IF_ICMPNE] #I -> #AZ
===#Block DU(size=2, flags=10100)===
   0. lvar78 = com.hang.plugin.manager.TreasureItemManager.dfwdiihcrgrbndlu(lvar78, 1207160413);
   1. goto V
      -> UnconditionalJump[GOTO] #DU -> #V
      <- Switch[28214770] #T -> #DU
===#Block V(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar25 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.ovegptpfdypksny(), lvar78);
   2. lvar26 = org.bukkit.Material.valueOf(lvar25);
   3. return lvar26;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 835178306)
      goto BX
      -> ConditionalJump[IF_ICMPNE] #V -> #BX
      <- UnconditionalJump[GOTO] #DU -> #V
===#Block BX(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 835178306)
      goto BX
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {70327400 ^ lvar78})
      goto BX
   2. _consume({2060233287 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BX -> #BX
      <- ConditionalJump[IF_ICMPNE] #BX -> #BX
      <- DefaultSwitch #DO -> #BX
      <- ConditionalJump[IF_ICMPNE] #V -> #BX
      <- Switch[254375611] #DO -> #BX
===#Block BU(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 240762280)
      goto BU
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2041776176 ^ lvar78})
      goto BU
   2. _consume({282149557 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BU -> #BU
      <- ConditionalJump[IF_ICMPNE] #BU -> #BU
      <- Switch[1135952342] #CQ -> #BU
      <- DefaultSwitch #CQ -> #BU
      <- ConditionalJump[IF_ICMPNE] #T -> #BU
===#Block DX(size=2, flags=10100)===
   0. lvar78 = {18972043 ^ lvar78};
   1. goto AE
      -> UnconditionalJump[GOTO] #DX -> #AE
      <- Switch[28214782] #T -> #DX
===#Block AE(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar41 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.eacruwlloaiuxmf(), lvar78);
   2. lvar42 = org.bukkit.Material.valueOf(lvar41);
   3. return lvar42;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 122835440)
      goto BY
      -> ConditionalJump[IF_ICMPNE] #AE -> #BY
      <- UnconditionalJump[GOTO] #DX -> #AE
===#Block BY(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 122835440)
      goto BY
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1518476571 ^ lvar78})
      goto BY
   2. _consume({961816527 ^ lvar78});
   3. throw new java/lang/RuntimeException("double");
      -> ConditionalJump[IF_ICMPNE] #BY -> #BY
      <- ConditionalJump[IF_ICMPNE] #BY -> #BY
      <- ConditionalJump[IF_ICMPNE] #AE -> #BY
===#Block DR(size=1, flags=10100)===
   0. switch (edljcxxlexolrenr.bumanwckibjpqejj.rpxtgebgepherjkz(lvar78)) {
      case 28214770:
      	 goto	#DS
      case 1292083487:
      	 goto	#AD
      case 1682114449:
      	 goto	#DR
      case 1859619349:
      	 goto	#BG
      default:
      	 goto	#BG
   }
      -> Switch[1859619349] #DR -> #BG
      -> DefaultSwitch #DR -> #BG
      -> Switch[28214770] #DR -> #DS
      -> Switch[1292083487] #DR -> #AD
      -> Switch[1682114449] #DR -> #DR
      -> Immediate #DR -> #DS
      <- Switch[28214768] #T -> #DR
      <- Switch[1682114449] #DR -> #DR
===#Block DS(size=2, flags=100)===
   0. lvar78 = {240636704 ^ lvar78};
   1. goto AD
      -> UnconditionalJump[GOTO] #DS -> #AD
      <- Switch[28214770] #DR -> #DS
      <- Immediate #DR -> #DS
===#Block AD(size=5, flags=0)===
   0. // Frame: locals[0] [] stack[0] []
   1. lvar39 = com.hang.plugin.manager.TreasureItemManager.iivqgrpagm(com.hang.plugin.manager.TreasureItemManager.utdmhhkwoahmkaq(), lvar78);
   2. lvar40 = org.bukkit.Material.valueOf(lvar39);
   3. return lvar40;
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2095522984)
      goto BF
      -> ConditionalJump[IF_ICMPNE] #AD -> #BF
      <- Switch[1292083487] #DR -> #AD
      <- UnconditionalJump[GOTO] #DS -> #AD
===#Block BF(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != 2095522984)
      goto BF
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {391487732 ^ lvar78})
      goto BF
   2. _consume({633188083 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -187189448)
      goto BF
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1310160907 ^ lvar78})
      goto BF
   5. _consume({1763035083 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1686717525)
      goto BF
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {142400897 ^ lvar78})
      goto BF
   8. _consume({1117214829 ^ lvar78});
   9. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BF -> #BF
      <- ConditionalJump[IF_ICMPNE] #BF -> #BF
      <- ConditionalJump[IF_ICMPNE] #R -> #BF
      <- ConditionalJump[IF_ICMPNE] #Z -> #BF
      <- ConditionalJump[IF_ICMPNE] #AD -> #BF
===#Block BG(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1511030039)
      goto BG
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1840155363 ^ lvar78})
      goto BG
   2. _consume({2114551706 ^ lvar78});
   3. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- ConditionalJump[IF_ICMPNE] #BG -> #BG
      <- Switch[1859619349] #DR -> #BG
      <- DefaultSwitch #DR -> #BG
      <- ConditionalJump[IF_ICMPNE] #D -> #BG
===#Block AG(size=2, flags=0)===
   0. return lvar11;
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1391758064)
      goto BD
      -> ConditionalJump[IF_ICMPNE] #AG -> #BD
      <- Immediate #B -> #AG
===#Block BD(size=10, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1968181750)
      goto BD
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1198503486 ^ lvar78})
      goto BD
   2. _consume({1981879217 ^ lvar78});
   3. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1480146613)
      goto BD
   4. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {2096896243 ^ lvar78})
      goto BD
   5. _consume({892111720 ^ lvar78});
   6. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1391758064)
      goto BD
   7. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {1266179455 ^ lvar78})
      goto BD
   8. _consume({1162172891 ^ lvar78});
   9. throw new java/lang/IllegalAccessException("double");
      -> ConditionalJump[IF_ICMPNE] #BD -> #BD
      <- ConditionalJump[IF_ICMPNE] #BD -> #BD
      <- ConditionalJump[IF_ICMPNE] #AU -> #BD
      <- ConditionalJump[IF_ICMPNE] #E -> #BD
      <- ConditionalJump[IF_ICMPNE] #AG -> #BD
===#Block BK(size=4, flags=0)===
   0. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != -1703993696)
      goto BK
   1. if (edljcxxlexolrenr.bumanwckibjpqejj.kotauboffqqcdsba(lvar78) != {997869065 ^ lvar78})
      goto BK
   2. _consume({204671798 ^ lvar78});
   3. throw new java/io/IOException("double");
      -> ConditionalJump[IF_ICMPNE] #BK -> #BK
      <- Switch[1716273423] #CK -> #BK
      <- ConditionalJump[IF_ICMPNE] #A -> #BK
      <- DefaultSwitch #CK -> #BK
      <- ConditionalJump[IF_ICMPNE] #BK -> #BK
