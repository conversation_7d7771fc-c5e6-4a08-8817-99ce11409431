# 🔗 PlaceholderAPI 占位符说明

## 📋 **支持的占位符列表**

### **📊 等级系统占位符**

| 占位符 | 说明 | 示例输出 |
|--------|------|----------|
| `%evacuation_search_count%` | 玩家摸金次数 | `25` |
| `%evacuation_level%` | 玩家等级数字 | `5` |
| `%evacuation_level_name%` | 等级名称 | `见习摸金者` |
| `%evacuation_level_color%` | 等级颜色 | `§a` |
| `%evacuation_level_colored%` | 带颜色的等级名称 | `§a见习摸金者` |
| `%evacuation_level_format%` | 等级聊天格式 | `§7[§a见习摸金者§7]` |
| `%evacuation_next_level%` | 下一等级数字 | `6` |
| `%evacuation_next_level_name%` | 下一等级名称 | `熟练摸金者` |
| `%evacuation_level_progress%` | 等级升级进度百分比 | `75` |
| `%evacuation_level_progress_bar%` | 等级升级进度条 | `§b███████████████§7█████` |
| `%evacuation_level_searches_needed%` | 升级还需摸金次数 | `15` |
| `%evacuation_max_level%` | 最大等级 | `10` |
| `%evacuation_is_max_level%` | 是否达到最高等级 | `false` |

## 🎯 **使用示例**

### **1. 聊天格式**
```yaml
# 在聊天插件中使用
format: "{evacuation_level_format} {player}: {message}"
# 输出: §7[§a见习摸金者§7] 玩家名: 消息内容
```

### **2. 计分板显示**
```yaml
# 在计分板插件中使用
lines:
  - "§6摸金信息"
  - "§e等级: {evacuation_level_colored}"
  - "§e摸金次数: §a{evacuation_search_count}"
  - "§e升级进度:"
  - "{evacuation_level_progress_bar}"
  - "§e进度: §a{evacuation_level_progress}%"
```

### **3. 称号系统**
```yaml
# 在称号插件中使用
titles:
  treasure_hunter:
    display: "{evacuation_level_colored}"
    requirement: "{evacuation_search_count} >= 10"
```

### **4. GUI显示**
```yaml
# 在GUI插件中使用
items:
  player_info:
    name: "§6玩家信息"
    lore:
      - "§e摸金等级: {evacuation_level_colored}"
      - "§e摸金次数: §a{evacuation_search_count}"
      - "§e下一等级: {evacuation_next_level_name}"
      - "§e升级进度: §7({evacuation_level_progress}%)"
      - "{evacuation_level_progress_bar}"
      - "§e还需摸金: §c{evacuation_level_searches_needed} §e次"
```

## 🔧 **配置要求**

### **1. 安装PlaceholderAPI**
```bash
# 下载并安装PlaceholderAPI插件
# 版本要求: 2.10.0+
```

### **2. 注册占位符**
```bash
# 插件会自动注册占位符
# 无需手动操作
```

### **3. 验证占位符**
```bash
# 使用命令测试占位符
/papi parse <玩家名> %evacuation_rank_colored%
```

## 📈 **进度条说明**

### **等级进度条**
- **蓝色部分** (`§b█`): 已完成的进度
- **灰色部分** (`§7█`): 未完成的进度
- **总长度**: 20个字符
- **示例**: `§b███████████████§7█████` (75%进度)

## 🎮 **实际效果展示**

### **玩家信息面板**
```
§6§l=== 摸金信息 ===
§e当前等级: §a见习摸金者
§e摸金次数: §a25 §e次
§e下一等级: §b熟练摸金者
§e升级进度: §b███████████████§7█████ §7(75%)
§e还需摸金: §c15 §e次
§e最高等级: §6摸金王者
§6§l==================
```

### **聊天显示**
```
§7[§a见习摸金者§7] 玩家名: 我刚刚摸到了钻石！
```

## ⚠️ **注意事项**

1. **插件依赖**: 需要安装PlaceholderAPI插件
2. **版本兼容**: 支持Minecraft 1.8.8-1.21.4
3. **数据同步**: 占位符数据实时更新
4. **性能优化**: 占位符有缓存机制，不会影响服务器性能

## 🔄 **更新日志**

### **v1.8.5**
- ✅ 新增等级系统占位符
- ✅ 支持进度条显示
- ✅ 支持颜色格式化
- ✅ 优化性能和缓存
- ✅ 移除段位系统，专注等级系统

## 📞 **技术支持**

如需技术支持，请联系：
- **作者**: hangzong(航总)
- **微信**: hang060217
- **QQ群**: 361919269
