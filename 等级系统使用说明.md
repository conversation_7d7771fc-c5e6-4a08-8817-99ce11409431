# 🏆 Hang摸金插件 - 等级系统使用说明

## 📋 系统概述

等级系统是基于玩家在摸金箱中的搜索次数来计算等级的进阶系统。每次成功搜索到物品都会增加搜索次数，达到指定次数后自动升级并获得奖励。

## 🎮 功能特性

### ✨ 核心功能
- **自动等级计算**：基于搜索次数自动计算玩家等级
- **聊天前缀显示**：在聊天中显示玩家等级前缀
- **升级奖励系统**：达到新等级时自动给予奖励
- **升级广播**：全服广播玩家升级消息
- **等级查询**：查看自己或其他玩家的等级信息

### 🎯 等级配置
默认配置包含10个等级：

| 等级 | 名称 | 所需搜索次数 | 显示格式 |
|------|------|-------------|----------|
| 1 | 新手摸金者 | 0 | §7[§f新手摸金者§7] |
| 2 | 见习摸金者 | 10 | §7[§a见习摸金者§7] |
| 3 | 熟练摸金者 | 25 | §7[§b熟练摸金者§7] |
| 4 | 专业摸金者 | 50 | §7[§d专业摸金者§7] |
| 5 | 大师摸金者 | 100 | §7[§6大师摸金者§7] |
| 6 | 传奇摸金者 | 200 | §7[§c传奇摸金者§7] |
| 7 | 史诗摸金者 | 350 | §7[§5史诗摸金者§7] |
| 8 | 神话摸金者 | 500 | §7[§4神话摸金者§7] |
| 9 | 至尊摸金者 | 750 | §7[§e§l至尊摸金者§7] |
| 10 | 摸金王者 | 1000 | §6§l[§c§l摸金王者§6§l] |

## 🎁 升级奖励示例

### 等级2 - 见习摸金者
- 钻石 x1
- 升级提示消息

### 等级5 - 大师摸金者
- 钻石剑 x1
- 钻石 x5
- 经验值 +1000

### 等级10 - 摸金王者
- 钻石块 x10
- 绿宝石块 x10
- 金块 x10
- 经验值 +10000

## 📝 命令使用

### 🔍 查看等级信息
```
/evac level          # 查看自己的等级信息
/evac level [玩家名]  # 查看其他玩家的等级信息（需要管理员权限）
```

### 📊 等级信息显示内容
- 当前等级和等级名称
- 总搜索次数
- 距离下一等级所需的搜索次数
- 下一等级的名称

## ⚙️ 配置文件

### 📁 levels.yml 配置说明

```yaml
# 等级系统设置
settings:
  broadcast_levelup: true      # 是否广播升级消息
  show_level_in_chat: true     # 是否在聊天中显示等级前缀

# 消息配置
messages:
  levelup_broadcast: "§6玩家 §e{player} §6达到了摸金等级 §e{level} {level_name}§6！"

# 等级配置
levels:
  1:
    required_searches: 0                    # 所需搜索次数
    name: "新手摸金者"                      # 等级名称
    display_format: "§7[§f{level_name}§7]" # 聊天显示格式
    rewards:                                # 升级奖励命令
      - "/give {player} diamond 1"
```

### 🎨 显示格式变量
- `{level}` - 等级数字
- `{level_name}` - 等级名称
- `{searches}` - 搜索次数
- `{player}` - 玩家名称

### 🎁 奖励命令格式
- 以 `/` 开头的命令会由控制台执行
- 支持 `{player}` 变量替换为玩家名称
- 可以使用任何Minecraft命令

## 🔧 管理员功能

### 📊 数据管理
- 玩家等级数据自动保存到 `player_levels.yml`
- 支持配置热重载：`/evacuation reload`
- 插件关闭时自动保存所有数据

### 🎯 自定义等级
可以在 `levels.yml` 中添加更多等级：

```yaml
11:
  required_searches: 1500
  name: "摸金帝王"
  display_format: "§4§l[§6§l{level_name}§4§l]"
  rewards:
    - "/give {player} nether_star 1"
    - "/tellraw {player} [\"§4§l恭喜达到摸金帝王等级！\"]"
```

## 🎮 游戏体验

### 🏃‍♂️ 升级流程
1. 玩家放置摸金箱
2. 右键打开摸金箱开始搜索
3. 每搜索到一个物品，搜索次数****. 达到等级要求时自动升级
5. 播放升级音效和特效
6. 获得升级奖励
7. 全服广播升级消息

### 💬 聊天显示
玩家在聊天时会自动显示等级前缀：
```
[新手摸金者]玩家名: 你好！
[摸金王者]大佬: 恭喜升级！
```

### 🔊 升级提示
- 个人升级消息
- 升级音效播放
- 全服广播消息
- 自动奖励发放

## 🎯 使用建议

### 👥 服务器管理员
1. 根据服务器玩家活跃度调整等级所需搜索次数
2. 设计合适的升级奖励，保持游戏平衡
3. 定期备份 `player_levels.yml` 文件
4. 可以关闭升级广播以减少刷屏

### 🎮 玩家
1. 多使用摸金箱来提升等级
2. 使用 `/evac level` 查看进度
3. 邀请朋友一起摸金，增加游戏乐趣
4. 展示你的等级前缀！

## 🔄 版本兼容

- ✅ Minecraft 1.12.2
- ✅ Minecraft 1.20.1
- ✅ 支持热重载
- ✅ 向下兼容旧版本数据

---

**作者**: hangzong  
**技术支持**: V hang060217  
**交流群**: 361919269  
**版本**: 1.4.0
