package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import org.bukkit.Location;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.ItemStack;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;

/**
 * 摸金箱数据管理器
 * 负责摸金箱数据的持久化保存和加载
 *
 * <AUTHOR>
 */
public class ChestManager {

    private final HangPlugin plugin;
    private final File chestsFile;
    private YamlConfiguration chestsConfig;
    private int chestCount = 0;

    public ChestManager(HangPlugin plugin) {
        this.plugin = plugin;
        this.chestsFile = new File(plugin.getDataFolder(), "chests.yml");
        loadChestsConfig();
    }

    /**
     * 加载摸金箱配置文件
     */
    private void loadChestsConfig() {
        // 增强的配置文件加载逻辑
        if (!chestsFile.exists()) {
            // 检查是否有备份文件可以恢复
            File backupFile = new File(chestsFile.getAbsolutePath() + ".backup");
            if (backupFile.exists() && backupFile.length() > 0) {
                try {
                    java.nio.file.Files.copy(backupFile.toPath(), chestsFile.toPath());
                    plugin.getLogger().info("从备份文件恢复摸金箱数据: " + backupFile.getName());
                } catch (IOException e) {
                    plugin.getLogger().warning("警告：恢复备份文件失败: " + e.getMessage());
                }
            }

            // 如果仍然不存在，创建新文件
            if (!chestsFile.exists()) {
                try {
                    chestsFile.createNewFile();
                    plugin.getLogger().info("创建新的摸金箱数据文件: " + chestsFile.getName());
                } catch (IOException e) {
                    plugin.getLogger().severe("错误：无法创建摸金箱数据文件: " + e.getMessage());
                    return;
                }
            }
        }

        try {
            chestsConfig = YamlConfiguration.loadConfiguration(chestsFile);

            // 验证配置文件完整性
            if (chestsFile.length() > 0) {
                ConfigurationSection chestsSection = chestsConfig.getConfigurationSection("chests");
                int loadedChests = chestsSection != null ? chestsSection.getKeys(false).size() : 0;
                plugin.getLogger().info("摸金箱配置文件加载完成 (文件大小: " + chestsFile.length() + " 字节, 摸金箱: " + loadedChests + " 个)");
            } else {
                plugin.getLogger().info("摸金箱配置文件为空，将在首次保存时创建数据");
            }

        } catch (Exception e) {
            plugin.getLogger().severe("错误：加载摸金箱配置文件失败: " + e.getMessage());
            e.printStackTrace();

            // 尝试从备份恢复
            tryRecoverFromBackup();
        }
    }

    /**
     * 尝试从备份文件恢复
     */
    private void tryRecoverFromBackup() {
        File backupFile = new File(chestsFile.getAbsolutePath() + ".backup");
        if (backupFile.exists() && backupFile.length() > 0) {
            try {
                plugin.getLogger().info("🆘 尝试从备份文件恢复...");
                chestsConfig = YamlConfiguration.loadConfiguration(backupFile);

                // 验证备份文件是否有效
                ConfigurationSection chestsSection = chestsConfig.getConfigurationSection("chests");
                if (chestsSection != null) {
                    // 备份文件有效，复制到主文件
                    java.nio.file.Files.copy(backupFile.toPath(), chestsFile.toPath(),
                        java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                    plugin.getLogger().info("完成：从备份文件成功恢复摸金箱数据");
                } else {
                    plugin.getLogger().warning("警告：备份文件也无效，创建空配置");
                    chestsConfig = new YamlConfiguration();
                }
            } catch (Exception e) {
                plugin.getLogger().severe("错误：从备份文件恢复失败: " + e.getMessage());
                chestsConfig = new YamlConfiguration();
            }
        } else {
            plugin.getLogger().warning("警告：没有可用的备份文件，创建空配置");
            chestsConfig = new YamlConfiguration();
        }
    }

    // 异步保存队列
    private final java.util.concurrent.ConcurrentLinkedQueue<SaveTask> saveQueue = new java.util.concurrent.ConcurrentLinkedQueue<>();
    private final java.util.concurrent.atomic.AtomicBoolean isSaving = new java.util.concurrent.atomic.AtomicBoolean(false);
    private final java.util.concurrent.ScheduledExecutorService saveExecutor = java.util.concurrent.Executors.newSingleThreadScheduledExecutor();

    // 保存任务类
    private static class SaveTask {
        final Location location;
        final com.hang.plugin.listeners.PlayerListener.TreasureChestData data;
        final long timestamp;

        SaveTask(Location location, com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
            this.location = location;
            this.data = data;
            this.timestamp = System.currentTimeMillis();
        }
    }

    /**
     * 异步保存摸金箱数据到文件
     */
    public void saveChestDataAsync(Location location, com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
        // 检查是否启用异步保存
        if (!plugin.getConfig().getBoolean("performance.async_save.enabled", true)) {
            // 如果禁用异步保存，使用同步保存
            saveChestData(location, data);
            return;
        }

        // 检查队列大小限制
        int maxQueueSize = plugin.getConfig().getInt("performance.async_save.max_queue_size", 1000);
        if (saveQueue.size() >= maxQueueSize) {
            plugin.getLogger().warning("警告：保存队列已满 (" + saveQueue.size() + "/" + maxQueueSize + ")，强制同步保存");
            saveChestData(location, data);
            return;
        }

        // 添加到保存队列
        saveQueue.offer(new SaveTask(location, data));

        // 如果没有正在保存，启动保存任务
        if (!isSaving.get()) {
            processSaveQueue();
        }
    }

    /**
     * 🔄 处理保存队列
     */
    private void processSaveQueue() {
        if (!isSaving.compareAndSet(false, true)) {
            return; // 已经在保存中
        }

        saveExecutor.execute(() -> {
            try {
                long startTime = System.currentTimeMillis();
                int savedCount = 0;

                // 批量处理保存队列
                SaveTask task;
                while ((task = saveQueue.poll()) != null) {
                    try {
                        saveChestDataSync(task.location, task.data);
                        savedCount++;
                    } catch (Exception e) {
                        plugin.getLogger().warning("保存摸金箱数据失败: " + e.getMessage());
                    }
                }

                // 批量写入文件
                if (savedCount > 0) {
                    saveConfigFile();

                    long duration = System.currentTimeMillis() - startTime;

                    // 只在调试模式或耗时过长时显示日志
                    if (plugin.getConfig().getBoolean("debug.enabled", false) || duration > 1000) {
                        plugin.getLogger().info("异步保存完成: " + savedCount + " 个摸金箱 (耗时: " + duration + "ms)");
                    }
                }

            } finally {
                isSaving.set(false);

                // 如果队列中还有任务，继续处理
                if (!saveQueue.isEmpty()) {
                    processSaveQueue();
                }
            }
        });
    }

    /**
     * 同步保存摸金箱数据到内存配置（不写文件）
     */
    private void saveChestDataSync(Location location, com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
        String locationKey = locationToString(location);
        ConfigurationSection chestSection = chestsConfig.createSection("chests." + locationKey);

        // 保存基本信息
        chestSection.set("world", location.getWorld().getName());
        chestSection.set("x", location.getBlockX());
        chestSection.set("y", location.getBlockY());
        chestSection.set("z", location.getBlockZ());
        chestSection.set("lastRefreshTime", data.getLastRefreshTime());
        chestSection.set("nextRefreshTime", data.getNextRefreshTime());
        chestSection.set("originalItemCount", data.getOriginalItemCount());
        chestSection.set("openTime", data.getOpenTime());

        // 保存摸金箱类型
        chestSection.set("chestType", data.getChestType());

        // 保存当前搜索者信息
        if (data.getCurrentSearcher() != null) {
            chestSection.set("currentSearcher", data.getCurrentSearcher().toString());
            chestSection.set("searchStartTime", data.getSearchStartTime());
        }

        // 保存物品数据
        ConfigurationSection itemsSection = chestSection.createSection("items");
        for (Map.Entry<Integer, ItemStack> entry : data.getItems().entrySet()) {
            int slot = entry.getKey();
            ItemStack item = entry.getValue();

            if (item != null && isValidItem(item)) {
                // 使用增强的序列化方式
                String serializedItem = com.hang.plugin.utils.ItemSerializer.serializeItemStack(item);
                if (serializedItem != null) {
                    itemsSection.set(slot + ".serializedItem", serializedItem);
                } else {
                    // 降级到Bukkit序列化
                    itemsSection.set(slot + ".item", item);
                }

                // 保存对应的物品数据（支持模组物品）
                Object itemData = data.getItemData().get(slot);
                if (itemData instanceof com.hang.plugin.manager.TreasureItemManager.TreasureItem) {
                    com.hang.plugin.manager.TreasureItemManager.TreasureItem treasureItem =
                        (com.hang.plugin.manager.TreasureItemManager.TreasureItem) itemData;
                    ConfigurationSection treasureSection = itemsSection.createSection(slot + ".treasureData");
                    treasureSection.set("id", treasureItem.getId());
                    treasureSection.set("material", treasureItem.getMaterial().name());
                    treasureSection.set("amount", treasureItem.getAmount());
                    treasureSection.set("name", treasureItem.getName());
                    treasureSection.set("probability", treasureItem.getProbability());
                    treasureSection.set("searchSpeed", treasureItem.getSearchSpeed());
                    treasureSection.set("data", treasureItem.getData());

                    if (treasureItem.getLore() != null && !treasureItem.getLore().isEmpty()) {
                        treasureSection.set("lore", treasureItem.getLore());
                    }

                    if (treasureItem.getCommands() != null && !treasureItem.getCommands().isEmpty()) {
                        treasureSection.set("commands", treasureItem.getCommands());
                    }
                } else if (itemData instanceof com.hang.plugin.manager.ModItemManager.ModItem) {
                    // 修复：完整保存模组物品数据
                    com.hang.plugin.manager.ModItemManager.ModItem modItem =
                        (com.hang.plugin.manager.ModItemManager.ModItem) itemData;

                    ConfigurationSection modSection = itemsSection.createSection(slot + ".modData");
                    modSection.set("id", modItem.getId());
                    modSection.set("modId", modItem.getModId());
                    modSection.set("itemId", modItem.getItemId());
                    modSection.set("amount", modItem.getAmount());
                    modSection.set("name", modItem.getName());
                    modSection.set("probability", modItem.getProbability());
                    modSection.set("searchSpeed", modItem.getSearchSpeed());
                    modSection.set("chestTypes", modItem.getChestTypes()); // 保存摸金箱类型

                    if (modItem.getLore() != null && !modItem.getLore().isEmpty()) {
                        modSection.set("lore", modItem.getLore());
                    }

                    if (modItem.getCommands() != null && !modItem.getCommands().isEmpty()) {
                        modSection.set("commands", modItem.getCommands());
                    }

                    // 保存序列化的ItemStack数据以完整恢复物品
                    String modSerializedItem = com.hang.plugin.utils.ItemSerializer.serializeItemStack(item);
                    if (modSerializedItem != null) {
                        modSection.set("serializedItem", modSerializedItem);
                    }

                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().info("已保存模组物品数据: " + modItem.getId() +
                                               " (槽位: " + slot + ", 类型: " + String.join(",", modItem.getChestTypes()) + ")");
                    }
                }
            }
        }

        // 保存已搜索的槽位
        chestSection.set("searchedSlots", data.getSearchedSlots().toArray(new Integer[0]));
    }

    /**
     * 兼容方法：同步保存摸金箱数据（立即写入文件）
     * 用于向后兼容，建议使用 saveChestDataAsync
     */
    public void saveChestData(Location location, com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
        saveChestDataSync(location, data);
        saveConfig();
    }

    /**
     * 批量保存摸金箱数据（只保存到内存配置，不立即写文件）
     * 用于优化关服保存性能
     */
    public void saveChestDataBatch(Location location, com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
        saveChestDataSync(location, data);
        // 不调用 saveConfig()，由调用者统一保存文件
    }

    /**
     * 从文件加载摸金箱数据
     */
    public com.hang.plugin.listeners.PlayerListener.TreasureChestData loadChestData(Location location) {
        String locationKey = locationToString(location);
        ConfigurationSection chestSection = chestsConfig.getConfigurationSection("chests." + locationKey);

        if (chestSection == null) {
            return null;
        }

        // 修复：加载摸金箱类型
        String chestType = chestSection.getString("chestType", "common");
        com.hang.plugin.listeners.PlayerListener.TreasureChestData data =
            new com.hang.plugin.listeners.PlayerListener.TreasureChestData(chestType);

        // 加载基本信息
        data.setLastRefreshTime(chestSection.getLong("lastRefreshTime", System.currentTimeMillis()));
        data.setNextRefreshTime(chestSection.getLong("nextRefreshTime", 0));
        data.setOpenTime(chestSection.getLong("openTime", 0));

        // 修复：正确加载originalItemCount，如果为0则设为-1让其重新计算
        int originalItemCount = chestSection.getInt("originalItemCount", -1);
        if (originalItemCount == 0) {
            // 如果保存的是0，说明数据可能有问题，设为-1让其重新计算
            originalItemCount = -1;
        }
        data.setOriginalItemCount(originalItemCount);

        // 修复：完整加载当前搜索者信息
        String searcherStr = chestSection.getString("currentSearcher");
        if (searcherStr != null) {
            try {
                UUID searcherUUID = UUID.fromString(searcherStr);
                data.setCurrentSearcher(searcherUUID);

                // 恢复搜索开始时间（如果TreasureChestData支持）
                long searchStartTime = chestSection.getLong("searchStartTime", 0);
                if (searchStartTime > 0) {
                    try {
                        // 使用反射设置searchStartTime，如果方法存在的话
                        java.lang.reflect.Method setSearchStartTimeMethod =
                            data.getClass().getMethod("setSearchStartTime", long.class);
                        setSearchStartTimeMethod.invoke(data, searchStartTime);
                    } catch (Exception e) {
                        // 如果方法不存在，记录调试信息
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().info("TreasureChestData类不支持setSearchStartTime方法，跳过搜索时间恢复");
                        }
                    }
                }
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的搜索者UUID: " + searcherStr);
            }
        }

        // 加载物品数据
        ConfigurationSection itemsSection = chestSection.getConfigurationSection("items");
        if (itemsSection != null) {
            for (String slotStr : itemsSection.getKeys(false)) {
                try {
                    int slot = Integer.parseInt(slotStr);
                    ConfigurationSection slotSection = itemsSection.getConfigurationSection(slotStr);

                    if (slotSection != null) {
                        // 修复：增强的物品加载逻辑
                        ItemStack item = loadItemFromSection(slotSection);
                        if (item != null && isValidItem(item)) {
                            data.getItems().put(slot, item);

                            // 加载TreasureItem数据
                            ConfigurationSection treasureSection = slotSection.getConfigurationSection("treasureData");
                            if (treasureSection != null) {
                                com.hang.plugin.manager.TreasureItemManager.TreasureItem treasureItem = loadTreasureItemFromConfig(treasureSection);
                                if (treasureItem != null) {
                                    data.getItemData().put(slot, treasureItem);
                                }
                            }

                            // 加载模组物品数据
                            ConfigurationSection modSection = slotSection.getConfigurationSection("modData");
                            if (modSection != null) {
                                com.hang.plugin.manager.ModItemManager.ModItem modItem = loadModItemFromConfig(modSection);
                                if (modItem != null) {
                                    data.getItemData().put(slot, modItem);

                                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                                        plugin.getLogger().info("已加载模组物品数据: " + modItem.getId() +
                                                               " (槽位: " + slot + ", 类型: " + String.join(",", modItem.getChestTypes()) + ")");
                                    }
                                }
                            }
                        }
                    }
                } catch (NumberFormatException e) {
                    plugin.getLogger().warning("无效的槽位数字: " + slotStr);
                }
            }
        }

        // 加载已搜索的槽位
        if (chestSection.contains("searchedSlots")) {
            for (int slot : chestSection.getIntegerList("searchedSlots")) {
                data.getSearchedSlots().add(slot);
            }
        }

        // 修复：确保originalItemCount正确设置
        if (data.getOriginalItemCount() <= 0 && !data.getItems().isEmpty()) {
            // 如果originalItemCount未正确设置但有物品数据，根据物品数量设置
            data.setOriginalItemCount(data.getItems().size());
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("修复originalItemCount: " + data.getItems().size() + " (位置: " + locationToString(location) + ")");
            }
        }

        // 验证加载的数据
        if (validateChestData(data)) {
            return data;
        } else {
            plugin.getLogger().warning("摸金箱数据验证失败，位置: " + locationToString(location));
            return null;
        }
    }

    /**
     * 从配置中加载TreasureItem
     */
    private com.hang.plugin.manager.TreasureItemManager.TreasureItem loadTreasureItemFromConfig(ConfigurationSection section) {
        try {
            String id = section.getString("id");
            String materialName = section.getString("material");
            int amount = section.getInt("amount", 1);
            String name = section.getString("name");
            double probability = section.getDouble("probability", 1.0);
            int searchSpeed = section.getInt("searchSpeed", 1);

            if (id == null || materialName == null) {
                return null;
            }

            org.bukkit.Material material;
            try {
                material = org.bukkit.Material.valueOf(materialName);
            } catch (IllegalArgumentException e) {
                plugin.getLogger().warning("无效的材料类型: " + materialName);
                return null;
            }

            // 加载lore和commands
            java.util.List<String> lore = null;
            if (section.contains("lore")) {
                lore = section.getStringList("lore");
            }

            java.util.List<String> commands = null;
            if (section.contains("commands")) {
                commands = section.getStringList("commands");
            }

            short data = (short)section.getInt("data", 0);

            com.hang.plugin.manager.TreasureItemManager.TreasureItem treasureItem =
                new com.hang.plugin.manager.TreasureItemManager.TreasureItem(
                    id, material, amount, data, name, lore, probability, searchSpeed, commands
                );

            return treasureItem;
        } catch (Exception e) {
            plugin.getLogger().warning("加载TreasureItem时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 删除摸金箱数据
     */
    public void removeChestData(Location location) {
        String locationKey = locationToString(location);
        chestsConfig.set("chests." + locationKey, null);
        saveConfig();
    }

    /**
     * 加载所有摸金箱数据到PlayerListener
     */
    public void loadAllChestData() {
        ConfigurationSection chestsSection = chestsConfig.getConfigurationSection("chests");
        if (chestsSection == null) {
            return;
        }

        int loadedCount = 0;
        for (String locationKey : chestsSection.getKeys(false)) {
            try {
                Location location = stringToLocation(locationKey);
                if (location != null) {
                    com.hang.plugin.listeners.PlayerListener.TreasureChestData data = loadChestData(location);
                    if (data != null) {
                        plugin.getPlayerListener().saveTreasureChestData(location, data);
                        loadedCount++;
                    }
                }
            } catch (Exception e) {
                plugin.getLogger().warning("加载摸金箱数据时出错 (" + locationKey + "): " + e.getMessage());
            }
        }

        chestCount = loadedCount;
    }

    /**
     * 强制同步保存所有待保存的数据
     */
    public void forceSaveAll() {
        try {
            long startTime = System.currentTimeMillis();

            // 等待异步保存完成
            while (!saveQueue.isEmpty() || isSaving.get()) {
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }

            // 从PlayerListener获取所有摸金箱数据并保存
            if (plugin.getPlayerListener() != null) {
                plugin.getPlayerListener().saveAllChestDataToFile();
            }

            // 保存配置文件
            saveConfigFile();

            long duration = System.currentTimeMillis() - startTime;

            // 显示保存结果
            if (plugin.getConfig().getBoolean("debug.enabled", false) || duration > 500) {
                plugin.getLogger().info("🔒 强制同步保存完成 (耗时: " + duration + "ms)");
            }
        } catch (Exception e) {
            plugin.getLogger().severe("强制保存摸金箱数据时出错: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 保存所有摸金箱数据到文件
     * 这个方法将在插件关闭时被调用
     */
    public void saveAllChestData() {
        forceSaveAll();
    }

    /**
     * 🧹 清理资源
     */
    public void cleanup() {
        try {
            // 强制保存所有数据
            forceSaveAll();

            // 关闭线程池
            if (saveExecutor != null && !saveExecutor.isShutdown()) {
                saveExecutor.shutdown();
                try {
                    if (!saveExecutor.awaitTermination(5, java.util.concurrent.TimeUnit.SECONDS)) {
                        saveExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    saveExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            // 清空队列
            saveQueue.clear();

        } catch (Exception e) {
            plugin.getLogger().severe("清理ChestManager资源时出错: " + e.getMessage());
        }
    }

    /**
     * 保存配置文件
     */
    private void saveConfig() {
        try {
            // 修复：1.12.2版本数据保存增强
            // 确保父目录存在
            if (!chestsFile.getParentFile().exists()) {
                boolean created = chestsFile.getParentFile().mkdirs();
                plugin.getLogger().info("创建数据目录: " + chestsFile.getParentFile().getAbsolutePath() + " - " + (created ? "成功" : "失败"));
            }

            // 保存前验证数据完整性
            validateConfigData();

            // 修复：清理可能的null值，防止SnakeYAML错误
            cleanupNullValues();

            // 创建临时文件先保存，然后原子性替换
            File tempFile = new File(chestsFile.getAbsolutePath() + ".tmp");

            // 确保临时文件不存在（清理之前可能残留的临时文件）
            if (tempFile.exists()) {
                tempFile.delete();
            }

            try {
                chestsConfig.save(tempFile);
            } catch (Exception saveError) {
                plugin.getLogger().severe("YAML保存失败，尝试修复数据: " + saveError.getMessage());

                // 清理失败的临时文件
                if (tempFile.exists()) {
                    tempFile.delete();
                }

                // 尝试修复并重新保存
                if (repairConfigData()) {
                    chestsConfig.save(tempFile);
                } else {
                    throw saveError; // 如果修复失败，抛出原始错误
                }
            }

            // 验证临时文件是否成功保存
            if (tempFile.exists() && tempFile.length() > 0) {
                try {
                    // 备份原文件（如果存在）
                    if (chestsFile.exists()) {
                        File backupFile = new File(chestsFile.getAbsolutePath() + ".backup");
                        if (backupFile.exists()) {
                            backupFile.delete();
                        }
                        chestsFile.renameTo(backupFile);
                    }

                    // 原子性替换
                    boolean renamed = tempFile.renameTo(chestsFile);
                    if (!renamed) {
                        // 如果重命名失败，检查临时文件是否还存在
                        if (tempFile.exists()) {
                            try {
                                // 尝试复制
                                java.nio.file.Files.copy(tempFile.toPath(), chestsFile.toPath(),
                                    java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                            } catch (Exception copyEx) {
                                plugin.getLogger().severe("复制临时文件失败: " + copyEx.getMessage());
                                throw copyEx;
                            }
                        } else {
                            throw new java.io.IOException("临时文件不存在，无法完成保存操作: " + tempFile.getAbsolutePath());
                        }
                    }
                } finally {
                    // 确保临时文件被清理（无论操作是否成功）
                    if (tempFile.exists()) {
                        tempFile.delete();
                    }
                }

                // 验证最终文件
                if (chestsFile.exists() && chestsFile.length() > 0) {
                    // 只在调试模式下显示详细保存信息
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().info("完成：摸金箱数据已保存: " + chestsFile.getAbsolutePath() +
                            " (大小: " + chestsFile.length() + " 字节, 摸金箱数量: " + getConfigChestCount() + ")");

                        // 验证文件可读性
                        try {
                            YamlConfiguration testConfig = YamlConfiguration.loadConfiguration(chestsFile);
                            ConfigurationSection chestsSection = testConfig.getConfigurationSection("chests");
                            int loadedChests = chestsSection != null ? chestsSection.getKeys(false).size() : 0;
                            plugin.getLogger().info("完成：文件验证成功，可读取 " + loadedChests + " 个摸金箱数据");
                        } catch (Exception e) {
                            plugin.getLogger().warning("警告：文件保存成功但验证失败: " + e.getMessage());
                        }
                    }
                } else {
                    plugin.getLogger().severe("错误：摸金箱数据文件保存失败，文件不存在或为空");
                }
            } else {
                plugin.getLogger().severe("错误：临时文件保存失败");
            }

        } catch (IOException e) {
            plugin.getLogger().severe("错误：无法保存摸金箱数据文件: " + e.getMessage());
            e.printStackTrace();

            // 增强的备用保存方法
            try {
                String timestamp = String.valueOf(System.currentTimeMillis());
                String backupPath = chestsFile.getAbsolutePath() + ".emergency." + timestamp;
                File emergencyBackup = new File(backupPath);
                chestsConfig.save(emergencyBackup);
                plugin.getLogger().info("🆘 已创建紧急备份文件: " + backupPath);

                // 尝试恢复到主文件
                if (emergencyBackup.exists() && emergencyBackup.length() > 0) {
                    java.nio.file.Files.copy(emergencyBackup.toPath(), chestsFile.toPath(),
                        java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                    plugin.getLogger().info("🔄 已从紧急备份恢复主文件");
                }
            } catch (IOException backupError) {
                plugin.getLogger().severe("错误：紧急备份也失败: " + backupError.getMessage());
                backupError.printStackTrace();
            }
        }
    }

    /**
     * 验证配置数据完整性
     */
    private void validateConfigData() {
        try {
            ConfigurationSection chestsSection = chestsConfig.getConfigurationSection("chests");
            if (chestsSection == null) {
                // 静默处理，没有数据时不输出日志
                return;
            }

            // 验证每个摸金箱数据的完整性
            int validChests = 0;
            int invalidChests = 0;

            for (String locationKey : chestsSection.getKeys(false)) {
                ConfigurationSection chestSection = chestsSection.getConfigurationSection(locationKey);
                if (chestSection != null &&
                    chestSection.contains("world") &&
                    chestSection.contains("x") &&
                    chestSection.contains("y") &&
                    chestSection.contains("z")) {
                    validChests++;
                } else {
                    invalidChests++;
                    // 只在调试模式下显示无效数据警告
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().warning("警告：发现无效的摸金箱数据: " + locationKey);
                    }
                }
            }

            // 只在调试模式下显示验证结果
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("📊 数据验证完成: 有效=" + validChests + ", 无效=" + invalidChests);
            }

        } catch (Exception e) {
            plugin.getLogger().warning("警告：配置数据验证失败: " + e.getMessage());
        }
    }

    /**
     * 修复：清理可能的null值，防止SnakeYAML错误
     */
    private void cleanupNullValues() {
        try {
            ConfigurationSection chestsSection = chestsConfig.getConfigurationSection("chests");
            if (chestsSection == null) {
                return;
            }

            java.util.List<String> keysToRemove = new java.util.ArrayList<>();

            for (String locationKey : chestsSection.getKeys(false)) {
                ConfigurationSection chestSection = chestsSection.getConfigurationSection(locationKey);
                if (chestSection == null) {
                    keysToRemove.add(locationKey);
                    continue;
                }

                // 检查并清理null值
                for (String key : chestSection.getKeys(true)) {
                    Object value = chestSection.get(key);
                    if (value == null) {
                        chestSection.set(key, null); // 明确设置为null以移除
                        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                            plugin.getLogger().info("清理null值: " + locationKey + "." + key);
                        }
                    }
                }
            }

            // 移除无效的节点
            for (String key : keysToRemove) {
                chestsSection.set(key, null);
                if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                    plugin.getLogger().info("移除无效节点: " + key);
                }
            }

        } catch (Exception e) {
            plugin.getLogger().warning("清理null值时出错: " + e.getMessage());
        }
    }

    /**
     * 修复：尝试修复配置数据
     */
    private boolean repairConfigData() {
        try {
            plugin.getLogger().info("尝试修复配置数据...");

            // 创建新的配置对象
            YamlConfiguration newConfig = new YamlConfiguration();
            ConfigurationSection chestsSection = chestsConfig.getConfigurationSection("chests");

            if (chestsSection == null) {
                plugin.getLogger().info("没有摸金箱数据需要修复");
                return true;
            }

            ConfigurationSection newChestsSection = newConfig.createSection("chests");
            int repairedCount = 0;

            for (String locationKey : chestsSection.getKeys(false)) {
                try {
                    ConfigurationSection chestSection = chestsSection.getConfigurationSection(locationKey);
                    if (chestSection == null) continue;

                    // 验证必要字段
                    if (chestSection.contains("world") &&
                        chestSection.contains("x") &&
                        chestSection.contains("y") &&
                        chestSection.contains("z")) {

                        ConfigurationSection newChestSection = newChestsSection.createSection(locationKey);

                        // 复制有效数据
                        for (String key : chestSection.getKeys(false)) {
                            Object value = chestSection.get(key);
                            if (value != null) {
                                newChestSection.set(key, value);
                            }
                        }
                        repairedCount++;
                    }
                } catch (Exception e) {
                    plugin.getLogger().warning("跳过损坏的摸金箱数据: " + locationKey);
                }
            }

            // 替换配置
            chestsConfig = newConfig;
            plugin.getLogger().info("配置修复完成，恢复了 " + repairedCount + " 个摸金箱数据");
            return true;

        } catch (Exception e) {
            plugin.getLogger().severe("配置修复失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取当前配置文件中的摸金箱数量
     */
    public int getConfigChestCount() {
        try {
            ConfigurationSection chestsSection = chestsConfig.getConfigurationSection("chests");
            return chestsSection != null ? chestsSection.getKeys(false).size() : 0;
        } catch (Exception e) {
            return 0;
        }
    }

    /**
     * 公共的保存配置文件方法
     * 供外部调用（如自动保存任务）
     */
    public void saveConfigFile() {
        saveConfig();
    }

    /**
     * 将位置转换为字符串键
     */
    private String locationToString(Location location) {
        return location.getWorld().getName() + "_" +
               location.getBlockX() + "_" +
               location.getBlockY() + "_" +
               location.getBlockZ();
    }

    /**
     * 将字符串键转换为位置
     */
    private Location stringToLocation(String locationKey) {
        try {
            String[] parts = locationKey.split("_");
            if (parts.length != 4) {
                return null;
            }

            String worldName = parts[0];
            int x = Integer.parseInt(parts[1]);
            int y = Integer.parseInt(parts[2]);
            int z = Integer.parseInt(parts[3]);

            org.bukkit.World world = plugin.getServer().getWorld(worldName);
            if (world == null) {
                return null;
            }

            return new Location(world, x, y, z);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取摸金箱数量
     * @return 摸金箱数量
     */
    public int getChestCount() {
        return chestCount;
    }

    /**
     * 验证物品是否有效
     */
    private boolean isValidItem(ItemStack item) {
        if (item == null) return false;
        if (item.getType() == null) return false;
        if (item.getAmount() <= 0) return false;

        try {
            // 尝试序列化验证
            String serialized = com.hang.plugin.utils.ItemSerializer.serializeItemStack(item);
            return serialized != null;
        } catch (Exception e) {
            plugin.getLogger().warning("物品验证失败: " + e.getMessage());
            return false;
        }
    }

    /**
     * 从配置节加载物品
     */
    private ItemStack loadItemFromSection(ConfigurationSection section) {
        // 优先尝试自定义反序列化
        String serializedItem = section.getString("serializedItem");
        if (serializedItem != null) {
            ItemStack item = com.hang.plugin.utils.ItemSerializer.deserializeItemStack(serializedItem);
            if (item != null) {
                return item;
            }
        }

        // 如果失败，尝试Bukkit反序列化
        return section.getItemStack("item");
    }

    /**
     * 从配置中加载模组物品
     */
    private com.hang.plugin.manager.ModItemManager.ModItem loadModItemFromConfig(ConfigurationSection section) {
        try {
            String id = section.getString("id");
            String modId = section.getString("modId");
            String itemId = section.getString("itemId");
            int amount = section.getInt("amount", 1);
            String name = section.getString("name");
            double probability = section.getDouble("probability", 0.1);
            int searchSpeed = section.getInt("searchSpeed", 3);

            if (id == null || modId == null || itemId == null) {
                return null;
            }

            // 加载lore和commands
            java.util.List<String> lore = null;
            if (section.contains("lore")) {
                lore = section.getStringList("lore");
            }

            java.util.List<String> commands = null;
            if (section.contains("commands")) {
                commands = section.getStringList("commands");
            }

            // 加载摸金箱类型
            java.util.List<String> chestTypes = section.getStringList("chestTypes");
            if (chestTypes.isEmpty()) {
                chestTypes = java.util.Arrays.asList("common");
            }

            // 创建ModItem（需要获取材料信息）
            org.bukkit.Material material = org.bukkit.Material.STONE; // 默认材料
            short data = 0;

            // 尝试从序列化数据中获取更准确的信息
            String serializedItem = section.getString("serializedItem");
            if (serializedItem != null) {
                ItemStack restoredItem = com.hang.plugin.utils.ItemSerializer.deserializeItemStack(serializedItem);
                if (restoredItem != null) {
                    material = restoredItem.getType();
                    // 对于1.8版本，尝试获取data值
                    try {
                        data = restoredItem.getDurability();
                    } catch (Exception e) {
                        data = 0;
                    }
                }
            }

            return new com.hang.plugin.manager.ModItemManager.ModItem(
                id, material, amount, data, name, lore,
                modId, itemId, 0, "", "",
                probability, searchSpeed, commands, chestTypes
            );

        } catch (Exception e) {
            plugin.getLogger().warning("加载模组物品时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证摸金箱数据的完整性
     */
    private boolean validateChestData(com.hang.plugin.listeners.PlayerListener.TreasureChestData data) {
        if (data == null) {
            return false;
        }

        try {
            // 验证物品数据
            for (Map.Entry<Integer, ItemStack> entry : data.getItems().entrySet()) {
                if (!isValidItem(entry.getValue())) {
                    plugin.getLogger().warning("发现无效物品数据，槽位: " + entry.getKey());
                    // 移除无效物品而不是整体失败
                    data.getItems().remove(entry.getKey());
                    data.getItemData().remove(entry.getKey());
                }
            }

            // 验证时间数据
            if (data.getLastRefreshTime() < 0 || data.getNextRefreshTime() < 0) {
                plugin.getLogger().warning("发现无效时间数据，重置为当前时间");
                data.setLastRefreshTime(System.currentTimeMillis());
                data.setNextRefreshTime(0);
            }

            // 验证搜索者数据
            if (data.getCurrentSearcher() != null) {
                // 检查搜索者是否仍在线（可选）
                org.bukkit.entity.Player searcher = plugin.getServer().getPlayer(data.getCurrentSearcher());
                if (searcher == null) {
                    // 搜索者已离线，清除搜索状态
                    data.setCurrentSearcher(null);
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().info("搜索者已离线，清除搜索状态");
                    }
                }
            }

            return true;

        } catch (Exception e) {
            plugin.getLogger().warning("验证摸金箱数据时出错: " + e.getMessage());
            return false;
        }
    }
}
