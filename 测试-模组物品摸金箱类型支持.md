# 🧪 模组物品摸金箱类型支持 - 测试指南

## 📋 **测试准备**

### 1. **启动服务器**
```bash
# 启动服务器并观察启动日志
java -jar server.jar
```

### 2. **检查配置加载**
启动时应该看到类似日志：
```
[INFO] 已加载 8 个模组物品
[INFO] 摸金箱种类 'weapon' 找到 X 个模组物品
[INFO] 摸金箱种类 'equipment' 找到 Y 个模组物品
```

## 🔧 **测试步骤**

### 测试1: **配置文件验证**

#### 检查 mod_items.yml
```bash
# 确认配置文件包含 chest_types
cat plugins/HangEvacuation/mod_items.yml | grep -A 20 "thermal_wrench"
```

应该看到：
```yaml
thermal_wrench:
  # ... 其他配置 ...
  chest_types: ["weapon", "equipment"]
```

### 测试2: **调试模式测试**

#### 启用调试模式
在 `config.yml` 中设置：
```yaml
debug:
  enabled: true
```

#### 重载配置
```
/evac reload
```

#### 观察调试日志
应该看到详细的物品分配信息：
```
[INFO] 正在为摸金箱种类 'weapon' 获取物品
[INFO] 摸金箱种类 'weapon' 找到 2 个模组物品
[INFO]   - thermal_wrench: thermal:wrench (适用箱子: weapon, equipment)
[INFO]   - ic2_diamond_drill: ic2:diamond_drill (适用箱子: weapon, equipment)
```

### 测试3: **游戏内功能测试**

#### 给予不同类型的摸金箱
```
/evac give [玩家] 1 weapon    # 武器箱
/evac give [玩家] 1 equipment # 装备箱
/evac give [玩家] 1 supply    # 补给箱
/evac give [玩家] 1 common    # 普通箱
```

#### 测试物品分配
1. **打开武器箱** - 应该能找到 thermal_wrench 和 ic2_diamond_drill
2. **打开装备箱** - 应该能找到所有配置为equipment的模组物品
3. **打开补给箱** - 应该能找到 chisel_marble 和 tinkers_cobalt_ingot
4. **打开普通箱** - 应该能找到所有物品（包括所有模组物品）

### 测试4: **战利品管理GUI测试**

#### 打开管理界面
```
/evac gui
```

#### 测试保存功能
1. 点击 "保存配置" 按钮
2. 应该看到消息：
   ```
   §a配置已保存！
   §7包括模组物品的摸金箱类型配置
   ```

#### 验证配置持久化
1. 重启服务器
2. 检查 mod_items.yml 文件
3. 确认 chest_types 配置被正确保存

### 测试5: **新物品添加测试**

#### 手动添加新模组物品
在 mod_items.yml 中添加：
```yaml
  test_mod_item:
    material: DIAMOND
    amount: 1
    name: "§b测试模组物品"
    mod_id: "testmod"
    item_id: "test_item"
    give_command: "give {player} testmod:test_item 1"
    probability: 0.1
    search_speed: 5
    chest_types: ["equipment", "supply"]  # 测试多类型
```

#### 重载并测试
```
/evac reload
```

检查该物品是否出现在装备箱和补给箱中。

## ✅ **预期结果**

### 🎯 **正确的分配结果**

| 摸金箱类型 | 应包含的模组物品 |
|-----------|-----------------|
| `weapon` | thermal_wrench, ic2_diamond_drill |
| `equipment` | thermal_wrench, ic2_diamond_drill, ae2_drive, thaumcraft_research, enderio_travel_anchor, mekanism_portable_tank, tinkers_cobalt_ingot |
| `supply` | chisel_marble, tinkers_cobalt_ingot, mekanism_portable_tank |
| `ammo` | (无，除非手动配置) |
| `medical` | (无，除非手动配置) |
| `common` | 所有配置为common的模组物品 |

### 📊 **调试日志示例**
```
[INFO] 正在为摸金箱种类 'weapon' 获取物品
[INFO] 类别 'weapon' 找到 X 个物品
[INFO] 摸金箱种类 'weapon' 找到 2 个模组物品
[INFO]   - thermal_wrench: thermal:wrench (适用箱子: weapon, equipment)
[INFO]   - ic2_diamond_drill: ic2:diamond_drill (适用箱子: weapon, equipment)
[INFO] 摸金箱种类 'weapon' 找到 Y 个物品
```

## 🐛 **故障排除**

### 问题1: 模组物品不出现在指定箱子中
**检查项目：**
1. chest_types 配置是否正确
2. 是否重载了配置
3. 调试日志是否显示正确的分配

### 问题2: 配置保存失败
**检查项目：**
1. 文件权限是否正确
2. 配置文件格式是否有效
3. 服务器日志是否有错误信息

### 问题3: 向后兼容问题
**检查项目：**
1. 没有chest_types的物品是否默认为common
2. 现有配置是否被破坏

## 🎉 **测试完成标准**

✅ 所有模组物品按配置正确分配到对应摸金箱
✅ 调试日志显示正确的分配信息
✅ 战利品管理GUI保存功能正常工作
✅ 配置文件正确保存chest_types
✅ 向后兼容性正常
✅ 新添加的模组物品支持chest_types

**当所有测试通过时，模组物品摸金箱类型支持功能即为完全实现！**
