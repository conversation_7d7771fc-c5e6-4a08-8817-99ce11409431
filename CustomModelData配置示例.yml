# 🎨 HangEvacuation CustomModelData 配置示例
# 
# 这个文件展示了如何配置 CustomModelData 来实现自定义搜索动画
# 适用于 Minecraft 1.14+ 版本，配合 ItemsAdder 或材质包使用

# ===============================================
#           基础 CustomModelData 配置
# ===============================================

# 在 config.yml 中的配置示例:
items:
  # 正在搜索的物品 (红色玻璃板)
  searching-item:
    material: STAINED_GLASS_PANE
    data: 14
    name: "§c正在搜索..."
    lore:
      - "§7搜索进行中..."
    # 🆕 CustomModelData 设置 (仅在1.14+版本生效)
    # 设置为0表示不使用CustomModelData，使用正数值来指定自定义模型
    custom_model_data: 100001  # 示例: 使用 100001 作为自定义模型ID
  
  # 未搜索的物品 (灰色玻璃板)
  unsearched-item:
    material: STAINED_GLASS_PANE
    data: 7
    name: "§7未搜索"
    lore:
      - "§7等待自动搜索"
    # 未搜索物品不需要CustomModelData，保持原版外观

# ===============================================
#           ItemsAdder 配置示例
# ===============================================

# 如果您使用 ItemsAdder，可以创建以下配置:
# 文件位置: plugins/ItemsAdder/data/items_packs/your_pack/configs/items.yml

items:
  # 搜索动画玻璃板
  searching_glass_animation:
    display_name: "§c正在搜索..."
    material: RED_STAINED_GLASS_PANE
    custom_model_data: 100001
    # 这里可以添加您的动画配置
    # 例如: 旋转、发光、粒子效果等

  # 您也可以为不同颜色创建相同的动画
  searching_glass_orange:
    display_name: "§6正在搜索..."
    material: ORANGE_STAINED_GLASS_PANE
    custom_model_data: 100001  # 使用相同的模型ID保持动画一致

  searching_glass_yellow:
    display_name: "§e正在搜索..."
    material: YELLOW_STAINED_GLASS_PANE
    custom_model_data: 100001  # 使用相同的模型ID保持动画一致

  searching_glass_green:
    display_name: "§a正在搜索..."
    material: LIME_STAINED_GLASS_PANE
    custom_model_data: 100001  # 使用相同的模型ID保持动画一致

# ===============================================
#           材质包配置示例
# ===============================================

# 如果您使用材质包，需要在以下位置创建模型文件:
# assets/minecraft/models/item/red_stained_glass_pane.json

# 示例 JSON 配置:
{
  "parent": "item/generated",
  "textures": {
    "layer0": "item/red_stained_glass_pane"
  },
  "overrides": [
    {
      "predicate": {
        "custom_model_data": 100001
      },
      "model": "custom/searching_animation"
    }
  ]
}

# 然后创建动画模型文件:
# assets/minecraft/models/custom/searching_animation.json

# ===============================================
#           推荐的 CustomModelData 值
# ===============================================

# 🎯 推荐使用范围:
# 100000 - 199999: HangEvacuation 插件专用
# 200000 - 299999: 其他摸金相关插件
# 300000 - 399999: 服务器自定义物品

# 🚫 避免使用范围:
# 1 - 1000: 常用范围，容易与其他插件冲突
# 1000000+: 过大的数值，可能影响性能

# ===============================================
#           不同动画效果的配置示例
# ===============================================

# 示例 1: 简单旋转动画
items:
  searching-item:
    custom_model_data: 100001  # 旋转动画

# 示例 2: 发光效果
items:
  searching-item:
    custom_model_data: 100002  # 发光动画

# 示例 3: 粒子效果
items:
  searching-item:
    custom_model_data: 100003  # 粒子动画

# 示例 4: 复合效果
items:
  searching-item:
    custom_model_data: 100004  # 旋转+发光+粒子

# ===============================================
#           版本兼容性说明
# ===============================================

# ✅ Minecraft 1.14+: 完全支持 CustomModelData
# ✅ Minecraft 1.8-1.13: 自动忽略 CustomModelData，使用原版外观
# ✅ 所有服务端: Spigot, Paper, Mohist 等都支持

# ===============================================
#           故障排除
# ===============================================

# 问题 1: CustomModelData 不生效
# 解决方案: 
# - 确认服务器版本为 1.14+
# - 检查配置文件语法是否正确
# - 确认 custom_model_data 值大于 0

# 问题 2: 动画不显示
# 解决方案:
# - 确认材质包或 ItemsAdder 配置正确
# - 检查客户端是否加载了正确的材质包
# - 确认模型文件路径正确

# 问题 3: 与其他插件冲突
# 解决方案:
# - 使用不同的 CustomModelData 值范围
# - 检查其他插件的 CustomModelData 使用情况
# - 使用推荐的数值范围 (100000+)

# ===============================================
#           测试配置
# ===============================================

# 用于测试的简单配置:
test_config:
  items:
    searching-item:
      material: STAINED_GLASS_PANE
      data: 14
      name: "§c测试搜索动画"
      lore:
        - "§7这是一个测试配置"
        - "§7用于验证 CustomModelData 功能"
      custom_model_data: 999999  # 测试用的特殊值

# 测试步骤:
# 1. 将 custom_model_data 设置为 999999
# 2. 重载插件配置
# 3. 打开摸金箱开始搜索
# 4. 观察玻璃板是否应用了 CustomModelData
# 5. 如果没有材质包，外观应该与原版相同
# 6. 如果有对应的材质包，应该显示自定义模型

# ===============================================
#           性能优化建议
# ===============================================

# 1. 避免过于复杂的动画模型
# 2. 限制同时显示的动画数量
# 3. 定期清理不使用的 CustomModelData 配置
# 4. 监控服务器性能，确保动画不影响游戏体验

# ===============================================
#           更多资源
# ===============================================

# 📚 相关文档:
# - Minecraft Wiki: CustomModelData
# - ItemsAdder 官方文档
# - 材质包制作教程

# 🎨 推荐工具:
# - Blockbench: 3D模型编辑器
# - ItemsAdder: 自定义物品插件
# - OptiFine: 客户端优化模组

# 💬 技术支持:
# - QQ: hang060217
# - 微信: hang060217
# - QQ群: 361919269
