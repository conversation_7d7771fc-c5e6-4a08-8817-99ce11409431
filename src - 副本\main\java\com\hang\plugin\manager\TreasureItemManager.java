package com.hang.plugin.manager;

import com.hang.plugin.HangPlugin;
import com.hang.plugin.utils.ItemSerializer;
import org.bukkit.Material;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.io.File;
import java.util.*;

/**
 * 摸金箱物品管理器
 *
 * <AUTHOR>
 */
public class TreasureItemManager {

    private final HangPlugin plugin;
    private final File itemsFile;
    private FileConfiguration itemsConfig;
    private final Map<String, TreasureItem> treasureItems;
    private final Random random;
    private ModItemManager modItemManager;

    public TreasureItemManager(HangPlugin plugin) {
        this.plugin = plugin;
        this.itemsFile = new File(plugin.getDataFolder(), "treasure_items.yml");
        this.treasureItems = new HashMap<>();
        this.random = new Random();
        // 模组物品已合并到序列化系统，不再需要单独的ModItemManager
        this.modItemManager = null;

        loadTreasureItems();
    }

    /**
     * 加载摸金箱物品配置
     */
    public void loadTreasureItems() {
        if (!itemsFile.exists()) {
            plugin.saveResource("treasure_items.yml", false);
        }

        try {
            itemsConfig = YamlConfiguration.loadConfiguration(itemsFile);
        } catch (Exception e) {
            plugin.getLogger().severe("配置文件格式错误: " + itemsFile.getName());
            plugin.getLogger().severe("错误详情: " + e.getMessage());
            plugin.getLogger().info("正在创建新的配置文件...");

            // 备份错误的配置文件
            try {
                java.nio.file.Files.copy(itemsFile.toPath(),
                    new java.io.File(itemsFile.getParent(), itemsFile.getName() + ".backup").toPath(),
                    java.nio.file.StandardCopyOption.REPLACE_EXISTING);
                plugin.getLogger().info("已备份错误配置文件为: " + itemsFile.getName() + ".backup");
            } catch (Exception backupError) {
                plugin.getLogger().warning("备份配置文件失败: " + backupError.getMessage());
            }

            // 删除错误的配置文件并重新创建
            itemsFile.delete();
            plugin.saveResource("treasure_items.yml", false);
            itemsConfig = YamlConfiguration.loadConfiguration(itemsFile);
        }

        treasureItems.clear();

        ConfigurationSection itemsSection = itemsConfig.getConfigurationSection("items");
        if (itemsSection != null) {
            for (String key : itemsSection.getKeys(false)) {
                ConfigurationSection itemSection = itemsSection.getConfigurationSection(key);
                if (itemSection != null) {
                    TreasureItem item = loadTreasureItem(key, itemSection);
                    if (item != null) {
                        treasureItems.put(key, item);
                    }
                }
            }
        }

        // 简化的日志输出
        if (treasureItems.isEmpty()) {
            plugin.getLogger().warning("警告：没有加载到任何摸金箱物品！请检查 treasure_items.yml 配置文件");
        }
        // 不在这里输出信息，等待统一显示
    }

    /**
     * 获取兼容的材料类型
     */
    private Material getCompatibleMaterial(String materialName) {
        try {
            // 首先尝试直接获取
            return Material.valueOf(materialName.toUpperCase());
        } catch (IllegalArgumentException e) {
            // 如果失败，尝试兼容性映射
            String upperName = materialName.toUpperCase();

            // 旧版本材料映射到新版本
            switch (upperName) {
                case "INK_SACK":
                    // 根据data值返回不同的材料
                    return Material.valueOf("LAPIS_LAZULI"); // 默认青金石
                case "EXP_BOTTLE":
                    return Material.valueOf("EXPERIENCE_BOTTLE");
                case "SULPHUR":
                    return Material.valueOf("GUNPOWDER");
                case "WOOD":
                    return Material.valueOf("OAK_PLANKS");
                case "LOG":
                    return Material.valueOf("OAK_LOG");
                case "WORKBENCH":
                    return Material.valueOf("CRAFTING_TABLE");
                case "FURNACE":
                    return Material.valueOf("FURNACE");
                default:
                    // 尝试Material.matchMaterial
                    Material matched = Material.matchMaterial(materialName);
                    if (matched != null) {
                        return matched;
                    }

                    plugin.getLogger().warning("未知的材料类型: " + materialName + "，使用STONE代替");
                    return Material.STONE;
            }
        }


    }

    /**
     * 智能概率标准化，避免重复转换和精度误差
     */
    private double normalizeProbability(double configValue) {
        // 如果配置值在0-1之间，说明是小数格式，需要转换为百分比
        if (configValue >= 0.0 && configValue <= 1.0) {
            double result = configValue * 100.0;
            // 修复精度误差：四舍五入到6位小数
            return Math.round(result * 1000000.0) / 1000000.0;
        }
        // 如果配置值在1-100之间，说明已经是百分比格式，直接使用
        else if (configValue > 1.0 && configValue <= 100.0) {
            return configValue;
        }
        // 异常值，使用默认值
        else {
            plugin.getLogger().warning("异常的概率值: " + configValue + "，使用默认值5.0%");
            return 5.0;
        }
    }

    /**
     * 从配置加载单个摸金箱物品（支持序列化）
     */
    private TreasureItem loadTreasureItem(String id, ConfigurationSection section) {
        try {
            // 检查是否有序列化数据
            String serializedData = section.getString("serialized_item");
            if (serializedData != null && !serializedData.trim().isEmpty()) {
                // 加载序列化物品
                ItemStack item = ItemSerializer.deserializeItemStack(serializedData);
                if (item != null) {
                    double probability = section.getDouble("probability", 0.0);
                    // 修复：智能概率转换，避免重复转换和精度误差
                    probability = normalizeProbability(probability);
                    int searchSpeed = section.getInt("search_speed", 3);
                    List<String> commands = section.getStringList("commands");
                    List<String> chestTypes = section.getStringList("chest_types");

                    return new TreasureItem(id, item, probability, searchSpeed, commands, chestTypes);
                } else {
                    plugin.getLogger().warning("物品 " + id + " 序列化数据无效，尝试加载为普通物品");
                }
            }

            // 加载普通物品
            String materialName = section.getString("material");
            if (materialName == null) {
                plugin.getLogger().warning("物品 " + id + " 缺少材质配置");
                return null;
            }

            Material material = getCompatibleMaterial(materialName);

            // 支持范围随机数量
            String amountConfig = section.getString("amount", "1");
            short data = (short) section.getInt("data", 0);
            String name = section.getString("name");
            List<String> lore = section.getStringList("lore");
            double probability = section.getDouble("probability", 0.0);
            // 修复：智能概率转换，避免重复转换和精度误差
            probability = normalizeProbability(probability);
            int searchSpeed = section.getInt("search_speed", 3);
            List<String> commands = section.getStringList("commands");
            List<String> chestTypes = section.getStringList("chest_types");

            // 检查是否使用范围随机数量
            if (amountConfig.contains("-")) {
                // 使用范围随机构造函数
                return new TreasureItem(id, material, amountConfig, data, name, lore, probability, searchSpeed, commands, chestTypes);
            } else {
                // 使用固定数量构造函数
                int amount;
                try {
                    amount = Integer.parseInt(amountConfig);
                } catch (NumberFormatException e) {
                    amount = 1; // 默认值
                }
                return new TreasureItem(id, material, amount, data, name, lore, probability, searchSpeed, commands, chestTypes);
            }

        } catch (Exception e) {
            plugin.getLogger().warning("加载物品 " + id + " 时出错: " + e.getMessage());
            return null;
        }
    }

    /**
     * 根据概率随机选择一个物品（包含模组物品）
     */
    public Object getRandomItem() {
        return getRandomItem(null); // 使用默认的所有物品
    }

    /**
     * 根据摸金箱种类和概率随机选择一个物品
     */
    public Object getRandomItem(String chestType) {
        List<Object> allItems = new ArrayList<>();

        // 根据摸金箱种类获取对应的物品
        if (chestType != null) {
            allItems.addAll(getItemsByChestType(chestType));
        } else {
            // 添加所有普通物品
            allItems.addAll(treasureItems.values());

            // 添加模组物品
            if (modItemManager != null) {
                allItems.addAll(modItemManager.getAllModItems());
            }
        }

        if (allItems.isEmpty()) {
            plugin.getLogger().warning("摸金箱种类 " + chestType + " 没有配置任何物品！");
            return null;
        }

        // 多次尝试选择，直到找到符合概率的物品
        for (int attempt = 0; attempt < 10; attempt++) {
            Object item = allItems.get(random.nextInt(allItems.size()));

            double probability = 0.0;
            if (item instanceof TreasureItem) {
                probability = ((TreasureItem) item).getChance();
            } else if (item instanceof ModItemManager.ModItem) {
                probability = ((ModItemManager.ModItem) item).getProbability() * 100; // 转换为百分比
            }

            if (random.nextDouble() * 100 < probability) {
                return item;
            }
        }

        // 如果10次尝试都没有选中，返回随机一个
        return allItems.get(random.nextInt(allItems.size()));
    }

    /**
     * 根据摸金箱种类获取对应的物品列表
     */
    private List<Object> getItemsByChestType(String chestType) {
        List<Object> items = new ArrayList<>();

        // 调试信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("正在为摸金箱种类 '" + chestType + "' 获取物品");
        }

        // 添加普通物品（根据chest_types配置）
        items.addAll(getItemsByCategory(chestType));

        // 添加模组物品（根据chest_types配置）
        if (modItemManager != null) {
            items.addAll(getModItemsByChestType(chestType));
        }

        // 调试信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("摸金箱种类 '" + chestType + "' 找到 " + items.size() + " 个物品");
        }

        return items;
    }

    /**
     * 根据摸金箱种类获取对应的模组物品列表
     */
    private List<ModItemManager.ModItem> getModItemsByChestType(String chestType) {
        // ModItemManager已禁用，返回空列表
        return new ArrayList<>();
    }

    /**
     * 根据类别获取物品（新版本：使用配置中的摸金箱类型）
     */
    private List<TreasureItem> getItemsByCategory(String category) {
        List<TreasureItem> items = new ArrayList<>();

        for (TreasureItem item : treasureItems.values()) {
            // 检查物品的摸金箱类型配置
            if (item.getChestTypes().contains(category)) {
                items.add(item);
            }
        }

        // 调试信息
        if (plugin.getConfig().getBoolean("debug.enabled", false)) {
            plugin.getLogger().info("类别 '" + category + "' 找到 " + items.size() + " 个物品");
            for (TreasureItem item : items) {
                plugin.getLogger().info("  - " + item.getId() + ": " + item.getMaterial().name() +
                                      " (适用箱子: " + String.join(", ", item.getChestTypes()) + ")");
            }
        }

        // 如果特定类别没有物品，返回一些默认物品
        if (items.isEmpty()) {
            items.addAll(getDefaultItemsForCategory(category));
            if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                plugin.getLogger().info("类别 '" + category + "' 没有专属物品，使用默认物品 " + items.size() + " 个");
            }
        }

        return items;
    }

    /**
     * 判断物品是否属于指定类别
     */
    private boolean isItemInCategory(TreasureItem item, String category) {
        Material material = item.getMaterial();
        String materialName = material.name().toLowerCase();

        switch (category.toLowerCase()) {
            case "weapon":
                return materialName.contains("sword") || materialName.contains("axe") ||
                       materialName.contains("bow") || materialName.contains("crossbow") ||
                       materialName.contains("trident") || materialName.equals("diamond_sword") ||
                       materialName.contains("pickaxe") || materialName.contains("shovel");

            case "ammo":
                return materialName.contains("arrow") || materialName.contains("firework") ||
                       materialName.equals("tnt") || materialName.equals("gunpowder") ||
                       materialName.equals("blaze_rod") || materialName.equals("blaze_powder");

            case "medical":
                return materialName.contains("potion") || materialName.contains("apple") ||
                       materialName.contains("bread") || materialName.contains("stew") ||
                       materialName.equals("experience_bottle") || materialName.contains("milk");

            case "supply":
                return materialName.contains("food") || materialName.contains("bread") ||
                       materialName.equals("coal") || materialName.equals("iron_ingot") ||
                       materialName.equals("redstone") || materialName.contains("log") ||
                       materialName.contains("planks");

            case "equipment":
                return materialName.contains("diamond") || materialName.contains("emerald") ||
                       materialName.contains("gold") || materialName.equals("ender_pearl") ||
                       materialName.contains("enchanted") || item.getChance() <= 5.0; // 稀有物品

            default:
                return true;
        }
    }

    /**
     * 为类别提供默认物品（如果配置中没有对应类别的物品）
     */
    private List<TreasureItem> getDefaultItemsForCategory(String category) {
        List<TreasureItem> defaultItems = new ArrayList<>();

        // 从现有物品中选择一些作为默认物品
        for (TreasureItem item : treasureItems.values()) {
            // 选择一些通用物品作为默认
            if (item.getMaterial() == Material.COAL ||
                item.getMaterial() == Material.IRON_INGOT ||
                item.getMaterial() == Material.REDSTONE) {
                defaultItems.add(item);
            }
        }

        return defaultItems;
    }

    /**
     * 根据概率随机选择一个普通物品（兼容旧方法）
     */
    public TreasureItem getRandomTreasureItem() {
        if (treasureItems.isEmpty()) {
            return null;
        }

        List<TreasureItem> items = new ArrayList<>(treasureItems.values());

        for (int attempt = 0; attempt < 10; attempt++) {
            TreasureItem item = items.get(random.nextInt(items.size()));
            if (random.nextDouble() * 100 < item.getChance()) {
                return item;
            }
        }

        return items.get(random.nextInt(items.size()));
    }

    /**
     * 创建ItemStack（支持模组物品）
     */
    public ItemStack createItemStack(Object item) {
        if (item instanceof TreasureItem) {
            return createItemStack((TreasureItem) item);
        } else if (item instanceof ModItemManager.ModItem) {
            // ModItemManager已禁用，不应该有ModItem类型的物品
            plugin.getLogger().warning("检测到已废弃的ModItem类型物品，请转换为序列化物品");
            return null;
        }
        return null;
    }

    /**
     * 创建物品的ItemStack（支持序列化模组物品）
     * 修复：正确处理序列化的模组物品
     */
    public ItemStack createItemStack(TreasureItem treasureItem) {
        if (treasureItem.getMaterial() == Material.AIR || treasureItem.getAmount() <= 0) {
            return null; // 空物品或数量为0的物品
        }

        try {
            // 重要：如果有序列化数据，优先使用序列化数据恢复物品
            if (treasureItem.getSerializedData() != null && !treasureItem.getSerializedData().trim().isEmpty()) {
                ItemStack deserializedItem = com.hang.plugin.utils.ItemSerializer.deserializeItemStack(treasureItem.getSerializedData());

                if (deserializedItem != null) {
                    // 完成：成功反序列化，返回完整的模组物品
                    if (plugin.getConfig().getBoolean("debug.enabled", false)) {
                        plugin.getLogger().info("完成：成功反序列化物品: " + deserializedItem.getType().name() +
                            " (保持完整NBT数据)");

                        // 验证是否保持了模组特征
                        boolean isStillModItem = com.hang.plugin.utils.ItemSerializer.hasModData(deserializedItem);
                        plugin.getLogger().info("反序列化后模组物品状态: " + isStillModItem);
                    }

                    return deserializedItem;
                } else {
                    // 警告：反序列化失败，降级到传统方式
                    plugin.getLogger().warning("反序列化失败，降级到传统方式创建物品: " + treasureItem.getId());
                }
            }

            // 传统方式创建物品（用于非序列化物品或反序列化失败的情况）
            ItemStack item;

            // 使用随机数量
            int randomAmount = treasureItem.getRandomAmount();

            if (isLegacyVersion()) {
                // 1.12.2 及以下版本，使用数据值
                item = new ItemStack(treasureItem.getMaterial(), randomAmount, treasureItem.getData());
            } else {
                // 1.13+ 版本，不使用数据值
                item = new ItemStack(treasureItem.getMaterial(), randomAmount);
            }

            if (treasureItem.getName() != null || !treasureItem.getLore().isEmpty()) {
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    if (treasureItem.getName() != null) {
                        meta.setDisplayName(treasureItem.getName());
                    }

                    if (!treasureItem.getLore().isEmpty()) {
                        meta.setLore(treasureItem.getLore());
                    }

                    item.setItemMeta(meta);
                }
            }

            return item;

        } catch (Exception e) {
            plugin.getLogger().warning("创建物品失败: " + treasureItem.getMaterial().name() + " - " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 检查是否为旧版本
     */
    private boolean isLegacyVersion() {
        try {
            String version = org.bukkit.Bukkit.getVersion();
            // 简单的版本检查
            return version.contains("1.8") || version.contains("1.9") ||
                   version.contains("1.10") || version.contains("1.11") || version.contains("1.12");
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 给予玩家物品（支持模组物品）
     */
    public void giveItemToPlayer(Object item, Player player) {
        if (item instanceof TreasureItem) {
            TreasureItem treasureItem = (TreasureItem) item;
            ItemStack itemStack = createItemStack(treasureItem);
            if (itemStack != null) {
                player.getInventory().addItem(itemStack);
            }
            executeCommands(treasureItem, player);
        } else if (item instanceof ModItemManager.ModItem) {
            // ModItemManager已禁用，不应该有ModItem类型的物品
            plugin.getLogger().warning("检测到已废弃的ModItem类型物品，无法给予玩家");
        }
    }

    /**
     * 执行物品相关的命令（支持模组物品）
     */
    public void executeCommands(Object item, Player player) {
        if (item instanceof TreasureItem) {
            executeCommands((TreasureItem) item, player);
        } else if (item instanceof ModItemManager.ModItem) {
            // 模组物品的命令在giveModItem中执行
        }
    }

    /**
     * 执行普通物品相关的命令
     */
    public void executeCommands(TreasureItem treasureItem, Player player) {
        if (treasureItem.getCommands().isEmpty()) {
            return;
        }

        for (String command : treasureItem.getCommands()) {
            String processedCommand = command.replace("{player}", player.getName());

            try {
                plugin.getServer().dispatchCommand(plugin.getServer().getConsoleSender(), processedCommand);
            } catch (Exception e) {
                plugin.getLogger().warning("执行命令失败: " + processedCommand + " - " + e.getMessage());
            }
        }
    }

    /**
     * 获取默认箱子槽位数量 (仅用于向下兼容)
     * 优先使用摸金箱种类系统的配置
     */
    public int getChestSlots() {
        return itemsConfig.getInt("chest_settings.slots", 5);
    }

    /**
     * 根据摸金箱种类获取槽位数量
     * 优先级: mojin.yml 中的种类配置 > treasure_items.yml 中的默认配置
     * 支持范围随机槽位
     */
    public int getChestSlots(String chestType) {
        if (chestType != null && plugin.getChestTypeManager() != null) {
            com.hang.plugin.manager.ChestTypeManager.ChestType type =
                plugin.getChestTypeManager().getChestType(chestType);
            if (type != null) {
                return type.getRandomSlots(); // 使用随机槽位功能
            }
        }
        // 降级到默认配置 (向下兼容)
        return getChestSlots();
    }

    public int getSearchCooldown() {
        // 修复：从config.yml读取搜索冷却时间
        return plugin.getConfig().getInt("treasure-chest.search-cooldown", 1);
    }

    public String getChestName() {
        // 已弃用：现在使用摸金箱种类系统的名称
        // 保留此方法用于向下兼容，返回默认名称
        return "§6摸金箱";
    }

    /**
     * 根据摸金箱种类获取名称
     */
    public String getChestName(String chestType) {
        if (chestType != null && plugin.getChestTypeManager() != null) {
            com.hang.plugin.manager.ChestTypeManager.ChestType type =
                plugin.getChestTypeManager().getChestType(chestType);
            if (type != null) {
                return type.getName();
            }
        }
        // 降级到默认名称
        return getChestName();
    }

    public int getRefreshTime() {
        return plugin.getConfig().getInt("treasure-chest.refresh-time", 5);
    }

    /**
     * 根据摸金箱种类获取刷新时间
     */
    public int getRefreshTime(String chestType) {
        if (chestType != null && plugin.getChestTypeManager() != null) {
            com.hang.plugin.manager.ChestTypeManager.ChestType type =
                plugin.getChestTypeManager().getChestType(chestType);
            if (type != null) {
                return type.getRefreshTime();
            }
        }
        // 降级到默认配置
        return getRefreshTime();
    }

    public boolean isHologramEnabled() {
        // 修复：从config.yml读取浮空字启用状态
        return plugin.getConfig().getBoolean("treasure-chest.hologram_enabled", true);
    }

    /**
     * 获取物品搜索速度（支持模组物品）
     */
    public int getItemSearchSpeed(Object item) {
        if (item instanceof TreasureItem) {
            return ((TreasureItem) item).getSearchSpeed();
        } else if (item instanceof ModItemManager.ModItem) {
            // ModItemManager已禁用，返回默认值
            return 3;
        }
        return 3; // 默认搜索速度
    }

    /**
     * 获取物品名称（支持模组物品）
     * 修复：使用统一的友好名称生成逻辑
     */
    public String getItemName(Object item) {
        if (item instanceof TreasureItem) {
            TreasureItem treasureItem = (TreasureItem) item;

            // 策略1：优先使用保存的名称（避免序列化丢失问题）
            if (treasureItem.getName() != null && !treasureItem.getName().equals(treasureItem.getMaterial().name())) {
                // 移除颜色代码并返回
                return treasureItem.getName().replaceAll("§[0-9a-fk-or]", "");
            }

            // 策略2：从实际ItemStack获取友好名称
            try {
                ItemStack actualItem = treasureItem.getItemStack();
                if (actualItem != null) {
                    return getFriendlyItemName(actualItem);
                }
            } catch (Exception e) {
                // 如果获取失败，继续使用其他方法
                plugin.getLogger().warning("从ItemStack获取物品名称失败: " + e.getMessage());
            }

            // 策略3：生成友好的显示名称
            String materialName = treasureItem.getMaterial().name();
            return convertMaterialNameToChinese(materialName);

        } else if (item instanceof ModItemManager.ModItem) {
            // ModItemManager已禁用，返回默认名称
            return "已废弃的模组物品";
        }
        return "未知物品";
    }

    /**
     * 获取友好的物品名称用于显示
     */
    private String getFriendlyItemName(ItemStack itemStack) {
        if (itemStack == null) {
            return "未知物品";
        }

        // 优先使用物品的显示名称
        if (itemStack.hasItemMeta() && itemStack.getItemMeta().hasDisplayName()) {
            String displayName = itemStack.getItemMeta().getDisplayName();
            // 移除颜色代码
            return displayName.replaceAll("§[0-9a-fk-or]", "");
        }

        // 尝试使用NMS获取物品的本地化名称
        try {
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                String nmsName = plugin.getNMSManager().getAdapter().getItemDisplayName(itemStack);
                if (nmsName != null && !nmsName.equals(itemStack.getType().name())) {
                    return nmsName.replaceAll("§[0-9a-fk-or]", "");
                }
            }
        } catch (Exception e) {
            // NMS获取失败，继续使用其他方法
        }

        // 尝试从Lore中提取物品名称
        if (itemStack.hasItemMeta() && itemStack.getItemMeta().hasLore()) {
            List<String> lore = itemStack.getItemMeta().getLore();
            for (int i = 0; i < Math.min(lore.size(), 3); i++) {
                String line = lore.get(i);
                if (line != null) {
                    String cleanLine = line.replaceAll("§[0-9a-fk-or]", "").trim();
                    // 检查是否像物品名称
                    if (cleanLine.length() > 1 && cleanLine.length() < 30 &&
                        !cleanLine.contains(":") && !cleanLine.contains("[") &&
                        !cleanLine.contains("(") && !cleanLine.contains("/") &&
                        !cleanLine.matches(".*\\d+.*") && // 不包含数字
                        cleanLine.matches(".*[\\u4e00-\\u9fa5].*")) { // 包含中文字符
                        return cleanLine;
                    }
                }
            }
        }

        // 最后使用材料名称的中文转换
        return convertMaterialNameToChinese(itemStack.getType().name());
    }

    /**
     * 将材料名称转换为中文
     */
    private String convertMaterialNameToChinese(String materialName) {
        // 特殊处理TACZ物品
        if (materialName.startsWith("TACZ_")) {
            String taczName = materialName.replace("TACZ_", "").replace("_", " ");
            return "TACZ " + formatTaczName(taczName);
        }

        // 常见材料的中文映射
        switch (materialName) {
            case "DIAMOND_SWORD": return "钻石剑";
            case "DIAMOND_PICKAXE": return "钻石镐";
            case "DIAMOND_AXE": return "钻石斧";
            case "DIAMOND_SHOVEL": return "钻石锹";
            case "DIAMOND_HOE": return "钻石锄";
            case "DIAMOND_HELMET": return "钻石头盔";
            case "DIAMOND_CHESTPLATE": return "钻石胸甲";
            case "DIAMOND_LEGGINGS": return "钻石护腿";
            case "DIAMOND_BOOTS": return "钻石靴子";
            case "IRON_SWORD": return "铁剑";
            case "IRON_PICKAXE": return "铁镐";
            case "IRON_AXE": return "铁斧";
            case "IRON_SHOVEL": return "铁锹";
            case "IRON_HOE": return "铁锄";
            case "IRON_HELMET": return "铁头盔";
            case "IRON_CHESTPLATE": return "铁胸甲";
            case "IRON_LEGGINGS": return "铁护腿";
            case "IRON_BOOTS": return "铁靴子";
            case "GOLDEN_SWORD": return "金剑";
            case "GOLDEN_PICKAXE": return "金镐";
            case "GOLDEN_AXE": return "金斧";
            case "GOLDEN_SHOVEL": return "金锹";
            case "GOLDEN_HOE": return "金锄";
            case "GOLDEN_HELMET": return "金头盔";
            case "GOLDEN_CHESTPLATE": return "金胸甲";
            case "GOLDEN_LEGGINGS": return "金护腿";
            case "GOLDEN_BOOTS": return "金靴子";
            case "NETHERITE_SWORD": return "下界合金剑";
            case "NETHERITE_PICKAXE": return "下界合金镐";
            case "NETHERITE_AXE": return "下界合金斧";
            case "NETHERITE_SHOVEL": return "下界合金锹";
            case "NETHERITE_HOE": return "下界合金锄";
            case "NETHERITE_HELMET": return "下界合金头盔";
            case "NETHERITE_CHESTPLATE": return "下界合金胸甲";
            case "NETHERITE_LEGGINGS": return "下界合金护腿";
            case "NETHERITE_BOOTS": return "下界合金靴子";
            case "BOW": return "弓";
            case "CROSSBOW": return "弩";
            case "SHIELD": return "盾牌";
            case "APPLE": return "苹果";
            case "GOLDEN_APPLE": return "金苹果";
            case "BREAD": return "面包";
            case "COOKED_BEEF": return "熟牛肉";
            case "COOKED_PORKCHOP": return "熟猪肉";
            case "ENDER_PEARL": return "末影珍珠";
            case "DIAMOND": return "钻石";
            case "EMERALD": return "绿宝石";
            case "GOLD_INGOT": return "金锭";
            case "IRON_INGOT": return "铁锭";
            case "COAL": return "煤炭";
            case "LAPIS_LAZULI": return "青金石";
            case "REDSTONE": return "红石";
            case "BLAZE_ROD": return "烈焰棒";
            case "EXP_BOTTLE": return "经验瓶";
            case "GLASS_BOTTLE": return "玻璃瓶";
            case "POTION": return "药水";
            case "SPLASH_POTION": return "喷溅药水";
            case "LINGERING_POTION": return "滞留药水";
            case "ENCHANTED_BOOK": return "附魔书";
            case "BOOK": return "书";
            case "PAPER": return "纸";
            case "LEATHER": return "皮革";
            case "STRING": return "线";
            case "FEATHER": return "羽毛";
            case "GUNPOWDER": return "火药";
            case "FLINT": return "燧石";
            case "WHEAT": return "小麦";
            case "CARROT": return "胡萝卜";
            case "POTATO": return "马铃薯";
            case "BEETROOT": return "甜菜根";
            case "SUGAR": return "糖";
            case "EGG": return "鸡蛋";
            case "MILK_BUCKET": return "牛奶桶";
            case "WATER_BUCKET": return "水桶";
            case "LAVA_BUCKET": return "岩浆桶";
            case "BUCKET": return "桶";
            case "SADDLE": return "鞍";
            case "NAME_TAG": return "命名牌";
            case "LEAD": return "拴绳";
            case "COMPASS": return "指南针";
            case "CLOCK": return "时钟";
            case "MAP": return "地图";
            case "SHEARS": return "剪刀";
            case "FISHING_ROD": return "钓鱼竿";
            case "FLINT_AND_STEEL": return "打火石";
            case "TORCH": return "火把";
            case "LADDER": return "梯子";
            case "CHEST": return "箱子";
            case "CRAFTING_TABLE": return "工作台";
            case "FURNACE": return "熔炉";
            case "ANVIL": return "铁砧";
            case "ENCHANTING_TABLE": return "附魔台";
            case "ENDER_CHEST": return "末影箱";
            case "BEACON": return "信标";
            case "TOTEM_OF_UNDYING": return "不死图腾";
            case "ELYTRA": return "鞘翅";
            case "TRIDENT": return "三叉戟";
            case "NAUTILUS_SHELL": return "鹦鹉螺壳";
            case "HEART_OF_THE_SEA": return "海洋之心";
            case "CONDUIT": return "潮涌核心";
            case "PRISMARINE_SHARD": return "海晶碎片";
            case "PRISMARINE_CRYSTALS": return "海晶砂粒";
            case "NETHER_STAR": return "下界之星";
            case "WITHER_SKELETON_SKULL": return "凋灵骷髅头颅";
            case "DRAGON_EGG": return "龙蛋";
            case "DRAGON_HEAD": return "龙首";
            case "END_CRYSTAL": return "末地水晶";
            case "CHORUS_FRUIT": return "紫颂果";
            case "POPPED_CHORUS_FRUIT": return "爆裂紫颂果";
            case "SHULKER_SHELL": return "潜影贝壳";
            case "PHANTOM_MEMBRANE": return "幻翼膜";
            // 添加更多常见物品...
            default:
                // 如果没有映射，返回格式化的英文名称
                return materialName.toLowerCase().replace("_", " ");
        }
    }

    /**
     * 格式化TACZ物品名称
     */
    private String formatTaczName(String taczName) {
        // 将常见的TACZ物品名称转换为更友好的格式
        String formatted = taczName.toLowerCase();

        // 替换常见的词汇
        formatted = formatted.replace("modern", "现代");
        formatted = formatted.replace("kinetic", "动能");
        formatted = formatted.replace("gun", "枪");
        formatted = formatted.replace("rifle", "步枪");
        formatted = formatted.replace("pistol", "手枪");
        formatted = formatted.replace("sniper", "狙击");
        formatted = formatted.replace("assault", "突击");
        formatted = formatted.replace("submachine", "冲锋");
        formatted = formatted.replace("machine", "机");
        formatted = formatted.replace("shotgun", "霰弹枪");
        formatted = formatted.replace("grenade", "手榴弹");
        formatted = formatted.replace("launcher", "发射器");
        formatted = formatted.replace("scope", "瞄准镜");
        formatted = formatted.replace("silencer", "消音器");
        formatted = formatted.replace("magazine", "弹匣");
        formatted = formatted.replace("ammo", "弹药");
        formatted = formatted.replace("bullet", "子弹");

        // 首字母大写
        String[] words = formatted.split(" ");
        StringBuilder result = new StringBuilder();
        for (String word : words) {
            if (word.length() > 0) {
                if (result.length() > 0) {
                    result.append(" ");
                }
                result.append(word.substring(0, 1).toUpperCase()).append(word.substring(1));
            }
        }

        return result.toString();
    }

    /**
     * 获取模组物品管理器（已废弃）
     */
    public ModItemManager getModItemManager() {
        // ModItemManager已禁用，返回null
        return null;
    }

    /**
     * 重载配置
     */
    public void reload() {
        loadTreasureItems();
        // ModItemManager已禁用，不需要重载
    }

    /**
     * 获取所有物品列表
     */
    public List<TreasureItem> getAllItems() {
        return new ArrayList<>(treasureItems.values());
    }

    /**
     * 获取摸金箱物品数量
     */
    public int getTreasureItemCount() {
        return treasureItems.size();
    }

    /**
     * 添加物品
     */
    public void addItem(TreasureItem item) {
        treasureItems.put(item.getId(), item);
    }

    /**
     * 移除物品
     */
    public void removeItem(String id) {
        treasureItems.remove(id);
    }

    /**
     * 更新物品
     */
    public void updateItem(TreasureItem item) {
        treasureItems.put(item.getId(), item);
    }

    /**
     * 从ItemStack创建TreasureItem（支持序列化）
     */
    public TreasureItem createFromItemStack(String id, ItemStack itemStack) {
        return createFromItemStackWithChestType(id, itemStack, "common");
    }

    /**
     * 从ItemStack创建TreasureItem，指定摸金箱种类
     * 修复：直接从原始ItemStack提取真实显示名称
     */
    public TreasureItem createFromItemStackWithChestType(String id, ItemStack itemStack, String chestType) {
        try {
            plugin.getLogger().info("[调试] 开始创建TreasureItem，ID: " + id + ", 摸金箱种类: " + chestType);

            if (itemStack == null) {
                plugin.getLogger().warning("[调试] ItemStack为null，无法创建TreasureItem");
                return null;
            }

            plugin.getLogger().info("[调试] 物品类型: " + itemStack.getType().name() + ", 数量: " + itemStack.getAmount());

            // 关键修复：在序列化之前先提取真实的物品名称
            String realItemName = extractRealItemName(itemStack);
            plugin.getLogger().info("[调试] 提取到的真实物品名称: " + realItemName);

            // 获取默认概率和搜索速度
            double defaultProbability = plugin.getConfig().getDouble("treasure-chest.default_probability", 10.0);
            int defaultSearchSpeed = plugin.getConfig().getInt("treasure-chest.default_search_speed", 3);

            // 创建TreasureItem，使用配置的默认值
            TreasureItem result = new TreasureItem(id, itemStack, realItemName, defaultProbability, defaultSearchSpeed, new ArrayList<>(), Arrays.asList(chestType));
            plugin.getLogger().info("[调试] TreasureItem创建成功");

            return result;

        } catch (Exception e) {
            plugin.getLogger().severe("[调试] 创建TreasureItem时发生异常: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 从原始ItemStack提取真实的显示名称（支持模组物品）
     */
    private String extractRealItemName(ItemStack itemStack) {
        if (itemStack == null) {
            return null;
        }

        // 方法1：尝试使用NMS获取真实名称
        try {
            if (plugin.getNMSManager() != null && plugin.getNMSManager().isInitialized()) {
                String nmsName = plugin.getNMSManager().getAdapter().getItemDisplayName(itemStack);
                if (nmsName != null && !nmsName.equals(itemStack.getType().name())) {
                    plugin.getLogger().info("[调试] NMS提取名称: " + nmsName);
                    return nmsName;
                }
            }
        } catch (Exception e) {
            plugin.getLogger().info("[调试] NMS提取失败: " + e.getMessage());
        }

        // 方法2：从ItemMeta获取显示名称
        if (itemStack.hasItemMeta()) {
            ItemMeta meta = itemStack.getItemMeta();
            if (meta.hasDisplayName()) {
                String displayName = meta.getDisplayName();
                plugin.getLogger().info("[调试] ItemMeta提取名称: " + displayName);
                return displayName;
            }

            // 方法3：从Lore中提取中文名称（TACZ等模组物品）
            if (meta.hasLore() && !meta.getLore().isEmpty()) {
                List<String> lore = meta.getLore();
                plugin.getLogger().info("[调试] 开始从Lore提取名称，Lore行数: " + lore.size());

                for (int i = 0; i < Math.min(lore.size(), 5); i++) {
                    String line = lore.get(i);
                    if (line != null) {
                        // 移除颜色代码
                        String cleanLine = line.replaceAll("§[0-9a-fk-or]", "").trim();
                        plugin.getLogger().info("调试：Lore[" + i + "] 清理后: " + cleanLine);

                        // 检查是否包含中文字符且像物品名称
                        if (cleanLine.length() > 1 && cleanLine.length() < 30 &&
                            !cleanLine.contains(":") && !cleanLine.contains("[") &&
                            !cleanLine.contains("(") && !cleanLine.contains("/") &&
                            !cleanLine.matches(".*\\d+.*") && // 不包含数字
                            cleanLine.matches(".*[\\u4e00-\\u9fa5].*")) { // 包含中文字符
                            plugin.getLogger().info("调试：从Lore提取到中文名称: " + cleanLine);
                            return cleanLine;
                        }
                    }
                }
            }
        }

        // 方法4：为模组物品生成友好名称
        String materialName = itemStack.getType().name();
        if (materialName.startsWith("TACZ_")) {
            String friendlyName = "TACZ " + materialName.replace("TACZ_", "").replace("_", " ").toLowerCase();
            return friendlyName;
        }

        return materialName;
    }

    /**
     * 检查物品是否有复杂数据（需要序列化）
     * 修复：正确识别有附魔的物品需要序列化保存
     */
    private boolean hasComplexData(ItemStack item) {
        if (item == null) return false;

        // 首先检查是否为模组物品，模组物品总是需要序列化
        if (ItemSerializer.hasModData(item)) {
            return true;
        }

        // 修复：检查是否有附魔，附魔物品需要序列化保存
        if (item.getEnchantments() != null && !item.getEnchantments().isEmpty()) {
            return true;
        }

        // 对于原版物品，检查是否有其他复杂的NBT数据
        if (item.hasItemMeta()) {
            ItemMeta meta = item.getItemMeta();

            // 检查是否有需要序列化保存的特殊数据
            try {
                // 使用反射检查是否有自定义NBT数据（1.14+的PersistentDataContainer）
                if (hasCustomNBTData(meta)) {
                    return true;
                }

                // 检查是否有特殊的属性修饰符
                if (hasAttributeModifiers(meta)) {
                    return true;
                }

                // 检查是否有隐藏标志等特殊设置
                if (hasSpecialFlags(meta)) {
                    return true;
                }

                // 新增：检查是否有其他复杂的元数据
                if (hasOtherComplexMeta(meta)) {
                    return true;
                }

            } catch (Exception e) {
                // 如果检查失败，保守起见认为需要序列化
                return true;
            }
        }

        // 只有最基础的物品（无附魔、无特殊属性）才使用传统方式保存
        return false;
    }

    /**
     * 检查是否有自定义NBT数据
     */
    private boolean hasCustomNBTData(ItemMeta meta) {
        try {
            // 1.14+版本检查PersistentDataContainer
            Object container = meta.getClass().getMethod("getPersistentDataContainer").invoke(meta);
            boolean isEmpty = (Boolean) container.getClass().getMethod("isEmpty").invoke(container);
            return !isEmpty;
        } catch (Exception e) {
            // 1.14以下版本或检查失败，返回false
            return false;
        }
    }

    /**
     * 检查是否有属性修饰符
     */
    private boolean hasAttributeModifiers(ItemMeta meta) {
        try {
            Object modifiers = meta.getClass().getMethod("getAttributeModifiers").invoke(meta);
            if (modifiers != null) {
                boolean isEmpty = (Boolean) modifiers.getClass().getMethod("isEmpty").invoke(modifiers);
                return !isEmpty;
            }
        } catch (Exception e) {
            // 检查失败，返回false
        }
        return false;
    }

    /**
     * 检查是否有特殊标志
     */
    private boolean hasSpecialFlags(ItemMeta meta) {
        try {
            // 检查ItemFlags（隐藏附魔、属性等）
            Object flags = meta.getClass().getMethod("getItemFlags").invoke(meta);
            if (flags != null) {
                boolean isEmpty = (Boolean) flags.getClass().getMethod("isEmpty").invoke(flags);
                return !isEmpty;
            }
        } catch (Exception e) {
            // 检查失败，返回false
        }
        return false;
    }

    /**
     * 检查是否有其他复杂的元数据
     */
    private boolean hasOtherComplexMeta(ItemMeta meta) {
        try {
            // 检查是否有自定义模型数据 (CustomModelData)
            if (meta.getClass().getMethod("hasCustomModelData").invoke(meta).equals(true)) {
                return true;
            }
        } catch (Exception e) {
            // 1.14以下版本没有CustomModelData，忽略
        }

        try {
            // 检查是否有本地化名称
            if (meta.getClass().getMethod("hasLocalizedName").invoke(meta).equals(true)) {
                return true;
            }
        } catch (Exception e) {
            // 某些版本可能没有此方法，忽略
        }

        // 可以根据需要添加更多检查
        return false;
    }

    /**
     * 重载配置
     */
    public void reloadConfig() {
        plugin.reloadConfig();
        loadTreasureItems();
    }

    /**
     * 保存配置
     */
    public void saveConfig() {
        try {
            // 获取配置文件
            File configFile = new File(plugin.getDataFolder(), "treasure_items.yml");
            org.bukkit.configuration.file.YamlConfiguration config = new org.bukkit.configuration.file.YamlConfiguration();

            // 清空现有配置
            config.set("items", null);

            // 保存所有物品
            for (TreasureItem item : treasureItems.values()) {
                String path = "items." + item.getId();

                // 修复：如果有序列化数据，优先保存序列化数据（不仅限于模组物品）
                if (item.getSerializedData() != null) {
                    config.set(path + ".serialized_item", item.getSerializedData());
                    config.set(path + ".description", item.getDescription());
                    // 标记为序列化物品
                    config.set(path + ".is_serialized", true);
                } else {
                    // 普通物品，保存传统格式
                    config.set(path + ".material", item.getMaterial().name());
                    config.set(path + ".amount", item.getAmount());

                    // 数据值（仅在非0时保存）
                    if (item.getData() != 0) {
                        config.set(path + ".data", item.getData());
                    }

                    // 自定义名称（仅在非空时保存）
                    if (item.getName() != null && !item.getName().isEmpty()) {
                        config.set(path + ".name", item.getName());
                    }

                    // 物品描述（仅在非空时保存）
                    if (item.getLore() != null && !item.getLore().isEmpty()) {
                        config.set(path + ".lore", item.getLore());
                    }

                    // 标记为传统物品
                    config.set(path + ".is_serialized", false);
                }

                // 通用属性
                // 修复：智能概率保存，避免精度误差
                double probabilityToSave = item.getChance() / 100.0;
                // 四舍五入到6位小数，避免浮点数精度问题
                probabilityToSave = Math.round(probabilityToSave * 1000000.0) / 1000000.0;
                config.set(path + ".probability", probabilityToSave);
                config.set(path + ".search_speed", item.getSearchSpeed());

                // 命令（仅在非空时保存）
                if (item.getCommands() != null && !item.getCommands().isEmpty()) {
                    config.set(path + ".commands", item.getCommands());
                }

                // 标记物品类型
                config.set(path + ".is_mod_item", item.isModItem());

                // 保存摸金箱种类信息
                if (item.getChestTypes() != null && !item.getChestTypes().isEmpty()) {
                    config.set(path + ".chest_types", item.getChestTypes());
                }
            }

            // 保存到文件
            config.save(configFile);
            plugin.getLogger().info("已保存 " + treasureItems.size() + " 个战利品配置到 treasure_items.yml");

        } catch (Exception e) {
            plugin.getLogger().severe("保存配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }



    /**
     * 摸金箱物品数据类
     * 支持序列化存储和模组物品
     */
    public static class TreasureItem {
        private final String id;
        private final Material material;
        private int amount;
        private final short data;
        private final String name;
        private final List<String> lore;
        private double chance; // 改名为chance，并可修改
        private int searchSpeed; // 搜索速度（秒），物品越贵重搜索越慢
        private final List<String> commands;

        // 新增：序列化支持
        private final String serializedData; // 完整的ItemStack序列化数据
        private final boolean isModItem; // 是否为模组物品

        // 新增：摸金箱类型支持
        private final List<String> chestTypes; // 适用的摸金箱种类

        // 新增：范围随机数量支持
        private final String amountRange; // 数量范围字符串，如 "1-5"
        private final boolean hasAmountRange; // 是否使用范围随机

        public TreasureItem(String id, Material material, int amount, short data,
                           String name, List<String> lore, double chance, List<String> commands) {
            this.id = id;
            this.material = material;
            this.amount = amount;
            this.data = data;
            this.name = name;
            this.lore = lore != null ? lore : new ArrayList<>();
            this.chance = chance;
            this.searchSpeed = 3; // 默认搜索速度3秒
            this.commands = commands != null ? commands : new ArrayList<>();
            this.serializedData = null; // 普通物品不需要序列化
            this.isModItem = false;
            this.chestTypes = Arrays.asList("common"); // 默认只在普通摸金箱中出现
            this.amountRange = null; // 不使用范围随机
            this.hasAmountRange = false;
        }

        // 新的构造函数，支持搜索速度
        public TreasureItem(String id, Material material, int amount, short data,
                           String name, List<String> lore, double chance, int searchSpeed, List<String> commands) {
            this.id = id;
            this.material = material;
            this.amount = amount;
            this.data = data;
            this.name = name;
            this.lore = lore != null ? lore : new ArrayList<>();
            this.chance = chance;
            this.searchSpeed = searchSpeed;
            this.commands = commands != null ? commands : new ArrayList<>();
            this.serializedData = null; // 普通物品不需要序列化
            this.isModItem = false;
            this.chestTypes = Arrays.asList("common"); // 默认只在普通摸金箱中出现
            this.amountRange = null; // 不使用范围随机
            this.hasAmountRange = false;
        }

        // 新的构造函数，支持摸金箱类型
        public TreasureItem(String id, Material material, int amount, short data,
                           String name, List<String> lore, double chance, int searchSpeed,
                           List<String> commands, List<String> chestTypes) {
            this.id = id;
            this.material = material;
            this.amount = amount;
            this.data = data;
            this.name = name;
            this.lore = lore != null ? lore : new ArrayList<>();
            this.chance = chance;
            this.searchSpeed = searchSpeed;
            this.commands = commands != null ? commands : new ArrayList<>();
            this.serializedData = null; // 普通物品不需要序列化
            this.isModItem = false;
            this.chestTypes = chestTypes != null && !chestTypes.isEmpty() ?
                             new ArrayList<>(chestTypes) : Arrays.asList("common");
            this.amountRange = null; // 不使用范围随机
            this.hasAmountRange = false;
        }

        // 新的构造函数，支持范围随机数量
        public TreasureItem(String id, Material material, String amountRange, short data,
                           String name, List<String> lore, double chance, int searchSpeed,
                           List<String> commands, List<String> chestTypes) {
            this.id = id;
            this.material = material;
            this.data = data;
            this.name = name;
            this.lore = lore != null ? lore : new ArrayList<>();
            this.chance = chance;
            this.searchSpeed = searchSpeed;
            this.commands = commands != null ? commands : new ArrayList<>();
            this.serializedData = null; // 普通物品不需要序列化
            this.isModItem = false;
            this.chestTypes = chestTypes != null && !chestTypes.isEmpty() ?
                             new ArrayList<>(chestTypes) : Arrays.asList("common");

            // 处理范围随机数量
            if (amountRange != null && amountRange.contains("-")) {
                this.amountRange = amountRange;
                this.hasAmountRange = true;
                // 设置默认数量为范围的最小值
                String[] parts = amountRange.split("-");
                try {
                    this.amount = Integer.parseInt(parts[0].trim());
                } catch (NumberFormatException e) {
                    this.amount = 1; // 默认值
                }
            } else {
                this.amountRange = null;
                this.hasAmountRange = false;
                // 如果amountRange实际上是一个数字，使用它作为固定数量
                try {
                    this.amount = Integer.parseInt(amountRange);
                } catch (NumberFormatException e) {
                    this.amount = 1; // 默认值
                }
            }
        }

        // 新的构造函数，支持序列化物品（模组物品）
        public TreasureItem(String id, ItemStack itemStack, double chance, int searchSpeed, List<String> commands) {
            this.id = id;
            this.serializedData = ItemSerializer.serializeItemStack(itemStack);
            this.isModItem = ItemSerializer.hasModData(itemStack);

            // 从ItemStack提取基本信息
            this.material = itemStack.getType();
            this.amount = itemStack.getAmount();
            this.data = itemStack.getDurability();

            if (itemStack.hasItemMeta()) {
                ItemMeta meta = itemStack.getItemMeta();
                this.name = meta.hasDisplayName() ? meta.getDisplayName() : null;
                this.lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
            } else {
                this.name = null;
                this.lore = new ArrayList<>();
            }

            this.chance = chance;
            this.searchSpeed = searchSpeed;
            this.commands = commands != null ? commands : new ArrayList<>();
            this.chestTypes = Arrays.asList("common"); // 默认只在普通摸金箱中出现
            this.amountRange = null; // 序列化物品不使用范围随机
            this.hasAmountRange = false;
        }

        // 新的构造函数，支持预提取的物品名称
        public TreasureItem(String id, ItemStack itemStack, String preExtractedName, double chance, int searchSpeed, List<String> commands, List<String> chestTypes) {
            this.id = id;
            this.serializedData = ItemSerializer.serializeItemStack(itemStack);
            this.isModItem = ItemSerializer.hasModData(itemStack);

            // 从ItemStack提取基本信息
            this.material = itemStack.getType();
            this.amount = itemStack.getAmount();
            this.data = itemStack.getDurability();

            // 关键修复：直接使用预提取的真实名称
            this.name = preExtractedName;

            if (itemStack.hasItemMeta()) {
                ItemMeta meta = itemStack.getItemMeta();
                this.lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
            } else {
                this.lore = new ArrayList<>();
            }

            this.chance = chance;
            this.searchSpeed = searchSpeed;
            this.commands = commands != null ? commands : new ArrayList<>();
            this.chestTypes = chestTypes != null ? chestTypes : Arrays.asList("common");
            this.amountRange = null; // 序列化物品不使用范围随机
            this.hasAmountRange = false;
        }

        // 原有构造函数，支持序列化物品和摸金箱种类
        public TreasureItem(String id, ItemStack itemStack, double chance, int searchSpeed, List<String> commands, List<String> chestTypes) {
            this.id = id;
            this.serializedData = ItemSerializer.serializeItemStack(itemStack);
            this.isModItem = ItemSerializer.hasModData(itemStack);

            // 简化调试信息（静态内部类无法访问外部实例）
            // 调试信息将在外部方法中输出

            // 从ItemStack提取基本信息
            this.material = itemStack.getType();
            this.amount = itemStack.getAmount();
            this.data = itemStack.getDurability();

            // 修复：智能获取物品名称（支持模组物品）
            String extractedName = null;

            if (itemStack.hasItemMeta()) {
                ItemMeta meta = itemStack.getItemMeta();

                // 优先使用标准的显示名称
                if (meta.hasDisplayName()) {
                    extractedName = meta.getDisplayName();
                } else if (meta.hasLore() && !meta.getLore().isEmpty()) {
                    // 尝试从Lore中提取中文名称（TACZ物品的中文名称通常在Lore中）
                    List<String> lore = meta.getLore();

                    // 遍历Lore寻找可能的物品名称
                    for (int i = 0; i < Math.min(lore.size(), 5); i++) { // 只检查前5行
                        String line = lore.get(i);
                        if (line != null) {
                            // 移除颜色代码
                            String cleanLine = line.replaceAll("§[0-9a-fk-or]", "").trim();

                            // 检查是否像物品名称（包含中文字符，不包含数值、冒号等）
                            if (cleanLine.length() > 2 && cleanLine.length() < 30 &&
                                !cleanLine.contains(":") && !cleanLine.contains("[") &&
                                !cleanLine.contains("(") && !cleanLine.contains("/") &&
                                !cleanLine.matches(".*\\d+.*") && // 不包含数字
                                cleanLine.matches(".*[\\u4e00-\\u9fa5].*")) { // 包含中文字符
                                extractedName = cleanLine;
                                break; // 找到第一个符合条件的就停止
                            }
                        }
                    }

                    // 如果没找到中文名称，尝试英文名称
                    if (extractedName == null) {
                        String firstLine = lore.get(0);
                        if (firstLine != null && !firstLine.contains(":") &&
                            !firstLine.contains("[") && !firstLine.contains("§7") &&
                            firstLine.length() > 2 && firstLine.length() < 30) {
                            extractedName = firstLine.replaceAll("§[0-9a-fk-or]", "").trim();
                        }
                    }
                }

                this.lore = meta.hasLore() ? new ArrayList<>(meta.getLore()) : new ArrayList<>();
            } else {
                this.lore = new ArrayList<>();
            }

            // 如果还是没有名称，为模组物品生成友好名称
            if (extractedName == null && this.isModItem) {
                String materialName = itemStack.getType().name();
                if (materialName.startsWith("TACZ_")) {
                    extractedName = "TACZ " + materialName.replace("TACZ_", "").replace("_", " ").toLowerCase();
                }
            }

            this.name = extractedName;

            this.chance = chance;
            this.searchSpeed = searchSpeed;
            this.commands = commands != null ? commands : new ArrayList<>();
            this.chestTypes = chestTypes != null && !chestTypes.isEmpty() ?
                             new ArrayList<>(chestTypes) : Arrays.asList("common");
            this.amountRange = null; // 序列化物品不使用范围随机
            this.hasAmountRange = false;
        }

        // Getters
        public String getId() { return id; }
        public Material getMaterial() { return material; }
        public int getAmount() { return amount; }
        public short getData() { return data; }
        public String getName() { return name; }

        /**
         * 获取随机数量
         * 如果配置了范围随机，返回范围内的随机数量
         * 否则返回固定数量
         */
        public int getRandomAmount() {
            if (hasAmountRange && amountRange != null) {
                try {
                    String[] parts = amountRange.split("-");
                    if (parts.length == 2) {
                        int min = Integer.parseInt(parts[0].trim());
                        int max = Integer.parseInt(parts[1].trim());
                        if (min <= max) {
                            return min + new java.util.Random().nextInt(max - min + 1);
                        }
                    }
                } catch (NumberFormatException e) {
                    // 解析失败，返回固定数量
                }
            }
            return amount;
        }

        /**
         * 检查是否使用范围随机数量
         */
        public boolean hasAmountRange() {
            return hasAmountRange;
        }

        /**
         * 获取数量范围字符串
         */
        public String getAmountRange() {
            return amountRange;
        }
        public List<String> getLore() { return lore; }
        public double getChance() { return chance; }
        public double getProbability() { return chance; } // 兼容旧方法
        public int getSearchSpeed() { return searchSpeed; }
        public int getWeight() { return searchSpeed; } // 兼容旧方法，返回搜索速度
        public List<String> getCommands() { return commands; }
        public String getSerializedData() { return serializedData; }
        public boolean isModItem() { return isModItem; }
        public List<String> getChestTypes() { return new ArrayList<>(chestTypes); }

        // Setters for GUI editing
        /**
         * 设置命令列表（用于GUI编辑）
         */
        public void setCommands(List<String> newCommands) {
            this.commands.clear();
            if (newCommands != null) {
                this.commands.addAll(newCommands);
            }
        }
        public void setAmount(int amount) { this.amount = Math.max(1, Math.min(64, amount)); }
        public void setChance(double chance) { this.chance = Math.max(0, Math.min(100, chance)); }
        public void setSearchSpeed(int searchSpeed) { this.searchSpeed = Math.max(1, Math.min(30, searchSpeed)); }
        public void setWeight(int weight) { this.searchSpeed = Math.max(1, Math.min(30, weight)); } // 兼容旧方法

        /**
         * 获取真实的ItemStack
         * 如果是序列化物品，返回反序列化的ItemStack
         * 否则创建普通的ItemStack
         */
        public ItemStack getItemStack() {
            if (serializedData != null) {
                // 序列化物品，直接反序列化
                ItemStack item = ItemSerializer.deserializeItemStack(serializedData);
                if (item != null) {
                    // 对于序列化物品，如果有范围随机，更新数量
                    if (hasAmountRange) {
                        item.setAmount(getRandomAmount());
                    }
                    return item;
                }
            }

            // 普通物品，创建ItemStack，使用随机数量
            ItemStack item = new ItemStack(material, getRandomAmount(), data);

            if (name != null || !lore.isEmpty()) {
                ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    if (name != null) {
                        meta.setDisplayName(name);
                    }
                    if (!lore.isEmpty()) {
                        meta.setLore(lore);
                    }
                    item.setItemMeta(meta);
                }
            }

            return item;
        }

        /**
         * 获取物品的克隆副本
         */
        public ItemStack getClonedItemStack() {
            ItemStack original = getItemStack();
            return original != null ? original.clone() : null;
        }

        /**
         * 获取物品描述
         */
        public String getDescription() {
            ItemStack item = getItemStack();
            return item != null ? ItemSerializer.getItemDescription(item) : "未知物品";
        }
    }
}
