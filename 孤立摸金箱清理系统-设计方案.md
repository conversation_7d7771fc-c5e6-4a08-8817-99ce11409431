# 🧹 孤立摸金箱清理系统 - 设计方案

## 🎯 **问题分析**

当玩家升级插件并删除配置文件时，会出现以下问题：

### 🔴 **严重问题**
1. **孤立的摸金箱方块**: 世界中的摸金箱方块失去了数据支持
2. **孤立的浮空字**: ArmorStand实体仍然存在但没有对应的摸金箱数据
3. **无法交互**: 玩家无法正常使用这些摸金箱
4. **性能影响**: 浮空字更新任务会因为找不到数据而报错

### 🟡 **中等问题**
1. **视觉混乱**: 玩家看到摸金箱但无法使用
2. **数据不一致**: 内存中的数据与世界中的实体不匹配
3. **错误日志**: 大量的错误日志输出

## 🔧 **解决方案设计**

### 📋 **方案1: 智能检测和清理系统**

#### **1.1 启动时扫描**
- 插件启动时扫描所有已加载的区块
- 检测孤立的摸金箱方块和浮空字
- 提供清理选项

#### **1.2 区块加载时检测**
- 监听区块加载事件
- 检测新加载区块中的孤立实体
- 自动清理或提示管理员

#### **1.3 定期清理任务**
- 定期扫描在线玩家附近的区块
- 清理孤立的浮空字实体
- 记录清理日志

### 📋 **方案2: 数据恢复系统**

#### **2.1 自动重建数据**
- 检测到孤立摸金箱时自动创建默认数据
- 使用默认配置重新初始化摸金箱
- 保持摸金箱的可用性

#### **2.2 备份恢复**
- 自动备份重要的摸金箱数据
- 提供数据恢复命令
- 支持从备份恢复摸金箱状态

### 📋 **方案3: 混合解决方案（推荐）**

结合方案1和方案2的优点：
1. **智能检测**: 自动发现孤立的摸金箱和浮空字
2. **选择性处理**: 根据配置决定清理还是恢复
3. **用户友好**: 提供管理员命令进行手动处理
4. **性能优化**: 避免影响服务器性能

## 🛠️ **技术实现**

### **1. 孤立实体检测器**

```java
public class OrphanedChestDetector {
    
    /**
     * 检测孤立的摸金箱方块
     */
    public List<Location> detectOrphanedChests(World world) {
        List<Location> orphanedChests = new ArrayList<>();
        
        for (Chunk chunk : world.getLoadedChunks()) {
            for (BlockState blockState : chunk.getTileEntities()) {
                if (isPotentialChest(blockState.getBlock())) {
                    Location location = blockState.getLocation();
                    
                    // 检查是否有对应的摸金箱数据
                    if (!hasChestData(location) && hasHologram(location)) {
                        orphanedChests.add(location);
                    }
                }
            }
        }
        
        return orphanedChests;
    }
    
    /**
     * 检测孤立的浮空字
     */
    public List<ArmorStand> detectOrphanedHolograms(World world) {
        List<ArmorStand> orphanedHolograms = new ArrayList<>();
        
        for (Entity entity : world.getEntities()) {
            if (entity instanceof ArmorStand) {
                ArmorStand armorStand = (ArmorStand) entity;
                
                // 检查是否是摸金箱浮空字
                if (isChestHologram(armorStand)) {
                    Location chestLocation = getChestLocationFromHologram(armorStand);
                    
                    // 检查对应的摸金箱是否存在数据
                    if (!hasChestData(chestLocation)) {
                        orphanedHolograms.add(armorStand);
                    }
                }
            }
        }
        
        return orphanedHolograms;
    }
}
```

### **2. 清理管理器**

```java
public class OrphanedChestCleaner {
    
    /**
     * 清理孤立的摸金箱和浮空字
     */
    public CleanupResult cleanupOrphanedChests(World world, CleanupMode mode) {
        CleanupResult result = new CleanupResult();
        
        // 检测孤立实体
        List<Location> orphanedChests = detector.detectOrphanedChests(world);
        List<ArmorStand> orphanedHolograms = detector.detectOrphanedHolograms(world);
        
        switch (mode) {
            case REMOVE_ALL:
                // 移除所有孤立的摸金箱和浮空字
                result.removedChests = removeOrphanedChests(orphanedChests);
                result.removedHolograms = removeOrphanedHolograms(orphanedHolograms);
                break;
                
            case RESTORE_DATA:
                // 为孤立的摸金箱重建数据
                result.restoredChests = restoreChestData(orphanedChests);
                result.removedHolograms = removeOrphanedHolograms(orphanedHolograms);
                break;
                
            case INTERACTIVE:
                // 交互式处理，让管理员选择
                result = handleInteractiveCleanup(orphanedChests, orphanedHolograms);
                break;
        }
        
        return result;
    }
    
    /**
     * 恢复摸金箱数据
     */
    private int restoreChestData(List<Location> orphanedChests) {
        int restored = 0;
        
        for (Location location : orphanedChests) {
            // 创建默认的摸金箱数据
            TreasureChestData data = new TreasureChestData("common");
            
            // 保存到内存和文件
            plugin.getPlayerListener().saveTreasureChestData(location, data);
            plugin.getChestManager().saveChestData(location, data);
            
            restored++;
            plugin.getLogger().info("已恢复摸金箱数据: " + locationToString(location));
        }
        
        return restored;
    }
}
```

### **3. 启动时检测**

```java
public class StartupChestValidator {
    
    /**
     * 插件启动时验证摸金箱完整性
     */
    public void validateChestsOnStartup() {
        plugin.getLogger().info("正在检测孤立的摸金箱和浮空字...");
        
        int totalOrphanedChests = 0;
        int totalOrphanedHolograms = 0;
        
        for (World world : plugin.getServer().getWorlds()) {
            List<Location> orphanedChests = detector.detectOrphanedChests(world);
            List<ArmorStand> orphanedHolograms = detector.detectOrphanedHolograms(world);
            
            totalOrphanedChests += orphanedChests.size();
            totalOrphanedHolograms += orphanedHolograms.size();
            
            if (!orphanedChests.isEmpty() || !orphanedHolograms.isEmpty()) {
                plugin.getLogger().warning("在世界 " + world.getName() + " 中发现:");
                plugin.getLogger().warning("  - " + orphanedChests.size() + " 个孤立的摸金箱");
                plugin.getLogger().warning("  - " + orphanedHolograms.size() + " 个孤立的浮空字");
            }
        }
        
        if (totalOrphanedChests > 0 || totalOrphanedHolograms > 0) {
            plugin.getLogger().warning("发现 " + totalOrphanedChests + " 个孤立摸金箱和 " + 
                                     totalOrphanedHolograms + " 个孤立浮空字");
            plugin.getLogger().warning("使用命令 /evac cleanup 进行清理");
            
            // 根据配置自动处理
            handleAutoCleanup(totalOrphanedChests, totalOrphanedHolograms);
        } else {
            plugin.getLogger().info("未发现孤立的摸金箱或浮空字");
        }
    }
    
    /**
     * 自动清理处理
     */
    private void handleAutoCleanup(int orphanedChests, int orphanedHolograms) {
        String autoCleanupMode = plugin.getConfig().getString("auto_cleanup.mode", "warn");
        
        switch (autoCleanupMode.toLowerCase()) {
            case "remove":
                // 自动移除所有孤立实体
                plugin.getLogger().info("自动清理模式：移除所有孤立实体");
                cleanupAllWorlds(CleanupMode.REMOVE_ALL);
                break;
                
            case "restore":
                // 自动恢复摸金箱数据
                plugin.getLogger().info("自动清理模式：恢复摸金箱数据");
                cleanupAllWorlds(CleanupMode.RESTORE_DATA);
                break;
                
            case "warn":
            default:
                // 仅警告，不自动处理
                plugin.getLogger().info("自动清理模式：仅警告");
                break;
        }
    }
}
```

### **4. 管理员命令**

```java
// 在HangCommand中添加清理命令
case "cleanup":
    if (!sender.hasPermission("evacuation.admin")) {
        sender.sendMessage("§c你没有权限使用此命令！");
        return true;
    }
    
    if (args.length < 2) {
        sender.sendMessage("§e用法: /evac cleanup <scan|remove|restore|auto>");
        return true;
    }
    
    String action = args[1].toLowerCase();
    switch (action) {
        case "scan":
            // 扫描并显示孤立实体
            performCleanupScan(sender);
            break;
            
        case "remove":
            // 移除所有孤立实体
            performCleanupRemove(sender);
            break;
            
        case "restore":
            // 恢复摸金箱数据
            performCleanupRestore(sender);
            break;
            
        case "auto":
            // 自动处理
            performAutoCleanup(sender);
            break;
            
        default:
            sender.sendMessage("§c无效的清理操作！");
            break;
    }
    break;
```

## ⚙️ **配置选项**

```yaml
# config.yml 中添加清理配置
auto_cleanup:
  # 自动清理模式: warn(仅警告), remove(移除), restore(恢复)
  mode: "warn"
  
  # 启动时是否检测孤立实体
  check_on_startup: true
  
  # 定期清理间隔（分钟）
  periodic_cleanup_interval: 30
  
  # 是否在区块加载时检测
  check_on_chunk_load: true

cleanup:
  # 清理时是否备份数据
  backup_before_cleanup: true
  
  # 备份保留天数
  backup_retention_days: 7
  
  # 是否记录详细的清理日志
  detailed_logging: true
```

## 🎯 **用户体验**

### **管理员友好**
- 清晰的警告信息
- 详细的清理报告
- 灵活的处理选项

### **自动化处理**
- 智能检测机制
- 可配置的自动处理
- 最小化人工干预

### **安全保障**
- 数据备份机制
- 详细的操作日志
- 可撤销的操作

## 📊 **预期效果**

1. **✅ 问题解决**: 完全解决孤立摸金箱和浮空字问题
2. **✅ 性能优化**: 减少无效的实体更新和错误日志
3. **✅ 用户体验**: 提供清晰的问题提示和解决方案
4. **✅ 维护友好**: 简化服务器管理员的维护工作

这个系统将确保即使在配置文件丢失的情况下，插件也能优雅地处理孤立的摸金箱和浮空字，为用户提供最佳的使用体验。
