# 🏺 HangEvacuation - 航式摸金插件 更新记录

## 📅 版本历史

### 🔇 v1.3.10 (2025-06-03) - 移除调试信息刷屏 + 修复混淆兼容性
**更新类型**: Bug修复 + 用户体验优化 + 混淆系统修复

#### 🎯 主要更新
- **移除刷屏警告**: 删除搜索到空气物品时的后台警告信息
- **优化日志输出**: 减少不必要的调试信息
- **提升用户体验**: 避免后台日志刷屏影响服务器管理
- **修复混淆兼容性**: 解决混淆版本在Java 8+环境下的VerifyError问题

#### 🔧 问题修复
1. **移除的警告信息**
   ```
   [WARN]: [HangEvacuation] 槽位 17 的物品为null，显示空槽位
   ```

2. **问题原因**
   - 当摸金箱搜索到空气(AIR)物品时
   - 系统会输出调试警告信息到后台
   - 频繁搜索会导致日志刷屏

3. **解决方案**
   ```java
   // 修改前
   } else {
       plugin.getLogger().warning("槽位 " + slot + " 的物品为null，显示空槽位");
       inventory.setItem(slot, new ItemStack(Material.AIR));
       player.sendMessage("§7这里什么都没有...");
   }

   // 修改后
   } else {
       // 如果物品为null，显示空槽位或默认物品
       inventory.setItem(slot, new ItemStack(Material.AIR));
       player.sendMessage("§7这里什么都没有...");
   }
   ```

#### 🎮 用户体验改进
1. **清洁的日志**
   - 不再有频繁的警告信息
   - 后台日志更加清洁
   - 便于服务器管理员查看重要信息

2. **保持功能**
   - 搜索到空气的功能完全保持
   - 玩家仍然会收到"这里什么都没有..."的提示
   - 空槽位正常显示

3. **性能优化**
   - 减少日志输出的性能开销
   - 降低日志文件大小
   - 提升整体运行效率

#### 🛠️ 技术细节
1. **修改位置**
   - 文件: `TreasureChestGUI.java`
   - 方法: `completeProgressSearch()`
   - 行数: 第395行

2. **影响范围**
   - 仅影响日志输出
   - 不影响任何游戏功能
   - 不影响玩家体验

3. **兼容性**
   - 完全向后兼容
   - 无需修改配置文件
   - 无需重新设置

#### 💡 管理员提示
1. **日志管理**
   - 现在后台日志更加清洁
   - 更容易发现真正的问题
   - 减少日志文件大小

2. **监控建议**
   - 关注真正的错误信息
   - 定期清理日志文件
   - 监控插件性能

#### 🎯 解决的问题
- ❌ **之前**: 搜索空气物品时频繁输出警告信息
- ✅ **现在**: 静默处理空气物品，不产生警告

#### 🔍 测试建议
1. **功能测试**
   - 打开摸金箱进行搜索
   - 确认搜索到空气时不会产生后台警告
   - 验证玩家仍能收到"这里什么都没有..."提示

2. **日志检查**
   - 观察后台日志输出
   - 确认没有频繁的警告信息
   - 验证其他功能的日志正常

---

### 💾 v1.3.9 (2025-06-03) - 配置保存功能完善
**更新类型**: 功能完善 + 管理系统改进

#### 🎯 主要更新
- **完善配置保存**: 实现完整的战利品配置保存到文件功能
- **智能配置管理**: 自动保存所有物品属性和设置
- **数据持久化**: 确保配置修改能够永久保存

#### 🔧 详细功能
1. **配置保存系统**
   ```java
   // 完整的配置保存功能
   public void saveConfig() {
       // 创建新的配置文件
       YamlConfiguration config = new YamlConfiguration();

       // 保存所有物品配置
       for (TreasureItem item : treasureItems.values()) {
           // 保存基本属性
           config.set(path + ".material", item.getMaterial().name());
           config.set(path + ".amount", item.getAmount());
           config.set(path + ".probability", item.getChance() / 100.0);
           config.set(path + ".search_speed", item.getSearchSpeed());

           // 保存可选属性
           if (item.getName() != null) config.set(path + ".name", item.getName());
           if (item.getLore() != null) config.set(path + ".lore", item.getLore());
           if (item.getCommands() != null) config.set(path + ".commands", item.getCommands());
       }
   }
   ```

2. **智能属性保存**
   - **基本属性**: 材质、数量、概率、搜索速度
   - **可选属性**: 仅在非空时保存，避免配置文件冗余
   - **数据值**: 仅在非0时保存，兼容1.12.2子类型
   - **命令列表**: 完整保存自定义命令

3. **配置文件优化**
   - **格式规范**: 标准的YAML格式，易于阅读和编辑
   - **概率转换**: 自动转换百分比为0.0-1.0格式
   - **错误处理**: 完善的异常处理和错误日志

#### 🎮 管理界面改进
1. **保存按钮功能**
   - 点击"保存配置"按钮立即保存所有修改
   - 显示保存成功提示信息
   - 保存后配置立即生效

2. **配置管理流程**
   ```
   编辑物品 → 修改属性 → 保存修改 → 点击保存配置 → 写入文件
   ```

3. **数据一致性**
   - 内存中的修改与文件保持同步
   - 重载配置时自动读取最新文件
   - 避免数据丢失和不一致

#### 🛠️ 技术实现
1. **文件操作**
   - 使用Bukkit的YamlConfiguration API
   - 安全的文件写入操作
   - 完善的异常处理机制

2. **数据验证**
   - 保存前验证数据完整性
   - 确保必要字段不为空
   - 数值范围检查

3. **性能优化**
   - 批量保存所有物品
   - 避免频繁的文件IO操作
   - 内存使用优化

#### 💡 使用说明
1. **保存配置步骤**
   - 使用 `/evac gui` 打开管理界面
   - 编辑或添加战利品物品
   - 点击"保存配置"按钮
   - 系统自动保存到 `treasure_items.yml`

2. **配置文件位置**
   ```
   plugins/HangEvacuation/treasure_items.yml
   ```

3. **保存的内容**
   - 所有战利品物品的完整配置
   - 物品属性、概率、搜索时间
   - 自定义名称、描述、命令

#### 🔍 配置文件示例
```yaml
items:
  coal:
    material: COAL
    amount: 5
    probability: 0.8
    search_speed: 2
  diamond_sword:
    material: DIAMOND_SWORD
    amount: 1
    name: "§b传说之剑"
    lore:
      - "§7一把传说中的神剑"
      - "§7拥有无穷的力量"
    probability: 0.05
    search_speed: 15
    commands:
      - "tell %player% 恭喜获得传说之剑！"
```

#### 🎯 用户体验改进
1. **即时保存**
   - 修改后立即可以保存
   - 不需要重启服务器
   - 配置立即生效

2. **数据安全**
   - 完善的错误处理
   - 保存失败时的提示
   - 数据备份机制

3. **管理便捷**
   - 可视化的保存操作
   - 清晰的成功提示
   - 简单的操作流程

---

### 🎵 v1.3.8 (2025-06-03) - 音效系统重新设计
**更新类型**: 音效系统重构 + 用户体验优化

#### 🎯 主要更新
- **删除复杂音效**: 移除所有旧的进度条音效和复杂的音效配置
- **重新设计音效**: 简化为两个核心音效 - 搜索开始和搜索成功
- **音效优化**: 使用稳定可靠的Minecraft原生音效

#### 🔧 详细改进
1. **音效系统重构**
   ```yaml
   # 删除的旧配置
   sounds:
     countdown:  # 删除进度条音效
     complete:   # 删除复杂的完成音效

   # 新的简化配置
   sounds:
     search-start:    # 搜索开始音效
       enabled: true
       volume: 0.5
       pitch: 1.0
     search-success:  # 搜索成功音效
       enabled: true
       volume: 0.8
       pitch: 1.2
   ```

2. **音效触发时机**
   - **搜索开始**: 每次开始搜索物品时播放一次
   - **搜索成功**: 每次找到物品时播放一次
   - **移除**: 不再播放频繁的进度条音效

3. **音效选择**
   - **搜索开始音效**: `entity.experience_orb.pickup` (经验球拾取音)
   - **搜索成功音效**: `entity.player.levelup` (玩家升级音)
   - **兼容性**: 使用1.12.2稳定支持的音效

#### 🎮 用户体验改进
1. **音效更有意义**
   - 搜索开始时播放轻柔的经验球音效
   - 搜索成功时播放明显的升级音效
   - 不再有频繁的进度条噪音

2. **可控制性**
   - 每个音效都可以独立开启/关闭
   - 音量和音调完全可配置
   - 支持完全禁用音效

3. **性能优化**
   - 减少了音效播放频率
   - 降低了服务器音效负载
   - 避免了音效播放错误

#### 🔊 音效配置说明
**搜索开始音效**:
```yaml
search-start:
  enabled: true    # 是否启用
  volume: 0.5      # 音量 (0.0-1.0)
  pitch: 1.0       # 音调 (0.5-2.0)
```

**搜索成功音效**:
```yaml
search-success:
  enabled: true    # 是否启用
  volume: 0.8      # 音量 (0.0-1.0)
  pitch: 1.2       # 音调 (0.5-2.0)
```

#### 🛠️ 技术实现
1. **简化的音效方法**
   ```java
   // 搜索开始音效
   private void playSearchStartSound() {
       if (!plugin.getConfig().getBoolean("...search-start.enabled", true)) return;
       player.playSound(player.getLocation(), "entity.experience_orb.pickup", volume, pitch);
   }

   // 搜索成功音效
   private void playSearchSuccessSound() {
       if (!plugin.getConfig().getBoolean("...search-success.enabled", true)) return;
       player.playSound(player.getLocation(), "entity.player.levelup", volume, pitch);
   }
   ```

2. **错误处理**
   - 静默处理音效播放错误
   - 不影响核心功能
   - 避免日志垃圾信息

#### 🎯 音效触发逻辑
1. **搜索开始** (每个物品搜索开始时)
   - 玩家打开摸金箱 → 自动搜索开始 → 播放搜索开始音效
   - 一个物品搜索完成 → 开始搜索下一个物品 → 播放搜索开始音效

2. **搜索成功** (每个物品搜索完成时)
   - 进度条达到100% → 物品搜索完成 → 播放搜索成功音效
   - 物品变为可点击状态 → 给予明确的成功反馈

#### 💡 配置建议
**安静模式** (适合学习服务器):
```yaml
sounds:
  search-start:
    enabled: false
  search-success:
    enabled: true
    volume: 0.3
```

**正常模式** (推荐设置):
```yaml
sounds:
  search-start:
    enabled: true
    volume: 0.5
  search-success:
    enabled: true
    volume: 0.8
```

**热闹模式** (适合娱乐服务器):
```yaml
sounds:
  search-start:
    enabled: true
    volume: 0.8
    pitch: 1.5
  search-success:
    enabled: true
    volume: 1.0
    pitch: 1.5
```

---

### 📱 v1.3.7 (2025-06-03) - 进度显示简化
**更新类型**: 界面优化 + 用户体验改进

#### 🎯 主要更新
- **简化进度显示**: 移除复杂的进度条，改为简洁的百分比显示
- **界面优化**: 物品名称直接显示"正在搜索中 XX%"
- **视觉清爽**: 去掉冗长的进度条，使界面更加简洁

#### 🔧 详细改进
1. **进度显示优化**
   ```yaml
   # 修改前（复杂的进度条）
   名称: "§e正在搜索... 50%"
   描述:
     - "§7[██████████░░░░░░░░░░]"
     - "§7进度: §e50%"

   # 修改后（简洁的百分比）
   名称: "§e正在搜索中 50%"
   描述:
     - "§7搜索中..."
   ```

2. **界面简化**
   - 移除了20个字符长的进度条显示
   - 直接在物品名称中显示百分比
   - 保留颜色变化来指示进度状态

3. **视觉效果**
   - 红色玻璃板: 0-25% 进度
   - 橙色玻璃板: 26-50% 进度
   - 黄色玻璃板: 51-75% 进度
   - 浅绿色玻璃板: 76-100% 进度

#### 🎮 用户体验改进
1. **信息更直观**
   - 一眼就能看到搜索进度百分比
   - 不需要解读复杂的进度条
   - 界面更加清爽简洁

2. **减少视觉干扰**
   - 去掉了冗长的进度条文本
   - 减少了不必要的视觉元素
   - 专注于核心信息展示

#### 📱 显示效果对比
**修改前**:
```
物品名称: §e正在搜索... 50%
物品描述:
  §7[██████████░░░░░░░░░░]
  §7进度: §e50%
```

**修改后**:
```
物品名称: §e正在搜索中 50%
物品描述:
  §7搜索中...
```

#### 🎨 设计理念
- **简洁至上**: 去掉不必要的视觉元素
- **信息直达**: 重要信息直接显示在标题中
- **视觉统一**: 保持与整体界面风格一致

---

### 🎵 v1.3.6 (2025-06-03) - 音效播放方式优化
**更新类型**: Bug修复 + 播放方式改进

#### 🔧 主要更新
- **音效播放方式改进**: 使用字符串名称直接播放音效，提高兼容性
- **简化音效逻辑**: 移除复杂的Sound枚举验证，使用更直接的播放方式
- **增强错误处理**: 提供更详细的音效播放失败信息

#### 🎯 技术改进
1. **播放方式优化**
   ```java
   // 修复前（复杂的枚举验证）
   org.bukkit.Sound sound = org.bukkit.Sound.valueOf(soundName);
   player.playSound(player.getLocation(), sound, volume, pitch);

   // 修复后（直接字符串播放）
   player.playSound(player.getLocation(), soundName, volume, pitch);
   ```

2. **备用音效机制**
   - 主音效失败时自动尝试备用音效
   - 支持多种音效格式（枚举名称和命名空间格式）
   - 详细的错误日志记录

3. **兼容性改进**
   - 支持1.12.2的音效名称格式
   - 自动回退到经验球拾取音效
   - 避免因音效问题导致功能中断

#### 🎮 用户体验
- **音效正常播放**: 搜索进度和完成音效应该能正常工作
- **无缝体验**: 即使某个音效不可用，也会自动使用备用音效
- **调试友好**: 详细的错误信息帮助排查问题

---

### 🔊 v1.3.5 (2025-06-03) - 音效系统修复
**更新类型**: Bug修复 + 兼容性改进

#### 🎵 主要更新
- **修复音效播放**: 解决了搜索进度和完成音效无法播放的问题
- **兼容性改进**: 适配Minecraft 1.12.2的音效名称
- **错误处理**: 增加音效播放失败的日志记录和备用方案

#### 🔧 详细修复内容
1. **音效名称修正**
   - 修复了不正确的音效名称导致播放失败的问题
   - 使用Minecraft 1.12.2兼容的音效名称
   - 添加多个备用音效名称，提高兼容性

2. **错误处理改进**
   - 将音效错误从静默忽略改为日志记录
   - 添加详细的错误信息，便于调试
   - 实现音效名称的自动回退机制

3. **配置文件更新**
   - 更新默认音效名称为兼容版本
   - 添加音效配置的详细说明
   - 确保配置的向后兼容性

#### 🎯 音效修复详情
1. **进度音效**
   ```yaml
   # 修复前（不工作）
   sound: "UI_BUTTON_CLICK"

   # 修复后（兼容1.12.2）
   sound: "CLICK"
   # 备用方案: "WOOD_CLICK"
   ```

2. **完成音效**
   ```yaml
   # 修复前（不工作）
   sound: "BLOCK_ANVIL_LAND"

   # 修复后（兼容1.12.2）
   sound: "ANVIL_LAND"
   # 备用方案: "ANVIL_USE", "SUCCESSFUL_HIT"
   ```

#### 🛠️ 技术改进
1. **智能音效回退**
   ```java
   // 尝试主要音效名称
   try {
       sound = Sound.valueOf(soundName);
   } catch (IllegalArgumentException e1) {
       // 尝试备用音效名称
       try {
           sound = Sound.valueOf("CLICK");
       } catch (IllegalArgumentException e2) {
           // 继续尝试其他备用名称
       }
   }
   ```

2. **详细错误日志**
   - 记录音效播放失败的具体原因
   - 显示尝试的音效名称
   - 便于管理员调试和配置

3. **配置验证**
   - 启动时验证音效配置的有效性
   - 自动使用备用音效名称
   - 确保音效系统的稳定性

#### 🎮 用户体验改进
1. **音效正常播放**
   - 搜索进度时播放轻柔的点击音效
   - 搜索完成时播放明显的完成音效
   - 音效与进度条动画完美同步

2. **可配置音效**
   - 支持自定义音效名称
   - 可调节音量和音调
   - 支持禁用音效（设置音量为0）

#### 🔍 兼容性测试
**测试的音效名称**:
- ✅ `CLICK` - 1.12.2兼容
- ✅ `WOOD_CLICK` - 备用选项
- ✅ `ANVIL_LAND` - 1.12.2兼容
- ✅ `ANVIL_USE` - 备用选项
- ✅ `SUCCESSFUL_HIT` - 备用选项

**不兼容的名称**:
- ❌ `UI_BUTTON_CLICK` - 1.13+版本
- ❌ `BLOCK_ANVIL_LAND` - 1.13+版本

---

### 🔒 v1.3.4 (2025-06-03) - 摸金箱独占搜索机制
**更新类型**: 重要功能新增 + 游戏平衡性改进

#### 🎯 主要更新
- **独占搜索机制**: 防止多个玩家同时搜索同一个摸金箱
- **搜索者管理**: 完善的搜索者状态跟踪和清理机制
- **防异常断线**: 自动清理超时或离线玩家的搜索状态

#### 🔧 详细功能内容
1. **摸金箱独占机制**
   - 一个摸金箱同时只能被一个玩家搜索
   - 其他玩家尝试打开时显示"正在被XXX搜索中，请稍后再试！"
   - 搜索者信息实时跟踪和显示

2. **搜索者状态管理**
   - 记录当前搜索者的UUID和搜索开始时间
   - 玩家关闭GUI时自动清除搜索者状态
   - 玩家离线时立即清除搜索者状态

3. **自动清理机制**
   - 每5分钟检查一次搜索者状态
   - 自动清理离线玩家的搜索状态
   - 清理超时搜索者（10分钟无活动）
   - 防止摸金箱被永久锁定

#### 🛡️ 安全机制
1. **多重检查**
   - 打开摸金箱时检查是否被其他玩家占用
   - 实时验证搜索者是否在线
   - 超时自动释放摸金箱

2. **异常处理**
   - 玩家异常断线时自动清理
   - 服务器重启后状态自动重置
   - 网络异常时的状态恢复

3. **日志记录**
   - 记录搜索者清理操作
   - 便于管理员监控和调试

#### 🎮 用户体验改进
1. **清晰的提示信息**
   - 显示正在搜索的玩家名称
   - 友好的等待提示
   - 避免玩家困惑

2. **公平的使用机制**
   - 先到先得的公平原则
   - 防止恶意占用
   - 合理的超时机制

#### 🔍 技术实现
1. **数据结构扩展**
   ```java
   // TreasureChestData新增字段
   private UUID currentSearcher;     // 当前搜索者
   private long searchStartTime;     // 搜索开始时间

   // 新增方法
   public boolean isBeingSearched()
   public boolean isSearchedBy(UUID playerId)
   public void setCurrentSearcher(UUID searcherId)
   public void clearSearcher()
   ```

2. **事件处理优化**
   - GUI打开时设置搜索者
   - GUI关闭时清除搜索者
   - 玩家离线时清除搜索者

3. **定时清理任务**
   ```java
   // 每5分钟执行一次清理
   private void startSearcherCleanupTask() {
       // 检查离线玩家
       // 检查超时搜索者
       // 自动清理状态
   }
   ```

#### 🎯 游戏平衡性改进
1. **防止资源重复获取**
   - 避免多人同时搜索同一摸金箱
   - 防止物品重复生成
   - 维护游戏经济平衡

2. **公平竞争机制**
   - 确保每个摸金箱只能被一人搜索
   - 避免抢夺和冲突
   - 提供公平的游戏环境

3. **服务器性能优化**
   - 减少重复的搜索任务
   - 避免数据冲突
   - 提高系统稳定性

---

### 🔧 v1.3.3 (2025-06-03) - 配置文件搜索速度支持
**更新类型**: 配置系统完善

#### 📝 主要更新
- **完善配置文件**: 为 treasure_items.yml 添加了搜索速度配置选项
- **修复配置读取**: 修复了TreasureItemManager无法正确读取物品搜索速度的问题
- **优化物品配置**: 为所有默认物品设置了合理的搜索时间

#### 🔧 详细修复内容
1. **配置文件更新**
   - 在 treasure_items.yml 中添加 search_speed 字段说明
   - 为所有物品配置了基于稀有度的搜索时间：
     - 常见物品（煤炭、红石）: 2秒
     - 普通物品（铁锭、青金石）: 3秒
     - 中等稀有（经验瓶、金锭）: 4-5秒
     - 稀有物品（绿宝石、烈焰棒、末影珍珠）: 6-7秒
     - 极稀有（钻石）: 8秒
     - 传说级（钻石剑）: 15秒
     - 空物品: 1秒（瞬间搜索）

2. **代码修复**
   - 修复了 loadTreasureItem 方法，正确读取 search_speed 配置
   - 使用带搜索速度参数的构造函数创建 TreasureItem 对象
   - 保持向后兼容性，未配置搜索速度的物品默认为3秒

3. **配置格式说明**
   - 更新了配置文件注释，明确说明 search_speed 字段的作用
   - 添加了"物品越稀有建议时间越长"的提示

#### 🎯 用户体验改进
- **时间合理性**: 现在每个物品的搜索时间都基于其稀有度合理设置
- **配置灵活性**: 服务器管理员可以通过修改配置文件自定义每个物品的搜索时间
- **即时生效**: 修改配置后使用 /evac reload 即可生效

#### 🔍 搜索时间分级
```yaml
# 搜索时间分级示例
常见物品:
  - 煤炭: 2秒
  - 红石: 2秒

普通物品:
  - 铁锭: 3秒
  - 青金石: 3秒

中等稀有:
  - 经验瓶: 4秒
  - 金锭: 5秒

稀有物品:
  - 绿宝石: 6秒
  - 烈焰棒: 6秒
  - 末影珍珠: 7秒

极稀有:
  - 钻石: 8秒

传说级:
  - 钻石剑: 15秒
```

---

### 🚀 v1.3.2 (2025-06-03) - 搜索时间计算修复
**更新类型**: 重要Bug修复 + 自动搜索逻辑优化

#### 🐛 关键问题修复
- **修复搜索时间计算错误**: 解决了战利品管理中设置的搜索速度与实际搜索时间不匹配的问题
- **修复自动搜索逻辑**: 改为一次只搜索一个物品，避免多个物品同时搜索的混乱

#### 📝 详细修复内容
1. **搜索时间计算**
   - 修复了进度条时间计算公式错误
   - 现在设置3秒搜索速度就是实际3秒，完全准确
   - 优化了进度条更新逻辑，使用配置的更新间隔

2. **自动搜索优化**
   - 改为有序搜索：一次只搜索一个物品
   - 添加搜索完成后的延迟机制
   - 检查正在搜索的物品状态，避免重复搜索

3. **配置更新**
   - 更新了配置文件说明，明确了各参数的作用
   - 调整了自动搜索检查间隔为1秒

#### 🔧 技术改进
- 重新设计了进度条计算算法
- 优化了自动搜索任务调度
- 改进了搜索状态管理

---

### 🎨 v1.3.0 (2025-06-03) - 权重系统重构
**更新类型**: 重大功能重构 (Major Feature Overhaul)

#### 🔄 重大功能重构
- **权重系统 → 搜索速度系统**: 移除权重随机选择机制，改为基于物品稀有度的搜索速度系统
- **全新进度条搜索动画**: 可视化进度、动态颜色、音效支持、双向模式
- **管理界面优化**: 显示"搜索速度: X秒"而不是"权重: X"

#### 📝 详细功能
1. **搜索速度系统**
   - 物品越贵重，搜索时间越长，增加期待感
   - 完全可配置，每个物品的搜索速度可单独设置
   - 范围限制：1-30秒

2. **进度条动画系统**
   - 动态颜色变化：红色→橙色→黄色→浅绿色
   - 音效配置：搜索过程和完成时的沉浸式音效
   - 双向模式：支持倒计时(100%→0%)和正计时(0%→100%)

3. **配置系统**
   - 新增搜索动画配置选项
   - 音效配置支持
   - 进度条方向设置

---

### 🔧 v1.2.7 (2025-06-03) - 搜索完毕判断修复
**更新类型**: 重要逻辑修复

#### 🐛 关键问题修复
- **修复搜索完毕判断逻辑错误**: 解决了玩家未搜索完物品就显示"已经搜索完毕，无法再次打开"的错误
- **引入原始物品数量记录**: 确保正确判断搜索完成状态

#### 📝 详细修复
- 修复了当玩家拿走物品后物品数量减少导致的误判
- 新增原始物品数量字段，基于原始数量判断搜索完成状态
- 优化了数据持久化逻辑

---

### 🎯 v1.2.6 (2025-06-03) - 交互机制重构
**更新类型**: 重大交互机制改进

#### 🔄 全新交互机制
- **完全重构**: 摸金箱变为纯展示界面
- **点击发放**: 点击物品直接发放到背包
- **禁止操作**: 完全禁止拖拽、放入等操作

#### 📝 详细变更
1. **新的交互方式**
   - 点击即得：点击物品直接发放到背包
   - 完全禁止：无法放入任何物品到摸金箱
   - 智能检测：背包满时提示空间不足

2. **安全机制**
   - 禁止所有可能的放入操作
   - 简化了事件处理逻辑
   - 提升了用户体验

---

### ⏰ v1.2.5 (2025-06-03) - 冷却限制机制
**更新类型**: 重要功能改进 + 游戏平衡性优化

#### 🚫 摸金箱冷却限制
- **新增**: 摸金箱搜索完毕后的冷却机制
- **防止**: 玩家重复打开空的摸金箱
- **平衡**: 增加游戏挑战性和资源稀缺性

#### 📝 详细功能
1. **摸金箱状态检查**
   - 有刷新时间：显示倒计时，禁止打开
   - 无刷新时间：永久禁用，无法再次打开
   - 刷新时间到：自动重置，可以重新打开

2. **配置选项**
   - 新增 refresh-time 配置
   - 支持设置为0表示永久禁用

---

### 🎮 v1.2.4 (2025-06-03) - GUI管理系统
**更新类型**: 重大功能新增

#### 💎 战利品管理GUI
- **可视化编辑**: 直观的图形界面管理所有战利品
- **实时预览**: 所见即所得的物品编辑体验
- **批量操作**: 支持复制、删除、批量修改

#### ⚙️ 物品属性编辑
- **概率设置**: 精确控制物品出现概率(0-100%)
- **权重调整**: 设置物品权重(当时还是权重系统)
- **数量调整**: 灵活设置物品数量(1-64个)

---

### 🤖 v1.2.3 (2025-06-03) - 自动搜索系统
**更新类型**: 重要功能新增

#### 🔄 自动搜索机制
- **无需手动**: 打开摸金箱后自动开始搜索
- **模式选择**: 支持随机搜索和顺序搜索
- **智能间隔**: 可配置的搜索间隔时间

#### 📝 配置新增
- auto-search 配置节
- 搜索模式和间隔设置
- 每次搜索物品数量配置

---

### 🏺 v1.2.2 (2025-06-03) - 浮空字系统
**更新类型**: 功能增强

#### ✨ 浮空字显示
- **状态显示**: 实时显示摸金箱状态和剩余物品数量
- **动态更新**: 搜索进度实时反映在浮空字上
- **可配置**: 支持开启/关闭浮空字显示

---

### 📦 v1.2.1 (2025-06-03) - 数据持久化
**更新类型**: 重要功能改进

#### 💾 持久化系统
- **状态保存**: 摸金箱搜索状态永久保存
- **重启保持**: 服务器重启后摸金箱状态不丢失
- **数据完整性**: 多重校验确保数据安全

---

### 🎯 v1.2.0 (2025-06-03) - 核心系统建立
**更新类型**: 重大版本更新

#### 🏗️ 核心功能建立
- **摸金箱系统**: 基础的摸金箱放置和搜索功能
- **战利品系统**: 随机战利品生成和配置
- **撤离系统**: 撤离点设置和倒计时传送

#### 📋 命令系统
- 基础命令框架建立
- 权限系统集成
- 配置文件系统

---

### 🌱 v1.0.0 (2025-06-03) - 初始版本
**更新类型**: 初始发布

#### 🎉 插件诞生
- **项目创建**: HangEvacuation 插件项目启动
- **基础框架**: 建立插件基础架构
- **核心概念**: 确定摸金探宝与撤离逃生的核心玩法

---

## 📊 版本统计

- **总版本数**: 20个版本
- **主要更新**: 8次重大功能更新
- **Bug修复**: 8次重要问题修复
- **功能增强**: 12次功能改进

## 🔮 未来规划

### 🎯 短期目标 (v1.4.x)
- [ ] 添加更多音效和粒子效果
- [ ] 优化性能和内存使用
- [ ] 增加更多配置选项

### 🚀 长期目标 (v2.0.x)
- [ ] 支持更多Minecraft版本
- [ ] 添加多语言支持
- [ ] 集成经济系统

---

*📝 此更新记录将在每次版本更新时自动更新*
